#!/bin/bash
echo "===========================creating DNS======================="
project_id=`gcloud config list --format 'value(core.project)'`

mkdir -p /home/<USER>/ServiceAccountKeys
cd /home/<USER>

chmod -R 774 ServiceAccountKeys
cd ServiceAccountKeys

SERVICE_ACCOUNT_LIST=("runtime-gke" "gce-stage3-image-builder" "connector-cloudsql" "k8s-vault-data" "big-query-audit-log-sa" "dmeshpd10-gcs" "terraform")

for i in "${SERVICE_ACCOUNT_LIST[@]}"
do
    gsutil cp gs://"${project_id}"-key-management/"${i}"/"${i}"*.json .
done

sudo chown jenbld:jenbld /home/<USER>/ServiceAccountKeys/*

sudo chown jenbld:jenbld /home/<USER>/ServiceAccountKeys

sudo chmod 755 -R /home/<USER>/ServiceAccountKeys


gsutil cp -r gs://"${project_id}"-key-management/proxy_certs .
gsutil cp -r gs://"${project_id}"-key-management/jenkins .

chmod -R 644 /home/<USER>/ServiceAccountKeys/proxy_certs/
chmod 755 /home/<USER>/ServiceAccountKeys/proxy_certs/
chown jenbld:jenbld /home/<USER>/ServiceAccountKeys/proxy_certs/*
chown jenbld:jenbld /home/<USER>/ServiceAccountKeys/proxy_certs

chmod -R 644 /home/<USER>/ServiceAccountKeys/jenkins/
chmod 755 /home/<USER>/ServiceAccountKeys/jenkins/
chown jenbld:jenbld /home/<USER>/ServiceAccountKeys/jenkins/*
chown jenbld:jenbld /home/<USER>/ServiceAccountKeys/jenkins

mkdir -p /home/<USER>/env/dev
cd /home/<USER>/env/dev

gsutil cp -r gs://"${project_id}"-key-management/unity-microservices.* .

chmod 777 -R /home/<USER>/env/dev/
chown jenbld:jenbld /home/<USER>/env/dev/*
chown jenbld:jenbld /home/<USER>/env/dev

ls -la

echo "========download sops age key - start==============================="
if [[ $(gsutil ls gs://"${project_id}"-key-management/sops/age/keys.txt) ]]; then 
  SOPS_HOME=/home/<USER>/sops
  mkdir -p ${SOPS_HOME}/age
  gsutil cp gs://"${project_id}"-key-management/sops/age/keys.txt ${SOPS_HOME}/age/
  chown jenbld:jenbld -R ${SOPS_HOME}
fi
echo "========download sops age key - end ================================"  
echo "completed"

