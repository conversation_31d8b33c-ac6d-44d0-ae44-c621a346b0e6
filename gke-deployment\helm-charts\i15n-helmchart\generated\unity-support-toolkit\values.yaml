---
containerPort: 8080
nameOverride: "unity-support-toolkit"
replicaCount: 1
namespace: "ns-core-prod-apps"
autoscaling:
  enabled: false
podAnnotations:
  prometheus.io/scrape: "true"
  prometheus.io/port: "8080"
  prometheus.io/path: "/metrics"
  generated-time: "2025-04-24T02:28:46.*********"
  cluster-autoscaler.kubernetes.io/safe-to-evict: "true"
serviceAccount:
  name: "ns-core-prod-sa"
imagePullSecrets:
- name: "docker-secret"
image:
  repository: "gcr.io/hsbc-********-unity-prod"
  name: "unity2-kafka-operation-tool"
  prefix: "unity/i15n/"
  tag: "2.8.2"
  pullPolicy: "Always"
args: []
securityContext:
  allowPrivilegeEscalation: false
  capabilities:
    drop:
    - "ALL"
  readOnlyRootFilesystem: false
  runAsNonRoot: true
  runAsUser: *********
  seccompProfile:
    type: "RuntimeDefault"
resources:
  requests:
    cpu: "100m"
    memory: "50Mi"
  limits:
    cpu: "750m"
    memory: "1Gi"
livenessProbe:
  tcpSocket:
    port: 8080
  initialDelaySeconds: 1
  failureThreshold: 10
readinessProbe:
  tcpSocket:
    port: 8080
  initialDelaySeconds: 1
  failureThreshold: 10
hostAliases: []
service:
  enabled: true
  type: "ClusterIP"
  port: 80
  targetPort: 8080
ingress:
  enabled: true
  healthCheckPath: "/actuator/health/readiness"
  annotations:
    nginx.ingress.kubernetes.io/rewrite-target: "/"
    external-dns.alpha.kubernetes.io/ingress-hostname-source: "annotation-only"
    external-dns.alpha.kubernetes.io/hostname: "unity-support-toolkit-proxy.core-prod.vpc2.hsbc-********-unity-prod.prod.gcp.cloud.hk.hsbc"
    nginx.ingress.kubernetes.io/proxy-body-size: "25m"
  ingressClassName: "nginx"
  tls:
  - hosts:
    - "unity-support-toolkit.core-prod.vpc2.hsbc-********-unity-prod.prod.gcp.cloud.hk.hsbc"
    secretName: "ingress-tls-secret"
  hosts:
  - host: "unity-support-toolkit.core-prod.vpc2.hsbc-********-unity-prod.prod.gcp.cloud.hk.hsbc"
    paths:
    - path: "/kafka-lag-exporter"
      type: "ImplementationSpecific"
volumeMounts:
- name: "certs-volume"
  mountPath: "/hss/apps/certs"
- name: "secrets-volume"
  mountPath: "/hss/apps/secrets"
- name: "tmp-volume"
  mountPath: "/tmp"
- name: "config-volume"
  mountPath: "/hss/apps/config"
- name: "kafka-lag-exporter-volume"
  mountPath: "/opt/disk1/hss/apps/confluent/monitor/kafka-lag-exporter/conf"
- name: "runtime-config-volume"
  mountPath: "/hss/apps/config/runtime"
- name: "unity2-apps-certs-secret-i15n"
  mountPath: "/hss/apps/secrets/unity2-apps-certs-secret-i15n"
- name: "unity2-core-projection-psql-secret"
  mountPath: "/hss/apps/secrets/unity2-core-projection-psql-secret"
volumes:
- name: "runtime-config-volume"
  configMap:
    defaultMode: 493
    name: "unity-support-toolkit"
- name: "config-volume"
  emptyDir: {}
- name: "kafka-lag-exporter-volume"
  emptyDir: {}
- name: "tmp-volume"
  emptyDir: {}
- name: "certs-volume"
  configMap:
    name: "unity2-apps-certs-configmap-i15n"
- name: "secrets-volume"
  secret:
    secretName: "unity2-apps-certs-secret-i15n"
- name: "unity2-apps-certs-secret-i15n"
  secret:
    secretName: "unity2-apps-certs-secret-i15n"
- name: "unity2-core-projection-psql-secret"
  secret:
    secretName: "unity2-core-projection-psql-secret"
customLabels:
  service-category: "app-support"
  service-type: "SUPPORT_TOOLKIT"
configMapLabels: {}
kafkaAutoscalingConfigs: []
cloudSQLProxy:
  enabled: true
  image:
    repository: "gcr.io/cloud-sql-connectors/cloud-sql-proxy"
    tag: "2.15.0"
    pullPolicy: "Always"
  args:
  - "hsbc-********-unity-prod:asia-east2:core-prod-projection-558431"
  - "--port=3307"
  - "--address=127.0.0.1"
  - "--auto-iam-authn"
  - "--private-ip"
  securityContext:
    allowPrivilegeEscalation: false
    capabilities:
      drop:
      - "ALL"
    readOnlyRootFilesystem: false
    runAsNonRoot: true
    runAsUser: *********
    seccompProfile:
      type: "RuntimeDefault"
appDynamic:
  enabled: false
  image:
    repository: "gcr.io/hsbc-********-unity-prod/unity/i15n/appdynamics/java-agent"
    tag: "21.11.3.33314"
    pullPolicy: "Always"
fluentBit:
  enabled: false
  image:
    repository: "gcr.io/hsbc-********-unity-prod/unity/i15n/fluent-bit"
    tag: "3.2.5"
    pullPolicy: "Always"
target: "GKE"
envType: "PROD"
supportAttributes: {}
additionalContainers:
- name: "postgresqlclient"
  image:
    repository: "gcr.io/hsbc-********-unity-prod"
    name: "postgresql-client"
    prefix: "unity/postgresql/"
    tag: "10.21.1"
    pullPolicy: "Always"
  resources:
    requests:
      cpu: "50m"
      memory: "50Mi"
    limits:
      cpu: "500m"
      memory: "500Mi"
  args:
  - "tail"
  - "-f"
  - "/dev/null"
  env: []
  volumeMounts: []
  securityContext:
    allowPrivilegeEscalation: false
    capabilities:
      drop:
      - "ALL"
    readOnlyRootFilesystem: false
    runAsNonRoot: true
    runAsUser: 1000
    seccompProfile:
      type: "RuntimeDefault"
rbac:
  create: true
  pspEnabled: false
  pspUseAppArmor: true
  namespaced: true
  rules:
  - apiGroups:
    - ""
    resources:
    - "configmaps"
    verbs:
    - "get"
    - "watch"
    - "list"
  - apiGroups:
    - "apps"
    resources:
    - "deployments"
    - "deployments/status"
    - "deployments/scale"
    verbs:
    - "get"
    - "patch"
    - "list"
env:
- name: "KAFKA_KEYSTORE"
  value: "/hss/apps/certs/unity-microservices.jks"
- name: "KAFKA_TRUSTSTORE"
  value: "/hss/apps/certs/unity-microservices.ts"
- name: "KAFKA_SSL_CONFIG"
  value: "/hss/apps/config/unity-confluent-ssl.conf"
- name: "ENV"
  value: "PROD"
- name: "TARGET"
  value: "GKE"
- name: "KAFKA_CONSUMER_GROUP"
  value: "core-PROD-unity-support-toolkit"
- name: "PROJECT"
  value: "core"
- name: "DEFAULT_UNITY2_SCHEMA"
  value: "12bb8fb6_ba3d_4615_9b0b_f4f5cb38656f"
- name: "NAMESPACE"
  value: "ns-core-prod-apps"
- name: "ENV_NAME"
  value: "PROD"
- name: "SPRING_CONFIG_ADDITIONAL_LOCATION"
  value: "/hss/apps/config/runtime/"
- name: "SPRING_PROFILES_INCLUDE"
  value: "runtime,secret"
- name: "CLOUDSQL_ENDPOINT"
  value: ""
