#!/bin/bash

namespace=$1
lookupPod=$2
saPod=$3

path="test-framework-runtime/src/test/resources/test-cases/"

echo "Supplementing test config, namespace: ${namespace}, lookupPod: ${lookupPod}, saPod: ${saPod}"

substitution_list_file="generated/.substitutionList"

find ${path} -name 'test.json' -exec grep -Po '(?<=\$\{).*?(?=\})' {} \; | sort -u > $substitution_list_file

while IFS= read -r -u9 variable; do
  IFS=':' read -r key defaultValue<<< "$variable"

  echo "Substitute variable key: ${key}, default value: ${defaultValue}"
  pathInPod=$(kubectl exec -it ${lookupPod} -n ${namespace} -- cat /hss/apps/config/application-secret.yml | grep ${key} | sed -e 's|.*\$||' -e 's|{||g' -e 's|}".*||g' -e 's|\.|/|g')
  valueInPod=$(kubectl exec -it ${lookupPod} -n ${namespace} -- cat /hss/apps/secrets/${pathInPod})
  if [ -z "$valueInPod" ]
  then
    pathInPod=$(kubectl exec -it ${saPod} -n ${namespace} -- cat /hss/apps/config/application-secret.yml | grep ${key} | sed -e 's|.*\$||' -e 's|{||g' -e 's|}".*||g' -e 's|\.|/|g')
    valueInPod=$(kubectl exec -it ${saPod} -n ${namespace} -- cat /hss/apps/secrets/${pathInPod})
  fi

  if [ ! "$valueInPod" ]
  then
    find ${path} -name "test.json" -exec sed -i "s/\${${key}}/${defaultValue}/g" {} \;
  else
    find ${path} -name "test.json" -exec sed -i "s/\${${key}}/${valueInPod}/g" {} \;
  fi

done 9< $substitution_list_file