---
spring:
  application:
    name: "utc-timezone-offset-ocp--mnlhhsfp-hstzost-psa"
kafkaConfig:
  auto.offset.reset: "latest"
  bootstrap.servers: "kafka-0.kafka-internal.ns-core-prod-apps.svc.cluster.local:9093,kafka-1.kafka-internal.ns-core-prod-apps.svc.cluster.local:9093,kafka-2.kafka-internal.ns-core-prod-apps.svc.cluster.local:9093"
  group.id: "${KAFKA_CONSUMER_GROUP}"
  schema.registry.url: "https://hkl20077608.hc.cloud.hk.hsbc:8082/"
  security.protocol: "SSL"
  ssl.keystore.location: "/hss/apps/certs/unity-microservices.jks"
  ssl.keystore.password: "${KAFKA_KEYSTORE_PASSWORD}"
  ssl.truststore.location: "/hss/apps/certs/unity-microservices.ts"
  ssl.truststore.password: "${KAFKA_TRUSTSTORE_PASSWORD}"
management:
  endpoints:
    web:
      exposure:
        include: "health,prometheus"
  metrics:
    tags:
      application: "${spring.application.name}"
cache:
  provider: "memory"
sourceAdaptorConfig:
  projectId: "12bb8fb6-ba3d-4615-9b0b-f4f5cb38656f"
  name: "utc-timezone-offset"
  sourceAdaptor:
    sourceChannel: "KAFKA_AVRO"
    sourceDataFormat: "JSON"
    mode: "PIPELINE"
    additionalProperties:
      env: "PROD"
      sourceKafkaConfig:
        sourceKafkaProperties:
          security.protocol: "SSL"
          schema.registry.url: "https://hkl20077608.hc.cloud.hk.hsbc:8082/"
          ssl.truststore.location: "/hss/apps/certs/unity-microservices.ts"
          ssl.keystore.password: "${KAFKA_KEYSTORE_PASSWORD}"
          group.id: "${KAFKA_CONSUMER_GROUP}"
          ssl.keystore.location: "/hss/apps/certs/unity-microservices.jks"
          bootstrap.servers: "kafka-0.kafka-internal.ns-core-prod-apps.svc.cluster.local:9093,kafka-1.kafka-internal.ns-core-prod-apps.svc.cluster.local:9093,kafka-2.kafka-internal.ns-core-prod-apps.svc.cluster.local:9093"
          ssl.truststore.password: "${KAFKA_TRUSTSTORE_PASSWORD}"
          auto.offset.reset: "latest"
        sourceTopic: "OCP_.mnlhhsfp.hstzost"
    sourceDataKeyFields:
    - "SITE"
    - "TOCTCD"
    - "TOGMAB"
    kafkaPartitionKeyFields: []
    convertValuesToString: true
    routingConfig:
      mode: "DEFAULT"
      defaultTargetTopic: "unity2-PROD-core-source-adaptor-utc-timezone-offset-v1-2-0-out"
      batchTopicSuffix: "-batch"
