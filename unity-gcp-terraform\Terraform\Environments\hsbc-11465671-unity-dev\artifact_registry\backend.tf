terraform {
  required_providers {
    google = {
      source  = "hashicorp/google"
      version = ">= 5.22.0"
    }
    google-beta = {
      source  = "hashicorp/google-beta"
      version = "~> 5.22.0"
    }
    random = {
      source  = "hashicorp/random"
      version = "~> 2.2.0"
    }
    template = {
      source  = "hashicorp/template"
      version = "~> 2.1.2"
    }
    tls = {
      source  = "hashicorp/tls"
      version = "~> 2.1.0"
    }
  }
  backend "gcs" {
    bucket = "hsbc-11465671-unity-dev-terraform-state-bucket"
    prefix = "artifact_registry"
  }
}
