---
spring:
  datasource:
    url: "********************************/${DB_NAME}"
    username: "${DB_USER}"
    password: "${DB_PASSWORD}"
  jpa:
    show-sql: false
    generate-ddl: true
    properties:
      hibernate:
        dialect: "org.hibernate.dialect.PostgreSQLDialect"
        hbm2ddl:
          auto: "none"
  application:
    name: "udm-gen-ledger-acc-access"
kafkaConfig:
  auto.offset.reset: "latest"
  bootstrap.servers: "kafka-0.kafka-internal.ns-core-prod-apps.svc.cluster.local:9093,kafka-1.kafka-internal.ns-core-prod-apps.svc.cluster.local:9093,kafka-2.kafka-internal.ns-core-prod-apps.svc.cluster.local:9093"
  group.id: "${KAFKA_CONSUMER_GROUP}"
  schema.registry.url: "https://hkl20077608.hc.cloud.hk.hsbc:8082/"
  security.protocol: "SSL"
  ssl.keystore.location: "/hss/apps/certs/unity-microservices.jks"
  ssl.keystore.password: "${KAFKA_KEYSTORE_PASSWORD}"
  ssl.truststore.location: "/hss/apps/certs/unity-microservices.ts"
  ssl.truststore.password: "${KAFKA_TRUSTSTORE_PASSWORD}"
management:
  endpoints:
    web:
      exposure:
        include: "health,prometheus"
  metrics:
    tags:
      application: "${spring.application.name}"
cache:
  provider: "memory"
projectId: "12bb8fb6-ba3d-4615-9b0b-f4f5cb38656f"
accesses:
- persistenceEnabled: true
  sourceTopic: "unity2-PROD-core-transform-udm-gen-ledger-acc-out"
  tableName: "udm-gen-ledger-acc"
  columns:
  - name: "_trace_id"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "_id"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "created_by"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "created_time_stamp"
    dataType: "timestamp"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "deleted_by"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "deleted_time_stamp"
    dataType: "timestamp"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "updated_by"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "updated_time_stamp"
    dataType: "timestamp"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "_batch_id"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "SITE"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "GEACB"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "GEACS"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "GEACX"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "GEAC2N"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "GEACSN"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "GEACTP"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "GEBKSI"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "GEBLOP"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "GECABS"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "GECBBH"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "GECBBK"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "GECLBS"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "GECTCD"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "GECYCD"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "GEDLTN"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "GEDLUP"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "GEDPCD"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "GEDROP"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "GEDTAO"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "GEDTLS"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "GEDTNS"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "GEEXCD"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "GEGABS"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "GEGAT1"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "GEGAT2"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "GEGLBS"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "GEGMAB"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "GEISMR"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "GEKSAI"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "GELLBL"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "GEMDFL"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "GENOCR"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "GEOPIT"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "GERLBL"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "GERTPD"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "GESERL"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "GESMCY"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "GETLUP"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "GETRIN"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "GEVLOC"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "tranc_id"
    dataType: "text"
    isIndexed: true
    isMultiple: false
    contentSchema: []
  script: ""
  scriptEnabled: false
  retention:
    retentionEnabled: false
  sourceBatchTopicSuffix: "-batch"
  targetBatchTopicSuffix: "-batch"
  ddlAllowed: true
externalDataSourceJdbcConfigs: {}
