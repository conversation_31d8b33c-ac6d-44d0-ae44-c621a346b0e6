import com.poc.hss.fasttrack.transform.AbstractTransformer
import com.poc.hss.fasttrack.transform.model.TransformerBatchInputContext
import com.poc.hss.fasttrack.transform.model.TransformerRecord
import io.vertx.core.json.JsonObject
import org.slf4j.Logger
import org.slf4j.LoggerFactory

import java.util.stream.Collectors
class SecAdvControlTransformer extends AbstractTransformer {
    JsonObject constructRecord(JsonObject source){
        return new JsonObject()
                .put("site", source.getString("SITE")?.trim())
                .put("CADTRC", source.getString("CADTRC")?.trim())
                .put("CASER9", source.getString("CASER9")?.trim())
                .put("CACOPY", source.getString("CACOPY")?.trim())
                .put("CACTCD", source.getString("CACTCD")?.trim())
                .put("CAGMAB", source.getString("CAGMAB")?.trim())
                .put("CAACB",  source.getString("CAACB")?.trim())
                .put("CAACS", source.getString("CAACS")?.trim())
                .put("CAACX", source.getString("CAACX")?.trim())
                .put("CATXFT", source.getString("CATXFT")?.trim())
                .put("CATXFS", source.getString("CATXFS")?.trim())
                .put("CATXFQ", source.getString("CATXFQ")?.trim())
                .put("CATXFX", source.getString("CATXFX")?.trim())
                .put("CATGMT", source.getString("CATGMT")?.trim())
                .put("CATLID", source.getString("CATLID")?.trim())
    }

    private static final Logger logger = LoggerFactory.getLogger(SecAdvControlTransformer.class)

    @Override
    List<TransformerRecord> batchTransform(TransformerBatchInputContext context) {
        List<TransformerRecord> records = context.getRecords()
        return records.stream().map({
            rec ->
                JsonObject source = rec.getData();
                return rec.from(rec).data(constructRecord(source)).build()
        }).collect(Collectors.toList())
    }
}