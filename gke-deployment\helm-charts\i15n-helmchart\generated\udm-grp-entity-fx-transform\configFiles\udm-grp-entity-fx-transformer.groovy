import com.poc.hss.fasttrack.transform.AbstractTransformer
import com.poc.hss.fasttrack.transform.model.TransformerBatchInputContext
import com.poc.hss.fasttrack.transform.model.TransformerRecord
import io.vertx.core.json.JsonObject
import org.slf4j.Logger
import org.slf4j.LoggerFactory

import java.util.stream.Collectors

class GroupEntityFxTransformer extends AbstractTransformer {
    JsonObject constructRecord(JsonObject source){
        return new JsonObject()
                .put("site", source.getString("SITE")?.trim())
                .put("OPGPID", source.getString("OPGPID")?.trim())
                .put("OPEYID", source.getString("OPEYID")?.trim())
                .put("ODCTCD", source.getString("ODCTCD")?.trim())
                .put("ODGMAB", source.getString("ODGMAB")?.trim())
                .put("ODACB", source.getString("ODACB")?.trim())
                .put("ODACS", source.getString("ODACS")?.trim())
                .put("ODACX", source.getString("ODACX")?.trim())
                .put("ODTYCC", source.getString("ODTYCC")?.trim())
                .put("ODDCIN", source.getString("ODDCIN")?.trim())
                .put("ODAFCY", source.getString("ODAFCY")?.trim())
                .put("ODATCY", source.getString("ODATCY")?.trim())
                .put("ODALFR", source.getString("ODALFR")?.trim())
                .put("ODACOI", source.getString("ODACOI")?.trim())
                .put("ODDLUP", source.getString("ODDLUP")?.trim())
    }

    private static final Logger logger = LoggerFactory.getLogger(GroupEntityFxTransformer.class)

    @Override
    List<TransformerRecord> batchTransform(TransformerBatchInputContext context) {
        List<TransformerRecord> records = context.getRecords()
        return records.stream().map({
            rec ->
                JsonObject source = rec.getData();
                return rec.from(rec).data(constructRecord(source)).build()
        }).collect(Collectors.toList())
    }
}