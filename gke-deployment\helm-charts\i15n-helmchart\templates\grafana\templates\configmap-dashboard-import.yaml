{{- $files := .Files }}
{{- $target := printf "dashboards/%s/*.json" (.Values.target|lower)}}
{{- $target2 := printf "dashboards/%s-%s/*.json" (.Values.target|lower) "nonprod"}}
{{- $custom_chart1 := printf "grafana/dashboards/%s/*.json" (.Values.target|lower)}}
{{- $custom_chart2 := printf "grafana/dashboards/%s-%s/*.json" (.Values.target|lower) "nonprod"}}
{{- if eq .Values.envType "PROD" }}
{{- $target2 = printf "dashboards/%s-%s/*.json" (.Values.target|lower) "prod" }}
{{- $custom_chart2 = printf "grafana/dashboards/%s-%s/*.json" (.Values.target|lower) "prod" }}
{{- end }}
{{- range tuple $target $target2 $custom_chart1 $custom_chart2 "grafana/dashboards/*.json" "dashboards/*.json"}}
{{- range $file, $_ :=  $files.Glob . }}
apiVersion: v1
kind: ConfigMap
metadata:
  name: grafana-dashboard-{{$file|base|replace "." "-"|replace "-json" ""|lower}}
  namespace: {{ $.Release.Namespace  }}
  labels:
    grafana_dashboard: "1"
    {{- include "grafana.labels" $ | nindent 4 }}
binaryData:
  {{$file|base}}: {{ $files.Get $file|b64enc }}
---
{{- end}}
{{- end}}
