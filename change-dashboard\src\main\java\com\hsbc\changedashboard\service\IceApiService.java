package com.hsbc.changedashboard.service;

import com.hsbc.changedashboard.config.ApiConfig;
import com.hsbc.changedashboard.model.IceApiResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

@Service
public class IceApiService {

    private static final Logger logger = LoggerFactory.getLogger(IceApiService.class);

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private ApiConfig apiConfig;

    @Autowired
    private DateUtilService dateUtilService;

    public IceApiResponse getChanges() {
        try {
            String currentWeekMonday = dateUtilService.getCurrentWeekMonday();
            String nextWeekMonday = dateUtilService.getNextWeekMonday();

            logger.info("Fetching changes from ICE API for current week Monday: {} to next week Monday: {}", 
                       currentWeekMonday, nextWeekMonday);

            String url = UriComponentsBuilder.fromHttpUrl(apiConfig.getIce().getBaseUrl())
                    .path("/ice/api/v4/changes")
                    .queryParam("fields", apiConfig.getIce().getFields())
                    .queryParam("anyGsdAppIDs", apiConfig.getIce().getAppIds())
                    .queryParam("limit", "500")
                    .queryParam("beforeGsdScheduledStartDate", nextWeekMonday)
                    .queryParam("afterGsdScheduledStartDate", currentWeekMonday)
                    .queryParam("beforeGsdScheduledEndDate", nextWeekMonday)
                    .queryParam("afterGsdScheduledEndDate", currentWeekMonday)
                    .queryParam("gsdIsProduction", "true")
                    .toUriString();

            HttpHeaders headers = new HttpHeaders();
            headers.set("Authorization", apiConfig.getIce().getAuthorization());

            HttpEntity<String> entity = new HttpEntity<>(headers);

            logger.debug("Making request to ICE API: {}", url);

            ResponseEntity<IceApiResponse> response = restTemplate.exchange(
                    url, HttpMethod.GET, entity, IceApiResponse.class);

            logger.info("Successfully fetched {} changes from ICE API", 
                       response.getBody() != null && response.getBody().getChanges() != null ? 
                       response.getBody().getChanges().size() : 0);

            return response.getBody();

        } catch (Exception e) {
            logger.error("Error fetching changes from ICE API", e);
            throw new RuntimeException("Failed to fetch changes from ICE API", e);
        }
    }
}
