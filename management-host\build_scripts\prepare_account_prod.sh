#!/bin/bash
echo "$0 - started"
echo "[MGMT_HOST]service account key is copying from gcs is started"
cd $HOME

cd /root
echo "changed to root directory"
echo $PWD

mkdir -p .ssh
chmod -R 700 .ssh

cd $HOME

mkdir -p ServiceAccountKeys
chmod -R 774 ServiceAccountKeys
cd ServiceAccountKeys

#SERVICE_ACCOUNT_LIST=("gce-stage3-image-builder" "terraform,connector-cloudsql" "k8s-vault-data" "big-query-audit-log-sa")

#for i in "${SERVICE_ACCOUNT_LIST[@]}"
#do
#    gsutil cp gs://$PROJECT_ID-key-management/"${i}"/"${i}"-$PROJECT_ID.json.json .
#done

ls -la
echo "$0 - ended"