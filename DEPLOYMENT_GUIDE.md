# Deployment Guide: change-dashboard & risc

This guide provides step-by-step instructions for building, packaging, and deploying the `change-dashboard` and `risc` applications to GKE using your existing infrastructure with a modular stage-based approach.

## Prerequisites

### Required Tools
- Maven 3.6+
- Docker
- kubectl (configured for your GKE cluster)
- Helm 3.x
- yq (for YAML processing)
- Access to HSBC Nexus repositories
- Access to GCR (gcr.io/hsbc-9087302-unity-dev)

### Required Access
- GKE cluster access (gke-t2-vpc3)
- Namespace: ns-i15n-dev, ns-i15n-uat, ns-i15n-prod
- Service account: ns-i15n-{env}-sa
- Docker registry push permissions

## Quick Start

### Super Quick (Recommended for first-time users)

```bash
# Make scripts executable
chmod +x quick-start.sh

# Complete pipeline to dev environment
./quick-start.sh full-dev

# Deploy to UAT environment
./quick-start.sh full-uat

# Deploy to production (with confirmation)
./quick-start.sh full-prod
```

### Stage-Based Approach (Recommended for CI/CD)

```bash
# Make all scripts executable
chmod +x pipeline.sh stage-*.sh

# Run complete pipeline
./pipeline.sh

# Run specific stages
./pipeline.sh --stages maven-build,image-build

# Run for specific projects
./pipeline.sh --projects change-dashboard

# Run with options
./pipeline.sh --environment uat --parallel --push --wait
```

### Individual Stage Execution

```bash
# Stage 1: Maven Build
./stage-1-maven-build.sh change-dashboard
./stage-1-maven-build.sh risc --skip-tests

# Stage 2: Security Scanning
./stage-2-security-scan.sh change-dashboard
./stage-2-security-scan.sh risc --skip-sast

# Stage 3: Image Build
./stage-3-image-build.sh change-dashboard --push
./stage-3-image-build.sh risc --scan

# Stage 4: Deploy
./stage-4-deploy.sh change-dashboard --environment dev
./stage-4-deploy.sh risc --environment uat --wait
```

## Detailed Process

### 1. Build Process

The build process uses your existing `devops-helper` infrastructure:

#### For change-dashboard:
```bash
cd change-dashboard
source .devops/build.properties
../devops-helper/src/devops/scripts/build/actions.bash mvn_build
../devops-helper/src/devops/scripts/build/actions.bash docker_build
../devops-helper/src/devops/scripts/build/actions.bash container_scan
```

#### For risc:
```bash
cd risc
source .devops/build.properties
../devops-helper/src/devops/scripts/build/actions.bash mvn_build
../devops-helper/src/devops/scripts/build/actions.bash docker_build
../devops-helper/src/devops/scripts/build/actions.bash container_scan
```

### 2. Docker Images

Images are built and tagged as:
- `gcr.io/hsbc-9087302-unity-dev/unity/i15n/change-dashboard:0.0.1-SNAPSHOT`
- `gcr.io/hsbc-9087302-unity-dev/unity/i15n/risc:0.0.1-SNAPSHOT`

### 3. Deployment Process

The deployment uses your existing `gke-deployment` infrastructure:

```bash
cd gke-deployment
./deploy.sh change-dashboard ns-i15n-dev 5 start
./deploy.sh risc ns-i15n-dev 5 start
```

## Configuration Files

### Build Configuration
- `change-dashboard/.devops/build.properties`
- `risc/.devops/build.properties`

### Helm Values
- `gke-deployment/helm-charts/i15n-helmchart/change-dashboard/values.yaml`
- `gke-deployment/helm-charts/i15n-helmchart/risc/values.yaml`

### Docker Configuration
- `change-dashboard/Dockerfile`
- `risc/Dockerfile`

## Jenkins Integration

To integrate with your existing Jenkins pipeline:

1. Add the services to your charts list in `Jenkinsfile_custom`:
```groovy
def charts = [
    // ... existing charts ...
    'change-dashboard',
    'risc'
]
```

2. The existing pipeline will automatically:
   - Build the Maven projects
   - Create Docker images
   - Run security scans
   - Deploy using Helm
   - Monitor deployment status

## Monitoring and Health Checks

### Health Check Endpoints
- change-dashboard: `http://change-dashboard:8080/actuator/health`
- risc: `http://risc:8080/actuator/health`

### Prometheus Metrics
- change-dashboard: `http://change-dashboard:8080/actuator/prometheus`
- risc: `http://risc:8080/actuator/prometheus`

### Application URLs (if ingress enabled)
- change-dashboard: `https://change-dashboard.hsbc-9087302-unity-dev.dev.gcp.cloud.hk.hsbc`
- risc: `https://risc.hsbc-9087302-unity-dev.dev.gcp.cloud.hk.hsbc`

## Troubleshooting

### Common Issues

1. **Build Failures**
   - Check Maven dependencies and repository access
   - Verify Java version (should be 17)
   - Check Docker daemon is running

2. **Docker Build Issues**
   - Ensure auth.conf is properly configured
   - Verify access to HSBC base images
   - Check Docker BuildKit is enabled

3. **Deployment Issues**
   - Verify kubectl connectivity: `kubectl cluster-info`
   - Check namespace exists: `kubectl get namespace ns-i15n-dev`
   - Verify service account: `kubectl get sa ns-i15n-dev-sa -n ns-i15n-dev`

4. **Image Pull Issues**
   - Check docker-secret exists: `kubectl get secret docker-secret -n ns-i15n-dev`
   - Verify GCR access permissions

### Debugging Commands

```bash
# Check pod status
kubectl get pods -n ns-i15n-dev -l app.kubernetes.io/name=change-dashboard
kubectl get pods -n ns-i15n-dev -l app.kubernetes.io/name=risc

# Check pod logs
kubectl logs -n ns-i15n-dev deployment/change-dashboard
kubectl logs -n ns-i15n-dev deployment/risc

# Check service status
kubectl get svc -n ns-i15n-dev

# Check ingress status
kubectl get ingress -n ns-i15n-dev

# Describe deployment for detailed info
kubectl describe deployment change-dashboard -n ns-i15n-dev
kubectl describe deployment risc -n ns-i15n-dev
```

## Security Considerations

- All images use enterprise-approved base images
- Security scanning is enabled for both SAST and container scans
- Applications run as non-root users
- Network policies can be applied as needed
- Secrets are managed through Kubernetes secrets

## Next Steps

1. **Testing**: Run integration tests against deployed applications
2. **Monitoring**: Set up alerts and dashboards
3. **Scaling**: Configure HPA based on actual usage patterns
4. **Security**: Review and apply additional security policies
5. **CI/CD**: Integrate with your Jenkins pipeline for automated deployments
