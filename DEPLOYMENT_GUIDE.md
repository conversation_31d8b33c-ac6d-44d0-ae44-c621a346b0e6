# Deployment Guide: change-dashboard & risc

This guide provides step-by-step instructions for building, packaging, and deploying the `change-dashboard` and `risc` applications to GKE using your existing infrastructure.

## Prerequisites

### Required Tools
- <PERSON>ven 3.6+
- Docker
- kubectl (configured for your GKE cluster)
- Helm 3.x
- Access to HSBC Nexus repositories
- Access to GCR (gcr.io/hsbc-9087302-unity-dev)

### Required Access
- GKE cluster access (gke-t2-vpc3)
- Namespace: ns-i15n-dev
- Service account: ns-i15n-dev-sa
- Docker registry push permissions

## Quick Start

### Option 1: Build and Deploy Everything
```bash
# Make scripts executable
chmod +x build-and-deploy-all.sh

# Build and deploy both applications
./build-and-deploy-all.sh
```

### Option 2: Individual Steps

#### Build Only
```bash
# Build both applications
./build-and-deploy-all.sh build-only

# Or build individually
./build-change-dashboard.sh
./build-risc.sh
```

#### Deploy Only
```bash
# Deploy both applications (assumes images are already built)
./build-and-deploy-all.sh deploy-only

# Or deploy individually
./deploy-change-dashboard.sh
./deploy-risc.sh
```

## Detailed Process

### 1. Build Process

The build process uses your existing `devops-helper` infrastructure:

#### For change-dashboard:
```bash
cd change-dashboard
source .devops/build.properties
../devops-helper/src/devops/scripts/build/actions.bash mvn_build
../devops-helper/src/devops/scripts/build/actions.bash docker_build
../devops-helper/src/devops/scripts/build/actions.bash container_scan
```

#### For risc:
```bash
cd risc
source .devops/build.properties
../devops-helper/src/devops/scripts/build/actions.bash mvn_build
../devops-helper/src/devops/scripts/build/actions.bash docker_build
../devops-helper/src/devops/scripts/build/actions.bash container_scan
```

### 2. Docker Images

Images are built and tagged as:
- `gcr.io/hsbc-9087302-unity-dev/unity/i15n/change-dashboard:0.0.1-SNAPSHOT`
- `gcr.io/hsbc-9087302-unity-dev/unity/i15n/risc:0.0.1-SNAPSHOT`

### 3. Deployment Process

The deployment uses your existing `gke-deployment` infrastructure:

```bash
cd gke-deployment
./deploy.sh change-dashboard ns-i15n-dev 5 start
./deploy.sh risc ns-i15n-dev 5 start
```

## Configuration Files

### Build Configuration
- `change-dashboard/.devops/build.properties`
- `risc/.devops/build.properties`

### Helm Values
- `gke-deployment/helm-charts/i15n-helmchart/change-dashboard/values.yaml`
- `gke-deployment/helm-charts/i15n-helmchart/risc/values.yaml`

### Docker Configuration
- `change-dashboard/Dockerfile`
- `risc/Dockerfile`

## Jenkins Integration

To integrate with your existing Jenkins pipeline:

1. Add the services to your charts list in `Jenkinsfile_custom`:
```groovy
def charts = [
    // ... existing charts ...
    'change-dashboard',
    'risc'
]
```

2. The existing pipeline will automatically:
   - Build the Maven projects
   - Create Docker images
   - Run security scans
   - Deploy using Helm
   - Monitor deployment status

## Monitoring and Health Checks

### Health Check Endpoints
- change-dashboard: `http://change-dashboard:8080/actuator/health`
- risc: `http://risc:8080/actuator/health`

### Prometheus Metrics
- change-dashboard: `http://change-dashboard:8080/actuator/prometheus`
- risc: `http://risc:8080/actuator/prometheus`

### Application URLs (if ingress enabled)
- change-dashboard: `https://change-dashboard.hsbc-9087302-unity-dev.dev.gcp.cloud.hk.hsbc`
- risc: `https://risc.hsbc-9087302-unity-dev.dev.gcp.cloud.hk.hsbc`

## Troubleshooting

### Common Issues

1. **Build Failures**
   - Check Maven dependencies and repository access
   - Verify Java version (should be 17)
   - Check Docker daemon is running

2. **Docker Build Issues**
   - Ensure auth.conf is properly configured
   - Verify access to HSBC base images
   - Check Docker BuildKit is enabled

3. **Deployment Issues**
   - Verify kubectl connectivity: `kubectl cluster-info`
   - Check namespace exists: `kubectl get namespace ns-i15n-dev`
   - Verify service account: `kubectl get sa ns-i15n-dev-sa -n ns-i15n-dev`

4. **Image Pull Issues**
   - Check docker-secret exists: `kubectl get secret docker-secret -n ns-i15n-dev`
   - Verify GCR access permissions

### Debugging Commands

```bash
# Check pod status
kubectl get pods -n ns-i15n-dev -l app.kubernetes.io/name=change-dashboard
kubectl get pods -n ns-i15n-dev -l app.kubernetes.io/name=risc

# Check pod logs
kubectl logs -n ns-i15n-dev deployment/change-dashboard
kubectl logs -n ns-i15n-dev deployment/risc

# Check service status
kubectl get svc -n ns-i15n-dev

# Check ingress status
kubectl get ingress -n ns-i15n-dev

# Describe deployment for detailed info
kubectl describe deployment change-dashboard -n ns-i15n-dev
kubectl describe deployment risc -n ns-i15n-dev
```

## Security Considerations

- All images use enterprise-approved base images
- Security scanning is enabled for both SAST and container scans
- Applications run as non-root users
- Network policies can be applied as needed
- Secrets are managed through Kubernetes secrets

## Next Steps

1. **Testing**: Run integration tests against deployed applications
2. **Monitoring**: Set up alerts and dashboards
3. **Scaling**: Configure HPA based on actual usage patterns
4. **Security**: Review and apply additional security policies
5. **CI/CD**: Integrate with your Jenkins pipeline for automated deployments
