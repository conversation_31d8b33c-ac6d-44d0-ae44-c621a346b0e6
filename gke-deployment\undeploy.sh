#!/bin/bash
if [ $# == 0 ]; then
    echo "usage: $0 [appName] [namespace]"
    exit 1
fi

app_name=$1
namespace=$2
echo -------------------------------------------------------------------------------------------------------------------------
result=`helm uninstall ${app_name} -n ${namespace} 2>&1`;
if [[ $? == 0 || $result == *"release: not found"  ]] ; then
  echo "Normal:" $result
else
  echo -------------------------------------------------------------------------------------------------------------------------
  exit 1
fi
echo -------------------------------------------------------------------------------------------------------------------------