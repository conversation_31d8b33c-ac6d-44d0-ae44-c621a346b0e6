#!/bin/bash
set -euo pipefail

# Stage 1: Maven Build
# Usage: ./stage-1-maven-build.sh <project-name> [options]

# Function to print usage
usage() {
    echo "Usage: $0 <project-name> [options]"
    echo ""
    echo "Arguments:"
    echo "  project-name    Name of the project directory (e.g., change-dashboard, risc)"
    echo ""
    echo "Options:"
    echo "  --skip-tests    Skip running tests"
    echo "  --clean         Run clean before build"
    echo "  --offline       Run Maven in offline mode"
    echo "  --help, -h      Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 change-dashboard"
    echo "  $0 risc --skip-tests"
    echo "  $0 change-dashboard --clean --offline"
    exit 1
}

# Parse arguments
if [ $# -lt 1 ]; then
    echo "Error: Project name is required"
    usage
fi

PROJECT_NAME="$1"
shift

# Default options
SKIP_TESTS=false
CLEAN_BUILD=false
OFFLINE_MODE=false

# Parse options
while [[ $# -gt 0 ]]; do
    case $1 in
        --skip-tests)
            SKIP_TESTS=true
            shift
            ;;
        --clean)
            CLEAN_BUILD=true
            shift
            ;;
        --offline)
            OFFLINE_MODE=true
            shift
            ;;
        --help|-h)
            usage
            ;;
        *)
            echo "Error: Unknown option $1"
            usage
            ;;
    esac
done

echo "=== Stage 1: Maven Build for ${PROJECT_NAME} ==="
echo "Configuration:"
echo "  Project: ${PROJECT_NAME}"
echo "  Skip Tests: ${SKIP_TESTS}"
echo "  Clean Build: ${CLEAN_BUILD}"
echo "  Offline Mode: ${OFFLINE_MODE}"

# Validate project directory exists
if [ ! -d "${PROJECT_NAME}" ]; then
    echo "Error: Project directory '${PROJECT_NAME}' not found"
    echo "Available projects:"
    ls -d */ 2>/dev/null | grep -E "(change-dashboard|risc)" || echo "  No valid projects found"
    exit 1
fi

# Load build configuration
BUILD_CONFIG_FILE="${PROJECT_NAME}/.devops/build.properties"
if [ -f "${BUILD_CONFIG_FILE}" ]; then
    echo "Loading build configuration from ${BUILD_CONFIG_FILE}"
    source "${BUILD_CONFIG_FILE}"
else
    echo "Warning: No build.properties found at ${BUILD_CONFIG_FILE}, using defaults"
    # Set default values
    export mvn_build_enable=true
    export jdk_version=17
    export mvn_skip_tests=false
    export mvn_maven_test_skip=false
    export mvn_opts="-Xmx2048m -Djavax.net.ssl.trustStore=/etc/pki/java/cacerts"
fi

# Override test settings if skip-tests is specified
if [ "${SKIP_TESTS}" = "true" ]; then
    export mvn_skip_tests=true
    export mvn_maven_test_skip=true
fi

# Validate devops-helper exists
DEVOPS_HELPER_SCRIPT="devops-helper/src/devops/scripts/build/actions.bash"
if [ ! -f "${DEVOPS_HELPER_SCRIPT}" ]; then
    echo "Error: devops-helper script not found at ${DEVOPS_HELPER_SCRIPT}"
    echo "Please ensure devops-helper is available in the repository"
    exit 1
fi

# Change to project directory
cd "${PROJECT_NAME}"

# Check if pom.xml exists
if [ ! -f "pom.xml" ]; then
    echo "Error: pom.xml not found in ${PROJECT_NAME}"
    echo "This script is designed for Maven projects"
    exit 1
fi

# Get project information
PROJECT_VERSION=$(mvn help:evaluate -Dexpression=project.version -q -DforceStdout 2>/dev/null || echo "unknown")
PROJECT_ARTIFACT_ID=$(mvn help:evaluate -Dexpression=project.artifactId -q -DforceStdout 2>/dev/null || echo "unknown")
echo "Project Artifact ID: ${PROJECT_ARTIFACT_ID}"
echo "Project Version: ${PROJECT_VERSION}"

# Ensure Maven wrapper or Maven is available
if [ -f "./mvnw" ]; then
    echo "Using Maven wrapper"
    MVN_CMD="./mvnw"
elif command -v mvn &> /dev/null; then
    echo "Using system Maven"
    MVN_CMD="mvn"
else
    echo "Error: Neither Maven wrapper nor system Maven found"
    exit 1
fi

# Check Maven version
echo "Maven version:"
${MVN_CMD} --version

# Set Maven options
MAVEN_OPTS="${mvn_opts:-}"
if [ "${OFFLINE_MODE}" = "true" ]; then
    MAVEN_OPTS="${MAVEN_OPTS} -o"
fi

echo ""
echo "=== Running Maven Build ==="

# Determine Maven goals
MAVEN_GOALS="compile"
if [ "${CLEAN_BUILD}" = "true" ]; then
    MAVEN_GOALS="clean ${MAVEN_GOALS}"
fi

# Add package goal
MAVEN_GOALS="${MAVEN_GOALS} package"

# Set test options
TEST_OPTIONS=""
if [ "${mvn_skip_tests}" = "true" ]; then
    TEST_OPTIONS="${TEST_OPTIONS} -DskipTests=true"
fi
if [ "${mvn_maven_test_skip}" = "true" ]; then
    TEST_OPTIONS="${TEST_OPTIONS} -Dmaven.test.skip=true"
fi

# Use devops-helper for Maven build
echo "Running Maven build using devops-helper..."
echo "Command: ${DEVOPS_HELPER_SCRIPT} mvn_build"
../"${DEVOPS_HELPER_SCRIPT}" mvn_build

BUILD_EXIT_CODE=$?

if [ ${BUILD_EXIT_CODE} -eq 0 ]; then
    echo ""
    echo "=== Maven Build Successful ==="
    
    # Verify JAR file was created
    JAR_FILE="target/${PROJECT_ARTIFACT_ID}-${PROJECT_VERSION}.jar"
    if [ -f "${JAR_FILE}" ]; then
        echo "✅ JAR file created: ${JAR_FILE}"
        ls -lh "${JAR_FILE}"
        
        # Check if it's an executable JAR
        if jar tf "${JAR_FILE}" | grep -q "BOOT-INF"; then
            echo "✅ Spring Boot executable JAR detected"
        else
            echo "ℹ️  Standard JAR file (not Spring Boot executable)"
        fi
    else
        echo "⚠️  Warning: Expected JAR file not found: ${JAR_FILE}"
        echo "Available JAR files:"
        ls -lh target/*.jar 2>/dev/null || echo "  No JAR files found"
    fi
    
    # Show target directory contents
    echo ""
    echo "Target directory contents:"
    ls -la target/ | head -10
    
    # Create build info file for next stages
    BUILD_INFO_FILE="target/build-info.properties"
    cat > "${BUILD_INFO_FILE}" <<EOF
# Build information for ${PROJECT_NAME}
project.name=${PROJECT_NAME}
project.artifactId=${PROJECT_ARTIFACT_ID}
project.version=${PROJECT_VERSION}
build.timestamp=$(date -u +"%Y-%m-%dT%H:%M:%SZ")
build.stage=maven-build
build.status=success
jar.file=${JAR_FILE}
EOF
    echo "✅ Build info saved to ${BUILD_INFO_FILE}"
    
else
    echo ""
    echo "❌ Maven Build Failed"
    echo "Exit code: ${BUILD_EXIT_CODE}"
    
    # Create failure info
    BUILD_INFO_FILE="target/build-info.properties"
    mkdir -p target
    cat > "${BUILD_INFO_FILE}" <<EOF
# Build information for ${PROJECT_NAME}
project.name=${PROJECT_NAME}
project.artifactId=${PROJECT_ARTIFACT_ID}
project.version=${PROJECT_VERSION}
build.timestamp=$(date -u +"%Y-%m-%dT%H:%M:%SZ")
build.stage=maven-build
build.status=failed
build.exit_code=${BUILD_EXIT_CODE}
EOF
    
    exit ${BUILD_EXIT_CODE}
fi

echo ""
echo "=== Stage 1 Complete: Maven Build ==="
echo "Next stage: ./stage-2-security-scan.sh ${PROJECT_NAME}"

# Return to original directory
cd ..
