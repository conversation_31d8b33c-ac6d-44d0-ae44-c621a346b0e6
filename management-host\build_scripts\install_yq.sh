#!/usr/bin/env bash
set -euo pipefail
artifacts_repo=https://hkl20090861.hc.cloud.hk.hsbc/devops
echo "<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<< yq installation >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>"
cd /tmp
echo "[MGMT_HOST] changed path to /tmp"
echo "[MGMT_HOST] downloading yq binary from bucket"
echo $PWD
curl ${artifacts_repo}/yq-4.13.4.gz | tar -xz
mv yq_linux_amd64 /usr/local/bin/yq
chmod 755 /usr/local/bin/yq
echo "<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<< yq installation completed >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>"
