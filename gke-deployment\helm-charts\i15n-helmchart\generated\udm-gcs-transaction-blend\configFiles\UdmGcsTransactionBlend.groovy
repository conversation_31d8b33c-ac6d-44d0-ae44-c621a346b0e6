import com.poc.hss.fasttrack.service.LookupService
import com.poc.hss.fasttrack.transform.AbstractTransformer
import com.poc.hss.fasttrack.transform.model.TransformerBatchInputContext
import com.poc.hss.fasttrack.transform.model.TransformerRecord
import io.vertx.core.json.JsonObject
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import java.text.DecimalFormat
import java.util.stream.Collectors

class UdmGcsTransactionBlend extends AbstractTransformer {

    private static final Logger logger = LoggerFactory.getLogger(UdmGcsTransactionBlend.class);

    List<TransformerRecord> batchTransform(TransformerBatchInputContext context) {
        List<TransformerRecord> finalOutputData = new ArrayList<>()
        LookupService lookupService = context.getLookupService()
        List<TransformerRecord> records = context.getRecords()
        Map<String, List<TransformerRecord>> records_by_source =
                records.stream().collect(Collectors.groupingBy(rec -> rec.source))

        List<TransformerRecord> tradeRecords = records_by_source.get(UdmGcsTransactionConstant.GCS_TRADE);
        if (UdmGcsTransactionHelper.isListNotNullAndEmpty.test(tradeRecords)) {
            logger.info("================UDM GCS TRANSACTION BLEND=================")
            finalOutputData = processTradeDataEvent(tradeRecords, lookupService)
        }
        List<TransformerRecord> outputFieldList = new ArrayList<>()
        if (null != finalOutputData && finalOutputData.size() > 0) {
            // map transaction data to API output format
            outputFieldList = mapGcsTradeFields(finalOutputData)
        }
        return outputFieldList;
    }

    static List<TransformerRecord> processTradeDataEvent(List<TransformerRecord> tradeRecordList, LookupService lookupService) {
        //1. get tradeMap for received trade record from GCS_TRADE table
        String tradeListToProcess = tradeRecordList.stream()
                .map(rec -> rec.getId())
                .distinct()
                .collect(Collectors.joining(","));
        logger.info("Start Processing TRADE Data Business Event for records :: " + tradeListToProcess)
        //1. query and blend Counterparty data from GCS_COUNTERPARTY using counterparty_id for respective trade
        UdmGcsCounterparty.queryAndBlendCounterPartyData.accept(tradeRecordList, lookupService)

        //2. query and blend Account data from GCS_ACCOUNT using account_id for respective trade
        UdmGcsAccount.queryAndBlendAccountData.accept(tradeRecordList, lookupService)

        //3. query and blend location data from GCS_LOCATION using location_id,place_of_safekeeping for respective trade
        UdmGcsLocation.queryAndBlendLocationData.accept(tradeRecordList, lookupService)

        //4. query and blend Instrument data from GCS_INSTRUMENT using instrument_id for respective trade
        UdmGcsInstrument.queryAndBendInstrumentData.accept(tradeRecordList, lookupService)

        //5. query and blend Instrument data from GCS_INSTRUMENT_PRICE using instrument_id for respective trade
        UdmGcsInstrumentPrice.queryAndBendInstrumentPriceData.accept(tradeRecordList, lookupService)

        //6. query and blend Client data from GCS_CLIENT using legal_entity_id+legal_entity_sub_fund_indicator for respective trade
        UdmGcsClient.queryAndBlendClientData.accept(tradeRecordList, lookupService)

        return tradeRecordList
    }

    static List<TransformerRecord> mapGcsTradeFields(List<TransformerRecord> targetData) {
        return targetData.stream()
                .map(record -> {
                    JsonObject msg = record.getData()
                    String _id = msg.getString("_id") != null ? msg.getString("_id") : record.getId()
                    logger.info("Upserting records for id : " + record.getId())
                    String externalTradeStatus = UdmGcsTransactionHelper.deriveExternalTradeStatus(msg)
                    return TransformerRecord.from(record).id(_id)
                            .data(new JsonObject()
                                    .put("site", msg.getString("site").toUpperCase())
                                    .put("SiteCode", msg.getString("site").toUpperCase())
                                    .put("HSBCTransactionIdentification", UdmGcsTransactionHelper.deriveHbcTransactionIdentification(msg))
                                    .put("ClientTransactionIdentification", msg.getString("customer_reference"))
                                    .put("LocalCustodianName", UdmGcsTransactionHelper.deriveLocalCustodianName(msg))
                                    .put("SafekeepingAccountIdentification", UdmGcsTransactionHelper.deriveSafekeepingAccountIdentification(msg.getString("sec_acc_id")))
                                    .put("sec_acc_id", msg.getString("sec_acc_id"))
                                    .put("SafekeepingAccountName", UdmGcsTransactionHelper.formatOutputFieldNullToEmpty(msg.getString("account_name")))
                                    .put("SourceSystemTransactionType", UdmGcsTransactionHelper.deriveSourceSystemTransactionType(msg.getString("transaction_type")))
                                    .put("SecuritiesMovementType", UdmGcsTransactionHelper.deriveSecuritiesMovementType(msg))
                                    .put("FinancialInstrumentIdentificationIsin", UdmGcsTransactionHelper.formatOutputFieldNullToEmpty(msg.getString("instrument_id")))
                                    .put("FinancialInstrumentIdentificationSedol", UdmGcsTransactionHelper.formatOutputFieldNullToEmpty(msg.getString("sedol")))
                                    .put("FinancialInstrumentIdentificationCusip", UdmGcsTransactionHelper.formatOutputFieldNullToEmpty(msg.getString("cusip")))
                                    .put("FinancialInstrumentIdentificationDescription", UdmGcsTransactionHelper.formatOutputFieldNullToEmpty(msg.getString("instrument_description")))
                                    .put("QuantityType", UdmGcsTransactionHelper.formatOutputFieldNullToEmpty(msg.getString("quantity_unit")))
                                    .put("LocationCode", UdmGcsTransactionHelper.deriveLocationCodeOrDescription(msg, msg.getString("place_of_safekeeping")))
                                    .put("LocalCustodianCode", UdmGcsTransactionHelper.deriveLocalCustodianCodeOrDescription(msg, msg.getString("place_of_safekeeping")))
                                    .put("LocationDescription", UdmGcsTransactionHelper.deriveLocationCodeOrDescription(msg, msg.getString("place_of_safekeeping_name")))
                                    .put("LocalCustodianDescription", UdmGcsTransactionHelper.deriveLocalCustodianCodeOrDescription(msg, msg.getString("place_of_safekeeping_name")))
                                    .put("RegistrationCode", msg.getString("registration"))
                                    .put("RegistrationDescription", msg.getString("registration_desc"))
                                    .put("PlaceOfTradeMic", UdmGcsTransactionHelper.derivePlaceOfTradeMicorTypeCode("TradMic", msg.getString("place_of_trade_mic")))
                                    .put("PlaceOfTradeTypeCode", UdmGcsTransactionHelper.derivePlaceOfTradeMicorTypeCode("TradTypeCode", msg.getString("place_of_trade_mic")))
                                    .put("CounterpartyBic", UdmGcsTransactionHelper.deriveCounterpartyBic(msg, "Brkr",
                                            null,
                                            msg.getString("broker_id_type"),
                                            msg.getString("broker_id_name"),
                                            msg.getString("counterparty_broker_swift_code")))
                                    .put("CounterpartyName", msg.getString("broker_cpty_desc"))
                                    .put("CounterpartyDataSourceSchemeType", UdmGcsTransactionHelper.deriveCounterpartyDataSourceSchemeCodeOrType(msg,
                                            "Brkr",
                                            msg.getString("broker_id_type"),
                                            null,
                                            msg.getString("broker_id_type")))
                                    .put("CounterpartyDataSourceSchemeCode", UdmGcsTransactionHelper.deriveCounterpartyDataSourceSchemeCodeOrType(msg,
                                            "Brkr",
                                            msg.getString("broker_id_type"),
                                            null,
                                            msg.getString("broker_id_name")))
                                    .put("CounterpartyAccountIdentification", UdmGcsTransactionHelper.formatOutputFieldNullToEmpty(msg.getString("broker_safekeeping_acc_no")))
                                    .put("DepositoryIdentification", UdmGcsTransactionHelper.formatOutputFieldNullToEmpty(msg.getString("depository_ref")))
                                    .put("ParentTransactionIdentification", UdmGcsTransactionHelper.formatOutputFieldNullToEmpty(msg.getString("parent_transaction_id")))
                                    .put("TradeDate", UdmGcsTransactionHelper.formatOutputField(msg.getString("trade_date")))
                                    .put("SettlementDate", UdmGcsTransactionHelper.formatOutputField(msg.getString("contractual_settlement_date")))
                                    .put("EffectiveSettlementDate", msg.getString("actual_settlement_date"))
                                    .put("ValueDate", msg.getString("value_date"))
                                    .put("SettlementAmountCurrency", msg.getString("settlement_currency"))
                                    .put("SettlementAmount", UdmGcsTransactionHelper.deriveAmountOrPriceFields(msg.getString("settlement_amount"), 13,2))
                                    .put("AccountBaseCurrency", msg.getString("account_settlement_amount_currency"))
                                    .put("AccountBaseCurrencySettlementAmount", UdmGcsTransactionHelper.deriveAmountOrPriceFields(msg.getString("account_settlement_amount"),13,2))
                                    .put("FxRateToAccountBaseCurrency", UdmGcsTransactionHelper.deriveFxRateToAccountBaseCurrencyAndFxReversalRate(msg.getString("fx_trade_to_acc_xchg_rate_ind"), msg.getString("fx_trade_to_acc_xchg_rate")))
                                    .put("DealPriceAmountCurrency", msg.getString("deal_price_currency"))
                                    .put("DealPriceAmount", UdmGcsTransactionHelper.deriveAmountOrPriceFields(msg.getString("deal_price"), 5,6))
                                    .put("CashAccountIdentification", UdmGcsTransactionHelper.formatOutputFieldNullToEmpty(msg.getString("settlement_cash_acc_no")))
                                    .put("AccruedInterestAmountCurrency", msg.getString("accrued_interest_amount_currency"))
                                    .put("AccruedInterestAmount",UdmGcsTransactionHelper.deriveAmountOrPriceFields(msg.getString("accrued_interest_amount"),9,2))
                                    .put("SecuritiesTransactionTypeCode", UdmGcsTransactionHelper.formatOutputFieldNullToEmpty(msg.getString("swift_settlement_type")))
                                    .put("SecuritiesTransactionTypeDescription", UdmGcsTransactionHelper.formatOutputFieldNullToEmpty(msg.getString("swift_settlement_type_desc")))
                                    .put("ExternalTradeStatus", externalTradeStatus)
                                    .put("SettlementQuantity", msg.getString("security_quantity"))
                                    .put("OriginalSettlementQuantity", msg.getString("parent_transaction_id") != "" ? msg.getString("security_quantity") : "")
                                    .put("PlaceOfSettlementBic", UdmGcsTransactionHelper.formatOutputFieldNullToEmpty(msg.getString("place_of_settlement")))
                                    .put("PlaceOfSettlementDescription", UdmGcsTransactionHelper.formatOutputFieldNullToEmpty(msg.getString("place_of_settlement_desc")))
                                    .put("ReceiversCustodianBic", UdmGcsTransactionHelper.deriveCounterpartyBic(msg, "IntrmPty",
                                            UdmGcsTransactionConstant.DELIVER_SECURITY_MOVEMENT_TYPE,
                                            msg.getString(UdmGcsTransactionConstant.COUNTERPARTY_INTERMEDIARY_TYPE),
                                            msg.getString(UdmGcsTransactionConstant.COUNTERPARTY_INTERMEDIARY_NAME),
                                            msg.getString(UdmGcsTransactionConstant.COUNTERPARTY_INTERMEDIARY_SWIFT_CODE)))
                                    .put("ReceiversCustodianName", UdmGcsTransactionHelper.deriveCounterpartyFields(msg,
                                            UdmGcsTransactionConstant.DELIVER_SECURITY_MOVEMENT_TYPE,
                                            msg.getString("intermediary_party_cpty_desc")))
                                    .put("ReceiversCustodianDataSourceSchemeType", UdmGcsTransactionHelper.deriveCounterpartyDataSourceSchemeCodeOrType(msg,
                                            "IntrmPty",
                                            msg.getString(UdmGcsTransactionConstant.COUNTERPARTY_INTERMEDIARY_TYPE),
                                            UdmGcsTransactionConstant.DELIVER_SECURITY_MOVEMENT_TYPE,
                                            msg.getString(UdmGcsTransactionConstant.COUNTERPARTY_INTERMEDIARY_TYPE)))
                                    .put("ReceiversCustodianDataSourceSchemeCode", UdmGcsTransactionHelper.deriveCounterpartyDataSourceSchemeCodeOrType(msg,
                                            "IntrmPty",
                                            msg.getString(UdmGcsTransactionConstant.COUNTERPARTY_INTERMEDIARY_TYPE),
                                            UdmGcsTransactionConstant.DELIVER_SECURITY_MOVEMENT_TYPE,
                                            msg.getString(UdmGcsTransactionConstant.COUNTERPARTY_INTERMEDIARY_NAME)))
                                    .put("ReceiversCustodianAccountIdentification", UdmGcsTransactionHelper.deriveCounterpartyFields(msg,
                                            UdmGcsTransactionConstant.DELIVER_SECURITY_MOVEMENT_TYPE,
                                            msg.getString("intermediary_party_safekeeping_acc_no")))
                                    .put("DeliverersCustodianBic", UdmGcsTransactionHelper.deriveCounterpartyBic(msg, "IntrmPty",
                                            UdmGcsTransactionConstant.RECEIVE_SECURITY_MOVEMENT_TYPE,
                                            msg.getString(UdmGcsTransactionConstant.COUNTERPARTY_INTERMEDIARY_TYPE),
                                            msg.getString(UdmGcsTransactionConstant.COUNTERPARTY_INTERMEDIARY_NAME),
                                            msg.getString(UdmGcsTransactionConstant.COUNTERPARTY_INTERMEDIARY_SWIFT_CODE)))
                                    .put("DeliverersCustodianName", UdmGcsTransactionHelper.deriveCounterpartyFields(msg,
                                            UdmGcsTransactionConstant.RECEIVE_SECURITY_MOVEMENT_TYPE,
                                            msg.getString("intermediary_party_cpty_desc").trim()))
                                    .put("DeliverersCustodianDataSourceSchemeType", UdmGcsTransactionHelper.deriveCounterpartyDataSourceSchemeCodeOrType(msg,
                                            "IntrmPty",
                                            msg.getString(UdmGcsTransactionConstant.COUNTERPARTY_INTERMEDIARY_TYPE),
                                            UdmGcsTransactionConstant.RECEIVE_SECURITY_MOVEMENT_TYPE,
                                            msg.getString(UdmGcsTransactionConstant.COUNTERPARTY_INTERMEDIARY_TYPE)))
                                    .put("DeliverersCustodianDataSourceSchemeCode", UdmGcsTransactionHelper.deriveCounterpartyDataSourceSchemeCodeOrType(msg,
                                            "IntrmPty",
                                            msg.getString(UdmGcsTransactionConstant.COUNTERPARTY_INTERMEDIARY_TYPE),
                                            UdmGcsTransactionConstant.RECEIVE_SECURITY_MOVEMENT_TYPE,
                                            msg.getString(UdmGcsTransactionConstant.COUNTERPARTY_INTERMEDIARY_NAME)))
                                    .put("DeliverersCustodianAccountIdentification", UdmGcsTransactionHelper.deriveCounterpartyFields(msg,
                                            UdmGcsTransactionConstant.RECEIVE_SECURITY_MOVEMENT_TYPE,
                                            msg.getString("intermediary_party_safekeeping_acc_no")))
                                    .put("BuyerBic", UdmGcsTransactionHelper.deriveCounterpartyBic(msg, "BuyrSellr",
                                            UdmGcsTransactionConstant.DELIVER_SECURITY_MOVEMENT_TYPE,
                                            msg.getString(UdmGcsTransactionConstant.COUNTERPARTY_BUYER_SELLER_TYPE),
                                            msg.getString(UdmGcsTransactionConstant.COUNTERPARTY_BUYER_SELLER_NAME),
                                            msg.getString(UdmGcsTransactionConstant.COUNTERPARTY_BUYER_SELLER_SWIFT_CODE)))
                                    .put("BuyerName", UdmGcsTransactionHelper.deriveCounterpartyFields(msg,
                                            UdmGcsTransactionConstant.DELIVER_SECURITY_MOVEMENT_TYPE,
                                            msg.getString("buyer_seller_cpty_desc")))
                                    .put("BuyerDataSourceSchemeType", UdmGcsTransactionHelper.deriveCounterpartyDataSourceSchemeCodeOrType(msg,
                                            "BuyrSellr",
                                            msg.getString(UdmGcsTransactionConstant.COUNTERPARTY_BUYER_SELLER_TYPE),
                                            UdmGcsTransactionConstant.DELIVER_SECURITY_MOVEMENT_TYPE,
                                            msg.getString(UdmGcsTransactionConstant.COUNTERPARTY_BUYER_SELLER_TYPE)))
                                    .put("BuyerDataSourceSchemeCode", UdmGcsTransactionHelper.deriveCounterpartyDataSourceSchemeCodeOrType(msg,
                                            "BuyrSellr",
                                            msg.getString(UdmGcsTransactionConstant.COUNTERPARTY_BUYER_SELLER_TYPE),
                                            UdmGcsTransactionConstant.DELIVER_SECURITY_MOVEMENT_TYPE,
                                            msg.getString(UdmGcsTransactionConstant.COUNTERPARTY_BUYER_SELLER_NAME)))
                                    .put("BuyerAccountIdentification", UdmGcsTransactionHelper.deriveSellerOrBuyerAccountIdentification(msg,
                                            UdmGcsTransactionConstant.DELIVER_SECURITY_MOVEMENT_TYPE,
                                            msg.getString("buyer_seller_safekeeping_acc_no")))
                                    .put("SellerBic", UdmGcsTransactionHelper.deriveCounterpartyBic(msg, "BuyrSellr",
                                            UdmGcsTransactionConstant.RECEIVE_SECURITY_MOVEMENT_TYPE,
                                            msg.getString(UdmGcsTransactionConstant.COUNTERPARTY_BUYER_SELLER_TYPE),
                                            msg.getString(UdmGcsTransactionConstant.COUNTERPARTY_BUYER_SELLER_NAME),
                                            msg.getString(UdmGcsTransactionConstant.COUNTERPARTY_BUYER_SELLER_SWIFT_CODE)))
                                    .put("SellerName", UdmGcsTransactionHelper.deriveCounterpartyFields(msg,
                                            UdmGcsTransactionConstant.RECEIVE_SECURITY_MOVEMENT_TYPE,
                                            msg.getString("buyer_seller_cpty_desc")))
                                    .put("SellerDataSourceSchemeType", UdmGcsTransactionHelper.deriveCounterpartyDataSourceSchemeCodeOrType(msg,
                                            "BuyrSellr",
                                            msg.getString(UdmGcsTransactionConstant.COUNTERPARTY_BUYER_SELLER_TYPE),
                                            UdmGcsTransactionConstant.RECEIVE_SECURITY_MOVEMENT_TYPE,
                                            msg.getString(UdmGcsTransactionConstant.COUNTERPARTY_BUYER_SELLER_TYPE)))
                                    .put("SellerDataSourceSchemeCode", UdmGcsTransactionHelper.deriveCounterpartyDataSourceSchemeCodeOrType(msg,
                                            "BuyrSellr",
                                            msg.getString(UdmGcsTransactionConstant.COUNTERPARTY_BUYER_SELLER_TYPE),
                                            UdmGcsTransactionConstant.RECEIVE_SECURITY_MOVEMENT_TYPE,
                                            msg.getString(UdmGcsTransactionConstant.COUNTERPARTY_BUYER_SELLER_NAME)))
                                    .put("SellerAccountIdentification", UdmGcsTransactionHelper.deriveSellerOrBuyerAccountIdentification(msg,
                                            UdmGcsTransactionConstant.RECEIVE_SECURITY_MOVEMENT_TYPE,
                                            msg.getString("buyer_seller_safekeeping_acc_no")))
                                    .put("SettlementInstructionProcessingAdditionalDetails", msg.getString("special_instruction"))
                                    .put("SettlementConfirmationAdditionalDetails", msg.getString("settlement_confirmation_additional_details"))
                                    .put("AdditionalMatchingReasonInformation", msg.getString("trade_status") == "MATCHED"
                                            ? UdmGcsTransactionHelper.formatOutputFieldNullToEmpty(msg.getString("remarks"))
                                            : "")
                                    .put("RepurchaseTransactionIdentification", UdmGcsTransactionHelper.formatOutputFieldNullToEmpty(msg.getString("repurchase_transaction_id")))
                                    .put("TerminationTransactionAmountCurrency", msg.getString("termination_transaction_currency"))
                                    .put("TerminationTransactionAmount", UdmGcsTransactionHelper.deriveAmountOrPriceFields(msg.getString("termination_transaction_amount"), 15, 2))
                                    .put("RateTypeCode", UdmGcsTransactionHelper.formatOutputFieldNullToEmpty(msg.getString("rate_type_code")))
                                    .put("RepurchaseRate", UdmGcsTransactionHelper.deriveAmountOrPriceFields(msg.getString("repurchase_rate"),11,6))
                                    .put("InterestComputationMethodCode", UdmGcsTransactionHelper.formatOutputFieldNullToEmpty(msg.getString("interest_computation_method_code")))
                                    .put("TerminationDate", msg.getString("termination_date"))
                                    .put("SwiftStatus1Code", UdmGcsTransactionHelper.formatOutputFieldNullToEmpty(msg.getString("swift_status_code_1")))
                                    .put("SwiftStatus1CodeDescription", UdmGcsTransactionHelper.formatOutputFieldNullToEmpty(msg.getString("swift_status_code_1_desc")))
                                    .put("SwiftStatus1CodeDetailDescription", UdmGcsTransactionHelper.formatOutputFieldNullToEmpty(msg.getString("swift_status_code_1_detail_desc")))
                                    .put("SwiftStatus1Reason1Code", UdmGcsTransactionHelper.formatOutputFieldNullToEmpty(msg.getString("swift_status_1_rsncode_1")))
                                    .put("SwiftStatus1Reason1CodeDescription", UdmGcsTransactionHelper.formatOutputFieldNullToEmpty(msg.getString("swift_status_1_rsncode_1_desc")))
                                    .put("SwiftStatus1Reason1CodeDetailDescription", UdmGcsTransactionHelper.formatOutputFieldNullToEmpty(msg.getString("swift_status_1_rsncode_1_detail_desc")))
                                    .put("SwiftStatus1Reason2Code", UdmGcsTransactionHelper.formatOutputFieldNullToEmpty(msg.getString("swift_status_1_rsncode_2")))
                                    .put("SwiftStatus1Reason2CodeDescription", UdmGcsTransactionHelper.formatOutputFieldNullToEmpty(msg.getString("swift_status_1_rsncode_2_desc")))
                                    .put("SwiftStatus1Reason2CodeDetailDescription", UdmGcsTransactionHelper.formatOutputFieldNullToEmpty(msg.getString("swift_status_1_rsncode_2_detail_desc")))
                                    .put("SwiftStatus2Code", UdmGcsTransactionHelper.formatOutputFieldNullToEmpty(msg.getString("swift_status_code_2")))
                                    .put("SwiftStatus2CodeDescription", UdmGcsTransactionHelper.formatOutputFieldNullToEmpty(msg.getString("swift_status_code_2_desc")))
                                    .put("SwiftStatus2CodeDetailDescription", UdmGcsTransactionHelper.formatOutputFieldNullToEmpty(msg.getString("swift_status_code_2_detail_desc")))
                                    .put("SwiftStatus2Reason1Code", UdmGcsTransactionHelper.formatOutputFieldNullToEmpty(msg.getString("swift_status_2_rsncode_1")))
                                    .put("SwiftStatus2Reason1CodeDescription", UdmGcsTransactionHelper.formatOutputFieldNullToEmpty(msg.getString("swift_status_2_rsncode_1_desc")))
                                    .put("SwiftStatus2Reason1CodeDetailDescription", UdmGcsTransactionHelper.formatOutputFieldNullToEmpty(msg.getString("swift_status_2_rsncode_1_detail_desc")))
                                    .put("SwiftStatus2Reason2Code", UdmGcsTransactionHelper.formatOutputFieldNullToEmpty(msg.getString("swift_status_2_rsncode_2")))
                                    .put("SwiftStatus2Reason2CodeDescription", UdmGcsTransactionHelper.formatOutputFieldNullToEmpty(msg.getString("swift_status_2_rsncode_2_desc")))
                                    .put("SwiftStatus2Reason2CodeDetailDescription", UdmGcsTransactionHelper.formatOutputFieldNullToEmpty(msg.getString("swift_status_2_rsncode_2_detail_desc")))
                                    .put("ReasonCodeDescriptionOrInformation", UdmGcsTransactionHelper.deriveReasonCodeDescriptionOrInformation(msg, externalTradeStatus))
                                    .put("LocalBrokerCommissionAmountCurrency", msg.getString("broker_commission_currency"))
                                    .put("LocalBrokerCommissionAmount",UdmGcsTransactionHelper.deriveAmountOrPriceFields(msg.getString("broker_commission_amount"),7,2))
                                    .put("RegistrarIdentification", UdmGcsTransactionHelper.formatOutputFieldNullToEmpty(msg.getString("registrar_id")))
                                    .put("RegistrarDescription", UdmGcsTransactionHelper.formatOutputFieldNullToEmpty(msg.getString("registrar_desc")))
                                    .put("SpecialCumExDividendIndicator", UdmGcsTransactionHelper.formatOutputFieldNullToEmpty(msg.getString("special_ex_cum_indicator")))
                            // new fields for rubix p1a
                                    .put("SystemTransactionIdentification", msg.getString("transaction_reference"))
                                    .put("CountryTerritoryLocationCode", msg.getString("country_code"))
                                    .put("GroupMemberIdentification", msg.getString("account_region"))
                                    .put("Payment", UdmGcsTransactionHelper.derivePayment(msg))
                                    .put("FinancialInstrumentIdentificationPrimaryIdentifier", UdmGcsTransactionHelper.formatOutputFieldNullToEmpty(msg.getString("instrument_id")))
                                    .put("SourceOfInstructionType", UdmGcsTransactionHelper.deriveSourceOfInstructionType(msg.getString("trade_source"),
                                            msg.getString("trade_instruction_source")))
                                    .put("CounterpartyIdentification", UdmGcsTransactionHelper.deriveCounterpartyIdentification(msg.getString(UdmGcsTransactionConstant.COUNTERPARTY_BROKER_TYPE),
                                            msg.getString(UdmGcsTransactionConstant.COUNTERPARTY_BROKER_NAME)))
                                    .put("LinkedTransactionIdentification", msg.getString("related_trade_reference"))
                                    .put("LocalCustodianIdentification", msg.getString("local_custodian_identification"))
                                    .put("ExchangeIdentification",UdmGcsTransactionHelper.deriveNumericField(msg.getString("fx_deal_ref")))
                                    .put("NoChangeOfBeneficialOwnershipIndicator", UdmGcsTransactionHelper.deriveNoChangeOfBeneficialOwnershipIndicator(msg.getString("change_of_beneficiary_ownership")))
                                    .put("AssociatedAccountIdentification", UdmGcsTransactionHelper.deriveAssociatedAccountIdentification("ACC_ID", msg))
                                    .put("AssociatedSubAccountIdentification", UdmGcsTransactionHelper.deriveAssociatedAccountIdentification("SUB_ACC_ID", msg))
                                    .put("InstructionDateTime", msg.getString("instruction_date"))
                                    .put("TradeAmount", UdmGcsTransactionHelper.deriveAmountOrPriceFields(msg.getString("deal_amount"),13,2))
                                    .put("SourceSystemTradeState", msg.getString("source_system_state"))
                                    .put("SourceSystemTradeStatus", msg.getString("source_system_status"))
                                    .put("CaptureUserIdentification", msg.getString("capture_user_identification"))
                                    .put("UnexecutionReasonCode", UdmGcsTransactionHelper.deriveUnexecutionReasonCodeAndInformation("REASON_CODE", msg.getString("trade_status"),
                                            msg.getString("unexecution_reason_code")))
                                    .put("AdditionalUnexecutionReasonInformation", UdmGcsTransactionHelper.deriveUnexecutionReasonCodeAndInformation("REASON_INFO", msg.getString("trade_status"),
                                            msg.getString("remarks")))
                                    .put("CancellationReasonCode", UdmGcsTransactionHelper.deriveUnexecutionReasonCodeAndInformation("CANCEL_REASON_CODE", msg.getString("trade_status"),
                                            msg.getString("unexecution_reason_code")))
                                    .put("AdditionalCancellationReasonInformation", UdmGcsTransactionHelper.deriveUnexecutionReasonCodeAndInformation("CANCEL_REASON_INFO", msg.getString("trade_status"),
                                            msg.getString("remarks")))
                                    .put("TradeDescription", msg.getString("txn_narrative"))
                                    .put("SourceSystemLastUpdatedDateTime", msg.getString("source_system_last_updated_date_time"))
                                    .put("FxInstructionStatus", msg.getString("fx_instruction_status"))
                                    .put("StampDutyTaxBasisIdentification", msg.getString("stamp_duty_status"))
                                    .put("StampDutyAmountCurrency", msg.getString("stamp_duty_currency"))
                                    .put("StampDutyAmount",UdmGcsTransactionHelper.deriveAmountOrPriceFields(msg.getString("stamp_duty_amount"),13,2 ))
                                    .put("PendingCancellationIndicator", msg.getString("pending_cancelation_indicator"))
                                    .put("CancellationOriginatorType", msg.getString("cancellation_originator_type"))
                                    .put("GenerateDepositoryInstructionIndicator", UdmGcsTransactionHelper.deriveGenerateDepositoryInstructionIndicator(msg))
                                    .put("SecuritiesSettlementType", UdmGcsTransactionHelper.deriveSecuritiesSettlementType(msg))
                                    .put("TradeMaintenanceStatus", UdmGcsTransactionHelper.deriveTradeMaintenanceStatus(msg))
                                    .put("TradeMatchingStatus", UdmGcsTransactionHelper.deriveTradeMatchingStatus(msg))
                                    .put("TradeStockSettlementStatus", UdmGcsTransactionHelper.deriveTradeStockSettlementStatus(msg,
                                            UdmGcsTransactionConstant.DELIVER_SECURITY_MOVEMENT_TYPE,
                                            UdmGcsTransactionConstant.RECEIVE_SECURITY_MOVEMENT_TYPE))
                                    .put("TradeCashSettlementStatus", UdmGcsTransactionHelper.deriveTradeCashSettlementStatus(msg,
                                            UdmGcsTransactionConstant.DELIVER_SECURITY_MOVEMENT_TYPE,
                                            UdmGcsTransactionConstant.RECEIVE_SECURITY_MOVEMENT_TYPE))
                                    .put("TradeHoldStatus", UdmGcsTransactionHelper.deriveTradeHoldStatus(msg))
                                    .put("InternalAccountTransferAccountIdentification", UdmGcsTransactionHelper.deriveInternalAccountTransferAccountIdentification(msg))
                                    .put("TradeIdentification", "")
                                    .put("MarketInfrastructureTransactionIdentification", "")
                                    .put("CrossBusinessTransactionIdentification", UdmGcsTransactionHelper.deriveCrossBusinessTransactionIdentification(msg))
                                    .put("InstructionIdentification", "")
                                    .put("InstructionDateTimeUtc", "")
                                    .put("InstructionSenderBic", "")
                            // BBDRPT-9972
                                    .put("StraightThroughProcessedIndicator", UdmGcsTransactionHelper.deriveStraightThroughProcessedIndicator(msg))
                                    .put("ClientGroupIdentification", "")
                                    .put("ClientEntityIdentification", "")
                                    .put("CustomerIdentification", "")
                                    .put("CashAccountName", "")
                                    .put("ChargeAccountIdentification", "")
                                    .put("TaxIdentification", "")
                                    .put("ChargeAccountName", "")
                                    .put("SettlementTransactionConditionCode", "")
                                    .put("SettlementTransactionCondition2Code", "")
                                    .put("LinkedIndicator", "")
                                    .put("BlockTradeIndicator", "")
                                    .put("NettingIndicator", "")
                                    .put("SecuritiesLendingIndicator", "")
                                    .put("SecuritiesBorrowingIndicator", "")
                                    .put("SplitTradeIndicator", "")
                                    .put("SameDaySettlementIndicator", "")
                                    .put("HKSettlementInstructionPaymentIndicator", "")
                                    .put("PartialSettlementAllowedIndicator", "")
                                    .put("CashPaymentMethod", "")
                                    .put("ContractualValueDate", "")
                                    .put("AccruedInterestDays", "")
                                    .put("InternalAccountTransferIndicator", "")
                                    .put("InternalAccountTransferType", "")
                                    .put("HKSettlementInstructionPurposeCode", "")
                                    .put("FinancialInstrumentIdentificationLocalSecurityCode", "")
                                    .put("ExpectedSettlementDate", "")
                                    .put("LocalCustodianEffectiveSettlementDate", "")
                                    .put("CsdrTradeReleaseDateTime", "")
                                    .put("CsdrTradeReleaseDateTimeUtc", "")
                                    .put("CsdrClientInstructedPartialSettlementQuantity", "")
                                    .put("CsdrReleasedPartialSettlementQuantity", "")
                                    .put("CsdrRejectedPartialSettlementQuantity", "")
                                    .put("CsdrRejectedPartialSettlementDate", "")
                                    .put("SettledAmount", "")
                                    .put("FxOrderCancellationIndicator", "")
                                    .put("RepurchaseTypeCode", "")
                                    .put("ChargeCode", "")
                                    .put("ChargeAmountCurrency", "")
                                    .put("ChargeAmount", "")
                                    .put("CounterpartyCashAccountIdentification", "")
                                    .put("CounterpartyCashAccountName", "")
                                    .put("CounterpartyAccountDataSourceSchemeCode", "")
                                    .put("CounterpartyAccountType", "")
                                    .put("CounterpartyAssociatedAccountIdentification", "")
                                    .put("ReceiversCustodianFormatOption", "")
                                    .put("DeliverersCustodianFormatOption", "")
                                    .put("BuyerFormatOption", "")
                                    .put("SellerFormatOption", "")
                                    .put("InternalAccountTransferTransactionIdentification", "")
                                    .put("InternalAccountTransferAccountName", "")
                                    .put("STASOA", "")
                                    .put("STASOS", "")
                                    .put("PoolIdentification", "")
                                    .put("TotalOfLinkedInstructions", "")
                                    .put("NettingTransactionIdentification", "")
                                    .put("RepurchaseLegType", "")
                                    .put("ClientInstructionReceivedIndicator", "")
                                    .put("HoldMatchedSettlementInstructionIndicator", "")
                                    .put("CashHoldStatus", "")
                                    .put("ScripHoldStatus", "")
                                    .put("SettlementInstructionCancellationStatus", "")
                                    .put("AccountingAndValuationMessageStatus", "")
                                    .put("AutoAmendmentStatus", "")
                                    .put("CsdrTradeHoldReleaseStatus", "")
                                    .put("SourceSystemPreviousStatus", "")
                                    .put("SourceSystemStatus1Reason1Code", "")
                                    .put("AdditionalStatus1Reason1Information", "")
                                    .put("SourceSystemStatus1Reason2Code", "")
                                    .put("SourceSystemStatus2Reason1Code", "")
                                    .put("SourceSystemStatus2Reason2Code", "")
                                    .put("AdditionalStatus2Reason2Information", "")
                                    .put("SecuritiesAccountStandingInstructionDetails", "")
                                    .put("MaintenanceFunctionType", "")
                                    //BBDRPT-11139
                                    .put("CancellationDate", UdmGcsTransactionHelper.deriveCancellationDate(msg.getString("source_system_last_updated_date_time"),msg.getString("source_system_state")))
                                    .put("SourceSystemLastUpdatedDateTimeUtc", "")
                                    .put("AdditionalStatus1Reason2Information", "")
                                    .put("AdditionalStatus2Reason1Information", "")
                                    .put("HbfrAccIndicator", msg.getString("hbfr_acc_indicator"))
                            // new fields for rubix p1b
                                    .put("InvestmentAmountCurrency", msg.getString("investment_amount_currency"))
                                    .put("InvestmentAmount", UdmGcsTransactionHelper.deriveAmountOrPriceFields(msg.getString("investment_amount"),13,2))
                                    .put("RepurchaseOppositeLegTransactionIdentification", msg.getString("repurchase_opposite_leg_transaction_identification"))
                                    .put("LateTradeIndicator", msg.getString("late_trade_indicator"))
                                    .put("DkTradeIndicator", msg.getString("dk_trade_indicator"))
                                    .put("SuppressNostroMessageIndicator", UdmGcsTransactionHelper.deriveSuppressNostroMessageIndicator(msg.getString("supress_nostro_message_indicator")))
                                    .put("SecuritiesRtgsIndicator", msg.getString("securities_rtgs_indicator"))
                                    .put("CurrentLocationCode", msg.getString("position_location_cd"))
                                    .put("SourceLocationCode", msg.getString("source_tx_location_cd"))
                                    .put("FinalLocationCode", msg.getString("final_tx_location_cd"))
                                    .put("FailedTradeIndicator", msg.getString("failed_trade_indicator"))
                                    .put("FxDealDate", msg.getString("fx_deal_date"))
                                    .put("AccountHolderNationalityCode", msg.getString("account_holder_nationalityCode"))
                                    .put("CrestSettlementPriorityCode", msg.getString("crest_settlement_priority_code"))
                                    .put("DealMatchingPriceAmountCurrency", msg.getString("deal_matching_price_amount_currency"))
                                    .put("DealMatchingPriceAmount", UdmGcsTransactionHelper.deriveAmountOrPriceFields(msg.getString("deal_matching_price_amount"),3,8))
                                    .put("SettlementAdviceIndicator", msg.getString("settlement_advice_indicator"))
                                    .put("CrestStampStatusCode", msg.getString("crest_stamp_status_code"))
                                    .put("CrestOriginatingSystemCode", msg.getString("crest_originating_system_code"))
                                    .put("CfetsIdentification", msg.getString("cfets_identification"))
                                    .put("FeeWaiverIndicator", msg.getString("fee_waiver_indicator"))
                                    .put("FaxIdentification", msg.getString("fax_identification"))
                                    .put("ContractualSettlementIndicator", msg.getString("contractual_settlement_indicator"))
                                    .put("ClientCancellationIdentification", msg.getString("client_cancellation_identification"))
                                    .put("PhysicallySettledIndicator", msg.getString("physically_settled_indicator"))
                                    .put("FedwireSplitIndicator", msg.getString("fed_wire_split_indicator"))
                                    .put("FxAgentIdentification", msg.getString("fx_agent_identification"))
                                    .put("FxLocationCode", msg.getString("fx_location_code"))
                                    .put("FxReversalIdentification",UdmGcsTransactionHelper.deriveNumericField(msg.getString("fx_reversal_identification")))
                                    .put("FxReversalDealDate", msg.getString("fx_reversal_deal_date"))
                                    .put("FxReversalAccountBaseCurrencySettlementAmount",UdmGcsTransactionHelper.deriveAmountOrPriceFields(msg.getString("fx_reversal_acc_base_ccy_settlement_amount"),13,2))
                                    .put("FxReversalStatus", msg.getString("fx_reversal_status"))
                                    .put("FxReversalValueDate", msg.getString("fx_reversal_value_date"))
                                    .put("FxReversalAgentIdentification", msg.getString("fx_reversal_agent_identification"))
                                    .put("FxStandingInstructionIndicator", msg.getString("fx_standing_instruction_indicator"))
                                    .put("FxCancellationIndicator", msg.getString("fx_cancellation_indicator"))
                                    .put("FxAdviceIndicator", msg.getString("fx_advice_indicator"))
                                    .put("FxFundingMessageIndicator", msg.getString("fx_funding_message_indicator"))
                                    .put("FxReversalRate", UdmGcsTransactionHelper.deriveFxRateToAccountBaseCurrencyAndFxReversalRate(msg.getString("reversal_xchg_rate_ind"), msg.getString("reversal_xchg_rate")))
                                    .put("DbvCreditPartyType", msg.getString("dbv_credit_party_type"))
                                    .put("DbvMarginPercent",UdmGcsTransactionHelper.deriveAmountOrPriceFields(msg.getString("dbv_margin_percent"), 3,2))
                                    .put("CrestStockDepositIdentification", msg.getString("crest_stock_deposit_identification"))
                                    .put("CrestPhysicalSharesHolderNationalityCode", msg.getString("crest_physical_shares_holder_nationality_code"))
                                    .put("CrestDeliveredQuantity",UdmGcsTransactionHelper.deriveAmountOrPriceFields(msg.getString("crest_dilevered_quantity"),5,6))
                                    .put("CrestBalanceCertificateQuantity", UdmGcsTransactionHelper.deriveAmountOrPriceFields(msg.getString("crest_balance_certificate_quantity"), 5,6))
                                    .put("PreVerificationIndicator", msg.getString("pre_verification_indicator"))
                                    .put("RepairReasonInformation", msg.getString("stp_indicator"))
                                    .put("TransactionReferenceAmountCurrency", msg.getString("transactionReferenceAmountCurrency"))
                                    .put("TransactionReferenceAmount", UdmGcsTransactionHelper.deriveTransactionReferenceAmount(msg.getString("transactionReferenceAmount"), msg.getString("security_quantity"), new DecimalFormat("0.00")))
                            //Ops MI
                                    .put("FinancialInstrumentIdentificationProductType", msg.getString("instrument_type"))
                                    .put("OwningBusinessName", UdmGcsTransactionHelper.deriveOwningBusinessName(msg))
                                    .put("ClientServiceLocationCode", msg.getString("client_service_location_code"))
                            //MENA allegement Use case
                                    .put("CounterpartyType", "")
                                    .put("SafekeepingAccountType", "")
                                    .put("DepositorySettlementType", "")
                                    .put("BursaSettlementType", "")
                                    .put("RentasLinkedSettlementAmendmentType", "")
                                    .put("RentasTransactionType", "")
                                    .put("FinancialInstrumentDetachableIndicator","")
                                    .put("RegistrationIndicator","")
                                    .put("FxIndicator","")
                                    .put("RentasRepurchaseType","")
                                    .put("RentasSellerType","")
                                    .put("RentasBuyerType","")
                                    .put("SuppressStockSufficiencyCheckIndicator","")
                                    .put("FundingArrangementIndicator","")
                            ).build()
                }).collect(Collectors.toList())
    }
}