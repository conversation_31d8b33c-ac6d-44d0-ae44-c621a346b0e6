

################################################################### SQL Resources #################################################################################################

module "cloudsql_cmb" {
  source = "git::ssh://*****************:8203/uoci/gcp-cloud-sql.git"

  project             = var.project
  database_version    = var.db_version
  db_machine_type     = var.db_tier
  name                = var.db_instance_name
  private_network     = var.sql_network
  region              = var.region
  zone                = var.sql_proxy_zone
  encryption_key_name = var.kms_key_sql_link


  availability_type               = var.db_availability_type
  backup_configuration_enabled    = true
  backup_configuration_start_time = "00:00"
  backup_configuration_location   = var.db_backup_configuration_location
  maintenance_window_day          = var.maintenance_window_day
  maintenance_window_hour         = var.maintenance_window_hour
  point_in_time_recovery          = var.point_in_time_recovery
  transaction_log_retention_days  = var.transaction_log_retention_days
  db_flags                        = var.db_flags
  query_insights_enabled          = var.query_insights_enabled

  database_user_names = var.database_user_names
  deletion_policy     = var.deletion_policy
  database_name = var.db_databases
  deletion_protection = var.deletion_protection
  master_instance_name = var.master_instance_name
}

resource "google_sql_database" "databases" {
  for_each = toset(var.db_databases)

  name     = each.value
  instance = var.db_instance_name

  depends_on = [module.cloudsql_cmb]
}
