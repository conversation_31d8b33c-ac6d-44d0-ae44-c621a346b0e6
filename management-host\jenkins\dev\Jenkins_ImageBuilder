def AGENT = 'unity-dev-jenkins-agent'
static final boolean C_TRUE = true
static final boolean C_FALSE = false
static final String C_SUCCESS = 'SUCCESS'
static final String C_FAILURE = 'FAILURE'
static final String C_NULL = null
static final String C_YES = 'Yes'
static final String C_NO = 'No'

pipeline {    
    agent { label env.AGENT_LABEL ?: AGENT }
    environment {
        // Fix github connection so it points to env specific variable  
        STASH_TOKEN= credentials('STASH_TOKEN')   
        GITHUB_TOKEN= credentials('GITHUB_TOKEN')   
    }

    stages {
            stage('Build image') {
                steps {
                    script {
                        withCredentials([
                            [$class: 'UsernamePasswordMultiBinding', credentialsId: 'NEXUS3_CONNECT', usernameVariable: 'NEXUSUSERNAME', passwordVariable: 'NEXUSPASSWORD'],
                            [$class: 'StringBinding', credentialsId: 'STASH_TOKEN', variable: 'STASH_TOKEN']
                        ]) {
                            this.sh '/usr/bin/packer build -timestamp-ui -on-error=cleanup \
                                -var BUILDSTAGE3=false \
                                -var STAGE=stage2 \
                                -var NEXUS_CREDS_USR=$NEXUSUSERNAME \
                                -var NEXUS_CREDS_PWD=$NEXUSPASSWORD \
                                -var STASH_TOKEN=$STASH_TOKEN \
                                -var "GITHUB_TOKEN=$GITHUB_TOKEN" \
                                -var-file=dev/variables.json dev/image.json'
                            this.sh '/usr/bin/packer build -timestamp-ui -on-error=cleanup \
                                -var BUILDSTAGE3=true \
                                -var STAGE=stage3 \
                                -var NEXUS_CREDS_USR=$NEXUSUSERNAME \
                                -var NEXUS_CREDS_PWD=$NEXUSPASSWORD \
                                -var STASH_TOKEN=$STASH_TOKEN \
                                -var GITHUB_TOKEN=$GITHUB_TOKEN \
                                -var-file=prod/variables.json prod/image.json'              
                    }                    
                }
            }
        }
    }

    post {
        unsuccessful {
            mail bcc: '',
                body: "Attention @here ${env.JOB_NAME} #${env.BUILD_NUMBER} has failed.",
                cc: '',
                from: 'unity_cloud_devops@noreply',
                replyTo: '',
                subject: "Failed Pipeline: ${currentBuild.fullDisplayName}",
                to: '<EMAIL>,<EMAIL>'
        }
        success {
            mail bcc: '',
                body: "Succesfully build Mngm Host: ${env.JOB_NAME} #${env.BUILD_NUMBER}",
                cc: '',
                from: 'unity_cloud_devops@noreply',
                replyTo: '',
                subject: "The pipeline ${currentBuild.fullDisplayName} completed successfully.",
                to: '<EMAIL>,<EMAIL>'
        }    
        cleanup {
            script {
                cleanWs()
            }
        }
    }		

} 
