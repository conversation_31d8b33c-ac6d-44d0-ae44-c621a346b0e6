---
spring:
  application:
    name: "udm-sec-tran-jrnl-ocp--sghhubfp-hsrdtjp-psa"
kafkaConfig:
  auto.offset.reset: "latest"
  bootstrap.servers: "kafka-0.kafka-internal.ns-core-prod-apps.svc.cluster.local:9093,kafka-1.kafka-internal.ns-core-prod-apps.svc.cluster.local:9093,kafka-2.kafka-internal.ns-core-prod-apps.svc.cluster.local:9093"
  group.id: "${KAFKA_CONSUMER_GROUP}"
  schema.registry.url: "https://hkl20077608.hc.cloud.hk.hsbc:8082/"
  security.protocol: "SSL"
  ssl.keystore.location: "/hss/apps/certs/unity-microservices.jks"
  ssl.keystore.password: "${KAFKA_KEYSTORE_PASSWORD}"
  ssl.truststore.location: "/hss/apps/certs/unity-microservices.ts"
  ssl.truststore.password: "${KAFKA_TRUSTSTORE_PASSWORD}"
management:
  endpoints:
    web:
      exposure:
        include: "health,prometheus"
  metrics:
    tags:
      application: "${spring.application.name}"
cache:
  provider: "memory"
sourceAdaptorConfig:
  projectId: "12bb8fb6-ba3d-4615-9b0b-f4f5cb38656f"
  name: "udm-sec-tran-jrnl"
  sourceAdaptor:
    sourceChannel: "KAFKA_AVRO"
    sourceDataFormat: "JSON"
    mode: "PIPELINE"
    additionalProperties:
      env: "PROD"
      sourceKafkaConfig:
        sourceKafkaProperties:
          security.protocol: "SSL"
          schema.registry.url: "https://hkl20077608.hc.cloud.hk.hsbc:8082/"
          ssl.truststore.location: "/hss/apps/certs/unity-microservices.ts"
          ssl.keystore.password: "${KAFKA_KEYSTORE_PASSWORD}"
          group.id: "${KAFKA_CONSUMER_GROUP}"
          ssl.keystore.location: "/hss/apps/certs/unity-microservices.jks"
          bootstrap.servers: "kafka-0.kafka-internal.ns-core-prod-apps.svc.cluster.local:9093,kafka-1.kafka-internal.ns-core-prod-apps.svc.cluster.local:9093,kafka-2.kafka-internal.ns-core-prod-apps.svc.cluster.local:9093"
          ssl.truststore.password: "${KAFKA_TRUSTSTORE_PASSWORD}"
          auto.offset.reset: "latest"
        sourceTopic: "OCP_.sghhubfp.hsrdtjp"
    sourceDataKeyFields:
    - "SITE"
    - "JHTXFT"
    - "JHTXFS"
    - "JHTXFQ"
    - "JHTXFX"
    - "JHTRDT"
    kafkaPartitionKeyFields: []
    idFormatOverride: "%s-%s-%06d-%02d-%01d-%08d"
    convertValuesToString: true
    routingConfig:
      mode: "DEFAULT"
      defaultTargetTopic: "unity2-PROD-core-source-adaptor-udm-sec-tran-jrnl-v1-2-0-out"
      batchTopicSuffix: "-batch"
