package com.hsbc.changedashboard.controller;

import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;

/**
 * Controller for serving the dashboard web interface
 */
@Controller
public class DashboardController {

    /**
     * Serves the main dashboard page
     * Spring Boot will automatically serve static files from src/main/resources/static/
     * This method redirects to the static index.html file
     */
    @GetMapping("/")
    public String dashboard() {
        return "redirect:/index.html";
    }

    /**
     * Alternative endpoint for accessing the dashboard
     */
    @GetMapping("/dashboard")
    public String dashboardAlternative() {
        return "redirect:/index.html";
    }
}
