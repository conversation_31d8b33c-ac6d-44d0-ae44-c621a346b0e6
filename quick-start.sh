#!/bin/bash
set -euo pipefail

# Quick Start Script for change-dashboard and risc
# This script provides common pipeline execution patterns

# Function to print usage
usage() {
    echo "Quick Start Script for change-dashboard and risc deployment"
    echo ""
    echo "Usage: $0 <command> [options]"
    echo ""
    echo "Commands:"
    echo "  build-all       Build both projects (maven + image)"
    echo "  deploy-dev      Deploy both projects to dev environment"
    echo "  deploy-uat      Deploy both projects to UAT environment"
    echo "  deploy-prod     Deploy both projects to production"
    echo "  full-dev        Complete pipeline to dev (build + deploy)"
    echo "  full-uat        Complete pipeline to UAT (build + deploy)"
    echo "  full-prod       Complete pipeline to production (build + deploy)"
    echo "  single PROJECT  Run full pipeline for single project"
    echo ""
    echo "Options:"
    echo "  --parallel      Run projects in parallel"
    echo "  --continue      Continue on failures"
    echo "  --dry-run       Show what would be executed"
    echo "  --help, -h      Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 build-all                    # Build both projects"
    echo "  $0 deploy-dev --parallel        # Deploy to dev in parallel"
    echo "  $0 full-uat --continue          # Full pipeline to UAT, continue on failures"
    echo "  $0 single change-dashboard      # Full pipeline for change-dashboard only"
    echo "  $0 full-prod --dry-run          # Show what would be executed for prod"
    exit 1
}

# Check if command is provided
if [ $# -lt 1 ]; then
    echo "Error: Command is required"
    usage
fi

COMMAND="$1"
shift

# Default options
PARALLEL=""
CONTINUE=""
DRY_RUN=""

# Parse options
while [[ $# -gt 0 ]]; do
    case $1 in
        --parallel)
            PARALLEL="--parallel"
            shift
            ;;
        --continue)
            CONTINUE="--continue"
            shift
            ;;
        --dry-run)
            DRY_RUN="--dry-run"
            shift
            ;;
        --help|-h)
            usage
            ;;
        *)
            # For single command, this might be the project name
            if [ "${COMMAND}" = "single" ]; then
                PROJECT="$1"
                shift
            else
                echo "Error: Unknown option $1"
                usage
            fi
            ;;
    esac
done

# Make pipeline script executable
chmod +x pipeline.sh

echo "=== Quick Start: ${COMMAND} ==="

case "${COMMAND}" in
    "build-all")
        echo "Building both projects (Maven + Docker images)..."
        ./pipeline.sh --stages maven-build,security-scan,image-build --push ${PARALLEL} ${CONTINUE} ${DRY_RUN}
        ;;
        
    "deploy-dev")
        echo "Deploying both projects to dev environment..."
        ./pipeline.sh --stages deploy --environment dev --wait ${PARALLEL} ${CONTINUE} ${DRY_RUN}
        ;;
        
    "deploy-uat")
        echo "Deploying both projects to UAT environment..."
        ./pipeline.sh --stages deploy --environment uat --wait ${PARALLEL} ${CONTINUE} ${DRY_RUN}
        ;;
        
    "deploy-prod")
        echo "Deploying both projects to production environment..."
        echo "⚠️  WARNING: This will deploy to PRODUCTION!"
        if [ "${DRY_RUN}" = "" ]; then
            read -p "Are you sure you want to deploy to production? (yes/no): " confirm
            if [ "${confirm}" != "yes" ]; then
                echo "Deployment cancelled"
                exit 0
            fi
        fi
        ./pipeline.sh --stages deploy --environment prod --wait ${PARALLEL} ${CONTINUE} ${DRY_RUN}
        ;;
        
    "full-dev")
        echo "Running complete pipeline to dev environment..."
        ./pipeline.sh --environment dev --push --wait ${PARALLEL} ${CONTINUE} ${DRY_RUN}
        ;;
        
    "full-uat")
        echo "Running complete pipeline to UAT environment..."
        ./pipeline.sh --environment uat --push --wait ${PARALLEL} ${CONTINUE} ${DRY_RUN}
        ;;
        
    "full-prod")
        echo "Running complete pipeline to production environment..."
        echo "⚠️  WARNING: This will build and deploy to PRODUCTION!"
        if [ "${DRY_RUN}" = "" ]; then
            read -p "Are you sure you want to deploy to production? (yes/no): " confirm
            if [ "${confirm}" != "yes" ]; then
                echo "Deployment cancelled"
                exit 0
            fi
        fi
        ./pipeline.sh --environment prod --push --wait ${PARALLEL} ${CONTINUE} ${DRY_RUN}
        ;;
        
    "single")
        if [ -z "${PROJECT:-}" ]; then
            echo "Error: Project name is required for single command"
            echo "Usage: $0 single <project-name>"
            echo "Available projects: change-dashboard, risc"
            exit 1
        fi
        
        echo "Running complete pipeline for ${PROJECT} to dev environment..."
        ./pipeline.sh --projects "${PROJECT}" --environment dev --push --wait ${CONTINUE} ${DRY_RUN}
        ;;
        
    *)
        echo "Error: Unknown command '${COMMAND}'"
        usage
        ;;
esac

echo ""
echo "=== Quick Start Complete ==="
