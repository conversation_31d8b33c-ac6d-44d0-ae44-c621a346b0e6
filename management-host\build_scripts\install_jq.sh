#!/usr/bin/env bash
set -euo pipefail

echo "<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<< jq >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>"
echo "[MGMT_HOST] starting jq installation"

sudo mkdir -p $JQ_PATH
echo "[MGMT_HOST] created jq directory"

#assigning permissions to terraform user
sudo chown -R root:root $JQ_PATH
sudo chmod -R 777 $JQ_PATH

#downloading maven from nexus
cd $BUILD_PATH/build_tools/
echo "[MGMT_HOST] downloading maven from nexus"
wget --user=${NEXUS_CREDS_USR} --password=${NEXUS_CREDS_PWD} "https://gbmt-nexus.prd.fx.gbm.cloud.uk.hsbc/repository/maven-release/com/hsbcib/eqd/tools/jq-linux64/$JQ_VERSION/${JQ_BINARY}"
sudo mkdir $JQ_PATH/$JQ_VERSION
sudo mv ${JQ_BINARY} $JQ_PATH/$JQ_VERSION/jq
sudo chmod 755 -R $JQ_PATH/$JQ_VERSION
sudo rm -rf ${JQ_BINARY}
echo "[MGMT_HOST] unzip and moved jq to maven path and completed jq installation"

echo "[MGMT_HOST] linking jq to /usr/bin"
sudo ln -s $JQ_PATH/$JQ_VERSION/jq /usr/bin

echo "[MGMT_HOST] completed jq installation"
