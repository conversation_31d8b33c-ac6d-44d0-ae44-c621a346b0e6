[Unit]
Description=Setting up Jenkinsagent connection on instance creation
After=key_management.service

[Service]
User=jenbld
Type=forking
WorkingDirectory=/opt/jenkins_agent/agent_service/
ExecStart=/bin/bash -c "export PATH=/opt/google/google-cloud-sdk/bin:$PATH; /opt/jenkins_agent/agent_service/agent_config.sh"
Restart=always
Environment=CLOUDSDK_ROOT_DIR=/opt/google/google-cloud-sdk

[Install]
WantedBy=multi-user.target
