# Management host machine repository
This repository is intended to host all files relevant to build, deploy and operate management host machine in Unity 2.0 GCP enviroment. Management host machine is VM inside GCP env. that is used as bastion host for developer to use in DEV enviroment, Jenkins agent that executes pipelines in both DEV and PROD env. and various other operational tasks. 

## Mgmt host machine tasks
List of tasks that management host machine is used for:
### Building mgmt host machine image
In hsbc GCP environment all machines are required to use images build on hsbc approved image. Management host machine is not an exception in that matter. This repository contains all the files, scripts etc. required to build such image. Image building process can take place either on management host machine itself (preferred option if machine is already present in env.) or on user laptop (suggested to use only in case of new environment setup)
### Jenkins slave operation
Production GCP env. in HSBC does not allow to direct access via ssh to machines. Therefore all the operations necessary to operate environment are required to be executed using Jenkins pipelines. Management host machine connects to Jenkins master, allowing execution of desired operation via jenkins pipeline mechanism
### Developer debug machine
On DEV environment mgmt host machine may be used as a convenient way to debug and operate various GCP components. There are several tools preinstalled onto the machine (kubectl, terraform, psql etc.) that make work with environment easy for devops team members.

## Git repository structure
There are 2 main branches in this repository - main and development. Branch "main" is used to operate DEV and PROD environment. Processes for each kind of environment are separated in file structure of repository. All pipelines that are used to operate on DEV/PROD env. point to master branch in that repository. Branch "development" is used as devops team testing branch. New features are developed on separate branches originating from "development" branch, then they are merged to "development" branch. Only after "development" branch tests are finished and confirmed that changes work properly branch "development" may be merged to branch "master" and used on environments.

![Flowchart](/MGMT_HOST_git.png)

## Repo structure
Repository contains following folders:
### /ansible 
Contains files required to install ansible on machine image. Note that due to ssh limitation on production env, Ansible practical usage in GCP is very limited. Its basically used only to lock stage3 machine during image building process
### /build_scripts
This directory contains bash scripts and configuration files that are used to install various tools and prepare files on mgmt host machine image.
### /build_tools
Directory with providers used to operate terraform from mgmt host. 
### /dev
Files used by packer to build DEV mgmt host image
### /uat
Files used by packer to build UAT mgmt host image
### /prod
Files used by packer to build PROD mgmt host image
### /system_init
Services to be executed on start of the mgmt host machine
### /image_promotion
Scripts used to transfer images from nexus to gcr
### /initial_setup
Set of scripts used to operate mgmt host machine, and setup new environment from scratch
### /jenkins
Directory containing jenkinsfiles used to operate mgmt host machine - for each env. in specific folder

# hsbc-9087302-unity-dev-mgmt-host
Repository for automation scripts for Mgmt host
