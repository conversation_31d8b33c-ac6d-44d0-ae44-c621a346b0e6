# Local .terraform directories
**/.terraform/
**/Scripts/bash/cert-request
**/Scripts/bash/stage
**/Scripts/bash/certificate-helper/stage
**/Terraform/Environments/Dev/cloud_sql/split
**/Terraform/Environments/Prod/cloud_sql/split

#jenkins/templates/*/stage
**/Jenkins/templates/nonprod/cloudsql/stage
**/Jenkins/templates/nonprod/configmaps/stage
**/Jenkins/templates/nonprod/monitoring/stage
**/Jenkins/templates/nonprod/secrets/stage
**/Jenkins/templates/nonprod/helmchartdeployment/stage
**/Jenkins/templates/prod/cloudsql/stage
**/Jenkins/templates/prod/configmaps/stage
**/Jenkins/templates/prod/monitoring/stage
**/Jenkins/templates/prod/secrets/stage
**/Jenkins/templates/prod/helmchartdeployment/stage


# .tfstate files
*.tfstate
*.tfstate.*

# Crash log files
crash.log

# Ignore any .tfvars files that are generated automatically for each Terraform run. Most
# .tfvars files are managed as part of configuration and so should be included in
# version control.
#
# example.tfvars

# Ignore override files as they are usually used to override resources locally and so
# are not checked in
override.tf
override.tf.json
*_override.tf
*_override.tf.json

# Include override files you do wish to add to version control using negated pattern
#
# !example_override.tf

# Include tfplan files to ignore the plan output of command: terraform plan -out=tfplan
# example: *tfplan*
**/Credentials/*.json
**/*.key
terraform/Certificates/access_token.txt
*.zip
*.cer
*.key
*.log

/Terraform/Environments/Dev/Credentials/
Credentials

#/RBAC_experiments/Credentials

/groovy_scripts/.kube/
/groovy_scripts/target

.idea
*.iml
/Terraform/Environments/Dev/configs/Proxy_config/Certs/
Terraform/Environments/Dev2/configs/Proxy_config/Certs/server.pem
Terraform/Environments/Dev2/configs/Proxy_config/Certs/ca.pem
*tfplan
/Terraform/Environments/Dev2/configs/GKE_Namespace/Secrets/
