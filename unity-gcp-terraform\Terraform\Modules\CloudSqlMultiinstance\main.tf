

locals {

  objects_list_converted_to_map = { for instance in var.sql_objects_list : instance.db_instance_name => instance }

}

module "multiinstance_sql" {
  source = "../CloudSql"

  for_each = local.objects_list_converted_to_map

  project         = var.project
  region          = var.region
  deletion_policy = var.deletion_policy
  point_in_time_recovery = true
  deletion_protection    = each.value.delete_protection
  db_version             = var.database_version
  kms_key_sql_link       = var.kms_key_sql_link
  sql_network            = var.private_network
  sql_proxy_zone         = var.sql_proxy_zone
  database_user_names = each.value.database_user_names
  db_tier             = each.value.db_tier
  db_availability_type = var.db_availability_type
  db_databases         = each.value.db_databases
  db_instance_name     = each.value.db_instance_name
  db_backup_configuration_location = var.db_backup_configuration_location
  transaction_log_retention_days = each.value.retention_log_time
  maintenance_window_day          = each.value.maintenance_window_day
  maintenance_window_hour         = each.value.maintenance_window_hour
  query_insights_enabled         = each.value.query_insights_enabled
  master_instance_name = each.value.master_instance_name
}





