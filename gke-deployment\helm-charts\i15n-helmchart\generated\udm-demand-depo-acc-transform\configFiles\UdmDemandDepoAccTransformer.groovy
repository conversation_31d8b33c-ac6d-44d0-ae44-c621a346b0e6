

import com.poc.hss.fasttrack.transform.AbstractTransformer
import com.poc.hss.fasttrack.transform.model.TransformerBatchInputContext
import com.poc.hss.fasttrack.transform.model.TransformerRecord
import io.vertx.core.json.JsonObject
import org.slf4j.Logger
import org.slf4j.LoggerFactory

import java.util.stream.Collectors
import java.util.stream.Stream

class UdmDemandDepoAccTransformer extends AbstractTransformer {

    JsonObject constructRecord(JsonObject source){
        new JsonObject()
                .put("SITE", source.getString("DFCTCD","").trim().toUpperCase())
                .put("DFACB", source.getString("DFACB","").trim())
                .put("DFACS", source.getString("DFACS","").trim())
                .put("DFACX", source.getString("DFACX","").trim())
                .put("DFDCB", source.getString("DFDCB","").trim())
                .put("DFDCG", source.getString("DFDCG","").trim())
                .put("DFDCS", source.getString("DFDCS","").trim())
                .put("DFMCB", source.getString("DFMCB","").trim())
                .put("DFMCS", source.getString("DFMCS","").trim())
                .put("DFMCX", source.getString("DFMCX","").trim())
                .put("DFAC2N", source.getString("DFAC2N","").trim())
                .put("DFACAI", source.getString("DFACAI","").trim())
                .put("DFACSN", source.getString("DFACSN","").trim())
                .put("DFADRQ", source.getString("DFADRQ","").trim())
                .put("DFAPTY", source.getString("DFAPTY","").trim())
                .put("DFATMC", source.getString("DFATMC","").trim())
                .put("DFATTC", source.getString("DFATTC","").trim())
                .put("DFBDUP", source.getString("DFBDUP","").trim())
                .put("DFBIAB", source.getString("DFBIAB","").trim())
                .put("DFBIAS", source.getString("DFBIAS","").trim())
                .put("DFBIAX", source.getString("DFBIAX","").trim())
                .put("DFBICT", source.getString("DFBICT","").trim())
                .put("DFBIOI", source.getString("DFBIOI","").trim())
                .put("DFBLCD", source.getString("DFBLCD","").trim())
                .put("DFBLME", source.getString("DFBLME","").trim())
                .put("DFBSCH", source.getString("DFBSCH","").trim())
                .put("DFCABS", source.getString("DFCABS","").trim())
                .put("DFCBBH", source.getString("DFCBBH","").trim())
                .put("DFCBBK", source.getString("DFCBBK","").trim())
                .put("DFCBCI", source.getString("DFCBCI","").trim())
                .put("DFCBCL", source.getString("DFCBCL","").trim())
                .put("DFCBST", source.getString("DFCBST","").trim())
                .put("DFCCIN", source.getString("DFCCIN","").trim())
                .put("DFCGEX", source.getString("DFCGEX","").trim())
                .put("DFCGID", source.getString("DFCGID","").trim())
                .put("DFCLBS", source.getString("DFCLBS","").trim())
                .put("DFCLVN", source.getString("DFCLVN","").trim())
                .put("DFCQDP", source.getString("DFCQDP","").trim())
                .put("DFCQID", source.getString("DFCQID","").trim())
                .put("DFCQRC", source.getString("DFCQRC","").trim())
                .put("DFCQTP", source.getString("DFCQTP","").trim())
                .put("DFCRGR", source.getString("DFCRGR","").trim())
                .put("DFCTCD", source.getString("DFCTCD","").trim().toUpperCase())
                .put("DFCTRS", source.getString("DFCTRS","").trim())
                .put("DFCUSR", source.getString("DFCUSR","").trim())
                .put("DFCYCD", source.getString("DFCYCD","").trim())
                .put("DFDFBT", source.getString("DFDFBT","").trim())
                .put("DFDLPU", source.getString("DFDLPU","").trim())
                .put("DFDLTN", source.getString("DFDLTN","").trim())
                .put("DFDLUP", source.getString("DFDLUP","").trim())
                .put("DFDPCD", source.getString("DFDPCD","").trim())
                .put("DFDSEX", source.getString("DFDSEX","").trim())
                .put("DFDTAO", source.getString("DFDTAO","").trim())
                .put("DFDTAP", source.getString("DFDTAP","").trim())
                .put("DFDTSS", source.getString("DFDTSS","").trim())
                .put("DFDTZB", source.getString("DFDTZB","").trim())
                .put("DFDYID", source.getString("DFDYID","").trim())
                .put("DFEBAI", source.getString("DFEBAI","").trim())
                .put("DFFDXM", source.getString("DFFDXM","").trim())
                .put("DFGABS", source.getString("DFGABS","").trim())
                .put("DFGAT1", source.getString("DFGAT1","").trim())
                .put("DFGAT2", source.getString("DFGAT2","").trim())
                .put("DFGHCL", source.getString("DFGHCL","").trim())
                .put("DFGLBS", source.getString("DFGLBS","").trim())
                .put("DFGMAB", source.getString("DFGMAB","").trim())
                .put("DFGMCI", source.getString("DFGMCI","").trim())
                .put("DFISTR", source.getString("DFISTR","").trim())
                .put("DFLANG", source.getString("DFLANG","").trim())
                .put("DFLCRA", source.getString("DFLCRA","").trim())
                .put("DFLLBL", source.getString("DFLLBL","").trim())
                .put("DFLLID", source.getString("DFLLID","").trim())
                .put("DFLSRA", source.getString("DFLSRA","").trim())
                .put("DFMDFL", source.getString("DFMDFL","").trim())
                .put("DFMKP1", source.getString("DFMKP1","").trim())
                .put("DFMKP2", source.getString("DFMKP2","").trim())
                .put("DFMKP3", source.getString("DFMKP3","").trim())
                .put("DFMKS1", source.getString("DFMKS1","").trim())
                .put("DFMKS2", source.getString("DFMKS2","").trim())
                .put("DFMKS3", source.getString("DFMKS3","").trim())
                .put("DFMNSK", source.getString("DFMNSK","").trim())
                .put("DFMTCD", source.getString("DFMTCD","").trim())
                .put("DFNATY", source.getString("DFNATY","").trim())
                .put("DFNTPD", source.getString("DFNTPD","").trim())
                .put("DFPBBL", source.getString("DFPBBL","").trim())
                .put("DFPBLN", source.getString("DFPBLN","").trim())
                .put("DFPBPN", source.getString("DFPBPN","").trim())
                .put("DFPLAC", source.getString("DFPLAC","").trim())
                .put("DFRALL", source.getString("DFRALL","").trim())
                .put("DFRCLS", source.getString("DFRCLS","").trim())
                .put("DFRCRC", source.getString("DFRCRC","").trim())
                .put("DFRCRT", source.getString("DFRCRT","").trim())
                .put("DFRDDG", source.getString("DFRDDG","").trim())
                .put("DFRDRC", source.getString("DFRDRC","").trim())
                .put("DFRDRT", source.getString("DFRDRT","").trim())
                .put("DFREQN", source.getString("DFREQN","").trim())
                .put("DFRLBL", source.getString("DFRLBL","").trim())
                .put("DFRSTR", source.getString("DFRSTR","").trim())
                .put("DFRTCQ", source.getString("DFRTCQ","").trim())
                .put("DFSAOG", source.getString("DFSAOG","").trim())
                .put("DFSCCM", source.getString("DFSCCM","").trim())
                .put("DFSTUS", source.getString("DFSTUS","").trim())
                .put("DFTAEX", source.getString("DFTAEX","").trim())
                .put("DFTAMF", source.getString("DFTAMF","").trim())
                .put("DFTATN", source.getString("DFTATN","").trim())
                .put("DFTCLF", source.getString("DFTCLF","").trim())
                .put("DFTDCF", source.getString("DFTDCF","").trim())
                .put("DFTLUP", source.getString("DFTLUP","").trim())
                .put("DFTRIN", source.getString("DFTRIN","").trim())
                .put("DFTTHD", source.getString("DFTTHD","").trim())
                .put("DFTTPF", source.getString("DFTTPF","").trim())
                .put("DFTUPI", source.getString("DFTUPI","").trim())
                .put("DFVLBL", source.getString("DFVLBL","").trim())
                .put("DFVLOC", source.getString("DFVLOC","").trim())
                .put("DFWSCI", source.getString("DFWSCI","").trim())
                .put("DFWTCI", source.getString("DFWTCI","").trim())
                .put("tranc_id", String.format("%s-%s-%03d-%06d-%03d",source.getString("DFCTCD","").trim().toUpperCase(),source.getString("DFGMAB","").trim(),
                        getInteger("DFACB",source),getInteger("DFACS",source),getInteger("DFACX",source)))
    }

    static Integer getInteger( String key,JsonObject obj) {
        String result = obj.getString(key);
        if (result != null && !result.trim().equals("")) {
            return new BigDecimal(result).intValueExact();
        } else {
            return 0;
        }
    }

    private static final Logger logger = LoggerFactory.getLogger(UdmDemandDepoAccTransformer.class)
    @Override
    List<TransformerRecord> batchTransform(TransformerBatchInputContext context) {
        List<TransformerRecord> records = context.getRecords()
        return records.stream()
                .filter(rec -> validData(rec,context))
                .map(rec -> rec.from(rec).data(constructRecord(rec.getData())).build())
                .collect(Collectors.toList())
    }

    boolean validData(TransformerRecord rec,TransformerBatchInputContext context){
        if(isKOPSEnv(context)){// now only QA&KW are deployed to kops, DDACMSP don't have gca data
            return ["QA","KW","KR","GCA"].contains(rec.getData().getString("DFCTCD","").trim().toUpperCase())
        }else{//EG OM are not on board now, QA/KW don't have data visa to deploy them to gcp
            return !["QA","KW","EG"].contains(rec.getData().getString("DFCTCD","").trim().toUpperCase())
        }
    }


    def isKOPSEnv(TransformerBatchInputContext context) {
        context.getEnv().getProperty("KAFKA_CONSUMER_GROUP").containsIgnoreCase("kops-") ? true : false;
    }
}

