#!/usr/bin/env bash
mkdir -p /tmp/ansible
curl -X GET -u ${NEXUS_CREDS_USR}:${NEXUS_CREDS_PWD} https://gbmt-nexus.prd.fx.gbm.cloud.uk.hsbc/repository/pypi-proxy/packages/pip/20.2.3/pip-20.2.3-py2.py3-none-any.whl > /tmp/ansible/pip-20.2.3-py2.py3-none-any.whl                                                                       

echo "Installing Ansible"
pip-3 install ansible --index-url=https://vagrant:<EMAIL>/repository/pypi-proxy/simple --trusted-host gbmt-nexus.prd.fx.gbm.cloud.uk.hsbc --cert /etc/pki/tls/cert.pem

chmod -R a+rX /usr/lib/python2.7/site-packages/
chmod -R a+rX /root/.ansible/plugins/modules
chmod -R a+rX /usr/share/ansible/plugins/modules
chmod -R a+rX /usr/lib/python2.7/site-packages/ansible
chmod -R a+rX /usr/lib64/python2.7/site-packages

sudo mkdir /etc/ansible
export PATH="/usr/local/lib/python2.7/site-packages:/usr/lib64/python2.7/site-packages:$PATH"
PYTHONPATH="$HOME/Scripts/:$PATH"

sudo mv $BUILD_PATH/ansible/ansible.cfg /etc/ansible/

echo "Check Ansible version"
ansible --version