terraform {

  required_version = "v0.13.7"

  required_providers {
    google = {
      source  = "hashicorp/google"
      version = "~> 4.35.0"
    }
    google-beta = {
      source  = "hashicorp/google-beta"
      version = "~> 4.35.0"
    }
    random = {
      source  = "hashicorp/random"
      version = "~> 2.2.0"
    }
    template = {
      source  = "hashicorp/template"
      version = "~> 2.1.2"
    }
    tls = {
      source  = "hashicorp/tls"
      version = "~> 2.1.0"
    }
  }
  backend "gcs" {
    bucket = "hsbc-11465671-unycfs-dev-terraform-state-bucket"
    prefix = "monitoring-state"
  }
}