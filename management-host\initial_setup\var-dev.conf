PROJECT_ID=hsbc-9087302-unity-dev
SERVICE_ACCOUNT_LIST=gce-stage3-image-builder,terraform,connector-cloudsql,k8s-vault-data,big-query-audit-log-sa
NEXUS_CREDS_USR=GB-SVCAT-HKLXP
NEXUS_CREDS_PWD=k84748UNuSwJtr
REGION=asia-east2
ZONE=asia-east2-b
MGMT_HOST_NAME=gce-asia-east2b-mgmt-host
CURRENT_TIME=$(date "+%m%d")
STAGE3_NAME=gmitest
STAGE2=true
STAGE=stage2
BUILDSTAGE3=false
MACHINE_TYPE=n2-standard-2
SUBNET=projects/hsbc-6320774-vpchost-asia-dev/regions/asia-east2/subnetworks/cinternal-vpc1-asia-east2
SUBNET2=projects/hsbc-9087302-unity-dev/regions/asia-east2/subnetworks/cinternal-vpc2-asia-east2-gkenodes2
TAGS=tt-9087302-unity-onprem-a-e-all,fwtag-all-dev-out-alm-nexus-2-production,fwtag-all-dev-out-alm-nexus-3-production,fwtag-all-dev-out-alm-nexus-3-uat,fwtag-all-dev-out-efx-nexus-2-production,fwtag-all-dev-out-efx-nexus-3-production,fwtag-all-dev-out-gbm-stash-production,t-dev-all-out-drn-proxy,tt-6320774-vpchost-asia-cinternal-drn-a-i-ssh,fwtag-all-dev-out-alm-github,tt-9087302-unity-10-92-0-0-x-18-a-e-webhook,tt-9087302-unity-webhook-a-i-stash
DISK_SIZE=200
SERVICE_ACCOUNT_NAME=gce-stage3-image-builder
IMAGE_FAMILY=gce-image-stage2-rhel7-mgmt-host-unity
PROD_IMAGE_FAMILY=gce-image-stage3-rhel7-mgmt-host-unity
CMEK_PROJECT=hsbc-6320774-kms-dev
SCOPES=https://www.googleapis.com/auth/cloud-platform
INSTANCE_TEMPLATE_NAME=gce-mgmt-host-dev-$(date "+%m%d")
MIG_NAME=gce-mig-mgmt-host-dev
SIZE=1
NEXUSREGISTRY=nexus3.hk.hsbc:18080/hsbc-********-mssbbd-unity
NEXUSREGISTRY_PROD=nexus3.hk.hsbc:18082/hsbc-********-mssbbd-unity
NEXUSREGISTRY_UAT=nexus3.hk.hsbc:18081/hsbc-********-mssbbd-unity
NEXUSREGISTRY_DEV=nexus3.hk.hsbc:18080/hsbc-********-mssbbd-unity
NEXUSREGISTRY_FOSS=nexus3.systems.uk.hsbc:18094/ghcr/********
NEXUSREGISTRY_UK_PRIVATE=nexus3.systems.uk.hsbc:18094
NEXUSREGISTRY_UK_PROD=nexus3.systems.uk.hsbc:18082
NEXUSREGISTRY_UK_UAT=nexus3.systems.uk.hsbc:18081
NEXUSREGISTRY_UK_DEV=nexus3.systems.uk.hsbc:18080
NEXUS_PATH=/com/hsbc/gbm/hss/unity/i15n/
CONTAINERREGISTRY=gcr.io
GCR_PATH=/hsbc-9087302-unity-dev/unity/i15n/
GCR_PATH_IN1=/hsbc-********-unyin1-dev/unity/i15n/
GCR_PATH_IN2=/hsbc-********-unyin2-dev/unity/i15n/
GCR_EVLAYER_PROJECT=hsbc-********-evlayer-dev
GCR_EVLAYER_PROJECT_SERVICE_ACCOUNT=<EMAIL>
GCR_EVLAYER_PATH=/${GCR_EVLAYER_PROJECT}/unity/i15n/
GCR_EVLAYER1_PROJECT=hsbc-********-evlayer1-dev
GCR_EVLAYER1_PROJECT_SERVICE_ACCOUNT=<EMAIL>
GCR_EVLAYER1_PATH=/${GCR_EVLAYER1_PROJECT}/unity/i15n/

