<?xml version='1.1' encoding='UTF-8'?>
<com.cloudbees.hudson.plugins.folder.Folder plugin="cloudbees-folder@6.928.v7c780211d66e">
  <actions/>
  <description></description>
  <displayName>GCP Resources Deployment - [Team/Project] SECRETS</displayName>
  <properties>
    <com.cloudbees.jenkins.plugins.foldersplus.SecurityGrantsFolderProperty plugin="cloudbees-folders-plus@3.32">
      <securityGrants/>
    </com.cloudbees.jenkins.plugins.foldersplus.SecurityGrantsFolderProperty>
    <org.jenkinsci.plugins.docker.workflow.declarative.FolderConfig plugin="docker-workflow@580.vc0c340686b_54">
      <dockerLabel></dockerLabel>
      <registry plugin="docker-commons@439.va_3cb_0a_6a_fb_29"/>
    </org.jenkinsci.plugins.docker.workflow.declarative.FolderConfig>
    <com.cloudbees.hudson.plugins.folder.properties.EnvVarsFolderProperty plugin="cloudbees-folders-plus@3.32">
      <properties></properties>
    </com.cloudbees.hudson.plugins.folder.properties.EnvVarsFolderProperty>
    <org.csanchez.jenkins.plugins.kubernetes.KubernetesFolderProperty plugin="kubernetes@4233.vb_67a_0e11a_039">
      <permittedClouds/>
    </org.csanchez.jenkins.plugins.kubernetes.KubernetesFolderProperty>
    <com.cloudbees.hudson.plugins.folder.properties.SubItemFilterProperty plugin="cloudbees-folders-plus@3.32"/>
    <org.jenkinsci.plugins.configfiles.folder.FolderConfigFileProperty plugin="config-file-provider@973.vb_a_80ecb_9a_4d0">
      <configs class="sorted-set">
        <comparator class="org.jenkinsci.plugins.configfiles.ConfigByIdComparator"/>
      </configs>
    </org.jenkinsci.plugins.configfiles.folder.FolderConfigFileProperty>
  </properties>
  <folderViews class="com.cloudbees.hudson.plugins.folder.views.DefaultFolderViewHolder">
    <views>
      <hudson.model.AllView>
        <owner class="com.cloudbees.hudson.plugins.folder.Folder" reference="../../../.."/>
        <name>All</name>
        <filterExecutors>false</filterExecutors>
        <filterQueue>false</filterQueue>
        <properties class="hudson.model.View$PropertyList"/>
      </hudson.model.AllView>
    </views>
    <tabBar class="hudson.views.DefaultViewsTabBar"/>
  </folderViews>
  <healthMetrics/>
  <icon class="com.cloudbees.hudson.plugins.folder.icons.StockFolderIcon"/>
</com.cloudbees.hudson.plugins.folder.Folder>