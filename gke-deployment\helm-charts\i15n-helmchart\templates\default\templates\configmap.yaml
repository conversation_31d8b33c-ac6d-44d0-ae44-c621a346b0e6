{{- $files := .Files }}
apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ include "i15n-helmchart.fullname" . }}
  namespace: {{ .Values.namespace }}
  labels:
    {{- include "i15n-helmchart.labels" . | nindent 4 }}
  {{- with .Values.configMapLabels }}
    {{- toYaml . | nindent 4 }}
  {{- end }}
data:
{{- $path := printf "%s/configFiles/*" .Values.nameOverride }}
{{ (.Files.Glob $path).AsConfig | indent 2 }}
binaryData:
{{- $path := printf "%s/configFiles/*.tgz" .Values.nameOverride }}
{{- range $file, $_ :=  $files.Glob $path }}
  {{$file|base}}: {{ $files.Get $file|b64enc }}
{{- end}}  
