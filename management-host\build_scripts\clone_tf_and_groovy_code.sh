
set -euo



env_code=$1
workdir_name="workdir"
branch="additional-path-logs" #TODO: change to master after merge

echo "current dir: $(pwd)"
mkdir "$workdir_name"
cd ${workdir_name}




git -c "http.extraHeader=Authorization: Bearer $STASH_TOKEN" clone -b "$branch"  https://stash.hk.hsbc/scm/uoci/unity-gcp-terraform.git




cd unity-gcp-terraform
mkdir Credentials
cd Credentials
echo "credentials path: $(pwd)}"

gsutil cp "gs://hsbc-9087302-unity-dev-key-management/gce-stage3-image-builder/gce-stage3-image-builder.json" .
gsutil cp "gs://hsbc-9087302-unity-dev-key-management/runtime-gke/runtime-gke.json" .

touch kubeconfig

image_builder_credentials_path=$(realpath "gce-stage3-image-builder.json")
runtime_gke_credentials_path=$(realpath "runtime-gke.json")
kubeconfig_path=$(realpath "kubeconfig")

echo "downloaded credentials files:\n image builder:${image_builder_credentials_path}\n runtime gke:${runtime_gke_credentials_path}\nkubeconfig:${kubeconfig_path}"

cd ../..



cd "unity-gcp-terraform/groovy_scripts"


mvn package -DskipTests
cd target

git status
echo "current commit hash: $(git rev-parse --verify HEAD)"
echo "running secret refresh in $(pwd)"

java -Dhost=REMOTE \
-Denv=${env_code} \
-Dcluster=UNITY \
-Dgoogle_default_credentials_file_path="$image_builder_credentials_path" \
-Dgoogle_workload_identity_credentials_path="$runtime_gke_credentials_path" \
-cp groovy_scripts-1.0-SNAPSHOT-jar-with-dependencies.jar \
unity.scripts.ReplaceServiceAccountKey


