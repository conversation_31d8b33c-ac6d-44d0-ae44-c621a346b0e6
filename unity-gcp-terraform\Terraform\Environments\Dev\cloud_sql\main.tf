################################################################### /Debug outputs Resources ######################################################################################

################################################################### SQL Resources #################################################################################################

data "google_compute_network" "sql_network" {
  name = var.project_network
}

resource "random_id" "this" {
  byte_length = 3
}

resource "random_id" "random" {
  byte_length = 3
}

locals {
  kms_key_sql_link = "projects/${var.kms_project}/locations/${var.region}/keyRings/sql/cryptoKeys/sqlSharedKey"
}

################################################################### /SQL Resources ################################################################################################

module multi_instance_sql_new {
  source = "../../../Modules/CloudSqlMultiinstance"
  database_version = var.db_version
  private_network  = data.google_compute_network.sql_network.self_link

  db_availability_type             = "ZONAL"
  kms_key_sql_link                 = local.kms_key_sql_link
  project                          = var.project
  region                           = var.region
  sql_proxy_zone                   = var.sql_proxy_zone
  db_backup_configuration_location = "asia-east2"

  sql_objects_list = var.multi_instance_sql_data 
}