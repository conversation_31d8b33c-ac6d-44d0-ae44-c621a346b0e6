#!/bin/bash
function print_usage {
  echo "Usage:$(basename $0)"
  echo ' --bearer-token              '
  echo ' --domain-name               '
  echo ' --subdomain-name            '
  echo ' --cert-keysize              '
  echo ' --cert-validity             '
  echo "Example:$(basename $0) --subdomain-name=ss-dev.vpc3 --domain-name=hsbc-9087302-unity-dev.dev.gcp.cloud.hk.hsbc --bearer-token=iUf1XpvojXhX/RzCT5TScw=="
}

function options() {
while [ "$1" != "" ];
do
  _param_=`printf "%s\n" $1 | awk -F= '{print $1}'`
  _value_=`printf "%s\n" $1 | sed 's/^[^=]*=//g'`
  if [[ $_value_ == $_param_ ]]; then
    shift
    _value_=$1
  fi
  case ${_param_} in
  --bearer-token)
    bearer_token="${_value_}"
    ;;
  --domain-name)
    domain_name="${_value_}"
    ;;
  --subdomain-name)
    subdomain_name="${_value_}"
    ;;
  --cert-keysize)
    cert_keysize="${_value_}"
    ;;
  --cert-validity)
    cert_validity="${_value_}"
    ;;
  --use-wildcard)
    use_wildcard="${_value_}"
    ;;    
  --help)
    print_usage
    exit
    ;;
  esac
  shift;
done
}

function retrieveCerts() {
echo downloading certificates[$common_name]
local retry=30
local i=0
cat /dev/null > ${cert_pemfile}.tmp
while [ $i -lt $retry ] && [ $(grep CertificateData ${cert_pemfile}.tmp | wc -l) -eq 0 ] ;do 
curl -w"\n" -s -X POST "${vedsdk}/certificates/Retrieve" \
 -H "accept: application/json" \
 -H "Authorization: Bearer ${bearer_token}" \
 -H "Content-Type: application/json" \
 -d '{"CertificateDN": "'${cert_dn}'", "Format":"Base64", "IncludePrivateKey":"false", "IncludeChain": "true" }' > ${cert_pemfile}.tmp
if [ $(grep CertificateData ${cert_pemfile}.tmp | wc -l) -eq 0 ]; then 
  cat ${cert_pemfile}.tmp
else
  cat ${cert_pemfile}.tmp | jq -r ".CertificateData" | base64 -di > ${cert_pemfile}
  cp -rp ${cert_pemfile} ${cert_cafile}
fi
((i=i+1))
done
rm -f ${cert_pemfile}.tmp
}

###############################################
# main 
###############################################
root=$(readlink -f $(dirname $0))/cert-request
options "$@"
if [[ ${use_wildcard:-1} -eq 1 ]]; then 
  policy_dn='Policy\\Aperture\\1TCDL\\Wildcard'
else
  policy_dn='Policy\\Aperture\\1TCDL'
fi
cadn='\\\\VED\\\\Policy\\\\CA Templates\\\\HSBC SHA-2 CA\\\\Webserver'
vedsdk=https://venafi-prod-wk.systems.uk.hsbc/vedsdk
cert_keysize=2048
cert_validity=735
common_name=${subdomain_name}.${domain_name}
random_name=$(openssl rand -hex 4).${domain_name}
certdir=${root}/${common_name}
cert_conf=${certdir}/ssl_req_v3.conf
cert_keyfile=${certdir}/server.key
cert_crtfile=${certdir}/server.crt
cert_csrfile=${certdir}/server.csr
cert_pemfile=${certdir}/server.pem
cert_cafile=${certdir}/ca.crt
cert_requestfile=${certdir}/request.json
cert_requestoutfile=${certdir}/request.out
if [[ ${use_wildcard} -eq 1 ]]; then 
  cert_dn='\\\\VED\\\\Policy\\\\Aperture\\\\1TCDL\\\\Wildcard\\\\'${common_name}
else
  cert_dn='\\\\VED\\\\Policy\\\\Aperture\\\\1TCDL\\\\'${common_name}
fi
echo cert_dn=${cert_dn}
echo use_wildcard=${use_wildcard}
export no_proxy=${no_proxy},systems.uk.hsbc
mkdir -p ${certdir}

###############################################################
# CREATE CERT CONFIGURATION
###############################################################
cat>${cert_conf}<<-EOT
[req]
distinguished_name = req_distinguished_name
x509_extensions = v3_req
req_extensions = v3_req
prompt = no
[req_distinguished_name]
C = CN
ST = Hong Kong SAR
L = Hong Kong
O = HSBC HSS IT
OU = Unity
CN = ${random_name}
[v3_req]
subjectAltName = @alt_names
EOT
if [[ ${use_wildcard:-1} -eq 1 ]]; then 
cat >> ${cert_conf} <<-EOT
[alt_names]
DNS.1 = *.${common_name}
DNS.2 = *.${domain_name}
EOT
else 
cat >> ${cert_conf} <<-EOT
[alt_names]
DNS.1 = ${common_name}
EOT
fi
cat >> ${cert_conf} <<-EOT
IP.1 = 127.0.0.1
EOT

###############################################################
# CREATE CERTS 
###############################################################
openssl req \
-x509 \
-newkey rsa:${cert_keysize} \
-sha256 \
-nodes \
-days ${cert_validity} \
-keyout ${cert_keyfile} \
-out ${cert_crtfile} \
-config ${cert_conf} \
-extensions 'v3_req'

###############################################################
# CREATE CSR 
###############################################################
openssl req -new -key ${cert_keyfile} -out ${cert_csrfile} \
  -config ${cert_conf} -extensions 'v3_req'

###############################################################
# REQUEST CERT
###############################################################
cat>${cert_requestfile}<<-EOT
{
  "PolicyDN": "${policy_dn}",
  "CADN": "${cadn}",
  "ObjectName": "${common_name}",
  "PKCS10": "$(cat ${cert_csrfile}|tr -d ["\n"]| sed 's|-----BEGIN CERTIFICATE REQUEST-----|-----BEGIN CERTIFICATE REQUEST-----\\n|g' | sed 's|-----END CERTIFICATE REQUEST-----|\\n-----END CERTIFICATE REQUEST-----|g')",
  "CustomFields": [
     {
      "Name": "Service Info : EIM Ref",
      "Values": [
        "11465671"
      ]
    },
    {
      "Name": "Certificate Owner/Escalation Contact",
      "Values": [
        "<EMAIL>"
      ]
    },
     {
      "Name": "Certificate Manager/Primary Contact",
      "Values": [
        "<EMAIL>"
      ]
    },
     {
      "Name": "Certificate Lead/Business Contact",
      "Values": [
        "<EMAIL>"
      ]
    }
  ]
}
EOT
echo requesting certfiicate[${common_name}]
response=$(
curl -s -w "\n" -X POST "${vedsdk}/certificates/Request" \
 -H "accept: application/json" \
 -H "Authorization: Bearer ${bearer_token}" \
 -H "Content-Type: application/json" \
 -o rs \
 -w "%{http_code}" \
 -d @${cert_requestfile})
echo rc=$response
if [[ $response -ne 200 ]]; then
  cat rs | jq .
  exit 1
else 
  cat rs > ${cert_requestoutfile}
fi  
###############################################################
# DOWNLOAD CERT
###############################################################
retrieveCerts
ls -l ${certdir}
