---
spring:
  datasource:
    url: "********************************/${DB_NAME}"
    username: "${DB_USER}"
    password: "${DB_PASSWORD}"
  jpa:
    show-sql: false
    generate-ddl: true
    properties:
      hibernate:
        dialect: "org.hibernate.dialect.PostgreSQLDialect"
        hbm2ddl:
          auto: "none"
  application:
    name: "udm-gcs-transaction-fusion"
kafkaConfig:
  auto.offset.reset: "latest"
  bootstrap.servers: "kafka-0.kafka-internal.ns-core-prod-apps.svc.cluster.local:9093,kafka-1.kafka-internal.ns-core-prod-apps.svc.cluster.local:9093,kafka-2.kafka-internal.ns-core-prod-apps.svc.cluster.local:9093"
  group.id: "${KAFKA_CONSUMER_GROUP}"
  schema.registry.url: "https://hkl20077608.hc.cloud.hk.hsbc:8082/"
  security.protocol: "SSL"
  ssl.keystore.location: "/hss/apps/certs/unity-microservices.jks"
  ssl.keystore.password: "${KAFKA_KEYSTORE_PASSWORD}"
  ssl.truststore.location: "/hss/apps/certs/unity-microservices.ts"
  ssl.truststore.password: "${KAFKA_TRUSTSTORE_PASSWORD}"
management:
  endpoints:
    web:
      exposure:
        include: "health,prometheus"
  metrics:
    tags:
      application: "${spring.application.name}"
cache:
  provider: "memory"
consumerAdaptorTopic: "unity2-PROD-core-fusion-udm-gcs-transaction-out"
projectId: "12bb8fb6-ba3d-4615-9b0b-f4f5cb38656f"
accesses:
- persistenceEnabled: true
  sourceTopic: "unity2-PROD-core-blend-udm-gcs-transaction-out"
  tableName: "UDM_GCS_TRANSACTION"
  columns:
  - name: "_trace_id"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "_id"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "created_by"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "created_time_stamp"
    dataType: "timestamp"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "deleted_by"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "deleted_time_stamp"
    dataType: "timestamp"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "updated_by"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "updated_time_stamp"
    dataType: "timestamp"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "_batch_id"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "SiteCode"
    dataType: "text"
    isIndexed: true
    isMultiple: false
    contentSchema: []
  - name: "HSBCTransactionIdentification"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "ClientTransactionIdentification"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "LocalCustodianName"
    dataType: "text"
    isIndexed: true
    isMultiple: false
    contentSchema: []
  - name: "SafekeepingAccountIdentification"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "SafekeepingAccountName"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "SourceSystemTransactionType"
    dataType: "text"
    isIndexed: true
    isMultiple: false
    contentSchema: []
  - name: "SecuritiesMovementType"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "FinancialInstrumentIdentificationIsin"
    dataType: "text"
    isIndexed: true
    isMultiple: false
    contentSchema: []
  - name: "FinancialInstrumentIdentificationSedol"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "FinancialInstrumentIdentificationCusip"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "FinancialInstrumentIdentificationDescription"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "QuantityType"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "LocationCode"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "LocalCustodianCode"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "LocationDescription"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "LocalCustodianDescription"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "RegistrationCode"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "RegistrationDescription"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "PlaceOfTradeMic"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "CounterpartyBic"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "CounterpartyName"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "CounterpartyDataSourceSchemeType"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "CounterpartyDataSourceSchemeCode"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "CounterpartyAccountIdentification"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "DepositoryIdentification"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "ParentTransactionIdentification"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "TradeDate"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "SettlementDate"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "EffectiveSettlementDate"
    dataType: "text"
    isIndexed: true
    isMultiple: false
    contentSchema: []
  - name: "ValueDate"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "SettlementAmountCurrency"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "SettlementAmount"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "AccountBaseCurrency"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "AccountBaseCurrencySettlementAmount"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "FxRateToAccountBaseCurrency"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "DealPriceAmountCurrency"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "DealPriceAmount"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "CashAccountIdentification"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "AccruedInterestAmountCurrency"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "AccruedInterestAmount"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "SecuritiesTransactionTypeCode"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "SecuritiesTransactionTypeDescription"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "ExternalTradeStatus"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "SettlementQuantity"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "OriginalSettlementQuantity"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "PlaceOfSettlementBic"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "PlaceOfSettlementDescription"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "ReceiversCustodianBic"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "ReceiversCustodianName"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "ReceiversCustodianDataSourceSchemeType"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "ReceiversCustodianDataSourceSchemeCode"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "ReceiversCustodianAccountIdentification"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "DeliverersCustodianBic"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "DeliverersCustodianName"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "DeliverersCustodianDataSourceSchemeType"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "DeliverersCustodianDataSourceSchemeCode"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "DeliverersCustodianAccountIdentification"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "BuyerBic"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "BuyerName"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "BuyerDataSourceSchemeType"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "BuyerDataSourceSchemeCode"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "BuyerAccountIdentification"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "SellerBic"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "SellerName"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "SellerDataSourceSchemeType"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "SellerDataSourceSchemeCode"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "SellerAccountIdentification"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "SettlementInstructionProcessingAdditionalDetails"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "SettlementConfirmationAdditionalDetails"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "AdditionalMatchingReasonInformation"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "RepurchaseTransactionIdentification"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "TerminationTransactionAmountCurrency"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "TerminationTransactionAmount"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "RateTypeCode"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "RepurchaseRate"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "InterestComputationMethodCode"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "TerminationDate"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "SwiftStatus1Code"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "SwiftStatus1CodeDescription"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "SwiftStatus1CodeDetailDescription"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "SwiftStatus1Reason1Code"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "SwiftStatus1Reason1CodeDescription"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "SwiftStatus1Reason1CodeDetailDescription"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "SwiftStatus1Reason2Code"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "SwiftStatus1Reason2CodeDescription"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "SwiftStatus1Reason2CodeDetailDescription"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "SwiftStatus2Code"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "SwiftStatus2CodeDescription"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "SwiftStatus2CodeDetailDescription"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "SwiftStatus2Reason1Code"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "SwiftStatus2Reason1CodeDescription"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "SwiftStatus2Reason1CodeDetailDescription"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "SwiftStatus2Reason2Code"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "SwiftStatus2Reason2CodeDescription"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "SwiftStatus2Reason2CodeDetailDescription"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "LocalBrokerCommissionAmountCurrency"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "LocalBrokerCommissionAmount"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "RegistrarIdentification"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "RegistrarDescription"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "SpecialCumExDividendIndicator"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "SystemTransactionIdentification"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "CountryTerritoryLocationCode"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "GroupMemberIdentification"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "Payment"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "FinancialInstrumentIdentificationPrimaryIdentifier"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "SourceOfInstructionType"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "CounterpartyIdentification"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "LinkedTransactionIdentification"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "LocalCustodianIdentification"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "ExchangeIdentification"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "NoChangeOfBeneficialOwnershipIndicator"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "AssociatedAccountIdentification"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "AssociatedSubAccountIdentification"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "InstructionDateTime"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "TradeAmount"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "SourceSystemTradeState"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "SourceSystemTradeStatus"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "CaptureUserIdentification"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "UnexecutionReasonCode"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "AdditionalUnexecutionReasonInformation"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "CancellationReasonCode"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "AdditionalCancellationReasonInformation"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "TradeDescription"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "SourceSystemLastUpdatedDateTime"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "FxInstructionStatus"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "StampDutyTaxBasisIdentification"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "StampDutyAmountCurrency"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "StampDutyAmount"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "PendingCancellationIndicator"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "CancellationOriginatorType"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "GenerateDepositoryInstructionIndicator"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "SecuritiesSettlementType"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "TradeMaintenanceStatus"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "TradeMatchingStatus"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "TradeStockSettlementStatus"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "TradeCashSettlementStatus"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "TradeHoldStatus"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "InternalAccountTransferAccountIdentification"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "TradeIdentification"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "MarketInfrastructureTransactionIdentification"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "CrossBusinessTransactionIdentification"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "InstructionIdentification"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "InstructionDateTimeUtc"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "InstructionSenderBic"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "StraightThroughProcessedIndicator"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "ClientGroupIdentification"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "ClientEntityIdentification"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "CustomerIdentification"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "CashAccountName"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "ChargeAccountIdentification"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "ChargeAccountName"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "TaxIdentification"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "SettlementTransactionConditionCode"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "SettlementTransactionCondition2Code"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "LinkedIndicator"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "BlockTradeIndicator"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "NettingIndicator"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "SecuritiesLendingIndicator"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "SecuritiesBorrowingIndicator"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "SplitTradeIndicator"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "SameDaySettlementIndicator"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "HKSettlementInstructionPaymentIndicator"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "SecuritiesRtgsIndicator"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "PartialSettlementAllowedIndicator"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "CashPaymentMethod"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "ContractualValueDate"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "AccruedInterestDays"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "InternalAccountTransferIndicator"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "InternalAccountTransferType"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "HKSettlementInstructionPurposeCode"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "FinancialInstrumentIdentificationLocalSecurityCode"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "FinancialInstrumentIdentificationProductType"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "ExpectedSettlementDate"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "LocalCustodianEffectiveSettlementDate"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "CsdrTradeReleaseDateTime"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "CsdrTradeReleaseDateTimeUtc"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "CsdrClientInstructedPartialSettlementQuantity"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "CsdrReleasedPartialSettlementQuantity"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "CsdrRejectedPartialSettlementQuantity"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "CsdrRejectedPartialSettlementDate"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "SettledAmount"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "TransactionReferenceAmountCurrency"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "TransactionReferenceAmount"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "FxOrderCancellationIndicator"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "RepurchaseTypeCode"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "ChargeCode"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "ChargeAmountCurrency"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "ChargeAmount"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "CounterpartyCashAccountIdentification"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "CounterpartyCashAccountName"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "CounterpartyAccountDataSourceSchemeCode"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "CounterpartyAccountType"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "CounterpartyAssociatedAccountIdentification"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "ReceiversCustodianFormatOption"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "DeliverersCustodianFormatOption"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "BuyerFormatOption"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "SellerFormatOption"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "InternalAccountTransferTransactionIdentification"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "InternalAccountTransferAccountName"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "STASOA"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "STASOS"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "PoolIdentification"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "TotalOfLinkedInstructions"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "NettingTransactionIdentification"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "RepurchaseLegType"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "ClientInstructionReceivedIndicator"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "HoldMatchedSettlementInstructionIndicator"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "CashHoldStatus"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "ScripHoldStatus"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "SettlementInstructionCancellationStatus"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "AccountingAndValuationMessageStatus"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "AutoAmendmentStatus"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "CsdrTradeHoldReleaseStatus"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "SourceSystemPreviousStatus"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "SourceSystemStatus1Reason1Code"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "AdditionalStatus1Reason1Information"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "SourceSystemStatus1Reason2Code"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "SourceSystemStatus2Reason1Code"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "SourceSystemStatus2Reason2Code"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "AdditionalStatus2Reason2Information"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "SecuritiesAccountStandingInstructionDetails"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "MaintenanceFunctionType"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "CancellationDate"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "SourceSystemLastUpdatedDateTimeUtc"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "AdditionalStatus1Reason2Information"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "AdditionalStatus2Reason1Information"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "PlaceOfTradeTypeCode"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "sec_acc_id"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "ReasonCodeDescriptionOrInformation"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "HbfrAccIndicator"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "InvestmentAmountCurrency"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "InvestmentAmount"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "RepurchaseOppositeLegTransactionIdentification"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "LateTradeIndicator"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "DkTradeIndicator"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "SuppressNostroMessageIndicator"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "CurrentLocationCode"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "SourceLocationCode"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "FinalLocationCode"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "FailedTradeIndicator"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "FxDealDate"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "AccountHolderNationalityCode"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "CrestSettlementPriorityCode"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "DealMatchingPriceAmountCurrency"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "DealMatchingPriceAmount"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "SettlementAdviceIndicator"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "CrestStampStatusCode"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "CrestOriginatingSystemCode"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "CfetsIdentification"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "FeeWaiverIndicator"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "FaxIdentification"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "ContractualSettlementIndicator"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "ClientCancellationIdentification"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "PhysicallySettledIndicator"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "FedwireSplitIndicator"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "FxAgentIdentification"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "FxLocationCode"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "FxReversalIdentification"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "FxReversalDealDate"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "FxReversalAccountBaseCurrencySettlementAmount"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "FxReversalStatus"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "FxReversalValueDate"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "FxReversalAgentIdentification"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "FxStandingInstructionIndicator"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "FxCancellationIndicator"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "FxAdviceIndicator"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "FxFundingMessageIndicator"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "FxReversalRate"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "DbvCreditPartyType"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "DbvMarginPercent"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "CrestStockDepositIdentification"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "CrestPhysicalSharesHolderNationalityCode"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "CrestDeliveredQuantity"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "CrestBalanceCertificateQuantity"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "PreVerificationIndicator"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "RepairReasonInformation"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "CounterpartyType"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "ClientServiceLocationCode"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "OwningBusinessName"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "SafekeepingAccountType"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "DepositorySettlementType"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "BursaSettlementType"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "RentasLinkedSettlementAmendmentType"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "RentasTransactionType"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "FinancialInstrumentDetachableIndicator"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "RegistrationIndicator"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "FxIndicator"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "RentasRepurchaseType"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "RentasSellerType"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "RentasBuyerType"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "SuppressStockSufficiencyCheckIndicator"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "FundingArrangementIndicator"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "FxRate"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "InternalAccountTransferSuspenseCashAccountId"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "InternalAccountTransferSuspenseCashAccountName"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "FinancialInstrumentCountryTerritoryTradedMarketCd"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "SettlementInstructionProcessingStatus"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "AmendUserIdentification"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "ConfirmUserIdentification"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "SettleUserIdentification"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "PlaceOfSafekeepingBic"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  script: "SET SCHEMA '12bb8fb6_ba3d_4615_9b0b_f4f5cb38656f'; CREATE INDEX IF NOT\
    \ EXISTS \"idx.udm_gcs_rubix_id\" ON \"UDM_GCS_TRANSACTION\" USING btree (_id);\
    \  CREATE INDEX IF NOT EXISTS \"idx_udm_gcs_rubix_hsbcTransactionIdentification\"\
    \ ON \"UDM_GCS_TRANSACTION\" USING btree (\"HSBCTransactionIdentification\");\
    \ CREATE INDEX IF NOT EXISTS \"idx_udm_gcs_rubix_safekeepingAccountIdentification\"\
    \ ON \"UDM_GCS_TRANSACTION\" USING btree (\"SafekeepingAccountIdentification\"\
    ); CREATE INDEX IF NOT EXISTS \"idx_udm_gcs_rubix_counterpartyAccountIdentification\"\
    \ ON \"UDM_GCS_TRANSACTION\" USING btree (\"CounterpartyAccountIdentification\"\
    ); CREATE INDEX IF NOT EXISTS \"idx_udm_gcs_rubix_financialInstrumentIdentificationIsin\"\
    \ ON \"UDM_GCS_TRANSACTION\" USING btree (\"FinancialInstrumentIdentificationIsin\"\
    ); CREATE INDEX IF NOT EXISTS \"idx_udm_gcs_rubix_countryTerritoryLocationCode\"\
    \ ON \"UDM_GCS_TRANSACTION\" USING btree (\"CountryTerritoryLocationCode\"); CREATE\
    \ INDEX IF NOT EXISTS \"idx_udm_gcs_rubix_groupMemberIdentification\" ON \"UDM_GCS_TRANSACTION\"\
    \ USING btree (\"GroupMemberIdentification\");"
  scriptEnabled: true
  retention:
    retentionEnabled: false
  sourceBatchTopicSuffix: "-batch"
  targetBatchTopicSuffix: "-batch"
  ddlAllowed: true
externalDataSourceJdbcConfigs: {}
