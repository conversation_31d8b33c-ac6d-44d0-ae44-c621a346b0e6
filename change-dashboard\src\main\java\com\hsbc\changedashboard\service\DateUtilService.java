package com.hsbc.changedashboard.service;

import org.springframework.stereotype.Service;

import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;

@Service
public class DateUtilService {

    private static final DateTimeFormatter ISO_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSSSSS'Z'");

    /**
     * Get Monday of the current week
     */
    public String getCurrentWeekMonday() {
        LocalDate today = LocalDate.now();
        LocalDate monday = today.with(DayOfWeek.MONDAY);
        return monday.atStartOfDay().atOffset(ZoneOffset.UTC).format(ISO_FORMATTER);
    }

    /**
     * Get Monday of the next week
     */
    public String getNextWeekMonday() {
        LocalDate today = LocalDate.now();
        LocalDate nextMonday = today.with(DayOfWeek.MONDAY).plusWeeks(1);
        return nextMonday.atStartOfDay().atOffset(ZoneOffset.UTC).format(ISO_FORMATTER);
    }
}
