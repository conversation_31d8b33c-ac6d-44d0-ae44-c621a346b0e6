#!/usr/bin/env bash

#TODO
# CHANGE CHMOD TO 755 for $TERRAFORM_PATH/bin/
# COPY .terraformrc to home/jenbld/ catalogue
# Change privilages to 755 for home/jenbld/.terraformrc
# Change privilages to 755 for /home/<USER>/.terraform.d/plugin-cache

set -euo pipefail

echo "<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<< TERRAFORM >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>"
echo "[MGMT_HOST] starting terraform installation"

if id "terraform" >/dev/null 2>&1; then
    echo "[MGMT_HOST] terraform user exists"
else
    echo "[MGMT_HOST] terraform user does not exist"
    sudo useradd terraform
   echo "[MGMT_HOST] created terraform user "
fi
sudo usermod -a -G root terraform
echo "[MGMT_HOST] added terraform user to root"

sudo mkdir -p $TERRAFORM_PATH
sudo mkdir -p $TERRAFORM_PATH/platform
sudo mkdir -p $TERRAFORM_PATH/bin
echo "[MGMT_HOST] created terraform and platform directory"

#assigning permissions to terraform user
sudo chown -R root:root $TERRAFORM_PATH
sudo chmod -R 777 $TERRAFORM_PATH
echo "[MGMT_HOST] assigning permissions to terraform user"

#copying terraform binaries & providers to terraform location
cd $BUILD_PATH/build_tools
echo "[MGMT_HOST] downloading package from nexus" 

TERRAFORM_VERSION_FULL="terraform_${TERRAFORM_VERSION}_linux_amd64.zip"

wget --user="vagrant" --password="vagrant" "https://gbmt-nexus.prd.fx.gbm.cloud.uk.hsbc/repository/hashicorp-releases//terraform/$TERRAFORM_VERSION/$TERRAFORM_VERSION_FULL"

sudo unzip -q $TERRAFORM_VERSION_FULL
sudo rm -rf $TERRAFORM_VERSION_FULL
sudo mv terraform $TERRAFORM_PATH/bin/
echo "[MGMT_HOST] unzip and moved terraform to terraform path"

#echo "[MGMT_HOST] unzip all the terraform providers"
cd $BUILD_PATH/build_tools/tf_providers/registry.terraform.io/hashicorp/

for filename in *.zip; do
    echo $filename
    NAME_AND_VERSION=`echo $filename | sed 's/terraform-provider-*//'`
    NAME_AND_VERSION=`echo $NAME_AND_VERSION | sed 's/_linux_amd64.zip//'`
    arrNAME_AND_VERSION=(${NAME_AND_VERSION//_/ })
    PROVIDER_NAME=${arrNAME_AND_VERSION[0]}
    PROVIDER_VERSION=${arrNAME_AND_VERSION[1]}
    echo $PROVIDER_NAME
    echo $PROVIDER_VERSION
    TARGET="linux_amd64"
    mkdir -p ${PROVIDER_NAME}/${PROVIDER_VERSION}/${TARGET}/
    mv $filename ${PROVIDER_NAME}/${PROVIDER_VERSION}/${TARGET}/
    unzip -q ${PROVIDER_NAME}/${PROVIDER_VERSION}/${TARGET}/$filename -d ${PROVIDER_NAME}/${PROVIDER_VERSION}/${TARGET}/
    rm ${PROVIDER_NAME}/${PROVIDER_VERSION}/${TARGET}/$filename
done

sudo cp -R $BUILD_PATH/build_tools/tf_providers/* $TERRAFORM_PATH/bin
echo "[MGMT_HOST] moved terraform and terraform providers to terraform path"

#CHANGE CHMOD TO 755 so user jenbld can access those catalogues

sudo ln -s $TERRAFORM_PATH/bin/terraform  /usr/bin/terraform
echo "[MGMT_HOST] linked terraform path to /usr/bin"

#seeting permissions to .terraformrc
sudo cd /home/<USER>
sudo cat >>.terraformrc <<HELLO
disable_checkpoint = true

plugin_cache_dir = "/home/<USER>/.terraform.d/plugin-cache"

provider_installation {
  filesystem_mirror {
    path = "$TERRAFORM_PATH/bin/"
    include = ["hashicorp/*"]
  }
  direct {
    exclude = ["hashicorp/*"]
  }
}

HELLO
sudo chown -R root:root .terraformrc
sudo cp .terraformrc /root/.terraformrc
echo "[MGMT_HOST] configured terraform plugin-cache and assigned permissions to .terraformrc"

#terraform init
sudo mkdir -p $TERRAFORM_PATH/test
sudo chmod -R 777 $TERRAFORM_PATH/test
cd $TERRAFORM_PATH/test
sudo cat >> provider.tf <<HELLO
provider "google" {}
provider "google-beta" {}
provider "kubernetes" {}
provider "local" {}
provider "random" {}
provider "tls" {}
provider "template" {}
HELLO

sudo chmod -R 777 provider.tf
echo "[MGMT_HOST] set-up terraform test provider.tf : $(whoami)"
terraform init
echo "[MGMT_HOST] initialized terraform"
echo "[MGMT_HOST] completed terraform $(terraform version) instalation"
