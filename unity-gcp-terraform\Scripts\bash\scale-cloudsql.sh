#!/bin/bash

function options() {
while [ "$1" != "" ];
do
  _param_=`printf "%s\n" $1 | awk -F= '{print $1}'`
  _value_=`printf "%s\n" $1 | sed 's/^[^=]*=//g'`
  if [[ $_value_ == $_param_ ]]; then
    shift
    _value_=$1
  fi
  case ${_param_} in
  --namespace)
    namespace="$(echo ${_value_}|sed 's|,|\||g')"
    ;;
  --exclude-namespace)
    exclude_namespace="-v \"$(echo ${_value_}|sed 's|,|\||g')\""
    ;;  
  --operation)
    operation="${_value_}"
    ;;
  --vpc)
    vpc="${_value_}"
    ;;
  --setup-kubeconfig)
    setup_kubeconfig="${_value_}"
    ;;
  --help)
    print_usage
    exit
    ;;
  esac
  shift;
done
}

function setup_kubeconfig(){
  set -x 
  export KUBECONFIG=${root}/kubeconfig.conf
  gcloud container clusters list --format='value(name,zone)' | grep ${vpc} | while read cluster region ; do 
  gcloud container clusters get-credentials ${cluster} --region=${region}
  done
  export KUBE_PROXY=$(gcloud compute addresses list --filter="NAME:gke-kubectl-${vpc}" --format='value(ADDRESS)'):3128
  export HTTPS_PROXY=${KUBE_PROXY}
  set +x
}

function constructor(){
root=$(readlink -f $(dirname $0))
setup_kubeconfig=1
namespace=".*"
exclude_namespace='".*"'
export no_proxy=${no_proxy},systems.uk.hsbc
}

function auto_start_stop_instances(){
[[ ${setup_kubeconfig} -eq 1 ]] && setup_kubeconfig
export HTTPS_PROXY=${KUBE_PROXY}
kubectl get namespace | grep ns-.*-apps | egrep "${namespace}" | awk '{print $1}' | while read n; 
do 
  export HTTPS_PROXY=${KUBE_PROXY}
  deployment_count=$(kubectl get deployment -n ${n} --field-selector=metadata.name!=unity-support-toolkit| wc -l)
  project=$(echo ${n} | sed -e 's|^ns-||g' -e 's|\-apps||g')
  [[ $(echo $project|grep "i15n-"|wc -l) -gt 0 ]] && extended_condition=" OR $(echo $project|sed 's|i15n|core|g')" || extended_condition=""
  echo namespace:[$n],deployment:[$deployment_count]
  unset HTTPS_PROXY
  if [ $deployment_count -eq 0 ]; then  
    gcloud sql instances list --filter="NAME:(${project}${extended_condition}) AND STATUS=RUNNABLE AND instanceType=CLOUD_SQL_INSTANCE" --format="value(name)"| eval "egrep ${exclude_namespace}" | while read instances; 
    do 
      set -x 
      gcloud sql instances patch ${instances} --activation-policy=NEVER > /var/tmp/gcloud.sql.patch.shutdown.${instances}.lst 2>&1 &
      set +x
    done
    gcloud sql instances list --filter="NAME:(${project}${extended_condition}) AND STATUS=RUNNABLE AND settings.databaseReplicationEnabled=true AND instanceType=READ_REPLICA_INSTANCE" --format="value(name)"| eval "egrep ${exclude_namespace}" | while read instances; 
    do 
      set -x 
      gcloud sql instances patch ${instances} --no-enable-database-replication -q > /var/tmp/gcloud.sql.patch.shutdown.${instances}.lst 2>&1 &
      set +x
    done     
  else
    gcloud sql instances list --filter="NAME:(${project}${extended_condition}) AND STATUS=RUNNABLE AND settings.databaseReplicationEnabled=false AND instanceType=READ_REPLICA_INSTANCE" --format="value(name)"| eval "egrep ${exclude_namespace}" | while read instances; 
    do 
      set -x 
      gcloud sql instances patch ${instances} --enable-database-replication -q > /var/tmp/gcloud.sql.patch.shutdown.${instances}.lst 2>&1 &
      set +x
    done   
    gcloud sql instances list --filter="NAME:(${project}${extended_condition}) AND STATUS=STOPPED AND instanceType=CLOUD_SQL_INSTANCE" --format="value(name)" | eval "egrep ${exclude_namespace}" | while read instances; 
    do 
      set -x 
      gcloud sql instances patch ${instances} --activation-policy=ALWAYS > /var/tmp/gcloud.sql.patch.startup.${instances}.lst 2>&1 &
      set +x
    done
  fi
done 
sleep 10
echo wait for all instances to be started/stopped 
process_count=$(ps -ef | grep gcloud | grep patch | grep activation-policy= | wc -l)
while [ ${process_count} -gt 0 ]; do 
  ps -ef | grep $$ | grep gcloud | grep patch | grep activation-policy=
  sleep 10
  process_count=$(ps -ef | grep gcloud | grep patch | grep activation-policy= | wc -l)
done  
}

function startup_instances(){
  set -xf 
  if [[ "$namespace" = ".*" ]]; then 
    project=$namespace
  else 
    project=$(echo ${namespace} | sed -e 's|^ns-||g' -e 's|\-apps||g')
  fi
  unset HTTPS_PROXY
  #startup replicaset
  gcloud sql instances list --filter="settings.databaseReplicationEnabled=false AND instanceType=READ_REPLICA_INSTANCE" --format="value(name)" | eval "egrep ${project}" | eval "egrep ${exclude_namespace}" | while read instances; 
  do
    gcloud sql instances patch ${instances} --enable-database-replication -q > /var/tmp/gcloud.sql.patch.startup.${instances}.lst 2>&1 &
  done  
  sleep 10
  #startup master database
  gcloud sql instances list --filter="STATUS=STOPPED AND instanceType=CLOUD_SQL_INSTANCE" --format="value(name)" | eval "egrep ${project}" | eval "egrep ${exclude_namespace}" | while read instances; 
  do
    gcloud sql instances patch ${instances} --activation-policy=ALWAYS -q > /var/tmp/gcloud.sql.patch.startup.${instances}.lst 2>&1 &
  done  
  sleep 10
  echo wait for all instances to be started/stopped 
  process_count=$(ps -ef | grep gcloud | grep patch | grep activation-policy= | wc -l)
  set +x
  while [ ${process_count} -gt 0 ]; do 
    ps -ef | grep $$ | grep gcloud | grep patch | grep activation-policy=
    sleep 10
    process_count=$(ps -ef | grep gcloud | grep patch | grep activation-policy= | wc -l)
  done  
}

function shutdown_instances(){
  set -xf
  if [[ "$namespace" = ".*" ]]; then 
    project=$namespace
  else 
    project=$(echo ${namespace} | sed -e 's|^ns-||g' -e 's|\-apps||g')
  fi
  unset HTTPS_PROXY
  gcloud sql instances list --filter="STATUS=RUNNABLE AND instanceType=CLOUD_SQL_INSTANCE" --format="value(name)" | eval "egrep ${project}" | eval "egrep ${exclude_namespace}" | while read instances; 
  do
    gcloud sql instances patch ${instances} --activation-policy=NEVER > /var/tmp/gcloud.sql.patch.shutdown.${instances}.lst 2>&1 &
  done  
  sleep 10
  #shutdown replicaset
  gcloud sql instances list --filter="settings.databaseReplicationEnabled=true AND instanceType=READ_REPLICA_INSTANCE" --format="value(name)" | eval "egrep ${project}" | eval "egrep ${exclude_namespace}" | while read instances; 
  do
    gcloud sql instances patch ${instances} --no-enable-database-replication -q > /var/tmp/gcloud.sql.patch.startup.${instances}.lst 2>&1 &
  done  
  sleep 10  
  echo wait for all instances to be started/stopped 
  process_count=$(ps -ef | grep gcloud | grep patch | grep activation-policy= | wc -l)
  set +x
  while [ ${process_count} -gt 0 ]; do 
    ps -ef | grep $$ | grep gcloud | grep patch | grep activation-policy=
    sleep 10
    process_count=$(ps -ef | grep gcloud | grep patch | grep activation-policy= | wc -l)
  done  
}

###############################################
# main
###############################################
constructor
options "$@"
case $operation in
  auto) auto_start_stop_instances ;;
  shutdown) shutdown_instances ;; 
  startup) startup_instances ;;
esac
