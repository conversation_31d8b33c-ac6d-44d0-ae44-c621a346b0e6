def AGENT = 'unity-dev-jenkins-agent'
import hudson.model.ParameterDefinition
import hudson.model.JobProperty
import groovy.json.*
import jenkins.model.*
import java.io.*
import java.io.File
import groovy.io.FileType

pipeline {    
    agent { label env.AGENT_LABEL ?: AGENT }
    environment {
        // Fix github connection so it points to env specific variable  
        STASH_TOKEN= credentials('STASH_TOKEN')   
        NEXUS_CREDS = credentials('NEXUS3_CONNECT')
    }

    stages {
        stage ('Promote image to the production contaier registry') { 
	        steps {
                script {
                    def imagesData = "${imagesList}"

                    echo """Images to promote ${imagesData}"""

                    imagesData.readLines().each{                        
                        String[] imageFullName;
                        imageFullName = it.replace(' ','').split('\\|');
                        if (! imageFullName[0].matches('#.*')) {
                          echo """Tagging image: ${imageFullName[0]} to ${imageFullName[1]}"""
                          sh(""" chmod 755 ./image_promotion/tag_image.sh; ./image_promotion/tag_image.sh --profile-env=dev --source-image-fullname=${imageFullName[0]} --target-image-fullname=${imageFullName[1]} --nexus-creds-usr=${NEXUS_CREDS_USR} --nexus-creds-psw=${NEXUS_CREDS_PSW} """)
                        }
                    }
                }
          }
        }
    }
   
    post {
        unsuccessful {
            mail bcc: '',
                body: "Attention @here ${env.JOB_NAME} #${env.BUILD_NUMBER} has failed.",
                cc: '',
                from: 'unity_cloud_devops@noreply',
                replyTo: '',
                subject: "Failed Pipeline: ${currentBuild.fullDisplayName}",
                to: '<EMAIL>,<EMAIL>'
        }
        success {
            mail bcc: '',
                body: "Succesfully Taged Image: ${env.JOB_NAME} #${env.BUILD_NUMBER}",
                cc: '',
                from: 'unity_cloud_devops@noreply',
                replyTo: '',
                subject: "The pipeline ${currentBuild.fullDisplayName} completed successfully.",
                to: '<EMAIL>,<EMAIL>'
        }     
        cleanup {
            script {
                cleanWs()
            }
        }
    }		

}
