{"machine_type": "n2-standard-4", "source_image_project_id": "hsbc-6320774-distcompute-prod", "source_image_family": "gce-rhel8-ver2-stage2", "image_description": "Management Host: can be used as Jenkins agent or Developer VM", "name_prefix": "gce", "image_name": "mgmt-host-release", "image_family": "gce-image", "image_faimly_suffix": "rhel8-mgmt-host-unity", "image_encryption_key": "projects/hsbc-6320774-kms-dev/locations/asia-east2/keyRings/computeEngine/cryptoKeys/HSMcomputeEngine", "disk_size": "200", "tags": "fwtag-all-dev-out-alm-nexus-2-production,fwtag-all-dev-out-alm-nexus-3-production,fwtag-all-dev-out-alm-nexus-3-uat,fwtag-all-dev-out-efx-nexus-2-production,fwtag-all-dev-out-efx-nexus-3-production,fwtag-all-dev-out-gbm-stash-production,t-dev-all-out-drn-proxy,tt-6320774-vpchost-asia-cinternal-drn-a-i-ssh,tt-9087302-unity-onprem-a-e-all", "project_id": "hsbc-9087302-unity-dev", "region": "asia-east2", "zone": "asia-east2-a", "service_account_email": "<EMAIL>", "scopes": "https://www.googleapis.com/auth/cloud-platform", "subnetwork": "projects/hsbc-6320774-vpchost-asia-dev/regions/asia-east2/subnetworks/cinternal-vpc1-asia-east2", "STASH_TOKEN": "", "GITHUB_TOKEN": "", "build_path": "/opt/build", "java_version": "java-1.8.0-openjdk-devel.x86_64", "git_version": "git-2.31.6-1.el7.x86_64.rpm", "docker_version": "docker-19.03.15.tgz", "docker_path": "/usr/bin", "packer_version": "packer_1.6.6-1.6.6.zip", "packer_path": "/usr/bin", "groovy_path": "/opt/jenkins_agent/build_tools/groovy", "groovy_version": "groovy-binary-3.0.6.zip", "maven_binary": "apache-maven-3.6.3-bin.tar.gz", "maven_version": "apache-maven-3.6.3", "maven_path": "/opt/maven", "terraform_version": "0.13.7", "terraform_path": "/opt/terraform", "jq_binary": "jq-linux64-1.6.0.bin", "jq_path": "/opt/jq", "jq_version": "1.6.0", "nexus_iq_path": "/opt/nexus_iq", "nexus_iq_version": "1.155.0-01", "jenkins_path": "/opt/jenkins_agent", "go_root": "/opt/.go", "go_path": "/opt/go", "go_version": "1.14.6", "bucket_binaries_path": "gs://hsbc-9087302-unity-dev-mgmt-host-pipeline/mgmt_host_binaries", "yum_dataplatforms_version": "yum-dataplatforms.x86_64", "STAGE": "stage2", "BUILDSTAGE3": "false", "appd_machine_agent_path": "/opt/appdynamics", "appd_machine_agent_version": "linux-x86_64-appd-machineagent-22.1.0.3252.zip"}