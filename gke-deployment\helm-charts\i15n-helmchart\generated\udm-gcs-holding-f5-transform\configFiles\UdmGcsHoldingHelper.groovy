import com.poc.hss.fasttrack.transform.model.LookupRequest
import io.vertx.core.json.JsonObject
import org.apache.commons.lang.StringUtils
import org.apache.poi.ss.formula.functions.T
import org.slf4j.Logger
import org.slf4j.LoggerFactory

import java.util.function.Predicate

class UdmGcsHoldingHelper {
    private static final Logger logger = LoggerFactory.getLogger(UdmGcsHoldingHelper.class)

    static Predicate<List<T>> isListNotNullAndEmpty = record -> null != record && !record.isEmpty()

    static LookupRequest createLookupRequest(String criteria, String tableName, List<String> fields) {
        return LookupRequest.builder()
                .accessSchemaName(tableName)
                .criteria(criteria)
                .fields(fields)
                .build()
    }

    static String deriveLocalCustodianCodeOrDescriptionOrCountryTerritoryCode(String locationCountryCode, String returnField) {
        if (locationCountryCode == "GB") {
            return ""
        } else {
            return formatOutputFieldNullToEmpty(returnField)
        }
    }

    static String deriveDepositoryCodeOrDescriptionOrCountryTerritoryCode(String locationCountryCode, String returnField) {
        if (locationCountryCode != "GB") {
            return ""
        } else {
            return formatOutputFieldNullToEmpty(returnField)
        }
    }

    static String deriveAccountBaseCurrency(JsonObject msg) {
        msg.getValue("settled_balance_report_currency")==null || msg.getValue("settled_balance_report_currency")=="null"? msg.getValue("traded_balance_report_currency"):msg.getValue("settled_balance_report_currency")
    }

    static String deriveAssociatedAccountIdentification(JsonObject msg) {
        String associatedAccount = formatOutputFieldNullToEmpty(msg.getValue("associated_sub_account"))
        if (!(msg.getValue("location_id") == "CST" || msg.getValue("location_id") == "CGO")) {
            return associatedAccount
        } else {
            return StringUtils.substringBefore(associatedAccount,"-")
        }

    }
    static String deriveAssociatedSubAccountIdentification(JsonObject msg) {
        if (!(msg.getValue("location_id") == "CST" || msg.getValue("location_id") == "CGO")) {
            return ""
        } else {
            return StringUtils.substringAfter(formatOutputFieldNullToEmpty(msg.getValue("associated_sub_account")),"-")
        }
    }


    static String deriveSafekeepingAccountIdentification(JsonObject msg) {

        return String.format("%s %s %s", msg.getString("system_country_code")?.trim(),
                msg.getString("account_region")?.trim(),
                msg.getString("security_account_number")?.trim()
        )

    }

    static String formatOutputField(String value) {
        return value == null || value.isEmpty() ? null : value
    }

    static String formatOutputFieldNullToEmpty(Object value) {
        return value == null || value == "null" ? "" : value
    }

    static String siteToAdminEntity(String site) {
        switch (site.toUpperCase()) {
            case UdmGcsHoldingConstant.GCUK.toUpperCase():
                return UdmGcsHoldingConstant.GB_HBEU;
            case UdmGcsHoldingConstant.GCE.toUpperCase():
                return UdmGcsHoldingConstant.GB_HBFR;
        }
    }
    
    static String getSiteBySourceSystem(String sourceSystem) {
        switch (sourceSystem) {
            case UdmGcsHoldingConstant.GCSF1:
                return UdmGcsHoldingConstant.GCUK
            case UdmGcsHoldingConstant.GCSF5:
                return UdmGcsHoldingConstant.GCE
            default:
                return ""
        }
    }


}
