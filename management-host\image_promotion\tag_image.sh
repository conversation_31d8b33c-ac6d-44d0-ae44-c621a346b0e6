#!/usr/bin/env bash
#set -euo pipefail

function print_usage {
  echo "Usage:$(basename $0)"
  echo ' --profile-env            '
  echo ' --source-image-fullname  '
  echo ' --target-image-fullname  '
  echo ' [--nexus-creds-usr]      '
  echo ' [--nexus-creds-psw]      '
  echo "Example:$(basename $0) --source-image-fullname=gcr.io/hsbc-9087302-unity-dev/unity/i15n/unity2-business-events:1.3.0-SNAPSHOT --target-image-fullname=gcr.io/hsbc-9087302-unity-dev/unity/i15n/uat/unity2-business-events:1.3.0-SNAPSHOT --profile-env=dev"
}

function options() {
while [ "$1" != "" ];
do
  _param_=`printf "%s\n" $1 | awk -F= '{print $1}'`
  _value_=`printf "%s\n" $1 | sed 's/^[^=]*=//g'`
  if [[ $_value_ == $_param_ ]]; then
    shift
    _value_=$1
  fi
  case ${_param_} in
  --profile-env)
    profile_env="${_value_}"
    ;;
  --nexus-creds-usr)
    nexus_creds_usr="${_value_}"
    ;;
  --nexus-creds-psw)
    nexus_creds_psw="${_value_}"
    ;;
  --source-image-fullname)
    source_image_fullname="${_value_}"
    ;;
  --target-image-fullname)
    target_image_fullname="${_value_}"
    ;;
  --debug)
    DEBUG_MODE=true
    ;;
  --help)
    print_usage
    exit
    ;;
  esac
  shift;
done
}

function constructor() {
_return_code_=0
echo "[MGMT_HOST] checking for default conf"
if [ ! -f "./initial_setup/var-${profile_env}.conf" ]; then
 echo "Parameter file var-${profile_env}.conf not found in initial_setup."
 exit 1
fi
source ./initial_setup/var-${profile_env}.conf
HTTPS_PROXY=googleapis-${profile_env}.gcp.cloud.uk.hsbc:3128
[[ "${DEBUG_MODE:-false}" == "true" ]] && set -x
}

function docker_login() {
if [ ! -z ${nexus_creds_usr} ] && [ ! -z ${nexus_creds_psw} ]; then
docker login ${NEXUSREGISTRY_PROD} --username ${nexus_creds_usr} --password-stdin <<< ${nexus_creds_psw}
docker login ${NEXUSREGISTRY_UAT} --username ${nexus_creds_usr} --password-stdin <<< ${nexus_creds_psw}
docker login ${NEXUSREGISTRY_DEV} --username ${nexus_creds_usr} --password-stdin <<< ${nexus_creds_psw}
docker login ${NEXUSREGISTRY_FOSS} --username ${nexus_creds_usr} --password-stdin <<< ${nexus_creds_psw}
docker login ${NEXUSREGISTRY_UK_PRIVATE} --username ${nexus_creds_usr} --password-stdin <<< ${nexus_creds_psw}
docker login ${NEXUSREGISTRY_UK_PROD} --username ${nexus_creds_usr} --password-stdin <<< ${nexus_creds_psw}
docker login ${NEXUSREGISTRY_UK_UAT} --username ${nexus_creds_usr} --password-stdin <<< ${nexus_creds_psw}
docker login ${NEXUSREGISTRY_UK_DEV} --username ${nexus_creds_usr} --password-stdin <<< ${nexus_creds_psw}
fi
gcloud auth configure-docker ${CONTAINERREGISTRY} --quiet
gcloud auth configure-docker asia.${CONTAINERREGISTRY} --quiet
}

function cleanup(){
if [ ! -z ${nexus_creds_usr} ] && [ ! -z ${nexus_creds_psw} ]; then
docker logout  ${NEXUSREGISTRY_PROD}
docker logout  ${NEXUSREGISTRY_UAT}
docker logout  ${NEXUSREGISTRY_DEV}
docker logout  ${NEXUSREGISTRY_FOSS}
docker logout  ${NEXUSREGISTRY_UK_PRIVATE}
docker logout  ${NEXUSREGISTRY_UK_PROD}
docker logout  ${NEXUSREGISTRY_UK_UAT}
docker logout  ${NEXUSREGISTRY_UK_DEV}
docker logout  ${CONTAINERREGISTRY}
fi
if [[ -f ./.docker_config/config.json ]]; then
  rm -f ./.docker_config/config.json
fi
}

function docker_action(){
echo source_image_fullname:${source_image_fullname}
echo target_image_fullname:${target_image_fullname}
docker pull ${source_image_fullname}
docker tag ${source_image_fullname} ${target_image_fullname}
docker push ${target_image_fullname}
_return_code_=$?
docker rmi ${source_image_fullname}
docker rmi ${target_image_fullname}
}

###############################################################
#MAIN
###############################################################
options "$@"
constructor
docker_login
docker_action
cleanup
exit ${_return_code_}
