export HTTPS_PROXY=10.91.5.251:3128
export NO_PROXY="stash.hk.hsbc,*.hsbc"

Deploy CRD
crd.3.2.yaml

Deploy CRD
Create global configuration
Update nginx configuraton
- startup parameter 
  - -global-configuration=nginx-ingress/nginx-configuration
Create Transport server  

GCP DNS records:
DEV: s3sftp.core-dev.u2kopd01.hsbc-9087302-unity-dev.dev.gcp.cloud.hk.hsbc
PROD: s3sftp.core-prod.u2kopp01.hsbc-11465671-unity-prod.prod.gcp.cloud.hk.hsbc


gcloud dns record-sets create s3sftp.core-prod.u2kopp01.hsbc-11465671-unity-prod.prod.gcp.cloud.hk.hsbc -z hsbc-11465671-unity-prod --rrdatas=130.217.136.22,130.217.134.45,130.217.142.36,130.217.142.35,130.217.134.44 --type=A --ttl=30