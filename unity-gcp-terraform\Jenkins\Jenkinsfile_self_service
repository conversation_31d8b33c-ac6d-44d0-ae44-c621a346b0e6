def skipRemainingStages = false
def project_namespace = ""
def branch = env.branch ?: "master"
def AGENT = "unity-dev-jen<PERSON>-agent"
static final String C_MINUTES = 'MINUTES'
static final boolean C_TRUE = true
static final boolean C_FALSE = false
static final String C_SUCCESS = 'SUCCESS'
static final String C_FAILURE = 'FAILURE'
static final String C_NULL = null
static final String C_YES = 'Yes'
static final String C_NO = 'No'
static final String C_CONFIG_JSON_FILE_PATH = './Jenkins/Configs/hsbc-gcp-config.json'
static final String C_GROOVY_CONNECTION_PATH = 'Jenkins/Configs/Connections.groovy'
static final String C_GROOVY_METHOD_FILE_PATH = "./Jenkins/Methods/JenkinsMethods.groovy"
static final String c_workspace_path = "${env.WORKSPACE}"
static final C_THIS = this

pipeline {
    agent { label env.AGENT_LABEL ?: AGENT }
    options {
      timeout(time: 2, unit: 'HOURS') 
    }
    parameters {
        string(name: "project", defaultValue: "", description: "Name of project that you would like to create namespace on")
        choice(name: 'project_target', choices: ['GKE', 'KOPS'], description: 'Project target')
        choice(name: 'project_gcp_environment', choices: ['hsbc-9087302-unity-dev','hsbc-********-unyin1-dev','hsbc-********-unyeu1-dev','hsbc-********-unybitlm-dev','hsbc-********-unyfaasia-dev','hsbc-********-unyempf-dev','hsbc-********-unity-dev','hsbc-********-unycfs-dev','hsbc-********-unyquartz-dev','hsbc-********-unyclientconn-dev','hsbc-********-unyas-dev','hsbc-********-unycsreport-dev','hsbc-********-unytreasury-dev'], description: "project gcp project")
        string(name: "clusterName", defaultValue: "", description: "E.g. hk-kops-u2kopd01")
        string(name: "authMethod", defaultValue: "IAM-DIRECT", description: "GCP ONLY - GKE cluster Authentication Method")
        choice(name: "enableCloudSqlReplication", choices: ["false","true"], description: "Enable cloud sql replicaset")
        choice(name: "generatePipelineOnly", choices: ["false","true"], description: "Only create jenkins pipeline")
        choice(name: "cloudSqlZones", choices: ["asia-east2-c","asia-south1-a,asia-south2-a","europe-west1-b","europe-west1-b,europe-west2-b","europe-west1-b,europe-west3-b","europe-west3-b,europe-west1-b"], description: "Cloud sql region")
        extendedChoice(           
          name: 'environments', 
          defaultValue: 'dev,uat', 
          description: 'environments to be created', 
          multiSelectDelimiter: ',', 
          quoteValue: false, 
          saveJSONParameterToFile: false, 
          type: 'PT_CHECKBOX', 
          value: 'dev,sit,uat,preprod', 
          visibleItemCount: 4)
    }
    stages {
        stage("Connect to GCP") {
            when {
                expression {
                    params.project_target == "GKE"
                }
            }
            steps {
                script {
                    env.v_project = this.sh(
                            script: """gcloud config list --format 'value(core.project)'""",
                            returnStdout: true
                    ).trim()
                    env.v_clusterName = this.sh(
                            script: """gcloud container clusters list --filter="NAME:gke-t2-vpc3" --region=asia-east2 --format 'value(NAME)'""",
                            returnStdout: true
                    ).trim()
                    env.v_kubeCtlLib = this.sh(
                            script: """gcloud compute addresses list --filter="NAME:gke-kubectl-vpc3" --format 'value(ADDRESS)'""",
                            returnStdout: true
                    ).trim()
                }
            }
        }
        stage("Connect to KOPS") {
            // What Role to use
            when {
                expression {
                    params.project_target == "KOPS"
                }
            }
            steps {
                script {
                    env.v_project = this.sh(
                            script: """gcloud config list --format 'value(core.project)'""",
                            returnStdout: true
                    ).trim()
                    this.sh """
                        unset HTTPS_PROXY
                        gsutil cp -r gs://hsbc-9087302-unity-dev-app-runtime-dependencies/secrets/env/kop/kubeconfig/jenkins-admin.hk-kops-u2kopd01 /tmp
                        kubectl get ns --kubeconfig=/tmp/jenkins-admin.hk-kops-u2kopd01
                        export KUBECONFIG=/tmp/jenkins-admin.hk-kops-u2kopd01
                    """
                }
            }
        }
        stage("Setup Environment") {
            steps {
                script {
                    project_namespace = params.project.toLowerCase().replace('-', '') 
                    cloudSqlZonesList = cloudSqlZones.split(',')                   
                    jenkinsMethodHelper = load("${env.WORKSPACE}/${C_GROOVY_METHOD_FILE_PATH}")
                    jenkinsMethodHelper.v_configJSONObject = jenkinsMethodHelper.createFileJSONObject(C_THIS, C_CONFIG_JSON_FILE_PATH, C_TRUE)
                    jenkinsMethodHelper.initializeEnvironment(v_envFolder="${project_gcp_environment}")
                    clusterName = jenkinsMethodHelper.v_clusterName
                    clusterProject = jenkinsMethodHelper.v_clusterProject
                    clusterRegion = jenkinsMethodHelper.v_clusterRegion
                    kubectlProxy = jenkinsMethodHelper.v_kubectlProxy
                    envSuffix = jenkinsMethodHelper.v_envSuffix
                    envs = environments.split(",")
                }
            }
        }
        stage("Checkout target branch") {
            steps {
                script {
                    this.sh "git checkout ${branch};"
                    current_branch = this.sh(
                        script: """git rev-parse --abbrev-ref HEAD;""",
                        returnStdout: true
                    ).trim()
                    if(current_branch != branch) {
                        skipRemainingStages = true
                        error(message: "Wrong branch. current_branch: ${current_branch} | target_branch: ${branch}")
                    }
                }
            }
        }
        stage("Generate cloudsql and monitoring varfiles and push the changes") {
            when {
                expression {
                    !skipRemainingStages && params.project_target == "GKE" && generatePipelineOnly == "false"
                }
            }
            steps {
                script {
                    for(env in envs) {
                        this.sh """
                                   cloudSqlZonesArray=(\$(echo $cloudSqlZones | sed 's|,| |g'));
                                   for i in \${!cloudSqlZonesArray[@]}; do 
                                     if [[ \${#cloudSqlZonesArray[@]} -gt 1 ]]; then 
                                       dbindex=-\$i
                                       db_backuplocation=\$(echo \${cloudSqlZonesArray[1]} | cut -f1-2 -d-)
                                     else
                                       dbindex=""
                                       db_backuplocation=""
                                     fi 
                                     python3 ./Scripts/python/create_cloudsql_tfvars.py ${project_namespace} ${env} \${cloudSqlZonesArray[\$i]} "\$dbindex" ${clusterProject} "\${db_backuplocation}";                                     
                                   done
                                   ls -l ./Terraform/Environments/${clusterProject}/cloud_sql/varfiles/${project_namespace}/
                                   """
                        this.sh """
                                   python3 ./Scripts/python/create_monitoring_tfvars.py ${project_namespace} ${env} ${clusterProject};                                
                                   ls -l ./Terraform/Environments/${clusterProject}/monitoring/varfiles/${project_namespace}/
                                   """
                    }
                    changes = this.sh(
                        script: """git ls-files --other --exclude-standard --directory""",
                        returnStdout: true
                    ).trim()
                    if(changes != "") {
                        withCredentials([gitUsernamePassword(credentialsId: 'STASH_ACCESS', gitToolName: 'git-tool')]) {
                            this.sh '''git config user.name $GIT_USERNAME;
                                       git config user.email "<>";
                                       git pull;
                                       if [[ $(git status --porcelain=v1|wc -l) -gt 0 ]]; then 
                                         git add -A;                                                                            
                                         git commit -m 'Add cloudsql_tfvars and monitoring_tfvars';
                                         git push origin HEAD;
                                       fi;
                                       '''
                        }
                    }
                }
            }
        }
        stage("create environment variable file") {
            when {
                expression {
                    !skipRemainingStages 
                }
            }
            steps {
                script {
                        this.sh """
                                cat > /tmp/.${project_target}.${clusterProject}.${clusterRegion}.${clusterName}.env <<-EOT
                                export __CLUSTER_TYPE__=${project_target}
                                export __CLUSTER_NAME__=${clusterName}
                                export __CLUSTER_REGION__=${clusterRegion}
                                export __CLUSTER_PROJECT__=${clusterProject}
                                export __KUBECTL_PROXY__=${kubectlProxy}
                                export __AUTH_METHOD__=${authMethod}
                                export __ENV_SUFFIX__=${envSuffix}

                                function authenticate() {			    
                                  case ${authMethod} in 
                                  IAM-DIRECT)
                                     unset HTTPS_PROXY
                                     gcloud container clusters get-credentials ${clusterName} --region=${clusterRegion} --project=${clusterProject}	
                                	 export HTTPS_PROXY=${kubectlProxy}
                                	;;
                                  KUBECONFIG)
                                     echo TODO
                                	 case ${project_target} in 
                                	 KOPS)
                                	   unset HTTPS_PROXY
                                       export KUBECONFIG=/tmp/jenkins-admin.hk-kops-u2kopd01;		   
                                	   ;;
                                	 GKE)
                                	   export KUBECONFIG=${KUBECONFIG}
                                	   export HTTPS_PROXY=${kubectlProxy}				   
                                      ;;			   
                                	 esac
                                    ;;				 
                                  esac
                                }
EOT
                                cat /tmp/.${project_target}.${clusterProject}.${clusterRegion}.${clusterName}.env
                                """
                    }
                }
            
        }        
        stage("Build new common job pipelines") {
            when {
                expression {
                    !skipRemainingStages && params.project_target == "GKE"
                }
            }
            steps {
                script {
                    withCredentials([usernamePassword(credentialsId: 'JENKINS_API_TOKEN', passwordVariable: 'JENKINS_API_TOKEN', usernameVariable: 'USERNAME')]) {
                        this.sh """                                
                                cd ./Jenkins/templates/nonprod/basefolder;
                                rm -rf ./stage
                                source /tmp/.${project_target}.${clusterProject}.${clusterRegion}.${clusterName}.env
                                authenticate                                                  
                                bash ./s_createJobXml.bash --project ${project_namespace};
                                bash ./s_uploadJenkinsConfig.bash;"""
                    }
                }            
                script {
                    withCredentials([usernamePassword(credentialsId: 'JENKINS_API_TOKEN', passwordVariable: 'JENKINS_API_TOKEN', usernameVariable: 'USERNAME')]) {
                        this.sh """                                
                                cd ./Jenkins/templates/nonprod/abortdeployment;
                                rm -rf ./stage
                                source /tmp/.${project_target}.${clusterProject}.${clusterRegion}.${clusterName}.env
                                authenticate                                                  
                                bash ./s_createJobXml.bash --project ${project_namespace};
                                bash ./s_uploadJenkinsConfig.bash;"""
                    }
                }
                script {
                    withCredentials([usernamePassword(credentialsId: 'JENKINS_API_TOKEN', passwordVariable: 'JENKINS_API_TOKEN', usernameVariable: 'USERNAME')]) {
                        this.sh """
                                cd ./Jenkins/templates/nonprod/environmentmanagement;
                                rm -rf ./stage
                                source /tmp/.${project_target}.${clusterProject}.${clusterRegion}.${clusterName}.env
                                authenticate                                 
                                bash ./s_createJobXml.bash --project ${project_namespace};
                                bash ./s_uploadJenkinsConfig.bash;"""
                    }
                }
                script {
                    withCredentials([usernamePassword(credentialsId: 'JENKINS_API_TOKEN', passwordVariable: 'JENKINS_API_TOKEN', usernameVariable: 'USERNAME')]) {
                        this.sh """ 
                                cd ./Jenkins/templates/nonprod/restartdeployment;
                                rm -rf ./stage
                                source /tmp/.${project_target}.${clusterProject}.${clusterRegion}.${clusterName}.env
                                authenticate                            
                                bash ./s_createJobXml.bash --project ${project_namespace};
                                bash ./s_uploadJenkinsConfig.bash;"""
                    }
                }
            }
        }        
        stage("Build new cloudsql pipelines") {
            when {
                expression {
                    !skipRemainingStages && params.project_target == "GKE"
                }
            }
            steps {
                script {
                    withCredentials([usernamePassword(credentialsId: 'JENKINS_API_TOKEN', passwordVariable: 'JENKINS_API_TOKEN', usernameVariable: 'USERNAME')]) {
                        this.sh """ cd ./Jenkins/templates/nonprod/cloudsql;
                                rm -rf ./stage
                                source /tmp/.${project_target}.${clusterProject}.${clusterRegion}.${clusterName}.env
                                bash ./s_createJobXml.bash --project ${project_namespace};
                                bash ./s_uploadJenkinsConfig.bash;"""
                    }
                }
            }
        }
        stage("Build new configmaps pipelines") {
            when {
                expression {
                    !skipRemainingStages
                }
            }
            steps {
                script {
                    withCredentials([usernamePassword(credentialsId: 'JENKINS_API_TOKEN', passwordVariable: 'JENKINS_API_TOKEN', usernameVariable: 'USERNAME')]) {
                        this.sh """
                                if [[ ${params.project_target} == 'GKE' ]]; then
                                    cd ./Jenkins/templates/nonprod/configmaps;                                    
                                else
                                    cd ./Jenkins/templates/kops/nonprod/configmaps;
                                fi
                                rm -rf ./stage;
                                source /tmp/.${project_target}.${clusterProject}.${clusterRegion}.${clusterName}.env
                                authenticate   
                                set +x;
                                chmod +x ./s_createJobXml.bash;
                                chmod +x ./s_uploadJenkinsConfig.bash;
                                ./s_createJobXml.bash --project ${project_namespace};
                                ./s_uploadJenkinsConfig.bash;
                                exit;"""
                    }
                }
            }
        }
        stage("Build new monitoring pipelines") {
            when {
                expression {
                    !skipRemainingStages && params.project_target == "GKE"
                }
            }
            steps {
                script {
                    withCredentials([usernamePassword(credentialsId: 'JENKINS_API_TOKEN', passwordVariable: 'JENKINS_API_TOKEN', usernameVariable: 'USERNAME')]) {
                        this.sh """ cd ./Jenkins/templates/nonprod/monitoring;
                                rm -rf ./stage
                                source /tmp/.${project_target}.${clusterProject}.${clusterRegion}.${clusterName}.env
                                bash ./s_createJobXml.bash --project ${project_namespace};
                                bash ./s_uploadJenkinsConfig.bash;"""
                    }
                }
            }
        }
        stage("Build new secrets pipelines") {
            when {
                expression {
                    !skipRemainingStages
                }
            }
            steps {
                script {
                    withCredentials([usernamePassword(credentialsId: 'JENKINS_API_TOKEN', passwordVariable: 'JENKINS_API_TOKEN', usernameVariable: 'USERNAME')]) {
                        this.sh """
                                if [[ ${params.project_target} == 'GKE' ]]; then
                                    cd ./Jenkins/templates/nonprod/secrets;                                    
                                else
                                    cd ./Jenkins/templates/kops/nonprod/secrets;
                                fi
                                rm -rf ./stage;
                                source /tmp/.${project_target}.${clusterProject}.${clusterRegion}.${clusterName}.env
                                authenticate
                                set +x;
                                chmod +x ./s_createJobXml.bash;
                                chmod +x ./s_uploadJenkinsConfig.bash;
                                ./s_createJobXml.bash --project ${project_namespace};
                                ./s_uploadJenkinsConfig.bash;
                                exit;"""
                    }
                }
            }
        }
        stage("Build new deployment pipelines") {
            when {
                expression {
                    !skipRemainingStages
                }
            }
            steps {
                script {
                    withCredentials([usernamePassword(credentialsId: 'JENKINS_API_TOKEN', passwordVariable: 'JENKINS_API_TOKEN', usernameVariable: 'USERNAME'), gitUsernamePassword(credentialsId: 'STASH_ACCESS', gitToolName: 'git-tool')]) {
                        target=params.project_target
                        if (target == "KOPS") {
                            target = "kop"
                        }
                        this.sh """
                                if [[ ${params.project_target} == 'GKE' ]]; then
                                    cd ./Jenkins/templates/nonprod/helmchartdeployment;                                                                    
                                else
                                    cd ./Jenkins/templates/kops/nonprod/helmchartdeployment;                                    
                                fi
                                set +x;
                                rm -rf ./stage;
                                export NO_PROXY=hsbc;
                                source /tmp/.${project_target}.${clusterProject}.${clusterRegion}.${clusterName}.env
                                authenticate
                                chmod +x ./s_createJobXml.bash;
                                chmod +x ./s_uploadJenkinsConfig.bash;                                
                                git config user.name $GIT_USERNAME;
                                git config user.email "<>";
                                git pull;
                                if [[ \$(git status --porcelain=v1|grep \"project.lst\" | wc -l) -gt 0 ]]; then
                                  echo \"adding project to project list\"
                                  git add ./project.lst;
                                  git commit -m 'Add project to project.lst';
                                  git push origin HEAD;
                                else
                                  echo \"skipped: adding project to project list\"
                                fi
                                ./s_createJobXml.bash ${params.project} ${target} ${params.clusterName};
                                ./s_uploadJenkinsConfig.bash ${project_namespace};
                                exit;"""
                    }
                }
            }
        }
        stage("Start the cloudsql pipline") {
            when {
                expression {
                    !skipRemainingStages && params.project_target == "GKE" && generatePipelineOnly == "false"
                }
            }
            steps {
                script {   
                   catchError(buildResult: 'UNSTABLE', stageResult: 'FAILURE') {                   
                     for (env in envs) {
                       for (i = 0; i < cloudSqlZonesList.size() ; i++) {
                         if (cloudSqlZonesList.size() > 1) {
                           dbindex="-"+i
                         }
                         else{ 
                           dbindex=""
                         }
                         //Skip replicas creation, only execute primary db creation with index 0 
                         if ( i == 0 ) {
                           jobName="/hsbc-********-unity/DEV/project_workspace/${project_namespace}/${clusterProject}/CLOUDSQL_Resources_Deployment/cloudsql_deployment_"+env+dbindex
                           try {
                             //TODO: change back Test Deployment to Create enviroment 
                             build job: jobName, parameters:[string(name: 'OPERATION', value: "Create enviroment")], wait:true    
                           } 
                           catch (NullPointerException e) {
                             println("This job does not exist")
                           }
                         }                       
                       }
                     }
                   }                          
                }
            }
        }
        stage("Start the monitoring pipline") {
            when {
                expression {
                    !skipRemainingStages && params.project_target == "GKE" && generatePipelineOnly == "false"
                }
            }
            steps {
                script {
                    catchError(buildResult: 'UNSTABLE', stageResult: 'FAILURE') {
                      for (env in envs) {        
                        jobName="/hsbc-********-unity/DEV/project_workspace/${project_namespace}/${clusterProject}/MONITORING_Resources_Deployment/monitoring_deployment_"+env
                        try {
                          //TODO: change back Test Deployment to Create enviroment 
                          build job: jobName, parameters:[string(name: 'OPERATION', value: "Test Deployment")], wait:true    
                        } 
                        catch (NullPointerException e) {
                          println("This job does not exist")
                        }
                      }                                               
                    }                    
                }
            }
        }
        stage("Grant cloudsqlsuperuser") {
            when {
                    expression {
                        !skipRemainingStages && params.project_target == "GKE" && generatePipelineOnly == "false"
                    }
            }
            steps {
                script {
                    project_namespace = params.project.toLowerCase().replace('-', '')
                    build job: "/hsbc-********-unity/DEV/Common/automatic/AUTO_GRANT_CLOUDSQL_PRIVS", parameters:[string(name: 'parameters', value: "--project=${clusterProject} --image-project=hsbc-9087302-unity-dev --region=${clusterRegion} --clustername=${clusterName} --kubeproxy=${kubectlProxy} --service-account-key-file=${jenkinsMethodHelper.v_salocation} --filter='${project_namespace}'")], wait:true
                }
            }
        }
        stage("Add SSL certificate to namesapce") {
            when {
                    expression {
                        !skipRemainingStages && generatePipelineOnly == "false"
                    }
            }
            steps {
                script {
                  domain=kubectlProxy.split(":")[0].split("\\.")[-6..-1].join(".")
                  switch (params.project_target) {
                    case "GKE":
                      build job: "/hsbc-********-unity/DEV/Common/automatic/TLS_CERTIFICATES_RENEW_ON_DEMAND", parameters:[
                        string(name: "namespace", value: "ns-${project_namespace}-dev-apps,ns-${project_namespace}-sit-apps,ns-${project_namespace}-uat-apps,ns-${project_namespace}-preprod-apps"), 
                        string(name: "vpc", value: "${clusterName}"), 
                        string(name: "domain", value: "${domain}"), 
                        string(name: "extra_opts", value: "--clusterproject=${clusterProject} --clustername=${clusterName} --clusterregion=${clusterRegion} --kubeproxy=${kubectlProxy}")
                      ], wait:true
                      break
                    case "KOPS":
                      build job: "/hsbc-********-unity/DEV/Common/automatic/TLS_CERTIFICATES_RENEW_U2KOPD01", parameters:[string(name: "namespace", value: "ns-${project_namespace}-dev-apps,ns-${project_namespace}-sit-apps,ns-${project_namespace}-uat-apps,ns-${project_namespace}-preprod-apps")], wait:true
                      break
                  }
                }
            }
        }
        stage("Grant Jenkins access") {
            when {
                expression {
                    !skipRemainingStages
                }
            }
            steps {
                script {
                    withCredentials([usernamePassword(credentialsId: 'JENKINS_API_TOKEN', passwordVariable: 'JENKINS_API_TOKEN', usernameVariable: 'USERNAME')]) {
                        this.sh "python3 ./Scripts/python/create_group_access.py ${params.project};"
                    }
                    
                }
            }
        }
    }
    post {
        cleanup {
            cleanWs()
        }
    }
}
