#!/bin/bash
component=helm
stagedir=./stage
stagefile=/tmp/$$.tmp
#namespace needs to be created before generating the pipeline
kubectl get namespaces -o custom-columns=:.metadata.name > ${stagefile}
projects=($(cat ${stagefile} | grep "^ns-" | cut -f2 -d- | sort -u))

if [[ ! -z "$1" ]] && [[ -z $(cat ./project.lst | grep -w "$1") ]]; then
  echo $1 >> project.lst 
fi

#Create project maps
declare -A project_map
while read p ; do 
 named=$(echo $p|sed 's|-||g'|tr [:upper:] [:lower:])
 project_map["$named"]=$p
done < project.lst 

#Create Jobs Xml
mkdir -p ${stagedir}
cat > ${stagedir}/jobs.xml <<-EOT
<folder _class='com.cloudbees.hudson.plugins.folder.Folder'>
$(
  for project in ${projects[@]}; do
    echo "  <job _class='com.cloudbees.hudson.plugins.folder.Folder'><name>${project}</name></job>"
  done 
)
</folder>
EOT

#Create Project Job Xml
#p=trimmed, project=branch-prefix
for p in ${projects[@]}; do
project=${project_map["${p}"]}
if [[ -z "${project}" ]]; then 
  echo "no project mapping found for $p [skipping]"
else
cat ${stagefile} | grep "^ns-" | grep ${p} | while read namespace; do 
_env_=$(echo $namespace | cut -f3 -d-)
mkdir -p ${stagedir}/${p}
cat project-folder-config.template.xml \
| sed \
 -e "s|__PROJECT__|${project}|g" \
 -e "s|__PROJECT_TRIMMED__|${p}|g" \
 -e "s|__ENV__|${_env_^^}|g" \
 > ${stagedir}/${p}/config.xml


#create job config xml 
job=$(echo ${component}_deployment_${_env_} | tr [:lower:] [:upper:])
mkdir -p ${stagedir}/${p}/${job}
jenkinsfile="Jenkinsfile_custom"
if [[ "${_env_^^}" != "PROD" ]]; then
  jenkinsfile="Jenkinsfile_manual"
fi
cat job-config.template.xml \
| sed \
 -e "s|__PROJECT__|${project}|g" \
 -e "s|__JENKINSFILE__|${jenkinsfile}|g" \
 -e "s|__ENV__|${_env_^^}${__GIT_BRANCH_SUFFIX__}|g" \
 -e "s|__CLUSTER_TYPE__|${__CLUSTER_TYPE__}|g" \
 -e "s|__CLUSTER_NAME__|${__CLUSTER_NAME__}|g" \
 -e "s|__CLUSTER_REGION__|${__CLUSTER_REGION__}|g" \
 -e "s|__CLUSTER_PROJECT__|${__CLUSTER_PROJECT__}|g" \
 -e "s|__KUBECTL_PROXY__|$(echo ${__KUBECTL_PROXY__}|cut -f1 -d:)|g" \
 -e "s|__AUTH_METHOD__|${__AUTH_METHOD__}|g" \
 -e "s|__ENV_SUFFIX__|${__ENV_SUFFIX__:-""}|g" \
 > ${stagedir}/${p}/${job}/config.xml

cat job-folder-config.template.xml \
| sed \
 -e "s|__PROJECT__|${project}|g" \
 -e "s|__ENV__|${_env_^^}${__GIT_BRANCH_SUFFIX__}|g" \
 -e "s|__CLUSTER_TYPE__|${__CLUSTER_TYPE__}|g" \
 -e "s|__CLUSTER_NAME__|${__CLUSTER_NAME__}|g" \
 -e "s|__CLUSTER_REGION__|${__CLUSTER_REGION__}|g" \
 -e "s|__CLUSTER_PROJECT__|${__CLUSTER_PROJECT__}|g" \
 -e "s|__KUBECTL_PROXY__|$(echo ${__KUBECTL_PROXY__}|cut -f1 -d:)|g" \
 -e "s|__AUTH_METHOD__|${__AUTH_METHOD__}|g" \
 > ${stagedir}/${p}/${job}/job-folder-config.template.xml
done

fi #project mapping found

done
