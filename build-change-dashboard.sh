#!/bin/bash
set -euo pipefail

# Build script for change-dashboard using devops-helper
echo "=== Building change-dashboard ==="

# Set environment variables
export PROJECT_NAME="change-dashboard"
export PROJECT_DIR="change-dashboard"

# Load build configuration
if [ -f "${PROJECT_DIR}/.devops/build.properties" ]; then
    source "${PROJECT_DIR}/.devops/build.properties"
    echo "Loaded build configuration from ${PROJECT_DIR}/.devops/build.properties"
else
    echo "Warning: No build.properties found, using defaults"
    export mvn_build_enable=true
    export docker_build_enable=true
    export jdk_version=17
fi

# Change to project directory
cd "${PROJECT_DIR}"

echo "=== Step 1: Maven Build ==="
if [ "${mvn_build_enable}" = "true" ]; then
    # Use devops-helper for Maven build
    ../devops-helper/src/devops/scripts/build/actions.bash mvn_build
    echo "Maven build completed successfully"
else
    echo "Maven build disabled"
fi

echo "=== Step 2: Docker Build ==="
if [ "${docker_build_enable}" = "true" ]; then
    # Create auth.conf for Docker build (if needed)
    if [ ! -f "auth.conf" ]; then
        echo "Creating auth.conf for Docker build..."
        # This would typically be provided by your CI/CD system
        touch auth.conf
    fi
    
    # Use devops-helper for Docker build
    ../devops-helper/src/devops/scripts/build/actions.bash docker_build
    echo "Docker build completed successfully"
else
    echo "Docker build disabled"
fi

echo "=== Step 3: Security Scanning ==="
if [ "${container_scan_enable:-false}" = "true" ]; then
    ../devops-helper/src/devops/scripts/build/actions.bash container_scan
    echo "Container security scan completed"
fi

if [ "${sast_scan_enable:-false}" = "true" ]; then
    ../devops-helper/src/devops/scripts/build/actions.bash sast_scan
    echo "SAST security scan completed"
fi

echo "=== Build completed successfully for ${PROJECT_NAME} ==="
echo "Docker image: gcr.io/hsbc-9087302-unity-dev/unity/i15n/${PROJECT_NAME}:$(mvn help:evaluate -Dexpression=project.version -q -DforceStdout)"

cd ..
