#!/usr/bin/env bash
set -euo pipefail

#generate gce stage3 image builder service account
#./initial_setup/generate_sa_key.sh dev

#create mgmt host gcs bucket
#please pass the input vale either dev or prod like sh ./initial_setup/create_gcebucket dev

sh ./initial_setup/create_gcsbucket.sh  dev

#create a managementhost manually
 sh ./initial_setup/create_mgmt_host.sh dev


#run packer to do build the mgmt host
#packer build -timestamp-ui -on-error=cleanup -var-file=variables.json image.json
packer build -timestamp-ui -on-error=cleanup -var "BUILDSTAGE3=true" -var "STAGE=stage3" -var "ENVIRONMENT=ist"  -var "STASH_TOKEN=TOKEN" -var "NEXUS_CREDS_USR=NEXSA" -var "NEXUS_CREDS_PWD=NEXUSPWD" -var-file=variables.json image.json

#template creation
gcloud compute instance-templates create gce-uscentral-mgmttemplate-$(date "+%m%d") --machine-type=n2-standard-4 --project hsbc-9087302-unity-dev-dev  --no-address --subnet projects/hsbc-6320774-vpchost-asia-dev/regions/asia-east2/subnetworks/cinternal-vpc2-asia-east2 --image-family gce-asia-east2-hsbc-rhel7-mgmt-host --tags fwtag-all-dev-in-ansible-tower-production,fwtag-all-dev-in-ansible-tower-staging,fw-hsbc-vpc-net1-nessusscan-ingress,hsbc-ccs11-ingress,fwtag-all-dev-in-nessus-scanner,fwtag-all-dev-inout-ccs-collectors-production,fwtag-all-dev-out-alm-github,fwtag-all-dev-out-alm-nexus-3-production,fwtag-all-dev-out-alm-nexus-3-uat,fwtag-all-dev-out-efx-nexus-2-production,fwtag-all-dev-out-efx-nexus-3-production,t-all-dev-in-drn-jumpservers,t-all-dev-in-unrestricted-api-forwarders,t-dev-all-out-alm-nexus-3-docker-uat,t-dev-all-out-drn-proxy,t-all-dev-all-in-ilb-healthcheck,t-6320774-dev-in-all-jenkinsslave-ssh,t-6320774-dev-in-all-seccloud-ssh,t-6320774-dev-out-all-seccloud-ssh,t-hsbc-cmbsp-sb-all-in-vpc1,t-hsbc-cmbsp-sb-all-in-vpc2,t-hsbc-cmbsp-sb-all-in-vpc3,t-hsbc-cmbsp-sb-all-in-vpc4,tt-10262534-cmbdigonb-130-178-57-232-a-i-ssh,tt-867330-ossnafilenet-10-105-64-0-24-a-e-all,tt-867330-ossnafilenet-10-105-64-0-24-a-i-all,tt-867330-ossnafilenet-130-47-33-0-24-a-e-all,tt-9919399-iccmcore-128-13-97-66-a-i-jenkins,tt-9919399-iccmcore-128-161-53-115-a-e-jenkins,tt-9919399-iccmcore-128-161-53-115-a-i-jenkins,dev-default-out-rule,t-8150310-global-risk-devops-tooling,tt-6320774-vpchost-us-cinternal-drn-a-i-ssh, tt-867330-ossnafilenet-128-0-0-0-2-a-i-all,tt-867330-ossnafilenet-128-0-0-0-2-a-e-all,tt-867330-ossnafilenet-10-0-0-0-8-a-i-all --boot-disk-size=200 --image-project=hsbc-9087302-unity-dev-dev --scopes=https://www.googleapis.com/auth/cloud-platform --boot-disk-kms-key projects/hsbc-6320774-kms-dev/locations/asia-east2/keyRings/computeEngine/cryptoKeys/HSMcomputeEngine

gcloud compute instance-templates create gce-mgmt-host-dev-$(date "+%Y%m%d") \
--machine-type=n2-standard-4 \
--project hsbc-9087302-unity-dev  \
--no-address \
--network-interface=subnet=projects/hsbc-6320774-vpchost-asia-dev/regions/asia-east2/subnetworks/cinternal-vpc1-asia-east2,no-address \
--network-interface=subnet=projects/hsbc-9087302-unity-dev/regions/asia-east2/subnetworks/cinternal-vpc2-asia-east2-gkenodes2,no-address \
--image-family gce-image-stage2-rhel8-mgmt-host-unity \
--scopes=https://www.googleapis.com/auth/cloud-platform,https://www.googleapis.com/auth/userinfo.email \
--boot-disk-kms-key projects/hsbc-6320774-kms-dev/locations/asia-east2/keyRings/computeEngine/cryptoKeys/computeEngine \
--boot-disk-size=200 \
--image-project=hsbc-9087302-unity-dev \
--service-account <EMAIL> \
--metadata=jenkins_agent_name=unity-dev-jenkins-agent \
--tags tt-9087302-unity-onprem-a-e-all,fwtag-all-dev-out-alm-nexus-2-production,fwtag-all-dev-out-alm-nexus-3-production,fwtag-all-dev-out-alm-nexus-3-uat,fwtag-all-dev-out-efx-nexus-2-production,fwtag-all-dev-out-efx-nexus-3-production,fwtag-all-dev-out-gbm-stash-production,t-dev-all-out-drn-proxy,tt-6320774-vpchost-asia-cinternal-drn-a-i-ssh,fwtag-all-dev-out-alm-github,tt-9087302-unity-10-92-0-0-x-18-a-e-webhook,tt-9087302-unity-webhook-a-i-stash

gcloud compute instance-templates create gce-mgmt-host-gcp-dev-$(date "+%Y%m%d") \
--machine-type=n2-standard-4 \
--project hsbc-9087302-unity-dev  \
--no-address \
--network-interface=subnet=projects/hsbc-6320774-vpchost-asia-dev/regions/asia-east2/subnetworks/cinternal-vpc1-asia-east2,no-address \
--network-interface=subnet=projects/hsbc-9087302-unity-dev/regions/asia-east2/subnetworks/cinternal-vpc2-asia-east2-gkenodes2,no-address \
--image-family gce-image-stage2-rhel8-mgmt-host-unity \
--scopes=https://www.googleapis.com/auth/cloud-platform,https://www.googleapis.com/auth/userinfo.email \
--boot-disk-kms-key projects/hsbc-6320774-kms-dev/locations/asia-east2/keyRings/computeEngine/cryptoKeys/computeEngine \
--boot-disk-size=200 \
--image-project=hsbc-9087302-unity-dev \
--service-account <EMAIL> \
--metadata=jenkins_agent_name=sa-hsbc-********-unity-linux-unity-dev-jenkins-agent \
--tags tt-9087302-unity-onprem-a-e-all,fwtag-all-dev-out-alm-nexus-2-production,fwtag-all-dev-out-alm-nexus-3-production,fwtag-all-dev-out-alm-nexus-3-uat,fwtag-all-dev-out-efx-nexus-2-production,fwtag-all-dev-out-efx-nexus-3-production,fwtag-all-dev-out-gbm-stash-production,t-dev-all-out-drn-proxy,tt-6320774-vpchost-asia-cinternal-drn-a-i-ssh,fwtag-all-dev-out-alm-github,tt-9087302-unity-10-92-0-0-x-18-a-e-webhook,tt-9087302-unity-webhook-a-i-stash

#mig creation
gcloud compute instance-groups managed create gce-mig-mgmt-host-dev --region asia-east2 --template  gce-mgmt-host-dev-$(date "+%Y%m%d") --size 1 --project hsbc-9087302-unity-dev
gcloud compute instance-groups managed create gce-mig-mgmt-host-gcp-dev --region asia-east2 --template  gce-mgmt-host-gcp-dev-$(date "+%Y%m%d") --size 1 --project hsbc-9087302-unity-dev

#rolling replace command
gcloud compute instance-groups managed rolling-action replace gce-uscentral1dev-mgmtmig-$(date "+%m%d")  --zone asia-east2-b

#start update template and recreate
gcloud compute instance-groups managed rolling-action start-update gce-mig-mgmt-host-dev \
--version=template=gce-mgmt-host-dev-$(date "+%m%d")  \
--type="opportunistic" --replacement-method=recreate  --region=asia-east2 --max-surge=0
