// Jenkins Pipeline Configuration for change-dashboard and risc
// This configuration should be added to your existing Jenkinsfile_custom

// Add these services to the charts list in your Jenkins pipeline
def additionalCharts = [
    'change-dashboard',
    'risc'
]

// Environment-specific configurations
def environments = [
    'dev': [
        'namespace': 'ns-i15n-dev',
        'cluster': 'gke-t2-vpc3',
        'project': 'hsbc-9087302-unity-dev',
        'region': 'asia-east2'
    ],
    'uat': [
        'namespace': 'ns-i15n-uat',
        'cluster': 'gke-t2-vpc3',
        'project': 'hsbc-9087302-unity-uat',
        'region': 'asia-east2'
    ],
    'prod': [
        'namespace': 'ns-i15n-prod',
        'cluster': 'gke-t2-vpc3',
        'project': 'hsbc-9087302-unity-prod',
        'region': 'asia-east2'
    ]
]

// Build configuration for each service
def buildConfigs = [
    'change-dashboard': [
        'dockerfile': 'change-dashboard/Dockerfile',
        'buildContext': 'change-dashboard',
        'imageName': 'unity/i15n/change-dashboard',
        'healthCheckPath': '/actuator/health'
    ],
    'risc': [
        'dockerfile': 'risc/Dockerfile',
        'buildContext': 'risc',
        'imageName': 'unity/i15n/risc',
        'healthCheckPath': '/actuator/health'
    ]
]

// Integration with existing pipeline
// Add this to your existing Jenkinsfile_custom in the appropriate stages:

/*
// In the build stage, add:
stage('Build Additional Services') {
    parallel {
        'change-dashboard': {
            dir('change-dashboard') {
                sh '''
                    source .devops/build.properties
                    ../devops-helper/src/devops/scripts/build/actions.bash mvn_build
                    ../devops-helper/src/devops/scripts/build/actions.bash docker_build
                '''
            }
        },
        'risc': {
            dir('risc') {
                sh '''
                    source .devops/build.properties
                    ../devops-helper/src/devops/scripts/build/actions.bash mvn_build
                    ../devops-helper/src/devops/scripts/build/actions.bash docker_build
                '''
            }
        }
    }
}

// In the deployment stage, add these to your charts list:
charts.addAll(['change-dashboard', 'risc'])

// The existing deploy.sh script will handle the deployment using the Helm values
*/

// Monitoring and alerting configuration
def monitoringConfig = [
    'change-dashboard': [
        'metrics': [
            'path': '/actuator/prometheus',
            'port': '8080'
        ],
        'alerts': [
            'cpu_threshold': '80%',
            'memory_threshold': '80%',
            'response_time_threshold': '2s'
        ]
    ],
    'risc': [
        'metrics': [
            'path': '/actuator/prometheus',
            'port': '8080'
        ],
        'alerts': [
            'cpu_threshold': '80%',
            'memory_threshold': '80%',
            'response_time_threshold': '2s'
        ]
    ]
]

// Security scanning configuration
def securityConfig = [
    'container_scan': true,
    'sast_scan': true,
    'dependency_scan': true,
    'compliance_check': true
]

return [
    'charts': additionalCharts,
    'environments': environments,
    'buildConfigs': buildConfigs,
    'monitoringConfig': monitoringConfig,
    'securityConfig': securityConfig
]
