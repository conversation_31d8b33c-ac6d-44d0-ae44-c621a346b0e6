#/bin/bash
if command -v curl &> /dev/null
then
  status=$(curl -s localhost:8080/actuator/health | grep -Po '"status":.*?[^\\]",' | sed -e 's|"||g' -e 's|,||g'  | cut -f2 -d:)
  httpcode=$(curl -s localhost:8080/actuator/health -w "%{http_code}" -o /dev/null)
  if [[ "${status}" = "OUT_OF_SERVICE" || "${status}" = "DOWN" ]]; then 
    rc=1 
  elif [[ ${httpcode} -eq 200 ]] ; then 
    rc=0
  else 
    rc=1  
  fi
else 
  #case when curl not found on pre 2.8.0 release, perform port check   
  IP_HEX=$(printf '%02X' $(echo 0.0.0.0 | tr '.' ' '))
  PORT_HEX=$(printf '%04X' 8080)

  echo curl not found: performing port check [8080]
  port_opened=$(grep  "$IP_HEX:$PORT_HEX" /proc/net/tcp | wc -l)  

  if [[ ${port_opened} -gt 0 ]]; then 
    rc=0
  else 
    rc=1  
  fi
fi
exit $rc