#!/usr/bin/env bash
set -euo pipefail
sudo touch output.log
sudo chmod 777 output.log
#echo $(pwd)
#exec &>>output.log
echo "<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<< JAVA 8 >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>"
echo "[MGMT_HOST] installing java 8"
sudo yum -y -q install $JAVA_VERSION
echo "[MGMT_HOST] completed installation of java version: $(java -version) in $(pwd)"
echo "<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<< JAVA 17 >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>"
echo "[MGMT_HOST] installing java 17"
mkdir -p /opt/java
cd /opt/java
curl https://hkl20090861.hc.cloud.hk.hsbc/devops/zulu17.38.21-ca-jdk17.0.5-linux_x64.tar.gz --output /dev/stdout | gunzip | tar -xf -
ln -s /opt/java/zulu17.38.21-ca-jdk17.0.5-linux_x64 /opt/java/zulu17
mv /opt/java/zulu17/lib/security/cacerts /opt/java/zulu17/lib/security/cacerts.$(date +%Y%m%d-%H%M)
ln -s /etc/pki/ca-trust/extracted/java/cacerts /opt/java/zulu17/lib/security/cacerts
chmod -R 755 /opt/java
echo "[MGMT_HOST] completed installation of java version: $(/opt/java/zulu17/bin/java -version) in $(pwd)"
#cat>output.log
