<?xml version='1.1' encoding='UTF-8'?>
<flow-definition plugin="workflow-job@1400.v7fd111b_ec82f">
  <actions>
    <org.jenkinsci.plugins.pipeline.modeldefinition.actions.DeclarativeJobAction plugin="pipeline-model-definition@2.2198.v41dd8ef6dd56"/>
    <org.jenkinsci.plugins.pipeline.modeldefinition.actions.DeclarativeJobPropertyTrackerAction plugin="pipeline-model-definition@2.2198.v41dd8ef6dd56">
      <jobProperties>
        <string>org.jenkinsci.plugins.workflow.job.properties.DisableConcurrentBuildsJobProperty</string>
        <string>jenkins.model.BuildDiscarderProperty</string>
      </jobProperties>
      <triggers/>
      <parameters>
        <string>GCS_PENALTY</string>
        <string>ARGS</string>
        <string>ENVIRONMENT</string>
        <string>LIMIT</string>
        <string>SUITES_TO_RUN</string>
        <string>DAYS_OFFSET</string>
        <string>DCC_UDM_MARKET</string>
        <string>GCA_MARKET</string>
        <string>GCS_UDM_MARKET</string>
        <string>GCS_MARKET</string>
        <string>RERUN</string>
        <string>DCC_MARKET</string>
        <string>DAYS_PERIOD</string>
      </parameters>
      <options/>
    </org.jenkinsci.plugins.pipeline.modeldefinition.actions.DeclarativeJobPropertyTrackerAction>
    <hudson.plugins.robot.RobotProjectAction plugin="robot@3.5.2">
      <project class="flow-definition" reference="../../.."/>
    </hudson.plugins.robot.RobotProjectAction>
  </actions>
  <description>Unity to DataMesh Tests&#xd;
</description>
  <displayName>Unity to DataMesh Tests</displayName>
  <keepDependencies>false</keepDependencies>
  <properties>
    <hudson.plugins.jira.JiraProjectProperty plugin="jira@3.12"/>
    <jenkins.model.BuildDiscarderProperty>
      <strategy class="hudson.tasks.LogRotator">
        <daysToKeep>-1</daysToKeep>
        <numToKeep>20</numToKeep>
        <artifactDaysToKeep>-1</artifactDaysToKeep>
        <artifactNumToKeep>-1</artifactNumToKeep>
      </strategy>
    </jenkins.model.BuildDiscarderProperty>
    <org.jenkinsci.plugins.workflow.job.properties.DisableConcurrentBuildsJobProperty>
      <abortPrevious>false</abortPrevious>
    </org.jenkinsci.plugins.workflow.job.properties.DisableConcurrentBuildsJobProperty>
    <com.sonyericsson.rebuild.RebuildSettings plugin="rebuild@330.v645b_7df10e2a_">
      <autoRebuild>false</autoRebuild>
      <rebuildDisabled>false</rebuildDisabled>
    </com.sonyericsson.rebuild.RebuildSettings>
    <io.jenkins.plugins.config.DevOpsJobProperty plugin="servicenow-devops@4.1.0">
      <ignoreSNErrors>false</ignoreSNErrors>
    </io.jenkins.plugins.config.DevOpsJobProperty>
    <hudson.plugins.jobConfigHistory.JobLocalConfiguration plugin="jobConfigHistory@1229.v3039470161a_d">
      <changeReasonComment></changeReasonComment>
    </hudson.plugins.jobConfigHistory.JobLocalConfiguration>
    <hudson.model.ParametersDefinitionProperty>
      <parameterDefinitions>
        <hudson.model.ChoiceParameterDefinition>
          <name>ENVIRONMENT</name>
          <description>Unity Environment</description>
          <choices>
            <string>uat</string>
            <string>sit</string>
            <string>dev</string>
          </choices>
        </hudson.model.ChoiceParameterDefinition>
        <com.cwctravel.hudson.plugins.extended__choice__parameter.ExtendedChoiceParameterDefinition plugin="extended-choice-parameter@381.v360a_25ea_017c">
          <name>SUITES_TO_RUN</name>
          <description>Suites to run</description>
          <quoteValue>false</quoteValue>
          <saveJSONParameterToFile>false</saveJSONParameterToFile>
          <visibleItemCount>10</visibleItemCount>
          <type>PT_CHECKBOX</type>
          <value>DCC UDM Trades To DataMesh,DCC Trades To DataMesh,DCC Holdings To DataMesh,GCS UDM Trades To DataMesh,GCS Trades To DataMesh,GCS Holdings To DataMesh,GCS Penalty To DataMesh,GCA UDM Trades To DataMesh</value>
          <defaultValue></defaultValue>
          <multiSelectDelimiter>,</multiSelectDelimiter>
        </com.cwctravel.hudson.plugins.extended__choice__parameter.ExtendedChoiceParameterDefinition>
        <hudson.model.ChoiceParameterDefinition>
          <name>DCC_UDM_MARKET</name>
          <description>Select market for DCC UDM Trades suite</description>
          <choices>
            <string>AE</string>
            <string>AU</string>
            <string>BH</string>
            <string>HK</string>
            <string>IN</string>
            <string>JP</string>
            <string>MY</string>
            <string>NZ</string>
            <string>PH</string>
            <string>SG</string>
            <string>TH</string>
            <string>TW</string>
            <string>VN</string>
          </choices>
        </hudson.model.ChoiceParameterDefinition>
        <hudson.model.ChoiceParameterDefinition>
          <name>DCC_MARKET</name>
          <description>Select market for DCC Trades and Holdings suites</description>
          <choices>
            <string>AE</string>
            <string>AU</string>
            <string>HK</string>
            <string>ID</string>
            <string>JP</string>
            <string>KR</string>
            <string>MY</string>
            <string>NZ</string>
            <string>PH</string>
            <string>SG</string>
            <string>TH</string>
            <string>VN</string>
          </choices>
        </hudson.model.ChoiceParameterDefinition>
        <hudson.model.ChoiceParameterDefinition>
          <name>GCS_UDM_MARKET</name>
          <description>Select market for UDM GCS Trades suite.</description>
          <choices>
            <string>GCUK</string>
            <string>GCE</string>
          </choices>
        </hudson.model.ChoiceParameterDefinition>
        <hudson.model.ChoiceParameterDefinition>
          <name>GCS_MARKET</name>
          <description>Select market for GCS Trades and GCS Holdings suites. Only &apos;GCUK&apos; is available.</description>
          <choices>
            <string>GCUK</string>
          </choices>
        </hudson.model.ChoiceParameterDefinition>
        <hudson.model.ChoiceParameterDefinition>
          <name>GCS_PENALTY</name>
          <description>Select market for GCS Penalty suite. Only &apos;GCUK&apos; is available.</description>
          <choices>
            <string>GCUK</string>
          </choices>
        </hudson.model.ChoiceParameterDefinition>
        <hudson.model.ChoiceParameterDefinition>
          <name>GCA_MARKET</name>
          <description>Select market for UDM GCS Trades suite. Only &apos;GCA&apos; is available.</description>
          <choices>
            <string>GCA</string>
          </choices>
        </hudson.model.ChoiceParameterDefinition>
        <hudson.model.StringParameterDefinition>
          <name>DAYS_OFFSET</name>
          <description>How many days back, the the data should be taken from (current date - days_offset = end date). &apos;0&apos; = &apos;today&apos;</description>
          <defaultValue>0</defaultValue>
          <trim>false</trim>
        </hudson.model.StringParameterDefinition>
        <hudson.model.StringParameterDefinition>
          <name>DAYS_PERIOD</name>
          <description>How many days back from the offset, the data should be taken from (current date - days_offset - days_period = start date). Minimum: 1</description>
          <defaultValue>1</defaultValue>
          <trim>false</trim>
        </hudson.model.StringParameterDefinition>
        <hudson.model.StringParameterDefinition>
          <name>LIMIT</name>
          <description>Number of ids to be compared</description>
          <defaultValue>500</defaultValue>
          <trim>false</trim>
        </hudson.model.StringParameterDefinition>
        <hudson.model.BooleanParameterDefinition>
          <name>RERUN</name>
          <description>Re-run failed test cases (useful in case on network/gateway disturbances)</description>
          <defaultValue>true</defaultValue>
        </hudson.model.BooleanParameterDefinition>
        <hudson.model.StringParameterDefinition>
          <name>ARGS</name>
          <description>Additional Robot arguments</description>
          <trim>false</trim>
        </hudson.model.StringParameterDefinition>
      </parameterDefinitions>
    </hudson.model.ParametersDefinitionProperty>
  </properties>
  <definition class="org.jenkinsci.plugins.workflow.cps.CpsScmFlowDefinition" plugin="workflow-cps@3894.3896.vca_2c931e7935">
    <scm class="hudson.plugins.git.GitSCM" plugin="git@5.2.2">
      <configVersion>2</configVersion>
      <userRemoteConfigs>
        <hudson.plugins.git.UserRemoteConfig>
          <url>https://stash.hk.hsbc/scm/unity-i15n-poc/unity-test-automation.git</url>
          <credentialsId>STASH_ACCESS</credentialsId>
        </hudson.plugins.git.UserRemoteConfig>
      </userRemoteConfigs>
      <branches>
        <hudson.plugins.git.BranchSpec>
          <name>UNITYIDN-3912_UDM_GSC_to_Mesh</name>
        </hudson.plugins.git.BranchSpec>
      </branches>
      <doGenerateSubmoduleConfigurations>false</doGenerateSubmoduleConfigurations>
      <gitTool>GIT_Latest_Linux</gitTool>
      <submoduleCfg class="empty-list"/>
      <extensions/>
    </scm>
    <scriptPath>jenkins/Jenkinsfile_DataMesh</scriptPath>
    <lightweight>false</lightweight>
  </definition>
  <triggers/>
  <disabled>false</disabled>
</flow-definition>