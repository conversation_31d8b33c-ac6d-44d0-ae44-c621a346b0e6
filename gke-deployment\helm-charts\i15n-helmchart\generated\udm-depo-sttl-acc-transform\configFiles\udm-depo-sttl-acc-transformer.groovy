

import com.poc.hss.fasttrack.transform.AbstractTransformer
import com.poc.hss.fasttrack.transform.model.TransformerBatchInputContext
import com.poc.hss.fasttrack.transform.model.TransformerRecord
import io.vertx.core.json.JsonObject
import org.slf4j.Logger
import org.slf4j.LoggerFactory

import java.util.stream.Collectors

class SecCptyDepositorySettlementAccountTransformer extends AbstractTransformer {
    JsonObject constructRecord(JsonObject source){
        return new JsonObject()
                .put("site", source.getString("SITE")?.trim())
                .put("BWSDLC", source.getString("BWSDLC")?.trim())
                .put("BWASOA", source.getString("BWASOA")?.trim())
                .put("BWASOS", source.getString("BWASOS")?.trim())
                .put("BWCPYC", source.getString("BWCPYC")?.trim())
                .put("counter_party_id", String.format("%s-%s",nonNull(source.getString("BWSDLC")),nonNull(source.getString("BWCPYC"))))
    }

    static String nonNull(String value){
        if(value == null)
            return ""
        return value.trim()
    }

    private static final Logger logger = LoggerFactory.getLogger(SecCptyDepositorySettlementAccountTransformer.class)

    @Override
    List<TransformerRecord> batchTransform(TransformerBatchInputContext context) {
        List<TransformerRecord> records = context.getRecords()
        return records.stream().map({
            rec ->
                JsonObject source = rec.getData();
                return rec.from(rec).data(constructRecord(source)).build()
        }).collect(Collectors.toList())
    }
}
