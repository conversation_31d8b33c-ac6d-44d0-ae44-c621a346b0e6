#!/bin/bash
export HOME=/root
cd $HOME
echo $PWD
echo  "Check available free space over disks"
sudo parted /dev/sda print free | grep -i " free space " 
#echo -e "\nn\np\n3\nt\n3\n8e\nw" | fdisk /dev/sda
echo -e "\nn\np\n3\n\n\nw" | fdisk /dev/sda
sleep 3s
echo -e "\nt\n3\n8e\n\nw" | fdisk /dev/sda
sleep 2s
partprobe
echo " checking new drive created "
lsblk
sudo pvcreate /dev/sda3
sudo pvs
sudo vgextend vg00 /dev/sda3
lvextend -l +20%FREE /dev/vg00/lv_opt
lvextend -l +80%FREE /dev/vg00/lv_var
sudo xfs_growfs /dev/vg00/lv_var
sudo xfs_growfs /dev/vg00/lv_opt
df -h
