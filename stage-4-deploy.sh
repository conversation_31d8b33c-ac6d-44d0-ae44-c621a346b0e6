#!/bin/bash
set -euo pipefail

# Stage 4: Deploy to GKE
# Usage: ./stage-4-deploy.sh <project-name> [options]

# Function to print usage
usage() {
    echo "Usage: $0 <project-name> [options]"
    echo ""
    echo "Arguments:"
    echo "  project-name    Name of the project to deploy (e.g., change-dashboard, risc)"
    echo ""
    echo "Options:"
    echo "  --environment   Target environment: dev|uat|prod (default: dev)"
    echo "  --namespace     Target namespace (default: auto-detected from environment)"
    echo "  --dry-run       Show what would be deployed without actually deploying"
    echo "  --wait          Wait for deployment to complete"
    echo "  --timeout       Timeout for deployment wait (default: 300s)"
    echo "  --help, -h      Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 change-dashboard"
    echo "  $0 risc --environment uat --wait"
    echo "  $0 change-dashboard --dry-run"
    exit 1
}

# Parse arguments
if [ $# -lt 1 ]; then
    echo "Error: Project name is required"
    usage
fi

PROJECT_NAME="$1"
shift

# Default configuration
ENVIRONMENT="dev"
NAMESPACE=""
DRY_RUN=false
WAIT_FOR_DEPLOYMENT=false
TIMEOUT="300"

# Parse options
while [[ $# -gt 0 ]]; do
    case $1 in
        --environment)
            ENVIRONMENT="$2"
            shift 2
            ;;
        --namespace)
            NAMESPACE="$2"
            shift 2
            ;;
        --dry-run)
            DRY_RUN=true
            shift
            ;;
        --wait)
            WAIT_FOR_DEPLOYMENT=true
            shift
            ;;
        --timeout)
            TIMEOUT="$2"
            shift 2
            ;;
        --help|-h)
            usage
            ;;
        *)
            echo "Error: Unknown option $1"
            usage
            ;;
    esac
done

# Environment-specific configurations
case "${ENVIRONMENT}" in
    "dev")
        NAMESPACE="${NAMESPACE:-ns-i15n-dev}"
        GCP_PROJECT="hsbc-9087302-unity-dev"
        DOMAIN_SUFFIX="dev.gcp.cloud.hk.hsbc"
        ;;
    "uat")
        NAMESPACE="${NAMESPACE:-ns-i15n-uat}"
        GCP_PROJECT="hsbc-9087302-unity-uat"
        DOMAIN_SUFFIX="uat.gcp.cloud.hk.hsbc"
        ;;
    "prod")
        NAMESPACE="${NAMESPACE:-ns-i15n-prod}"
        GCP_PROJECT="hsbc-9087302-unity-prod"
        DOMAIN_SUFFIX="prod.gcp.cloud.hk.hsbc"
        ;;
    *)
        echo "Error: Invalid environment '${ENVIRONMENT}'. Must be dev, uat, or prod"
        exit 1
        ;;
esac

echo "=== Stage 4: Deploy ${PROJECT_NAME} to GKE ==="
echo "Configuration:"
echo "  Project: ${PROJECT_NAME}"
echo "  Environment: ${ENVIRONMENT}"
echo "  Namespace: ${NAMESPACE}"
echo "  GCP Project: ${GCP_PROJECT}"
echo "  Dry Run: ${DRY_RUN}"
echo "  Wait for Deployment: ${WAIT_FOR_DEPLOYMENT}"

# Validate project directory exists
if [ ! -d "${PROJECT_NAME}" ]; then
    echo "Error: Project directory '${PROJECT_NAME}' not found"
    exit 1
fi

# Check if previous stages completed successfully
BUILD_INFO_FILE="${PROJECT_NAME}/target/build-info.properties"
if [ ! -f "${BUILD_INFO_FILE}" ]; then
    echo "Error: Build info file not found: ${BUILD_INFO_FILE}"
    echo "Please run previous stages first"
    exit 1
fi

# Load build info
source "${BUILD_INFO_FILE}"
if [ "${build.status:-}" != "success" ]; then
    echo "Error: Previous build stage failed"
    exit 1
fi

if [ "${image.status:-}" != "success" ]; then
    echo "Error: Previous image build stage failed"
    exit 1
fi

echo "Previous stage info:"
echo "  Build timestamp: ${build.timestamp:-unknown}"
echo "  Image: ${image.gcr:-unknown}"
echo "  Image push status: ${image.push_status:-unknown}"

# Check if gke-deployment directory exists
if [ ! -d "gke-deployment" ]; then
    echo "Error: gke-deployment directory not found"
    echo "Please ensure you're running this script from the repository root"
    exit 1
fi

# Check if Helm values exist
VALUES_FILE="gke-deployment/helm-charts/i15n-helmchart/${PROJECT_NAME}/values.yaml"
if [ ! -f "${VALUES_FILE}" ]; then
    echo "Error: Helm values file not found: ${VALUES_FILE}"
    echo "Please ensure the Helm values are created first"
    exit 1
fi

echo ""
echo "=== Pre-deployment Validation ==="

# Verify required tools
for tool in kubectl helm; do
    if ! command -v ${tool} &> /dev/null; then
        echo "Error: ${tool} not found. Please install ${tool}"
        exit 1
    fi
done

# Verify kubectl connectivity
echo "Checking kubectl connectivity..."
if ! kubectl cluster-info --request-timeout=10s > /dev/null 2>&1; then
    echo "Error: Cannot connect to Kubernetes cluster"
    echo "Please ensure kubectl is configured and you have access to the cluster"
    exit 1
fi
echo "✅ Kubectl connectivity verified"

# Get current context
CURRENT_CONTEXT=$(kubectl config current-context)
echo "Current kubectl context: ${CURRENT_CONTEXT}"

# Verify namespace exists or create it
echo "Checking namespace ${NAMESPACE}..."
if kubectl get namespace "${NAMESPACE}" > /dev/null 2>&1; then
    echo "✅ Namespace ${NAMESPACE} exists"
else
    if [ "${DRY_RUN}" = "true" ]; then
        echo "⚠️  Namespace ${NAMESPACE} does not exist (would be created)"
    else
        echo "⚠️  Namespace ${NAMESPACE} does not exist, creating..."
        kubectl create namespace "${NAMESPACE}"
        echo "✅ Namespace ${NAMESPACE} created"
    fi
fi

# Validate Helm values
echo "Validating Helm values..."
if helm template "${PROJECT_NAME}" -f "${VALUES_FILE}" gke-deployment/helm-charts/i15n-helmchart/templates/default/ > /dev/null 2>&1; then
    echo "✅ Helm values validation passed"
else
    echo "❌ Helm values validation failed"
    echo "Please check the Helm values file: ${VALUES_FILE}"
    exit 1
fi

# Update image tag in values if needed
PROJECT_VERSION="${project.version:-unknown}"
CURRENT_IMAGE_TAG=$(yq e '.image.tag' "${VALUES_FILE}" 2>/dev/null || echo "unknown")
if [ "${CURRENT_IMAGE_TAG}" != "${PROJECT_VERSION}" ]; then
    echo "Updating image tag from ${CURRENT_IMAGE_TAG} to ${PROJECT_VERSION}"
    if [ "${DRY_RUN}" = "false" ]; then
        # Create a backup
        cp "${VALUES_FILE}" "${VALUES_FILE}.backup"
        # Update the tag
        yq e ".image.tag = \"${PROJECT_VERSION}\"" -i "${VALUES_FILE}" 2>/dev/null || {
            echo "Warning: Could not update image tag automatically"
            echo "Please update image.tag in ${VALUES_FILE} to ${PROJECT_VERSION}"
        }
    fi
fi

# Show what will be deployed
echo ""
echo "=== Deployment Preview ==="
echo "Helm Chart: gke-deployment/helm-charts/i15n-helmchart/templates/default/"
echo "Values File: ${VALUES_FILE}"
echo "Release Name: ${PROJECT_NAME}"
echo "Namespace: ${NAMESPACE}"
echo "Image: ${image.gcr:-unknown}"

if [ "${DRY_RUN}" = "true" ]; then
    echo ""
    echo "=== DRY RUN: Showing what would be deployed ==="
    cd gke-deployment
    helm template "${PROJECT_NAME}" -f "helm-charts/i15n-helmchart/${PROJECT_NAME}/values.yaml" helm-charts/i15n-helmchart/templates/default/ --namespace "${NAMESPACE}"
    cd ..
    echo ""
    echo "=== DRY RUN COMPLETED ==="
    echo "To actually deploy, run without --dry-run flag"
    exit 0
fi

# Actual deployment
echo ""
echo "=== Deploying with Helm ==="

# Change to gke-deployment directory
cd gke-deployment

# Make deploy.sh executable
chmod +x ./deploy.sh

# Set deployment parameters
MAX_HISTORY="5"
POST_ACTION="start"
ADDITIONAL_OPTIONS=""

# Run deployment using existing deploy.sh script
echo "Executing: ./deploy.sh ${PROJECT_NAME} ${NAMESPACE} ${MAX_HISTORY} ${POST_ACTION} '${ADDITIONAL_OPTIONS}'"
./deploy.sh "${PROJECT_NAME}" "${NAMESPACE}" "${MAX_HISTORY}" "${POST_ACTION}" "${ADDITIONAL_OPTIONS}"

DEPLOY_EXIT_CODE=$?

if [ ${DEPLOY_EXIT_CODE} -eq 0 ]; then
    echo ""
    echo "=== Deployment Initiated Successfully ==="
    
    # Wait for deployment if requested
    if [ "${WAIT_FOR_DEPLOYMENT}" = "true" ]; then
        echo "Waiting for deployment to complete (timeout: ${TIMEOUT}s)..."
        kubectl rollout status deployment/"${PROJECT_NAME}" -n "${NAMESPACE}" --timeout="${TIMEOUT}s"
        
        if [ $? -eq 0 ]; then
            echo "✅ Deployment completed successfully"
        else
            echo "⚠️  Deployment timeout or failed"
        fi
    fi
    
    echo ""
    echo "=== Post-deployment Status ==="
    
    # Check deployment status
    echo "Deployment status:"
    kubectl get deployment "${PROJECT_NAME}" -n "${NAMESPACE}" 2>/dev/null || echo "  Deployment not found"
    
    # Check pods
    echo ""
    echo "Pod status:"
    kubectl get pods -l app.kubernetes.io/name="${PROJECT_NAME}" -n "${NAMESPACE}" 2>/dev/null || echo "  No pods found"
    
    # Check service
    echo ""
    echo "Service status:"
    kubectl get service "${PROJECT_NAME}" -n "${NAMESPACE}" 2>/dev/null || echo "  Service not found"
    
    # Check ingress
    echo ""
    echo "Ingress status:"
    if kubectl get ingress "${PROJECT_NAME}" -n "${NAMESPACE}" > /dev/null 2>&1; then
        INGRESS_HOST=$(kubectl get ingress "${PROJECT_NAME}" -n "${NAMESPACE}" -o jsonpath='{.spec.rules[0].host}' 2>/dev/null || echo "unknown")
        echo "  Ingress URL: https://${INGRESS_HOST}"
        kubectl get ingress "${PROJECT_NAME}" -n "${NAMESPACE}"
    else
        echo "  No ingress configured"
    fi
    
    # Health check
    echo ""
    echo "=== Health Check ==="
    if kubectl get pods -l app.kubernetes.io/name="${PROJECT_NAME}" -n "${NAMESPACE}" -o jsonpath='{.items[0].status.phase}' 2>/dev/null | grep -q "Running"; then
        echo "✅ Application is running"
        
        # Try to get health endpoint
        POD_NAME=$(kubectl get pods -l app.kubernetes.io/name="${PROJECT_NAME}" -n "${NAMESPACE}" -o jsonpath='{.items[0].metadata.name}' 2>/dev/null)
        if [ -n "${POD_NAME}" ]; then
            echo "Checking health endpoint..."
            kubectl exec "${POD_NAME}" -n "${NAMESPACE}" -- curl -f http://localhost:8080/actuator/health 2>/dev/null || echo "Health check endpoint not accessible"
        fi
    else
        echo "⚠️  Application may not be running properly"
    fi
    
    DEPLOYMENT_STATUS="success"
    
else
    echo ""
    echo "❌ Deployment failed with exit code ${DEPLOY_EXIT_CODE}"
    DEPLOYMENT_STATUS="failed"
fi

# Return to original directory
cd ..

# Update build info
echo ""
echo "=== Updating Build Information ==="

cat >> "${PROJECT_NAME}/target/build-info.properties" <<EOF

# Deployment results
deploy.timestamp=$(date -u +"%Y-%m-%dT%H:%M:%SZ")
deploy.stage=deploy
deploy.status=${DEPLOYMENT_STATUS}
deploy.environment=${ENVIRONMENT}
deploy.namespace=${NAMESPACE}
deploy.gcp_project=${GCP_PROJECT}
deploy.exit_code=${DEPLOY_EXIT_CODE}
deploy.wait=${WAIT_FOR_DEPLOYMENT}
EOF

if [ ${DEPLOY_EXIT_CODE} -eq 0 ]; then
    echo ""
    echo "=== Stage 4 Complete: Deployment ==="
    echo "✅ Deployment successful!"
    echo "Application: ${PROJECT_NAME}"
    echo "Environment: ${ENVIRONMENT}"
    echo "Namespace: ${NAMESPACE}"
    
    # Show access information
    echo ""
    echo "Access Information:"
    echo "  Application URL: https://${PROJECT_NAME}.${GCP_PROJECT}.${DOMAIN_SUFFIX}"
    echo "  Health Check: https://${PROJECT_NAME}.${GCP_PROJECT}.${DOMAIN_SUFFIX}/actuator/health"
    echo "  Metrics: https://${PROJECT_NAME}.${GCP_PROJECT}.${DOMAIN_SUFFIX}/actuator/prometheus"
    
else
    echo ""
    echo "=== Stage 4 Failed: Deployment ==="
    echo "❌ Deployment failed"
    echo ""
    echo "Troubleshooting steps:"
    echo "1. Check the Helm values file: ${VALUES_FILE}"
    echo "2. Verify the Docker image exists and is accessible"
    echo "3. Check namespace permissions and service account"
    echo "4. Review the deployment logs above for specific errors"
    echo "5. Use 'kubectl describe deployment ${PROJECT_NAME} -n ${NAMESPACE}' for more details"
    exit 1
fi
