---
spring:
  application:
    name: "udm-gcs-transaction-udm-gcs-trans-ods-bca"
kafkaConfig:
  auto.offset.reset: "latest"
  bootstrap.servers: "kafka-0.kafka-internal.ns-core-prod-apps.svc.cluster.local:9093,kafka-1.kafka-internal.ns-core-prod-apps.svc.cluster.local:9093,kafka-2.kafka-internal.ns-core-prod-apps.svc.cluster.local:9093"
  group.id: "${KAFKA_CONSUMER_GROUP}"
  schema.registry.url: "https://hkl20077608.hc.cloud.hk.hsbc:8082/"
  security.protocol: "SSL"
  ssl.keystore.location: "/hss/apps/certs/unity-microservices.jks"
  ssl.keystore.password: "${KAFKA_KEYSTORE_PASSWORD}"
  ssl.truststore.location: "/hss/apps/certs/unity-microservices.ts"
  ssl.truststore.password: "${KAFKA_TRUSTSTORE_PASSWORD}"
management:
  endpoints:
    web:
      exposure:
        include: "health,prometheus"
  metrics:
    tags:
      application: "${spring.application.name}"
cache:
  provider: "memory"
consumerAdaptor:
  name: "udm-gcs-trans-ods"
  sourceSchema: "UDM_GCS_TRANSACTION"
  sourceTopic: "unity2-PROD-core-fusion-udm-gcs-transaction-out"
  sourceBatchTopicSuffix: "-batch"
  consumerSourceMode: "MESSAGE"
  consumerTargetDataFormat: "JSON"
  consumerTargetChannel: "KAFKA"
  consumerTargetChannelConnectionConfig:
    kafkaConfig:
      kafkaProperties:
        security.protocol: "SSL"
        schema.registry.url: "https://hkl20077608.hc.cloud.hk.hsbc:8082/"
        ssl.truststore.location: "/hss/apps/certs/unity-microservices.ts"
        ssl.keystore.password: "${KAFKA_KEYSTORE_PASSWORD}"
        group.id: "${KAFKA_CONSUMER_GROUP}"
        ssl.keystore.location: "/hss/apps/certs/unity-microservices.jks"
        bootstrap.servers: "hkl20077608.hc.cloud.hk.hsbc:9094,hkl20077609.hc.cloud.hk.hsbc:9094,hkl20077944.hc.cloud.hk.hsbc:9094,hkl20077945.hc.cloud.hk.hsbc:9094"
        ssl.truststore.password: "${KAFKA_TRUSTSTORE_PASSWORD}"
        auto.offset.reset: "earliest"
      targetTopic: "udm-gcs-transaction-to-ods"
    sftpConfig:
      host: ""
      port: 0
      userName: ""
      password: ""
      privateKey: ""
      remoteDirectory: ""
      fileNamePattern: ""
      generateChecksum: false
    restConfig:
      oauth2Config:
        scopes: ""
        url: ""
        clientId: ""
        clientSecret: ""
        grantType: ""
        username: ""
        password: ""
      targetURL: ""
    mqConfig:
      host: ""
      port: 1414
      channel: ""
      queueManager: ""
      cipherSuite: ""
      appName: ""
      queueName: ""
      keyStore: ""
      keyPassword: ""
    solaceConfig:
      host: ""
      port: 1943
      userName: ""
      password: ""
      queueName: ""
      vpnName: ""
      clientId: ""
      trustStore: ""
      pubSubDomain: false
    symphonyConfig:
      basicAuthConfig:
        username: ""
        password: ""
      targetURL: "https://edist-tools.prd.fx.gbm.cloud.uk.hsbc:12000/symphony-webhook/v3/generic/room/"
    emailConfig:
      to: ""
      cc: ""
      bcc: ""
      subject: ""
      classification: "INTERNAL"
      targetURL: "https://apprunner.hk.hsbc/email-service/api/v1/send-email"
    pubSubConfig:
      targetProject: ""
      targetTopic: ""
      location: ""
      customerManagedEncryptionKey: ""
    xmatterConfig:
      key: ""
      source: "unity2-consumer-adaptor"
      object: "ns-core-prod-apps.udm-gcs-transaction-udm-gcs-trans-ods-bca"
      severity: "INFO"
      targetURL: "https://httpevents.systems.uk.hsbc/api/sendAlert"
  fieldNameMappings:
  - key: "_trace_id"
    value: "_trace_id"
    length: 0
    dataFormat: "TEXT"
  - key: "_id"
    value: "_id"
    length: 0
    dataFormat: "TEXT"
  - key: "created_by"
    value: "created_by"
    length: 0
    dataFormat: "TEXT"
  - key: "created_time_stamp"
    value: "created_time_stamp"
    length: 0
    dataFormat: "TEXT"
  - key: "deleted_by"
    value: "deleted_by"
    length: 0
    dataFormat: "TEXT"
  - key: "deleted_time_stamp"
    value: "deleted_time_stamp"
    length: 0
    dataFormat: "TEXT"
  - key: "updated_by"
    value: "updated_by"
    length: 0
    dataFormat: "TEXT"
  - key: "updated_time_stamp"
    value: "updated_time_stamp"
    length: 0
    dataFormat: "TEXT"
  - key: "_batch_id"
    value: "_batch_id"
    length: 0
    dataFormat: "TEXT"
  - key: "SiteCode"
    value: "SiteCode"
    length: 0
    dataFormat: "TEXT"
  - key: "HSBCTransactionIdentification"
    value: "HSBCTransactionIdentification"
    length: 0
    dataFormat: "TEXT"
  - key: "ClientTransactionIdentification"
    value: "ClientTransactionIdentification"
    length: 0
    dataFormat: "TEXT"
  - key: "LocalCustodianName"
    value: "LocalCustodianName"
    length: 0
    dataFormat: "TEXT"
  - key: "SafekeepingAccountIdentification"
    value: "SafekeepingAccountIdentification"
    length: 0
    dataFormat: "TEXT"
  - key: "SafekeepingAccountName"
    value: "SafekeepingAccountName"
    length: 0
    dataFormat: "TEXT"
  - key: "SourceSystemTransactionType"
    value: "SourceSystemTransactionType"
    length: 0
    dataFormat: "TEXT"
  - key: "SecuritiesMovementType"
    value: "SecuritiesMovementType"
    length: 0
    dataFormat: "TEXT"
  - key: "FinancialInstrumentIdentificationIsin"
    value: "FinancialInstrumentIdentificationIsin"
    length: 0
    dataFormat: "TEXT"
  - key: "FinancialInstrumentIdentificationSedol"
    value: "FinancialInstrumentIdentificationSedol"
    length: 0
    dataFormat: "TEXT"
  - key: "FinancialInstrumentIdentificationCusip"
    value: "FinancialInstrumentIdentificationCusip"
    length: 0
    dataFormat: "TEXT"
  - key: "FinancialInstrumentIdentificationDescription"
    value: "FinancialInstrumentIdentificationDescription"
    length: 0
    dataFormat: "TEXT"
  - key: "QuantityType"
    value: "QuantityType"
    length: 0
    dataFormat: "TEXT"
  - key: "LocationCode"
    value: "LocationCode"
    length: 0
    dataFormat: "TEXT"
  - key: "LocalCustodianCode"
    value: "LocalCustodianCode"
    length: 0
    dataFormat: "TEXT"
  - key: "LocationDescription"
    value: "LocationDescription"
    length: 0
    dataFormat: "TEXT"
  - key: "LocalCustodianDescription"
    value: "LocalCustodianDescription"
    length: 0
    dataFormat: "TEXT"
  - key: "RegistrationCode"
    value: "RegistrationCode"
    length: 0
    dataFormat: "TEXT"
  - key: "RegistrationDescription"
    value: "RegistrationDescription"
    length: 0
    dataFormat: "TEXT"
  - key: "PlaceOfTradeMic"
    value: "PlaceOfTradeMic"
    length: 0
    dataFormat: "TEXT"
  - key: "CounterpartyBic"
    value: "CounterpartyBic"
    length: 0
    dataFormat: "TEXT"
  - key: "CounterpartyName"
    value: "CounterpartyName"
    length: 0
    dataFormat: "TEXT"
  - key: "CounterpartyDataSourceSchemeType"
    value: "CounterpartyDataSourceSchemeType"
    length: 0
    dataFormat: "TEXT"
  - key: "CounterpartyDataSourceSchemeCode"
    value: "CounterpartyDataSourceSchemeCode"
    length: 0
    dataFormat: "TEXT"
  - key: "CounterpartyAccountIdentification"
    value: "CounterpartyAccountIdentification"
    length: 0
    dataFormat: "TEXT"
  - key: "DepositoryIdentification"
    value: "DepositoryIdentification"
    length: 0
    dataFormat: "TEXT"
  - key: "ParentTransactionIdentification"
    value: "ParentTransactionIdentification"
    length: 0
    dataFormat: "TEXT"
  - key: "TradeDate"
    value: "TradeDate"
    length: 0
    dataFormat: "TEXT"
  - key: "SettlementDate"
    value: "SettlementDate"
    length: 0
    dataFormat: "TEXT"
  - key: "EffectiveSettlementDate"
    value: "EffectiveSettlementDate"
    length: 0
    dataFormat: "TEXT"
  - key: "ValueDate"
    value: "ValueDate"
    length: 0
    dataFormat: "TEXT"
  - key: "SettlementAmountCurrency"
    value: "SettlementAmountCurrency"
    length: 0
    dataFormat: "TEXT"
  - key: "SettlementAmount"
    value: "SettlementAmount"
    length: 0
    dataFormat: "TEXT"
  - key: "AccountBaseCurrency"
    value: "AccountBaseCurrency"
    length: 0
    dataFormat: "TEXT"
  - key: "AccountBaseCurrencySettlementAmount"
    value: "AccountBaseCurrencySettlementAmount"
    length: 0
    dataFormat: "TEXT"
  - key: "FxRateToAccountBaseCurrency"
    value: "FxRateToAccountBaseCurrency"
    length: 0
    dataFormat: "TEXT"
  - key: "DealPriceAmountCurrency"
    value: "DealPriceAmountCurrency"
    length: 0
    dataFormat: "TEXT"
  - key: "DealPriceAmount"
    value: "DealPriceAmount"
    length: 0
    dataFormat: "TEXT"
  - key: "CashAccountIdentification"
    value: "CashAccountIdentification"
    length: 0
    dataFormat: "TEXT"
  - key: "AccruedInterestAmountCurrency"
    value: "AccruedInterestAmountCurrency"
    length: 0
    dataFormat: "TEXT"
  - key: "AccruedInterestAmount"
    value: "AccruedInterestAmount"
    length: 0
    dataFormat: "TEXT"
  - key: "SecuritiesTransactionTypeCode"
    value: "SecuritiesTransactionTypeCode"
    length: 0
    dataFormat: "TEXT"
  - key: "SecuritiesTransactionTypeDescription"
    value: "SecuritiesTransactionTypeDescription"
    length: 0
    dataFormat: "TEXT"
  - key: "ExternalTradeStatus"
    value: "ExternalTradeStatus"
    length: 0
    dataFormat: "TEXT"
  - key: "SettlementQuantity"
    value: "SettlementQuantity"
    length: 0
    dataFormat: "TEXT"
  - key: "OriginalSettlementQuantity"
    value: "OriginalSettlementQuantity"
    length: 0
    dataFormat: "TEXT"
  - key: "PlaceOfSettlementBic"
    value: "PlaceOfSettlementBic"
    length: 0
    dataFormat: "TEXT"
  - key: "PlaceOfSettlementDescription"
    value: "PlaceOfSettlementDescription"
    length: 0
    dataFormat: "TEXT"
  - key: "ReceiversCustodianBic"
    value: "ReceiversCustodianBic"
    length: 0
    dataFormat: "TEXT"
  - key: "ReceiversCustodianName"
    value: "ReceiversCustodianName"
    length: 0
    dataFormat: "TEXT"
  - key: "ReceiversCustodianDataSourceSchemeType"
    value: "ReceiversCustodianDataSourceSchemeType"
    length: 0
    dataFormat: "TEXT"
  - key: "ReceiversCustodianDataSourceSchemeCode"
    value: "ReceiversCustodianDataSourceSchemeCode"
    length: 0
    dataFormat: "TEXT"
  - key: "ReceiversCustodianAccountIdentification"
    value: "ReceiversCustodianAccountIdentification"
    length: 0
    dataFormat: "TEXT"
  - key: "DeliverersCustodianBic"
    value: "DeliverersCustodianBic"
    length: 0
    dataFormat: "TEXT"
  - key: "DeliverersCustodianName"
    value: "DeliverersCustodianName"
    length: 0
    dataFormat: "TEXT"
  - key: "DeliverersCustodianDataSourceSchemeType"
    value: "DeliverersCustodianDataSourceSchemeType"
    length: 0
    dataFormat: "TEXT"
  - key: "DeliverersCustodianDataSourceSchemeCode"
    value: "DeliverersCustodianDataSourceSchemeCode"
    length: 0
    dataFormat: "TEXT"
  - key: "DeliverersCustodianAccountIdentification"
    value: "DeliverersCustodianAccountIdentification"
    length: 0
    dataFormat: "TEXT"
  - key: "BuyerBic"
    value: "BuyerBic"
    length: 0
    dataFormat: "TEXT"
  - key: "BuyerName"
    value: "BuyerName"
    length: 0
    dataFormat: "TEXT"
  - key: "BuyerDataSourceSchemeType"
    value: "BuyerDataSourceSchemeType"
    length: 0
    dataFormat: "TEXT"
  - key: "BuyerDataSourceSchemeCode"
    value: "BuyerDataSourceSchemeCode"
    length: 0
    dataFormat: "TEXT"
  - key: "BuyerAccountIdentification"
    value: "BuyerAccountIdentification"
    length: 0
    dataFormat: "TEXT"
  - key: "SellerBic"
    value: "SellerBic"
    length: 0
    dataFormat: "TEXT"
  - key: "SellerName"
    value: "SellerName"
    length: 0
    dataFormat: "TEXT"
  - key: "SellerDataSourceSchemeType"
    value: "SellerDataSourceSchemeType"
    length: 0
    dataFormat: "TEXT"
  - key: "SellerDataSourceSchemeCode"
    value: "SellerDataSourceSchemeCode"
    length: 0
    dataFormat: "TEXT"
  - key: "SellerAccountIdentification"
    value: "SellerAccountIdentification"
    length: 0
    dataFormat: "TEXT"
  - key: "SettlementInstructionProcessingAdditionalDetails"
    value: "SettlementInstructionProcessingAdditionalDetails"
    length: 0
    dataFormat: "TEXT"
  - key: "SettlementConfirmationAdditionalDetails"
    value: "SettlementConfirmationAdditionalDetails"
    length: 0
    dataFormat: "TEXT"
  - key: "AdditionalMatchingReasonInformation"
    value: "AdditionalMatchingReasonInformation"
    length: 0
    dataFormat: "TEXT"
  - key: "RepurchaseTransactionIdentification"
    value: "RepurchaseTransactionIdentification"
    length: 0
    dataFormat: "TEXT"
  - key: "TerminationTransactionAmountCurrency"
    value: "TerminationTransactionAmountCurrency"
    length: 0
    dataFormat: "TEXT"
  - key: "TerminationTransactionAmount"
    value: "TerminationTransactionAmount"
    length: 0
    dataFormat: "TEXT"
  - key: "RateTypeCode"
    value: "RateTypeCode"
    length: 0
    dataFormat: "TEXT"
  - key: "RepurchaseRate"
    value: "RepurchaseRate"
    length: 0
    dataFormat: "TEXT"
  - key: "InterestComputationMethodCode"
    value: "InterestComputationMethodCode"
    length: 0
    dataFormat: "TEXT"
  - key: "TerminationDate"
    value: "TerminationDate"
    length: 0
    dataFormat: "TEXT"
  - key: "SwiftStatus1Code"
    value: "SwiftStatus1Code"
    length: 0
    dataFormat: "TEXT"
  - key: "SwiftStatus1CodeDescription"
    value: "SwiftStatus1CodeDescription"
    length: 0
    dataFormat: "TEXT"
  - key: "SwiftStatus1CodeDetailDescription"
    value: "SwiftStatus1CodeDetailDescription"
    length: 0
    dataFormat: "TEXT"
  - key: "SwiftStatus1Reason1Code"
    value: "SwiftStatus1Reason1Code"
    length: 0
    dataFormat: "TEXT"
  - key: "SwiftStatus1Reason1CodeDescription"
    value: "SwiftStatus1Reason1CodeDescription"
    length: 0
    dataFormat: "TEXT"
  - key: "SwiftStatus1Reason1CodeDetailDescription"
    value: "SwiftStatus1Reason1CodeDetailDescription"
    length: 0
    dataFormat: "TEXT"
  - key: "SwiftStatus1Reason2Code"
    value: "SwiftStatus1Reason2Code"
    length: 0
    dataFormat: "TEXT"
  - key: "SwiftStatus1Reason2CodeDescription"
    value: "SwiftStatus1Reason2CodeDescription"
    length: 0
    dataFormat: "TEXT"
  - key: "SwiftStatus1Reason2CodeDetailDescription"
    value: "SwiftStatus1Reason2CodeDetailDescription"
    length: 0
    dataFormat: "TEXT"
  - key: "SwiftStatus2Code"
    value: "SwiftStatus2Code"
    length: 0
    dataFormat: "TEXT"
  - key: "SwiftStatus2CodeDescription"
    value: "SwiftStatus2CodeDescription"
    length: 0
    dataFormat: "TEXT"
  - key: "SwiftStatus2CodeDetailDescription"
    value: "SwiftStatus2CodeDetailDescription"
    length: 0
    dataFormat: "TEXT"
  - key: "SwiftStatus2Reason1Code"
    value: "SwiftStatus2Reason1Code"
    length: 0
    dataFormat: "TEXT"
  - key: "SwiftStatus2Reason1CodeDescription"
    value: "SwiftStatus2Reason1CodeDescription"
    length: 0
    dataFormat: "TEXT"
  - key: "SwiftStatus2Reason1CodeDetailDescription"
    value: "SwiftStatus2Reason1CodeDetailDescription"
    length: 0
    dataFormat: "TEXT"
  - key: "SwiftStatus2Reason2Code"
    value: "SwiftStatus2Reason2Code"
    length: 0
    dataFormat: "TEXT"
  - key: "SwiftStatus2Reason2CodeDescription"
    value: "SwiftStatus2Reason2CodeDescription"
    length: 0
    dataFormat: "TEXT"
  - key: "SwiftStatus2Reason2CodeDetailDescription"
    value: "SwiftStatus2Reason2CodeDetailDescription"
    length: 0
    dataFormat: "TEXT"
  - key: "LocalBrokerCommissionAmountCurrency"
    value: "LocalBrokerCommissionAmountCurrency"
    length: 0
    dataFormat: "TEXT"
  - key: "LocalBrokerCommissionAmount"
    value: "LocalBrokerCommissionAmount"
    length: 0
    dataFormat: "TEXT"
  - key: "RegistrarIdentification"
    value: "RegistrarIdentification"
    length: 0
    dataFormat: "TEXT"
  - key: "RegistrarDescription"
    value: "RegistrarDescription"
    length: 0
    dataFormat: "TEXT"
  - key: "SpecialCumExDividendIndicator"
    value: "SpecialCumExDividendIndicator"
    length: 0
    dataFormat: "TEXT"
  - key: "SystemTransactionIdentification"
    value: "SystemTransactionIdentification"
    length: 0
    dataFormat: "TEXT"
  - key: "CountryTerritoryLocationCode"
    value: "CountryTerritoryLocationCode"
    length: 0
    dataFormat: "TEXT"
  - key: "GroupMemberIdentification"
    value: "GroupMemberIdentification"
    length: 0
    dataFormat: "TEXT"
  - key: "Payment"
    value: "Payment"
    length: 0
    dataFormat: "TEXT"
  - key: "FinancialInstrumentIdentificationPrimaryIdentifier"
    value: "FinancialInstrumentIdentificationPrimaryIdentifier"
    length: 0
    dataFormat: "TEXT"
  - key: "SourceOfInstructionType"
    value: "SourceOfInstructionType"
    length: 0
    dataFormat: "TEXT"
  - key: "CounterpartyIdentification"
    value: "CounterpartyIdentification"
    length: 0
    dataFormat: "TEXT"
  - key: "LinkedTransactionIdentification"
    value: "LinkedTransactionIdentification"
    length: 0
    dataFormat: "TEXT"
  - key: "LocalCustodianIdentification"
    value: "LocalCustodianIdentification"
    length: 0
    dataFormat: "TEXT"
  - key: "ExchangeIdentification"
    value: "ExchangeIdentification"
    length: 0
    dataFormat: "TEXT"
  - key: "NoChangeOfBeneficialOwnershipIndicator"
    value: "NoChangeOfBeneficialOwnershipIndicator"
    length: 0
    dataFormat: "TEXT"
  - key: "AssociatedAccountIdentification"
    value: "AssociatedAccountIdentification"
    length: 0
    dataFormat: "TEXT"
  - key: "AssociatedSubAccountIdentification"
    value: "AssociatedSubAccountIdentification"
    length: 0
    dataFormat: "TEXT"
  - key: "InstructionDateTime"
    value: "InstructionDateTime"
    length: 0
    dataFormat: "TEXT"
  - key: "TradeAmount"
    value: "TradeAmount"
    length: 0
    dataFormat: "TEXT"
  - key: "SourceSystemTradeState"
    value: "SourceSystemTradeState"
    length: 0
    dataFormat: "TEXT"
  - key: "SourceSystemTradeStatus"
    value: "SourceSystemTradeStatus"
    length: 0
    dataFormat: "TEXT"
  - key: "CaptureUserIdentification"
    value: "CaptureUserIdentification"
    length: 0
    dataFormat: "TEXT"
  - key: "UnexecutionReasonCode"
    value: "UnexecutionReasonCode"
    length: 0
    dataFormat: "TEXT"
  - key: "AdditionalUnexecutionReasonInformation"
    value: "AdditionalUnexecutionReasonInformation"
    length: 0
    dataFormat: "TEXT"
  - key: "CancellationReasonCode"
    value: "CancellationReasonCode"
    length: 0
    dataFormat: "TEXT"
  - key: "AdditionalCancellationReasonInformation"
    value: "AdditionalCancellationReasonInformation"
    length: 0
    dataFormat: "TEXT"
  - key: "TradeDescription"
    value: "TradeDescription"
    length: 0
    dataFormat: "TEXT"
  - key: "SourceSystemLastUpdatedDateTime"
    value: "SourceSystemLastUpdatedDateTime"
    length: 0
    dataFormat: "TEXT"
  - key: "FxInstructionStatus"
    value: "FxInstructionStatus"
    length: 0
    dataFormat: "TEXT"
  - key: "StampDutyTaxBasisIdentification"
    value: "StampDutyTaxBasisIdentification"
    length: 0
    dataFormat: "TEXT"
  - key: "StampDutyAmountCurrency"
    value: "StampDutyAmountCurrency"
    length: 0
    dataFormat: "TEXT"
  - key: "StampDutyAmount"
    value: "StampDutyAmount"
    length: 0
    dataFormat: "TEXT"
  - key: "PendingCancellationIndicator"
    value: "PendingCancellationIndicator"
    length: 0
    dataFormat: "TEXT"
  - key: "CancellationOriginatorType"
    value: "CancellationOriginatorType"
    length: 0
    dataFormat: "TEXT"
  - key: "GenerateDepositoryInstructionIndicator"
    value: "GenerateDepositoryInstructionIndicator"
    length: 0
    dataFormat: "TEXT"
  - key: "SecuritiesSettlementType"
    value: "SecuritiesSettlementType"
    length: 0
    dataFormat: "TEXT"
  - key: "TradeMaintenanceStatus"
    value: "TradeMaintenanceStatus"
    length: 0
    dataFormat: "TEXT"
  - key: "TradeMatchingStatus"
    value: "TradeMatchingStatus"
    length: 0
    dataFormat: "TEXT"
  - key: "TradeStockSettlementStatus"
    value: "TradeStockSettlementStatus"
    length: 0
    dataFormat: "TEXT"
  - key: "TradeCashSettlementStatus"
    value: "TradeCashSettlementStatus"
    length: 0
    dataFormat: "TEXT"
  - key: "TradeHoldStatus"
    value: "TradeHoldStatus"
    length: 0
    dataFormat: "TEXT"
  - key: "InternalAccountTransferAccountIdentification"
    value: "InternalAccountTransferAccountIdentification"
    length: 0
    dataFormat: "TEXT"
  - key: "TradeIdentification"
    value: "TradeIdentification"
    length: 0
    dataFormat: "TEXT"
  - key: "MarketInfrastructureTransactionIdentification"
    value: "MarketInfrastructureTransactionIdentification"
    length: 0
    dataFormat: "TEXT"
  - key: "CrossBusinessTransactionIdentification"
    value: "CrossBusinessTransactionIdentification"
    length: 0
    dataFormat: "TEXT"
  - key: "InstructionIdentification"
    value: "InstructionIdentification"
    length: 0
    dataFormat: "TEXT"
  - key: "InstructionDateTimeUtc"
    value: "InstructionDateTimeUtc"
    length: 0
    dataFormat: "TEXT"
  - key: "InstructionSenderBic"
    value: "InstructionSenderBic"
    length: 0
    dataFormat: "TEXT"
  - key: "StraightThroughProcessedIndicator"
    value: "StraightThroughProcessedIndicator"
    length: 0
    dataFormat: "TEXT"
  - key: "ClientGroupIdentification"
    value: "ClientGroupIdentification"
    length: 0
    dataFormat: "TEXT"
  - key: "ClientEntityIdentification"
    value: "ClientEntityIdentification"
    length: 0
    dataFormat: "TEXT"
  - key: "CustomerIdentification"
    value: "CustomerIdentification"
    length: 0
    dataFormat: "TEXT"
  - key: "CashAccountName"
    value: "CashAccountName"
    length: 0
    dataFormat: "TEXT"
  - key: "ChargeAccountIdentification"
    value: "ChargeAccountIdentification"
    length: 0
    dataFormat: "TEXT"
  - key: "ChargeAccountName"
    value: "ChargeAccountName"
    length: 0
    dataFormat: "TEXT"
  - key: "TaxIdentification"
    value: "TaxIdentification"
    length: 0
    dataFormat: "TEXT"
  - key: "SettlementTransactionConditionCode"
    value: "SettlementTransactionConditionCode"
    length: 0
    dataFormat: "TEXT"
  - key: "SettlementTransactionCondition2Code"
    value: "SettlementTransactionCondition2Code"
    length: 0
    dataFormat: "TEXT"
  - key: "LinkedIndicator"
    value: "LinkedIndicator"
    length: 0
    dataFormat: "TEXT"
  - key: "BlockTradeIndicator"
    value: "BlockTradeIndicator"
    length: 0
    dataFormat: "TEXT"
  - key: "NettingIndicator"
    value: "NettingIndicator"
    length: 0
    dataFormat: "TEXT"
  - key: "SecuritiesLendingIndicator"
    value: "SecuritiesLendingIndicator"
    length: 0
    dataFormat: "TEXT"
  - key: "SecuritiesBorrowingIndicator"
    value: "SecuritiesBorrowingIndicator"
    length: 0
    dataFormat: "TEXT"
  - key: "SplitTradeIndicator"
    value: "SplitTradeIndicator"
    length: 0
    dataFormat: "TEXT"
  - key: "SameDaySettlementIndicator"
    value: "SameDaySettlementIndicator"
    length: 0
    dataFormat: "TEXT"
  - key: "HKSettlementInstructionPaymentIndicator"
    value: "HKSettlementInstructionPaymentIndicator"
    length: 0
    dataFormat: "TEXT"
  - key: "SecuritiesRtgsIndicator"
    value: "SecuritiesRtgsIndicator"
    length: 0
    dataFormat: "TEXT"
  - key: "PartialSettlementAllowedIndicator"
    value: "PartialSettlementAllowedIndicator"
    length: 0
    dataFormat: "TEXT"
  - key: "CashPaymentMethod"
    value: "CashPaymentMethod"
    length: 0
    dataFormat: "TEXT"
  - key: "ContractualValueDate"
    value: "ContractualValueDate"
    length: 0
    dataFormat: "TEXT"
  - key: "AccruedInterestDays"
    value: "AccruedInterestDays"
    length: 0
    dataFormat: "TEXT"
  - key: "InternalAccountTransferIndicator"
    value: "InternalAccountTransferIndicator"
    length: 0
    dataFormat: "TEXT"
  - key: "InternalAccountTransferType"
    value: "InternalAccountTransferType"
    length: 0
    dataFormat: "TEXT"
  - key: "HKSettlementInstructionPurposeCode"
    value: "HKSettlementInstructionPurposeCode"
    length: 0
    dataFormat: "TEXT"
  - key: "FinancialInstrumentIdentificationLocalSecurityCode"
    value: "FinancialInstrumentIdentificationLocalSecurityCode"
    length: 0
    dataFormat: "TEXT"
  - key: "FinancialInstrumentIdentificationProductType"
    value: "FinancialInstrumentIdentificationProductType"
    length: 0
    dataFormat: "TEXT"
  - key: "ExpectedSettlementDate"
    value: "ExpectedSettlementDate"
    length: 0
    dataFormat: "TEXT"
  - key: "LocalCustodianEffectiveSettlementDate"
    value: "LocalCustodianEffectiveSettlementDate"
    length: 0
    dataFormat: "TEXT"
  - key: "CsdrTradeReleaseDateTime"
    value: "CsdrTradeReleaseDateTime"
    length: 0
    dataFormat: "TEXT"
  - key: "CsdrTradeReleaseDateTimeUtc"
    value: "CsdrTradeReleaseDateTimeUtc"
    length: 0
    dataFormat: "TEXT"
  - key: "CsdrClientInstructedPartialSettlementQuantity"
    value: "CsdrClientInstructedPartialSettlementQuantity"
    length: 0
    dataFormat: "TEXT"
  - key: "CsdrReleasedPartialSettlementQuantity"
    value: "CsdrReleasedPartialSettlementQuantity"
    length: 0
    dataFormat: "TEXT"
  - key: "CsdrRejectedPartialSettlementQuantity"
    value: "CsdrRejectedPartialSettlementQuantity"
    length: 0
    dataFormat: "TEXT"
  - key: "CsdrRejectedPartialSettlementDate"
    value: "CsdrRejectedPartialSettlementDate"
    length: 0
    dataFormat: "TEXT"
  - key: "SettledAmount"
    value: "SettledAmount"
    length: 0
    dataFormat: "TEXT"
  - key: "TransactionReferenceAmountCurrency"
    value: "TransactionReferenceAmountCurrency"
    length: 0
    dataFormat: "TEXT"
  - key: "TransactionReferenceAmount"
    value: "TransactionReferenceAmount"
    length: 0
    dataFormat: "TEXT"
  - key: "FxOrderCancellationIndicator"
    value: "FxOrderCancellationIndicator"
    length: 0
    dataFormat: "TEXT"
  - key: "RepurchaseTypeCode"
    value: "RepurchaseTypeCode"
    length: 0
    dataFormat: "TEXT"
  - key: "ChargeCode"
    value: "ChargeCode"
    length: 0
    dataFormat: "TEXT"
  - key: "ChargeAmountCurrency"
    value: "ChargeAmountCurrency"
    length: 0
    dataFormat: "TEXT"
  - key: "ChargeAmount"
    value: "ChargeAmount"
    length: 0
    dataFormat: "TEXT"
  - key: "CounterpartyCashAccountIdentification"
    value: "CounterpartyCashAccountIdentification"
    length: 0
    dataFormat: "TEXT"
  - key: "CounterpartyCashAccountName"
    value: "CounterpartyCashAccountName"
    length: 0
    dataFormat: "TEXT"
  - key: "CounterpartyAccountDataSourceSchemeCode"
    value: "CounterpartyAccountDataSourceSchemeCode"
    length: 0
    dataFormat: "TEXT"
  - key: "CounterpartyAccountType"
    value: "CounterpartyAccountType"
    length: 0
    dataFormat: "TEXT"
  - key: "CounterpartyAssociatedAccountIdentification"
    value: "CounterpartyAssociatedAccountIdentification"
    length: 0
    dataFormat: "TEXT"
  - key: "ReceiversCustodianFormatOption"
    value: "ReceiversCustodianFormatOption"
    length: 0
    dataFormat: "TEXT"
  - key: "DeliverersCustodianFormatOption"
    value: "DeliverersCustodianFormatOption"
    length: 0
    dataFormat: "TEXT"
  - key: "BuyerFormatOption"
    value: "BuyerFormatOption"
    length: 0
    dataFormat: "TEXT"
  - key: "SellerFormatOption"
    value: "SellerFormatOption"
    length: 0
    dataFormat: "TEXT"
  - key: "InternalAccountTransferTransactionIdentification"
    value: "InternalAccountTransferTransactionIdentification"
    length: 0
    dataFormat: "TEXT"
  - key: "InternalAccountTransferAccountName"
    value: "InternalAccountTransferAccountName"
    length: 0
    dataFormat: "TEXT"
  - key: "STASOA"
    value: "STASOA"
    length: 0
    dataFormat: "TEXT"
  - key: "STASOS"
    value: "STASOS"
    length: 0
    dataFormat: "TEXT"
  - key: "PoolIdentification"
    value: "PoolIdentification"
    length: 0
    dataFormat: "TEXT"
  - key: "TotalOfLinkedInstructions"
    value: "TotalOfLinkedInstructions"
    length: 0
    dataFormat: "TEXT"
  - key: "NettingTransactionIdentification"
    value: "NettingTransactionIdentification"
    length: 0
    dataFormat: "TEXT"
  - key: "RepurchaseLegType"
    value: "RepurchaseLegType"
    length: 0
    dataFormat: "TEXT"
  - key: "ClientInstructionReceivedIndicator"
    value: "ClientInstructionReceivedIndicator"
    length: 0
    dataFormat: "TEXT"
  - key: "HoldMatchedSettlementInstructionIndicator"
    value: "HoldMatchedSettlementInstructionIndicator"
    length: 0
    dataFormat: "TEXT"
  - key: "CashHoldStatus"
    value: "CashHoldStatus"
    length: 0
    dataFormat: "TEXT"
  - key: "ScripHoldStatus"
    value: "ScripHoldStatus"
    length: 0
    dataFormat: "TEXT"
  - key: "SettlementInstructionCancellationStatus"
    value: "SettlementInstructionCancellationStatus"
    length: 0
    dataFormat: "TEXT"
  - key: "AccountingAndValuationMessageStatus"
    value: "AccountingAndValuationMessageStatus"
    length: 0
    dataFormat: "TEXT"
  - key: "AutoAmendmentStatus"
    value: "AutoAmendmentStatus"
    length: 0
    dataFormat: "TEXT"
  - key: "CsdrTradeHoldReleaseStatus"
    value: "CsdrTradeHoldReleaseStatus"
    length: 0
    dataFormat: "TEXT"
  - key: "SourceSystemPreviousStatus"
    value: "SourceSystemPreviousStatus"
    length: 0
    dataFormat: "TEXT"
  - key: "SourceSystemStatus1Reason1Code"
    value: "SourceSystemStatus1Reason1Code"
    length: 0
    dataFormat: "TEXT"
  - key: "AdditionalStatus1Reason1Information"
    value: "AdditionalStatus1Reason1Information"
    length: 0
    dataFormat: "TEXT"
  - key: "SourceSystemStatus1Reason2Code"
    value: "SourceSystemStatus1Reason2Code"
    length: 0
    dataFormat: "TEXT"
  - key: "SourceSystemStatus2Reason1Code"
    value: "SourceSystemStatus2Reason1Code"
    length: 0
    dataFormat: "TEXT"
  - key: "SourceSystemStatus2Reason2Code"
    value: "SourceSystemStatus2Reason2Code"
    length: 0
    dataFormat: "TEXT"
  - key: "AdditionalStatus2Reason2Information"
    value: "AdditionalStatus2Reason2Information"
    length: 0
    dataFormat: "TEXT"
  - key: "SecuritiesAccountStandingInstructionDetails"
    value: "SecuritiesAccountStandingInstructionDetails"
    length: 0
    dataFormat: "TEXT"
  - key: "MaintenanceFunctionType"
    value: "MaintenanceFunctionType"
    length: 0
    dataFormat: "TEXT"
  - key: "CancellationDate"
    value: "CancellationDate"
    length: 0
    dataFormat: "TEXT"
  - key: "SourceSystemLastUpdatedDateTimeUtc"
    value: "SourceSystemLastUpdatedDateTimeUtc"
    length: 0
    dataFormat: "TEXT"
  - key: "AdditionalStatus1Reason2Information"
    value: "AdditionalStatus1Reason2Information"
    length: 0
    dataFormat: "TEXT"
  - key: "AdditionalStatus2Reason1Information"
    value: "AdditionalStatus2Reason1Information"
    length: 0
    dataFormat: "TEXT"
  - key: "PlaceOfTradeTypeCode"
    value: "PlaceOfTradeTypeCode"
    length: 0
    dataFormat: "TEXT"
  - key: "sec_acc_id"
    value: "sec_acc_id"
    length: 0
    dataFormat: "TEXT"
  - key: "ReasonCodeDescriptionOrInformation"
    value: "ReasonCodeDescriptionOrInformation"
    length: 0
    dataFormat: "TEXT"
  - key: "HbfrAccIndicator"
    value: "HbfrAccIndicator"
    length: 0
    dataFormat: "TEXT"
  - key: "InvestmentAmountCurrency"
    value: "InvestmentAmountCurrency"
    length: 0
    dataFormat: "TEXT"
  - key: "InvestmentAmount"
    value: "InvestmentAmount"
    length: 0
    dataFormat: "TEXT"
  - key: "RepurchaseOppositeLegTransactionIdentification"
    value: "RepurchaseOppositeLegTransactionIdentification"
    length: 0
    dataFormat: "TEXT"
  - key: "LateTradeIndicator"
    value: "LateTradeIndicator"
    length: 0
    dataFormat: "TEXT"
  - key: "DkTradeIndicator"
    value: "DkTradeIndicator"
    length: 0
    dataFormat: "TEXT"
  - key: "SuppressNostroMessageIndicator"
    value: "SuppressNostroMessageIndicator"
    length: 0
    dataFormat: "TEXT"
  - key: "CurrentLocationCode"
    value: "CurrentLocationCode"
    length: 0
    dataFormat: "TEXT"
  - key: "SourceLocationCode"
    value: "SourceLocationCode"
    length: 0
    dataFormat: "TEXT"
  - key: "FinalLocationCode"
    value: "FinalLocationCode"
    length: 0
    dataFormat: "TEXT"
  - key: "FailedTradeIndicator"
    value: "FailedTradeIndicator"
    length: 0
    dataFormat: "TEXT"
  - key: "FxDealDate"
    value: "FxDealDate"
    length: 0
    dataFormat: "TEXT"
  - key: "AccountHolderNationalityCode"
    value: "AccountHolderNationalityCode"
    length: 0
    dataFormat: "TEXT"
  - key: "CrestSettlementPriorityCode"
    value: "CrestSettlementPriorityCode"
    length: 0
    dataFormat: "TEXT"
  - key: "DealMatchingPriceAmountCurrency"
    value: "DealMatchingPriceAmountCurrency"
    length: 0
    dataFormat: "TEXT"
  - key: "DealMatchingPriceAmount"
    value: "DealMatchingPriceAmount"
    length: 0
    dataFormat: "TEXT"
  - key: "SettlementAdviceIndicator"
    value: "SettlementAdviceIndicator"
    length: 0
    dataFormat: "TEXT"
  - key: "CrestStampStatusCode"
    value: "CrestStampStatusCode"
    length: 0
    dataFormat: "TEXT"
  - key: "CrestOriginatingSystemCode"
    value: "CrestOriginatingSystemCode"
    length: 0
    dataFormat: "TEXT"
  - key: "CfetsIdentification"
    value: "CfetsIdentification"
    length: 0
    dataFormat: "TEXT"
  - key: "FeeWaiverIndicator"
    value: "FeeWaiverIndicator"
    length: 0
    dataFormat: "TEXT"
  - key: "FaxIdentification"
    value: "FaxIdentification"
    length: 0
    dataFormat: "TEXT"
  - key: "ContractualSettlementIndicator"
    value: "ContractualSettlementIndicator"
    length: 0
    dataFormat: "TEXT"
  - key: "ClientCancellationIdentification"
    value: "ClientCancellationIdentification"
    length: 0
    dataFormat: "TEXT"
  - key: "PhysicallySettledIndicator"
    value: "PhysicallySettledIndicator"
    length: 0
    dataFormat: "TEXT"
  - key: "FedwireSplitIndicator"
    value: "FedwireSplitIndicator"
    length: 0
    dataFormat: "TEXT"
  - key: "FxAgentIdentification"
    value: "FxAgentIdentification"
    length: 0
    dataFormat: "TEXT"
  - key: "FxLocationCode"
    value: "FxLocationCode"
    length: 0
    dataFormat: "TEXT"
  - key: "FxReversalIdentification"
    value: "FxReversalIdentification"
    length: 0
    dataFormat: "TEXT"
  - key: "FxReversalDealDate"
    value: "FxReversalDealDate"
    length: 0
    dataFormat: "TEXT"
  - key: "FxReversalAccountBaseCurrencySettlementAmount"
    value: "FxReversalAccountBaseCurrencySettlementAmount"
    length: 0
    dataFormat: "TEXT"
  - key: "FxReversalStatus"
    value: "FxReversalStatus"
    length: 0
    dataFormat: "TEXT"
  - key: "FxReversalValueDate"
    value: "FxReversalValueDate"
    length: 0
    dataFormat: "TEXT"
  - key: "FxReversalAgentIdentification"
    value: "FxReversalAgentIdentification"
    length: 0
    dataFormat: "TEXT"
  - key: "FxStandingInstructionIndicator"
    value: "FxStandingInstructionIndicator"
    length: 0
    dataFormat: "TEXT"
  - key: "FxCancellationIndicator"
    value: "FxCancellationIndicator"
    length: 0
    dataFormat: "TEXT"
  - key: "FxAdviceIndicator"
    value: "FxAdviceIndicator"
    length: 0
    dataFormat: "TEXT"
  - key: "FxFundingMessageIndicator"
    value: "FxFundingMessageIndicator"
    length: 0
    dataFormat: "TEXT"
  - key: "FxReversalRate"
    value: "FxReversalRate"
    length: 0
    dataFormat: "TEXT"
  - key: "DbvCreditPartyType"
    value: "DbvCreditPartyType"
    length: 0
    dataFormat: "TEXT"
  - key: "DbvMarginPercent"
    value: "DbvMarginPercent"
    length: 0
    dataFormat: "TEXT"
  - key: "CrestStockDepositIdentification"
    value: "CrestStockDepositIdentification"
    length: 0
    dataFormat: "TEXT"
  - key: "CrestPhysicalSharesHolderNationalityCode"
    value: "CrestPhysicalSharesHolderNationalityCode"
    length: 0
    dataFormat: "TEXT"
  - key: "CrestDeliveredQuantity"
    value: "CrestDeliveredQuantity"
    length: 0
    dataFormat: "TEXT"
  - key: "CrestBalanceCertificateQuantity"
    value: "CrestBalanceCertificateQuantity"
    length: 0
    dataFormat: "TEXT"
  - key: "PreVerificationIndicator"
    value: "PreVerificationIndicator"
    length: 0
    dataFormat: "TEXT"
  - key: "RepairReasonInformation"
    value: "RepairReasonInformation"
    length: 0
    dataFormat: "TEXT"
  - key: "CounterpartyType"
    value: "CounterpartyType"
    length: 0
    dataFormat: "TEXT"
  - key: "ClientServiceLocationCode"
    value: "ClientServiceLocationCode"
    length: 0
    dataFormat: "TEXT"
  - key: "OwningBusinessName"
    value: "OwningBusinessName"
    length: 0
    dataFormat: "TEXT"
