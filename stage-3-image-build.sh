#!/bin/bash
set -euo pipefail

# Stage 3: Docker Image Build
# Usage: ./stage-3-image-build.sh <project-name> [options]

# Function to print usage
usage() {
    echo "Usage: $0 <project-name> [options]"
    echo ""
    echo "Arguments:"
    echo "  project-name    Name of the project directory (e.g., change-dashboard, risc)"
    echo ""
    echo "Options:"
    echo "  --tag TAG       Additional tag for the image"
    echo "  --push          Push image to registry after build"
    echo "  --no-cache      Build without using cache"
    echo "  --scan          Run container security scan after build"
    echo "  --help, -h      Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 change-dashboard"
    echo "  $0 risc --push --scan"
    echo "  $0 change-dashboard --tag latest --no-cache"
    exit 1
}

# Parse arguments
if [ $# -lt 1 ]; then
    echo "Error: Project name is required"
    usage
fi

PROJECT_NAME="$1"
shift

# Default options
ADDITIONAL_TAG=""
PUSH_IMAGE=false
NO_CACHE=false
RUN_SCAN=false

# Parse options
while [[ $# -gt 0 ]]; do
    case $1 in
        --tag)
            ADDITIONAL_TAG="$2"
            shift 2
            ;;
        --push)
            PUSH_IMAGE=true
            shift
            ;;
        --no-cache)
            NO_CACHE=true
            shift
            ;;
        --scan)
            RUN_SCAN=true
            shift
            ;;
        --help|-h)
            usage
            ;;
        *)
            echo "Error: Unknown option $1"
            usage
            ;;
    esac
done

echo "=== Stage 3: Docker Image Build for ${PROJECT_NAME} ==="
echo "Configuration:"
echo "  Project: ${PROJECT_NAME}"
echo "  Additional Tag: ${ADDITIONAL_TAG:-none}"
echo "  Push Image: ${PUSH_IMAGE}"
echo "  No Cache: ${NO_CACHE}"
echo "  Run Scan: ${RUN_SCAN}"

# Validate project directory exists
if [ ! -d "${PROJECT_NAME}" ]; then
    echo "Error: Project directory '${PROJECT_NAME}' not found"
    exit 1
fi

# Check if previous stages completed successfully
BUILD_INFO_FILE="${PROJECT_NAME}/target/build-info.properties"
if [ ! -f "${BUILD_INFO_FILE}" ]; then
    echo "Error: Build info file not found: ${BUILD_INFO_FILE}"
    echo "Please run previous stages first"
    exit 1
fi

# Load build info
source "${BUILD_INFO_FILE}"
if [ "${build.status:-}" != "success" ]; then
    echo "Error: Previous build stage failed"
    exit 1
fi

echo "Previous stage info:"
echo "  Build timestamp: ${build.timestamp:-unknown}"
echo "  JAR file: ${jar.file:-unknown}"
echo "  Scan status: ${scan.status:-not-run}"

# Load build configuration
BUILD_CONFIG_FILE="${PROJECT_NAME}/.devops/build.properties"
if [ -f "${BUILD_CONFIG_FILE}" ]; then
    echo "Loading build configuration from ${BUILD_CONFIG_FILE}"
    source "${BUILD_CONFIG_FILE}"
else
    echo "Warning: No build.properties found, using defaults"
    export docker_build_enable=true
    export docker_registry_nexus_path="unity/i15n/"
    export docker_image_name_prefix="${PROJECT_NAME}-"
    export java_base_image="nexus3.systems.uk.hsbc:18096/com/hsbc/group/itid/es/mw/java/mw-azuljava-v17:17.36"
fi

# Validate Docker is available
if ! command -v docker &> /dev/null; then
    echo "Error: Docker not found. Please install Docker"
    exit 1
fi

# Check Docker daemon
if ! docker info > /dev/null 2>&1; then
    echo "Error: Docker daemon is not running"
    exit 1
fi

# Validate devops-helper exists
DEVOPS_HELPER_SCRIPT="devops-helper/src/devops/scripts/build/actions.bash"
if [ ! -f "${DEVOPS_HELPER_SCRIPT}" ]; then
    echo "Error: devops-helper script not found at ${DEVOPS_HELPER_SCRIPT}"
    exit 1
fi

# Change to project directory
cd "${PROJECT_NAME}"

# Check if Dockerfile exists
if [ ! -f "Dockerfile" ]; then
    echo "Error: Dockerfile not found in ${PROJECT_NAME}"
    echo "Please create a Dockerfile for this project"
    exit 1
fi

echo "Using Dockerfile:"
head -5 Dockerfile

# Get project information
PROJECT_VERSION="${project.version:-$(mvn help:evaluate -Dexpression=project.version -q -DforceStdout 2>/dev/null)}"
PROJECT_ARTIFACT_ID="${project.artifactId:-$(mvn help:evaluate -Dexpression=project.artifactId -q -DforceStdout 2>/dev/null)}"

# Verify JAR file exists
JAR_FILE="${jar.file:-target/${PROJECT_ARTIFACT_ID}-${PROJECT_VERSION}.jar}"
if [ ! -f "${JAR_FILE}" ]; then
    echo "Error: JAR file not found: ${JAR_FILE}"
    echo "Please run stage-1-maven-build.sh first"
    exit 1
fi

echo "JAR file verified: ${JAR_FILE}"
ls -lh "${JAR_FILE}"

# Prepare Docker build
echo ""
echo "=== Preparing Docker Build ==="

# Create auth.conf for Docker build if it doesn't exist
if [ ! -f "auth.conf" ]; then
    echo "Creating auth.conf for Docker build..."
    # This would typically be provided by your CI/CD system
    touch auth.conf
fi

# Set image names
NEXUS_REGISTRY="${docker_registry_nexus_url_dev:-nexus3.systems.uk.hsbc:18096}"
GCR_REGISTRY="gcr.io/hsbc-9087302-unity-dev"
IMAGE_PATH="${docker_registry_nexus_path}${PROJECT_NAME}"
NEXUS_IMAGE="${NEXUS_REGISTRY}/${IMAGE_PATH}:${PROJECT_VERSION}"
GCR_IMAGE="${GCR_REGISTRY}/${IMAGE_PATH}:${PROJECT_VERSION}"

echo "Image names:"
echo "  Nexus: ${NEXUS_IMAGE}"
echo "  GCR: ${GCR_IMAGE}"

# Additional tags
ADDITIONAL_TAGS=()
if [ -n "${ADDITIONAL_TAG}" ]; then
    ADDITIONAL_TAGS+=("${GCR_REGISTRY}/${IMAGE_PATH}:${ADDITIONAL_TAG}")
fi

# Add latest tag if this is not a SNAPSHOT
if [[ ! "${PROJECT_VERSION}" =~ SNAPSHOT ]]; then
    ADDITIONAL_TAGS+=("${GCR_REGISTRY}/${IMAGE_PATH}:latest")
fi

echo ""
echo "=== Building Docker Image ==="

# Build options
BUILD_OPTIONS=""
if [ "${NO_CACHE}" = "true" ]; then
    BUILD_OPTIONS="${BUILD_OPTIONS} --no-cache"
fi

# Use devops-helper for Docker build
echo "Running Docker build using devops-helper..."
echo "Command: ${DEVOPS_HELPER_SCRIPT} docker_build"

# Set environment variables for devops-helper
export version="${PROJECT_VERSION}"

../"${DEVOPS_HELPER_SCRIPT}" docker_build

BUILD_EXIT_CODE=$?

if [ ${BUILD_EXIT_CODE} -eq 0 ]; then
    echo "✅ Docker build completed successfully"
    
    # Verify images were created
    echo ""
    echo "Created images:"
    docker images | grep "${PROJECT_NAME}" | head -5
    
    # Tag additional tags
    for tag in "${ADDITIONAL_TAGS[@]}"; do
        echo "Tagging: ${tag}"
        docker tag "${GCR_IMAGE}" "${tag}"
    done
    
    # Get image information
    IMAGE_ID=$(docker images --format "{{.ID}}" "${GCR_IMAGE}" | head -1)
    IMAGE_SIZE=$(docker images --format "{{.Size}}" "${GCR_IMAGE}" | head -1)
    
    echo ""
    echo "Image details:"
    echo "  Image ID: ${IMAGE_ID}"
    echo "  Size: ${IMAGE_SIZE}"
    
else
    echo "❌ Docker build failed with exit code ${BUILD_EXIT_CODE}"
    
    # Update build info with failure
    cat >> "target/build-info.properties" <<EOF

# Docker build results
image.timestamp=$(date -u +"%Y-%m-%dT%H:%M:%SZ")
image.stage=image-build
image.status=failed
image.exit_code=${BUILD_EXIT_CODE}
EOF
    
    exit ${BUILD_EXIT_CODE}
fi

# Container Security Scanning
if [ "${RUN_SCAN}" = "true" ] || [ "${container_scan_enable:-false}" = "true" ]; then
    echo ""
    echo "=== Container Security Scanning ==="
    
    # Use devops-helper for container scan
    echo "Running container security scan..."
    ../"${DEVOPS_HELPER_SCRIPT}" container_scan
    
    SCAN_EXIT_CODE=$?
    if [ ${SCAN_EXIT_CODE} -eq 0 ]; then
        echo "✅ Container security scan passed"
        CONTAINER_SCAN_STATUS="passed"
    else
        echo "⚠️  Container security scan failed (exit code: ${SCAN_EXIT_CODE})"
        CONTAINER_SCAN_STATUS="failed"
        # Don't fail the build for scan failures, just warn
    fi
else
    echo "Container security scanning skipped"
    CONTAINER_SCAN_STATUS="skipped"
fi

# Push to registry
if [ "${PUSH_IMAGE}" = "true" ]; then
    echo ""
    echo "=== Pushing to Registry ==="
    
    # Push main image
    echo "Pushing ${GCR_IMAGE}..."
    docker push "${GCR_IMAGE}"
    
    # Push additional tags
    for tag in "${ADDITIONAL_TAGS[@]}"; do
        echo "Pushing ${tag}..."
        docker push "${tag}"
    done
    
    echo "✅ Images pushed successfully"
    PUSH_STATUS="success"
else
    echo "Image push skipped (use --push to enable)"
    PUSH_STATUS="skipped"
fi

# Update build info
echo ""
echo "=== Updating Build Information ==="

cat >> "target/build-info.properties" <<EOF

# Docker image build results
image.timestamp=$(date -u +"%Y-%m-%dT%H:%M:%SZ")
image.stage=image-build
image.status=success
image.nexus=${NEXUS_IMAGE}
image.gcr=${GCR_IMAGE}
image.id=${IMAGE_ID:-unknown}
image.size=${IMAGE_SIZE:-unknown}
image.push_status=${PUSH_STATUS}
image.scan_status=${CONTAINER_SCAN_STATUS}
image.additional_tags=$(IFS=,; echo "${ADDITIONAL_TAGS[*]}")
EOF

echo "✅ Build info updated"

echo ""
echo "=== Stage 3 Complete: Docker Image Build ==="
echo "✅ Docker image built successfully"
echo "Image: ${GCR_IMAGE}"
echo "Push Status: ${PUSH_STATUS}"
echo "Scan Status: ${CONTAINER_SCAN_STATUS}"
echo ""
echo "Next stage: ./stage-4-deploy.sh ${PROJECT_NAME}"

# Return to original directory
cd ..
