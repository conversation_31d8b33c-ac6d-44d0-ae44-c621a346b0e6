---
containerPort: 8080
nameOverride: "udm-demand-depo-acc-ocp--a1phubfp-ddacmsp-psa"
replicaCount: 1
namespace: "ns-core-prod-apps"
autoscaling:
  enabled: true
  minReplicas: 1
  maxReplicas: 8
  targetCPUUtilizationValue: "375m"
podAnnotations:
  prometheus.io/scrape: "true"
  prometheus.io/port: "8080"
  prometheus.io/path: "/actuator/prometheus"
  generated-time: "2025-04-24T07:13:18.*********"
  cluster-autoscaler.kubernetes.io/safe-to-evict: "true"
serviceAccount:
  name: "ns-core-prod-sa"
imagePullSecrets:
- name: "docker-secret"
image:
  repository: "gcr.io/hsbc-********-unity-prod"
  name: "unity2-pipeline-source-adaptor"
  prefix: "unity/i15n/"
  tag: "2.8.2"
  pullPolicy: "Always"
args:
- "/bin/sh"
- "-c"
- "/hss/apps/config/startup.sh /pipeline-source-adaptor.jar -XX:MaxRAMPercentage=75.0\
  \ -XX:MaxMetaspaceSize=256m"
securityContext:
  allowPrivilegeEscalation: false
  capabilities:
    drop:
    - "ALL"
  readOnlyRootFilesystem: false
  runAsNonRoot: true
  runAsUser: *********
  seccompProfile:
    type: "RuntimeDefault"
resources:
  requests:
    cpu: "100m"
    memory: "500Mi"
  limits:
    cpu: "750m"
    memory: "2000Mi"
livenessProbe:
  exec:
    command:
    - "/bin/bash"
    - "-c"
    - "/hss/apps/config/liveness.sh"
  initialDelaySeconds: 60
  failureThreshold: 10
  periodSeconds: 15
  timeoutSeconds: 5
readinessProbe:
  exec:
    command:
    - "/bin/bash"
    - "-c"
    - "/hss/apps/config/liveness.sh"
  initialDelaySeconds: 60
  failureThreshold: 10
  periodSeconds: 15
  timeoutSeconds: 5
hostAliases:
- ip: "**************"
  hostnames:
  - "hkl20146687.hc.cloud.hk.hsbc"
- ip: "*************"
  hostnames:
  - "hkl20112359.hc.cloud.hk.hsbc"
service:
  enabled: false
  type: "ClusterIP"
  port: 80
  targetPort: 8080
volumeMounts:
- name: "certs-volume"
  mountPath: "/hss/apps/certs"
- name: "secrets-volume"
  mountPath: "/hss/apps/secrets"
- name: "config-volume"
  mountPath: "/hss/apps/config"
- name: "tmp-volume"
  mountPath: "/tmp"
- name: "unity2-apps-certs-secret-i15n"
  mountPath: "/hss/apps/secrets/unity2-apps-certs-secret-i15n"
volumes:
- name: "tmp-volume"
  emptyDir: {}
- name: "certs-volume"
  configMap:
    name: "unity2-apps-certs-configmap-i15n"
- name: "secrets-volume"
  secret:
    secretName: "unity2-apps-certs-secret-i15n"
- name: "config-volume"
  configMap:
    defaultMode: 493
    name: "udm-demand-depo-acc-ocp--a1phubfp-ddacmsp-psa"
- name: "unity2-apps-certs-secret-i15n"
  secret:
    secretName: "unity2-apps-certs-secret-i15n"
customLabels:
  service-category: "app-runtime"
  pipeline0: "udm-demand-depo-acc"
  service-type: "PIPELINE_SOURCE_ADAPTOR"
configMapLabels: {}
kafkaAutoscalingConfigs: []
appDynamic:
  enabled: false
  image:
    repository: "gcr.io/hsbc-********-unity-prod/unity/i15n/appdynamics/java-agent"
    tag: "21.11.3.33314"
    pullPolicy: "Always"
fluentBit:
  enabled: false
  image:
    repository: "gcr.io/hsbc-********-unity-prod/unity/i15n/fluent-bit"
    tag: "3.2.5"
    pullPolicy: "Always"
target: "GKE"
envType: "PROD"
supportAttributes:
  namespace: "ns-core-prod-apps"
  deployment: "udm-demand-depo-acc-ocp--a1phubfp-ddacmsp-psa"
  consumerGroup: "core-PROD-udm-demand-depo-acc"
  cacheGroup: "udm-demand-depo-acc"
  sourceTopics: "OCP_.a1phubfp.ddacmsp"
  targetTopics: "unity2-PROD-core-source-adaptor-udm-demand-depo-acc-v1-2-0-out"
  serviceVersion: "2.8.2"
  minReplicas: "1"
  maxReplicas: "8"
  autoScalingType: "HORIZONTAL_POD_AUTOSCALING"
  lagFactor: "10000"
  kafkaBootstrapServer: "kafka-0.kafka-internal.ns-core-prod-apps.svc.cluster.local:9093,kafka-1.kafka-internal.ns-core-prod-apps.svc.cluster.local:9093,kafka-2.kafka-internal.ns-core-prod-apps.svc.cluster.local:9093"
  sourceChannel: "KAFKA_AVRO"
  sourceFormat: "JSON"
  targetBatchTopicSuffix: "-batch"
  componentType: "SOURCE_ADAPTOR"
  sameBrokers: "true"
additionalContainers: []
env:
- name: "SPRING_PROFILES_ACTIVE"
  value: "secret,runtime,infra"
- name: "ENV"
  value: "PROD"
- name: "TARGET"
  value: "GKE"
- name: "KAFKA_CONSUMER_GROUP"
  value: "core-PROD-udm-demand-depo-acc"
- name: "PROJECT"
  value: "core"
- name: "DEFAULT_UNITY2_SCHEMA"
  value: "12bb8fb6_ba3d_4615_9b0b_f4f5cb38656f"
