import com.google.common.cache.Cache
import com.google.common.cache.CacheBuilder
import com.poc.hss.fasttrack.model.ConsumerAdaptorInputContext
import com.poc.hss.fasttrack.model.ConversionResult
import com.poc.hss.fasttrack.outbound.converter.CustomMessageContextConverter
import com.poc.hss.fasttrack.transform.model.LookupRequest
import io.micrometer.core.instrument.Counter
import io.vertx.core.json.JsonObject
import org.apache.commons.codec.digest.DigestUtils
import org.apache.commons.lang3.StringUtils
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import java.text.SimpleDateFormat
import java.util.concurrent.TimeUnit
import java.util.stream.Collectors
import java.util.stream.Stream

import static org.apache.commons.lang3.StringUtils.isNotEmpty

class Unity2EventLayerMessageFormatter implements CustomMessageContextConverter {

    public static Cache<String, String> cache = CacheBuilder.newBuilder().expireAfterWrite(7, TimeUnit.DAYS).build()
    private static long deliveryCount
    private static long suppressionCount
    private static long totalCount
    private static final Logger logger = LoggerFactory.getLogger(Unity2EventLayerMessageFormatter.class)
    private static String keyField = "HSBCTransactionIdentification"
    private List<String> gcEntitledAccounts = new ArrayList<>()
    private List<String> krMaskedAccounts = new ArrayList<>()
    private Map<String, String> gcHkUkAccounts = new HashMap<>()
    private static SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS")
    private static String[] consentedAccounts = new String[]{"GCA-HK-HIFS-006-000004-401","GCA-HK-HIFS-006-000004-402","GCA-HK-HIFS-006-000004-403","GCA-HK-HIFS-006-000004-404","GCA-HK-HIFS-006-000004-406","GCA-HK-HIFS-006-000004-407","GCA-HK-HIFS-006-000004-408","GCA-HK-HIFS-006-000004-409","GCA-HK-HIFS-006-000004-410","GCA-HK-HIFS-006-000004-411"}
    private static String[] materialFields = new String[]{
            "TradeBusinessStatus",
            "TradeEconomicAvailabilityStatus",
            "SourceSystemTradeStatus",
            "MaintenanceFunctionType",
            "UnexecutionReasonCode",
            "GenerateDepositoryInstructionIndicator",
            "CashHoldStatus",
            "ScripHoldStatus",
            "TradeDate",
            "ExpectedSettlementDate",
            "CounterpartyIdentification",
            "ClientTransactionIdentification",
            "LinkedTransactionIdentification",
            "LocationCode",
            "SafekeepingAccountIdentification",
            "FinancialInstrumentIdentificationPrimaryIdentifier",
            "SettlementAmount",
            "SettlementAmountCurrency",
            "SettlementQuantity",
            "InternalAccountTransferAccountIdentification",
            "InternalAccountTransferTransactionIdentification",
            "SourceSystemTransactionType",
            "SameDaySettlementIndicator",
            "PoolIdentification",
            "AssociatedAccountIdentification",
            "AssociatedSubAccountIdentification",
            "SettlementInstructionCancellationStatus",
            "SourceOfInstructionType",
            "SecuritiesSettlementType",
            "InstructionDateTime",
            "LocalCustodianCode",
            "RepurchaseTransactionIdentification",
            "RepurchaseLegType",
            "SettlementInstructionProcessingAdditionalDetails",
            "AdditionalUnexecutionReasonInformation",
            "SettlementDate",
            "EffectiveSettlementDate",
            "CancellationReasonCode",
            "AdditionalCancellationReasonInformation",
            "SecuritiesRtgsIndicator",
            "SecuritiesLendingIndicator",
            "SecuritiesBorrowingIndicator",
            "TransactionReferenceAmountCurrency",
            "TransactionReferenceAmount",
            "PlaceOfSettlementBic",
            "SourceSystemTradeState",
            "FinancialInstrumentIdentificationIsin",
            "CounterpartyBic",
            "CounterpartyDataSourceSchemeCode",
            "BuyerBic",
            "BuyerDataSourceSchemeCode",
            "BuyerAccountIdentification",
            "SellerBic",
            "SellerDataSourceSchemeCode",
            "SellerAccountIdentification",
            "CashAccountIdentification",
            "ValueDate",
            "RegistrationCode",
            "PendingCancellationIndicator",
            "AccountBaseCurrency",
            "LocalCustodianIdentification",
            "DepositoryIdentification",
            "SwiftStatus1Reason1Code",
            "SecuritiesTransactionTypeCode",
            "FxInstructionStatus",
            "CrestSettlementPriorityCode",
            "FxLocationCode",
            "FxCancellationIndicator",
            "SecuritiesAccountStandingInstructionDetails",
            "BuyerName",
            "SellerName",
            "MarketInfrastructureTransactionIdentification",
            "HKSettlementInstructionPaymentIndicator",
            "LinkedIndicator",
            "ClientInstructionReceivedIndicator",
            "CashPaymentMethod",
            "CounterpartyCashAccountIdentification",
            "DealPriceAmountCurrency",
            "DealPriceAmount",
            "ReceiversCustodianBic",
            "DeliverersCustodianBic",
            "ReceiversCustodianAccountIdentification",
            "DeliverersCustodianAccountIdentification",
            "QuantityType",
            "InstructionIdentification",
            "ExchangeIdentification",
            "CounterpartyAssociatedAccountIdentification",
            "ContractualSettlementIndicator",
            "CounterpartyType",
            "ClientCancellationIdentification",
            "FxIndicator",
            "SuppressStockSufficiencyCheckIndicator",
            "DepositorySettlementType",
            "BursaSettlementType",
            "RegistrationIndicator",
            "TradeDescription",
            "HoldMatchedSettlementInstructionIndicator",
            "SourceSystemLastUpdatedDateTime",
            "FxRate",
            "InternalAccountTransferSuspenseCashAccountIdentification",
            "TradeIdentification",
            "InternalAccountTransferIndicator"
    }

    private static def APPROVED_SITES = getApprovedSites()
    private static def APPROVED_SITES_LOWER_CASE = APPROVED_SITES.stream().map(String::toLowerCase).collect(Collectors.toList())
    private static String[] sensitive_fields_NOT_PRINT_IN_LOG=[]

    private static List<String> getApprovedSites(){
        switch (System.getenv("ENV")){
            case "DEV":
            case "SIT":
            case "UAT":
            case "PREPROD": return ["HK","AU","TH","PH","MY","SG","JP","VN","NZ","ID","AE","BH","KW","QA","GCA","TW","IN","GCUK","KR","OM","LK","SA"]
            case "PROD":    return ["HK","AU","TH","PH","MY","SG","JP","VN","NZ","AE","GCA","BH","KW","QA","TW","KR","GCUK","OM"]
            default: return []
        }
    }

    @Override
    List<ConversionResult> convertMessage(ConsumerAdaptorInputContext context) {
        logger.info("ENV: {}",context.getEnvironment()?.getProperty("ENV")?.toUpperCase())
        if(System.getenv("ENV") == "PROD"){
            sensitive_fields_NOT_PRINT_IN_LOG=["SafekeepingAccountName","CustomerIdentification","SettlementInstructionProcessingAdditionalDetails","AdditionalUnexecutionReasonInformation"]
        }
        Counter totalCounter = context.getMeterRegistry()?.counter("UdmTotalEventCount")
        Counter deliveryCounter = context.getMeterRegistry()?.counter("UdmEventDeliveryCount")
        Counter suppressionCounter = context.getMeterRegistry()?.counter("UdmEventSuppressionCount")
        Set<String> accounts = context.getRecords().stream()
                .filter(e -> APPROVED_SITES_LOWER_CASE.contains(e.getString("site")?.trim()?.toLowerCase()))
                .map(e -> String.format("%s-%s", e.getString("site"), e.getString("SafekeepingAccountIdentification").replaceAll(' ','-')))
                .collect(Collectors.toSet())
        this.gcHkUkAccounts = context.getLookupService().queryList(LookupRequest.builder()
                .accessSchemaName("ENTITLEMENT_DCC")
                .fields(Arrays.asList("sec_acc_number","group_id"))
                .criteria(String.format("site in ('%s') and group_id in ('18','06') and sec_acc_number in ('%s')",String.join("','", APPROVED_SITES_LOWER_CASE),String.join("','", accounts)))
                .build())
                .stream()
                .collect(Collectors.toMap( f -> f.getString("sec_acc_number"), f -> f.getString("group_id"), (a,b) -> b))
        this.krMaskedAccounts = context.getLookupService().queryList(LookupRequest.builder()
                .accessSchemaName("KR_ACCOUNTS_TO_BE_MASKED")
                .fields(Arrays.asList("SafekeepingAccountIdentification"))
                .build())
                .stream()
                .map(f -> f.getString("SafekeepingAccountIdentification"))
                .filter(Objects::nonNull)
                .collect(Collectors.toList())
        def source = context.getRecords()
        def records = source.stream()
        //add the filter to control data sent to event layer
                .filter(e -> APPROVED_SITES.contains(e.getString("site","").trim().toUpperCase()))
                .map(e -> {
                    totalCounter?.increment()
                    totalCount++
                    JsonObject json = new JsonObject();
                    String secAccId = e.getString("sec_acc_id")
                    if(e.getString("business_line").equals("GC")) {
                        if(this.gcEntitledAccounts.isEmpty()) {
                            this.gcEntitledAccounts = context.getLookupService().queryList(LookupRequest.builder()
                                    .accessSchemaName("GCA_FILTERING_CRITERIA")
                                    .fields(Arrays.asList("_id"))
                                    .build())
                                    .stream()
                                    .map(f -> f.getString("_id"))
                                    .filter(Objects::nonNull)
                                    .collect(Collectors.toList())
                            logger.info("gcEntitledAccounts:{}, secAccId:{}", gcEntitledAccounts, secAccId)
                        }
                        if (!gcEntitledAccounts.contains(secAccId))
                            return null
                        if(!(context.getEnvironment().getProperty("ENV").toUpperCase().equals("PROD") || consentedAccounts.contains(secAccId)))
                            return null
                    }
                    json.put("MessageIdentification",UUID.randomUUID().toString())
                    json.put("HSBCTransactionIdentification",e.getString("HSBCTransactionIdentification"))
                    json.put("LocationDescription",defaultToBlankIfNull(e.getString("LocationDescription")))
                    json.put("OwningBusinessName",e.getString("OwningBusinessName"))
                    json.put("OtherBusinessName", deriveOtherBusinessName(e.getString("site"), e.getString("SafekeepingAccountIdentification")))
                    json.put("SourceSystemName",deriveSourceSystemName(e.getString("site"),e.getString("SiteCode")))
                    json.put("SourceSystemLastUpdatedDateTime",e.getString("SourceSystemLastUpdatedDateTime"))
                    String tradeBusinessStatus = defaultToBlankIfNull(deriveTradeBusinessStatus(e))
                    json.put("TradeBusinessStatus", tradeBusinessStatus)
                    json.put("TradeEconomicAvailabilityStatus",defaultToBlankIfNull(deriveTradeEconomicAvailabilityStatus(tradeBusinessStatus, e)))
                    json.put("SourceSystemTradeStatus",e.getString("SourceSystemTradeStatus"))
                    json.put("MaintenanceFunctionType",defaultToBlankIfNull(e.getString("MaintenanceFunctionType")))
                    json.put("UnexecutionReasonCode",defaultToBlankIfNull(e.getString("UnexecutionReasonCode")))
                    json.put("GenerateDepositoryInstructionIndicator",e.getString("GenerateDepositoryInstructionIndicator"))
                    json.put("CashHoldStatus",defaultToBlankIfNull(e.getString("CashHoldStatus")))
                    json.put("ScripHoldStatus",defaultToBlankIfNull(e.getString("ScripHoldStatus")))
                    json.put("SystemTransactionIdentification",e.getString("SystemTransactionIdentification"))
                    json.put("TradeDate",defaultToBlankIfNull(e.getString("TradeDate")))
                    json.put("ExpectedSettlementDate",e.getString("ExpectedSettlementDate"))
                    json.put("CounterpartyIdentification",defaultToBlankIfNull(e.getString("CounterpartyIdentification")))
                    json.put("ClientTransactionIdentification",e.getString("ClientTransactionIdentification"))
                    json.put("LinkedTransactionIdentification",defaultToBlankIfNull(e.getString("LinkedTransactionIdentification")))
                    json.put("LocationCode",e.getString("LocationCode"))
                    json.put("SafekeepingAccountIdentification",e.getString("SafekeepingAccountIdentification"))
                    json.put("FinancialInstrumentIdentificationPrimaryIdentifier",e.getString("FinancialInstrumentIdentificationPrimaryIdentifier"))
                    json.put("SettlementAmount",defaultToBlankIfNull(e.getString("SettlementAmount")))
                    json.put("SettlementAmountCurrency",e.getString("SettlementAmountCurrency"))
                    json.put("SettlementQuantity",e.getString("SettlementQuantity"))
                    json.put("InternalAccountTransferAccountIdentification",defaultToBlankIfNull(e.getString("InternalAccountTransferAccountIdentification")))
                    json.put("InternalAccountTransferTransactionIdentification",defaultToBlankIfNull(e.getString("InternalAccountTransferTransactionIdentification")))
                    json.put("SourceSystemTransactionType",e.getString("SourceSystemTransactionType"))
                    json.put("SameDaySettlementIndicator",defaultToBlankIfNull(e.getString("SameDaySettlementIndicator")))
                    json.put("PoolIdentification",defaultToBlankIfNull(e.getString("PoolIdentification")))
                    json.put("AssociatedAccountIdentification",defaultToBlankIfNull(e.getString("AssociatedAccountIdentification")))
                    json.put("AssociatedSubAccountIdentification",defaultToBlankIfNull(e.getString("AssociatedSubAccountIdentification")))
                    json.put("SettlementInstructionCancellationStatus",defaultToBlankIfNull(e.getString("SettlementInstructionCancellationStatus")))
                    json.put("SourceOfInstructionType",defaultToBlankIfNull(e.getString("SourceOfInstructionType")))
                    json.put("SecuritiesSettlementType", e.getString("SecuritiesSettlementType"))
                    json.put("InstructionDateTime", defaultToBlankIfNull(e.getString("InstructionDateTime")))
                    json.put("UnityUpdatedTimeStamp", dateFormat.parse(e.getString("updated_time_stamp")).getTime().toString())
                    json.put("LocalCustodianCode",defaultToBlankIfNull(e.getString("LocalCustodianCode")))
                    json.put("LocalCustodianDescription",defaultToBlankIfNull(e.getString("LocalCustodianDescription")))
                    json.put("RepurchaseTransactionIdentification",defaultToBlankIfNull(e.getString("RepurchaseTransactionIdentification")))
                    json.put("RepurchaseLegType",defaultToBlankIfNull(e.getString("RepurchaseLegType")))
                    json.put("SafekeepingAccountName",defaultToBlankIfNull(e.getString("SafekeepingAccountName")))
                    json.put("CustomerIdentification",defaultToBlankIfNull(e.getString("CustomerIdentification")))
                    json.put("CounterpartyName",defaultToBlankIfNull(e.getString("CounterpartyName")))
                    json.put("SettlementInstructionProcessingAdditionalDetails",defaultToBlankIfNull(e.getString("SettlementInstructionProcessingAdditionalDetails")))
                    json.put("AdditionalUnexecutionReasonInformation",defaultToBlankIfNull(e.getString("AdditionalUnexecutionReasonInformation")))
                    json.put("SettlementDate",defaultToBlankIfNull(e.getString("SettlementDate")))
                    json.put("EffectiveSettlementDate",defaultToBlankIfNull(e.getString("EffectiveSettlementDate")))
                    json.put("CancellationReasonCode",defaultToBlankIfNull(e.getString("CancellationReasonCode")))
                    json.put("AdditionalCancellationReasonInformation",defaultToBlankIfNull(e.getString("AdditionalCancellationReasonInformation")))
                    json.put("SourceSystemLastUpdatedDateTimeUtc",defaultToBlankIfNull(e.getString("SourceSystemLastUpdatedDateTimeUtc")))
                    json.put("InstructionDateTimeUtc",defaultToBlankIfNull(e.getString("InstructionDateTimeUtc")))
                    json.put("SecuritiesRtgsIndicator",defaultToBlankIfNull(e.getString("SecuritiesRtgsIndicator")))
                    json.put("SecuritiesLendingIndicator",defaultToBlankIfNull(e.getString("SecuritiesLendingIndicator")))
                    json.put("SecuritiesBorrowingIndicator",defaultToBlankIfNull(e.getString("SecuritiesBorrowingIndicator")))
                    json.put("ClientGroupIdentification",defaultToBlankIfNull(e.getString("ClientGroupIdentification")))
                    json.put("ClientEntityIdentification",defaultToBlankIfNull(e.getString("ClientEntityIdentification")))
                    json.put("TransactionReferenceAmountCurrency",defaultToBlankIfNull(e.getString("TransactionReferenceAmountCurrency")))
                    json.put("TransactionReferenceAmount",defaultToBlankIfNull(e.getString("TransactionReferenceAmount")))
                    json.put("CrossBusinessTransactionIdentification",defaultToBlankIfNull(e.getString("CrossBusinessTransactionIdentification")))
                    json.put("FinancialInstrumentIdentificationDescription",defaultToBlankIfNull(e.getString("FinancialInstrumentIdentificationDescription")))
                    //added on 2024-08-02
                    json.put("PlaceOfSettlementBic",defaultToBlankIfNull(e.getString("PlaceOfSettlementBic")))
                    json.put("SourceSystemTradeState",defaultToBlankIfNull(e.getString("SourceSystemTradeState")))
                    json.put("FinancialInstrumentIdentificationIsin",defaultToBlankIfNull(e.getString("FinancialInstrumentIdentificationIsin")))
                    json.put("CounterpartyBic",defaultToBlankIfNull(e.getString("CounterpartyBic")))
                    json.put("CounterpartyDataSourceSchemeCode",defaultToBlankIfNull(e.getString("CounterpartyDataSourceSchemeCode")))
                    json.put("BuyerBic",defaultToBlankIfNull(e.getString("BuyerBic")))
                    json.put("BuyerDataSourceSchemeCode",defaultToBlankIfNull(e.getString("BuyerDataSourceSchemeCode")))
                    json.put("BuyerAccountIdentification",defaultToBlankIfNull(e.getString("BuyerAccountIdentification")))
                    json.put("SellerBic",defaultToBlankIfNull(e.getString("SellerBic")))
                    json.put("SellerDataSourceSchemeCode",defaultToBlankIfNull(e.getString("SellerDataSourceSchemeCode")))
                    json.put("SellerAccountIdentification",defaultToBlankIfNull(e.getString("SellerAccountIdentification")))
                    json.put("CashAccountIdentification",defaultToBlankIfNull(e.getString("CashAccountIdentification")))
                    json.put("ValueDate",defaultToBlankIfNull(e.getString("ValueDate")))
                    json.put("RegistrationCode",defaultToBlankIfNull(e.getString("RegistrationCode")))
                    json.put("PendingCancellationIndicator",defaultToBlankIfNull(e.getString("PendingCancellationIndicator")))
                    json.put("AccountBaseCurrency",defaultToBlankIfNull(e.getString("AccountBaseCurrency")))
                    json.put("LocalCustodianIdentification",defaultToBlankIfNull(e.getString("LocalCustodianIdentification")))
                    json.put("DepositoryIdentification",defaultToBlankIfNull(e.getString("DepositoryIdentification")))
                    json.put("SwiftStatus1Reason1Code",defaultToBlankIfNull(e.getString("SwiftStatus1Reason1Code")))
                    json.put("SecuritiesTransactionTypeCode",defaultToBlankIfNull(e.getString("SecuritiesTransactionTypeCode")))
                    json.put("FxInstructionStatus",defaultToBlankIfNull(e.getString("FxInstructionStatus")))
                    json.put("CrestSettlementPriorityCode",defaultToBlankIfNull(e.getString("CrestSettlementPriorityCode")))
                    json.put("FxLocationCode",defaultToBlankIfNull(e.getString("FxLocationCode")))
                    json.put("FxCancellationIndicator",defaultToBlankIfNull(e.getString("FxCancellationIndicator")))
                    json.put("StraightThroughProcessedIndicator",defaultToBlankIfNull(e.getString("StraightThroughProcessedIndicator")))
                    json.put("SecuritiesAccountStandingInstructionDetails",defaultToBlankIfNull(e.getString("SecuritiesAccountStandingInstructionDetails")))
                    json.put("BuyerName",defaultToBlankIfNull(e.getString("BuyerName")))
                    json.put("SellerName",defaultToBlankIfNull(e.getString("SellerName")))
                    json.put("MarketInfrastructureTransactionIdentification",defaultToBlankIfNull(e.getString("MarketInfrastructureTransactionIdentification")))
                    json.put("HKSettlementInstructionPaymentIndicator",defaultToBlankIfNull(e.getString("HKSettlementInstructionPaymentIndicator")))
                    json.put("LinkedIndicator",defaultToBlankIfNull(e.getString("LinkedIndicator")))
                    json.put("FinancialInstrumentIdentificationLocalSecurityCode",defaultToBlankIfNull(e.getString("FinancialInstrumentIdentificationLocalSecurityCode")))
                    json.put("ClientInstructionReceivedIndicator",defaultToBlankIfNull(e.getString("ClientInstructionReceivedIndicator")))
                    json.put("CashPaymentMethod",defaultToBlankIfNull(e.getString("CashPaymentMethod")))
                    json.put("CounterpartyCashAccountIdentification",defaultToBlankIfNull(e.getString("CounterpartyCashAccountIdentification")))
                    json.put("DealPriceAmountCurrency",defaultToBlankIfNull(e.getString("DealPriceAmountCurrency")))
                    json.put("DealPriceAmount",defaultToBlankIfNull(e.getString("DealPriceAmount")))
                    json.put("ReceiversCustodianBic",defaultToBlankIfNull(e.getString("ReceiversCustodianBic")))
                    json.put("DeliverersCustodianBic",defaultToBlankIfNull(e.getString("DeliverersCustodianBic")))
                    json.put("ReceiversCustodianAccountIdentification",defaultToBlankIfNull(e.getString("ReceiversCustodianAccountIdentification")))
                    json.put("DeliverersCustodianAccountIdentification",defaultToBlankIfNull(e.getString("DeliverersCustodianAccountIdentification")))
                    json.put("QuantityType",defaultToBlankIfNull(e.getString("QuantityType")))
                    json.put("InstructionIdentification",defaultToBlankIfNull(e.getString("InstructionIdentification")))
                    json.put("ExchangeIdentification",defaultToBlankIfNull(e.getString("ExchangeIdentification")))
                    json.put("CounterpartyAssociatedAccountIdentification",defaultToBlankIfNull(e.getString("CounterpartyAssociatedAccountIdentification")))
                    json.put("ContractualSettlementIndicator",defaultToBlankIfNull(e.getString("ContractualSettlementIndicator")))
                    // For BBDRPT-7831
                    json.put("CounterpartyType",defaultToBlankIfNull(e.getString("CounterpartyType")))
                    json.put("ClientCancellationIdentification",defaultToBlankIfNull(e.getString("ClientCancellationIdentification")))
                    json.put("SafekeepingAccountType",defaultToBlankIfNull(e.getString("SafekeepingAccountType")))
                    json.put("FxIndicator",defaultToBlankIfNull(e.getString("FxIndicator")))
                    json.put("SuppressStockSufficiencyCheckIndicator",defaultToBlankIfNull(e.getString("SuppressStockSufficiencyCheckIndicator")))
                    json.put("DepositorySettlementType",defaultToBlankIfNull(e.getString("DepositorySettlementType")))
                    json.put("BursaSettlementType",defaultToBlankIfNull(e.getString("BursaSettlementType")))
                    json.put("RegistrationIndicator",defaultToBlankIfNull(e.getString("RegistrationIndicator")))
                    json.put("TradeDescription",defaultToBlankIfNull(e.getString("TradeDescription")))
                    json.put("CaptureUserIdentification",defaultToBlankIfNull(e.getString("CaptureUserIdentification")))
                    json.put("HoldMatchedSettlementInstructionIndicator",defaultToBlankIfNull(e.getString("HoldMatchedSettlementInstructionIndicator")))
                    // For MSSCUSTODY-5530 BBDRPT-9142
                    json.put("FxRate",defaultToBlankIfNull(e.getString("FxRate")))
                    json.put("InternalAccountTransferSuspenseCashAccountIdentification",defaultToBlankIfNull(e.getString("InternalAccountTransferSuspenseCashAccountId")))
                    json.put("TradeIdentification",defaultToBlankIfNull(e.getString("TradeIdentification")))
                    json.put("InternalAccountTransferIndicator",defaultToBlankIfNull(e.getString("InternalAccountTransferIndicator")))
                    json.put("CounterpartyCashAccountName",defaultToBlankIfNull(e.getString("CounterpartyCashAccountName")))
                    json.put("InternalAccountTransferSuspenseCashAccountName",defaultToBlankIfNull(e.getString("InternalAccountTransferSuspenseCashAccountName")))
                    def startTime = System.currentTimeMillis();
                    String materialValue = Arrays.stream(materialFields).map(field -> String.format("%s:%s",field, json.getString(field))).collect(Collectors.joining("|"))
                    String checksum = DigestUtils.sha3_512Hex(materialValue)
                    def checkSumOverhead = System.currentTimeMillis() - startTime;
                    if(cache.getIfPresent(json.getString(keyField))?.equals(checksum)){
                        suppressionCounter?.increment()
                        suppressionCount++
                        logger.info("checkSumOverhead: {}, delivered: {}, suppressed: {}, total: {}, checksum: {}, materialValue:{}", checkSumOverhead, deliveryCount, suppressionCount, totalCount, checksum, materialValue )
                        return null
                    }
                    cache.put(json.getString(keyField), checksum)
                    deliveryCounter?.increment()
                    deliveryCount++
                    logger.debug("Business Event Trade Status: \n" +
                            "HSBCTransactionIdentification:{}, \n" +
                            "TradeMaintenanceStatus_{}_{}:{}, \n" +
                            "TradeMatchingStatus_{}_{}:{}, \n" +
                            "TradeCashSettlementStatus_{}_{}:{}, \n" +
                            "TradeStockSettlementStatus:{}, \n" +
                            "TradeHoldStatus:{}, \n" +
                            "CashHoldStatus:{}, \n" +
                            "ScripHoldStatus:{}",
                            e.getString("HSBCTransactionIdentification"),
                            e.getString("SourceSystemTradeStatus"), e.getString("SourceSystemPreviousStatus"), e.getString("TradeMaintenanceStatus"),
                            e.getString("SourceSystemTradeStatus"), e.getString("SourceSystemPreviousStatus"), e.getString("TradeMatchingStatus"),
                            e.getString("SourceSystemTradeStatus"), e.getString("SourceSystemPreviousStatus"), e.getString("TradeCashSettlementStatus"),
                            e.getString("TradeStockSettlementStatus"),
                            e.getString("TradeHoldStatus"),
                            e.getString("CashHoldStatus"),
                            e.getString("ScripHoldStatus")
                    )
                    json = MaskingUtilityForKr.maskMessage(json, krMaskedAccounts)
                    logger.info("Json Message published to Event Layer: {\n{}\n}, checkSumOverhead: {}, delivered: {}, suppressed: {}, total: {}, unique {}, checksum: {}, materialValue:{} ",
                            json.map.entrySet().stream().filter(s -> !sensitive_fields_NOT_PRINT_IN_LOG.contains(s.getKey()))
                                    .map(entry -> "\""+entry.getKey()+"\":\""+entry.getValue()+"\"").collect(Collectors.joining(",\n")),
                            checkSumOverhead, deliveryCount, suppressionCount, totalCount, cache.size(), checksum, materialValue)
                    return json
                })
                .filter(Objects::nonNull)
                .map(e -> e.toString())
                .map(e -> ConversionResult.builder().body(e).build())
                .collect(Collectors.toList())
        return records
    }

    private String deriveSourceSystemName(String site, String market){
        if(site.equalsIgnoreCase("GCA")){
            return String.format("GHSS GC %s", market)
        }else  if(site.equalsIgnoreCase("GCUK")){
            return "GCS UK"
        }else  if(site.equalsIgnoreCase("GCE")){
            return "GCS EU"
        }else{
            return String.format("GHSS DCC %s", market)
        }
    }

    private String deriveOwningBusinessName(JsonObject e){
        String site = e.getString("site")
        String market = e.getString("SiteCode")
        String LocalCustodianCode = e.getString("LocalCustodianCode")
        if(site.equalsIgnoreCase("GCA")){
            return "GC HK"
        }else if (site.equalsIgnoreCase("GCUK")){
            return StringUtils.isBlank(LocalCustodianCode) ?  "DCC UK" : "GC UK"
        }else if (site.equalsIgnoreCase("GCE")){
            return "GC EU"
        }else{
            return String.format("DCC %s", market)
        }
    }

    private String deriveOtherBusinessName(String site,String account) {
        if(["GCA","GCUK","GCE"].contains(site)) return ""
        String lookupAccount = String.format("%s-%s", site, account.replaceAll(' ','-'))
        String groupId = gcHkUkAccounts.getOrDefault(lookupAccount,"XX")
        if(groupId.equals("18"))
            return "GC HK"
        else if(groupId.equals("06"))
            return "GC UK"
        else
            return "Other GC"
    }

    String deriveTradeBusinessStatus(JsonObject e){
        String tradeBusinessStatus = ""
        if(e.getString("TradeMaintenanceStatus") == "TradCanc"
                || e.getString("TradeMaintenanceStatus") == "TradCancPdgApprvl"
                || e.getString("TradeMaintenanceStatus") == "PdgApprvlSys"
                || e.getString("TradeMaintenanceStatus") == "PdgApprvlOthr"
                || e.getString("TradeMaintenanceStatus") == "TradCancPdgConfCSD"
                || e.getString("TradeMaintenanceStatus") == "TradCancPdgRvsl"
                || e.getString("TradeMaintenanceStatus") == "TradCancPdgConf"
                ||  e.getString("TradeMaintenanceStatus") == "TradCancPdgActn" )
            tradeBusinessStatus = e.getString("TradeMaintenanceStatus");
        else if(e.getString("TradeHoldStatus") == "PdgSplmtryActn"
                || e.getString("TradeHoldStatus") == "TradOnHldByHsbc"
                || e.getString("TradeHoldStatus") == "TradOnHldDueToSnctns"
                || e.getString("TradeHoldStatus") == "TradOnHldMssngClntInf"
                || e.getString("TradeHoldStatus") == "TradOnHldClntInstrMssng")
            tradeBusinessStatus = e.getString("TradeHoldStatus")
        else if((e.getString("TradeStockSettlementStatus") == "StockSttld"
                || e.getString("TradeStockSettlementStatus") == "StockTrfInstrSubmittdToCSD"
                || e.getString("TradeStockSettlementStatus") == "StockTrfCmpltdInCSD"
                || e.getString("TradeStockSettlementStatus") == "StockTrfRjctdInCSD")
                && (e.getString("TradeCashSettlementStatus") == "CshSttld"
                || ( e.getString("TradeCashSettlementStatus") == "" && e.getString("Payment") == "FREE")))
            tradeBusinessStatus = "TradSttld"
        else if((e.getString("TradeStockSettlementStatus") == "StockSttld"
                || e.getString("TradeStockSettlementStatus") == "StockTrfInstrSubmittdToCSD"
                || e.getString("TradeStockSettlementStatus") == "StockTrfCmpltdInCSD"
                || e.getString("TradeStockSettlementStatus") == "StockTrfRjctdInCSD")
                && (e.getString("TradeCashSettlementStatus") != "CshSttld"
                && !(e.getString("TradeCashSettlementStatus") == "" && e.getString("Payment") == "FREE")
                && e.getString("TradeCashSettlementStatus")!= "CshSttldPdgFxActn"))
            tradeBusinessStatus = "StockSttld"
        else if (e.getString("TradeStockSettlementStatus") == "StockPrtlySttld")
            tradeBusinessStatus = "PrtlySttld"
        else if((e.getString("TradeStockSettlementStatus") != "StockSttld"
                && e.getString("TradeStockSettlementStatus") != "StockPrtlySttld"
                && e.getString("TradeStockSettlementStatus") != "StockTrfInstrSubmittdToCSD"
                && e.getString("TradeStockSettlementStatus") != "StockTrfCmpltdInCSD"
                && e.getString("TradeStockSettlementStatus") != "StockTrfRjctdInCSD")
                && (e.getString("TradeCashSettlementStatus") == "CshSttld"))
            tradeBusinessStatus = e.getString("TradeCashSettlementStatus")
        else if(e.getString("TradeStockSettlementStatus") == "StockSttld"
                && e.getString("TradeCashSettlementStatus") == "CshSttldPdgFxActn")
            tradeBusinessStatus = "TradSttldPdgFxActn"
        else if(e.getString("TradeStockSettlementStatus") == "StockSttldPdgCertActn")
            tradeBusinessStatus = "TradSttldPdgCertActn"
        else if(e.getString("TradeStockSettlementStatus") == "CertRegnInPrgrs"
                && e.getString("TradeCashSettlementStatus") == "TradPmtInPrgrs")
            tradeBusinessStatus = "CertRegnAndTradPmtInPrgrs"
        else if((e.getString("TradeStockSettlementStatus") == "CertRegnInPrgrs"
                || e.getString("TradeStockSettlementStatus") == "CertDlvryPdgConf")
                && e.getString("TradeCashSettlementStatus") != "TradPmtInPrgrs" )
            tradeBusinessStatus  = e.getString("TradeStockSettlementStatus")
        else if(e.getString("TradeStockSettlementStatus") != "CertRegnInPrgrs"
                && (e.getString("TradeCashSettlementStatus") == "TradPmtInPrgrs"
                || e.getString("TradeCashSettlementStatus") == "TradPmtConf"
                || e.getString("TradeCashSettlementStatus") == "TradPmtPdgBrkr"))
            tradeBusinessStatus = e.getString("TradeCashSettlementStatus")
        // For BBDRPT-9524
        else if(e.getString("TradeStockSettlementStatus") == "PdgCertCaptr"
                || e.getString("TradeStockSettlementStatus") == "PdgCertVldtn"
                || e.getString("TradeStockSettlementStatus") == "CertCaptrInErr")
            tradeBusinessStatus = "PdgCertActn"
        else if(e.getString("TradeStockSettlementStatus") == "CertRcvd"
                || e.getString("TradeStockSettlementStatus") == "CertWdrwn")
            tradeBusinessStatus = "CertActnCmpltd"
        else if(defaultToBlankIfNull(e.getString("TradeMatchingStatus")) != "")
            tradeBusinessStatus = e.getString("TradeMatchingStatus")
        else
            tradeBusinessStatus = e.getString("TradeMaintenanceStatus")
        return tradeBusinessStatus
    }

    String deriveTradeEconomicAvailabilityStatus(String tradeBusinessStatus, JsonObject e){
        String tradeEconomicAvailabilityStatus = ""
        if ((tradeBusinessStatus == "TradCanc" || tradeBusinessStatus == "TradSttld" || tradeBusinessStatus == "PrtlySttld")
                || (tradeBusinessStatus == "StockSttld" && e.getString("TradeCashSettlementStatus") == "")
                || (tradeBusinessStatus == "CshSttld" && e.getString("TradeStockSettlementStatus") == ""))
            tradeEconomicAvailabilityStatus = tradeBusinessStatus
        else if((e.getString("TradeStockSettlementStatus") == "StockSttld"
                && e.getString("TradeCashSettlementStatus") == "CshSttldPdgFxActn")
                || (e.getString("TradeStockSettlementStatus") != "CertRegnInPrgrs"
                &&(e.getString("TradeCashSettlementStatus") == "TradPmtInPrgrs"
                || e.getString("TradeCashSettlementStatus") == "TradPmtConf"
                || e.getString("TradeCashSettlementStatus") == "TradPmtPdgBrkr")))
            tradeEconomicAvailabilityStatus = "PmtActnInPrgrs"
        else if(e.getString("TradeStockSettlementStatus") == "StockSttldPdgCertActn"
                ||((e.getString("TradeStockSettlementStatus") == "CertRegnInPrgrs"
                ||e.getString("TradeStockSettlementStatus") == "CertDlvryPdgConf")
                && e.getString("TradeCashSettlementStatus") != "TradPmtInPrgrs"))
            tradeEconomicAvailabilityStatus = "CertActnInPrgrs"
        else if(e.getString("TradeStockSettlementStatus") == "CertRegnInPrgrs"
                && e.getString("TradeCashSettlementStatus") == "TradPmtInPrgrs")
            tradeEconomicAvailabilityStatus = "CertAndPmtActnInPrgrs"
        else if((e.getString("TradeStockSettlementStatus") == "StockAllctd"
                || e.getString("TradeStockSettlementStatus") == "StockToBeAllctdOnSD"
                || e.getString("TradeStockSettlementStatus") == "StockNsffcntCvrdRcvbl"
                || e.getString("TradeStockSettlementStatus") == "StockNsffcnt"
                || e.getString("TradeStockSettlementStatus") == "StockUallctd"
                || e.getString("TradeStockSettlementStatus") == "NoStockChckReqrd")
                && (e.getString("TradeCashSettlementStatus") != "CshAllctd"
                && e.getString("TradeCashSettlementStatus") != "CshToBeAllctdOnSD"
                && e.getString("TradeCashSettlementStatus") != "CshNsffcnt"
                && e.getString("TradeCashSettlementStatus") != "CshUallctd"))
            tradeEconomicAvailabilityStatus = e.getString("TradeStockSettlementStatus")
        else if((e.getString("TradeStockSettlementStatus") != "StockAllctd"
                && e.getString("TradeStockSettlementStatus") != "StockToBeAllctdOnSD"
                && e.getString("TradeStockSettlementStatus") != "StockNsffcntCvrdRcvbl"
                && e.getString("TradeStockSettlementStatus") != "StockNsffcnt"
                && e.getString("TradeStockSettlementStatus") != "StockUallctd"
                && e.getString("TradeStockSettlementStatus") != "NoStockChckReqrd")
                && (e.getString("TradeCashSettlementStatus") == "CshAllctd"
                || e.getString("TradeCashSettlementStatus") == "CshToBeAllctdOnSD"
                || e.getString("TradeCashSettlementStatus") == "CshNsffcnt"
                || e.getString("TradeCashSettlementStatus") == "CshUallctd"))
            tradeEconomicAvailabilityStatus = e.getString("TradeCashSettlementStatus")
        // For BBDRPT-9524
        else if(e.getString("TradeStockSettlementStatus") == "CertPdgCaptr"
                || e.getString("TradeStockSettlementStatus") == "CertPdgVldtn"
                || e.getString("TradeStockSettlementStatus") == "CertCaptrInErr"
                || e.getString("TradeStockSettlementStatus") == "CertRcvd"
                || e.getString("TradeStockSettlementStatus") == "CertWdrwn")
            tradeEconomicAvailabilityStatus = e.getString("TradeStockSettlementStatus")
        return tradeEconomicAvailabilityStatus
    }

    String defaultToBlankIfNull(String str){
        str === null ? "" : str
    }
}

class MaskingUtilityForKr {
    private static final Logger logger = LoggerFactory.getLogger(MaskingUtilityForKr.class)
    public static final String MASKED_6CHAR = "XXXXXX"
    public static final String MASKED_20CHAR = "XXXXXXXXXXXXXXXXXXXX"
    public static final String MASKED_35CHAR = "XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX"


    static JsonObject maskMessage(JsonObject message, List<String> krMaskedAccounts) {
        if (message != null && message.getString("OwningBusinessName") == "DCC KR"){
            if(krMaskedAccounts!=null && !krMaskedAccounts.isEmpty() && isNotEmpty(message.getString("SafekeepingAccountIdentification")) && krMaskedAccounts.contains(message.getString("SafekeepingAccountIdentification"))) {
                if (isNotEmpty(message.getString("CustomerIdentification")) && message.getString("CustomerIdentification").contains("-")) {
                    message.put("CustomerIdentification", Stream.of(message.getString("CustomerIdentification").split("-")).collect(Collectors.toList()).with { it.set(it.size() - 1, MASKED_6CHAR);
                        it }.join("-"))
                }
                if (isNotEmpty(message.getString("AssociatedAccountIdentification"))) {
                    message.put("AssociatedAccountIdentification", MASKED_20CHAR)
                }
                if (isNotEmpty(message.getString("AssociatedSubAccountIdentification"))) {
                    message.put("AssociatedSubAccountIdentification", MASKED_20CHAR)
                }
                if (isNotEmpty(message.getString("SafekeepingAccountIdentification")) && message.getString("SafekeepingAccountIdentification").contains("-")) {
                    message.put("SafekeepingAccountIdentification", Stream.of(message.getString("SafekeepingAccountIdentification").split("-")).collect(Collectors.toList()).with { it.set(it.size() - 2, MASKED_6CHAR);
                        it }.join("-"))
                }
                if (isNotEmpty(message.getString("SafekeepingAccountName"))) {
                    message.put("SafekeepingAccountName", MASKED_35CHAR)
                }
                if (isNotEmpty(message.getString("CashAccountIdentification")) && message.getString("CashAccountIdentification").contains("-")) {
                    message.put("CashAccountIdentification", Stream.of(message.getString("CashAccountIdentification").split("-")).collect(Collectors.toList()).with { it.set(it.size() - 2, MASKED_6CHAR);
                        it }.join("-"))
                }
            }
            if(krMaskedAccounts!=null && !krMaskedAccounts.isEmpty() && isNotEmpty(message.getString("InternalAccountTransferAccountIdentification")) && krMaskedAccounts.contains(message.getString("InternalAccountTransferAccountIdentification"))){
                if(isNotEmpty(message.getString("InternalAccountTransferAccountIdentification")) && message.getString("InternalAccountTransferAccountIdentification").contains("-")){
                    message.put("InternalAccountTransferAccountIdentification", Stream.of(message.getString("InternalAccountTransferAccountIdentification").split("-")).collect(Collectors.toList()).with { it.set(it.size() - 2, MASKED_6CHAR); it }.join("-"))
                }
            }
        }
        return message
    }
}