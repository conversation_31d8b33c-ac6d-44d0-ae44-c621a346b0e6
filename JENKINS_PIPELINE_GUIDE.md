# Jenkins Pipeline Guide

This guide explains how to use the Jenkins pipeline for building and deploying `change-dashboard` and `risc` projects.

## Pipeline Overview

The Jenkins pipeline implements a 5-stage build and deployment process:

```
┌─────────────┐   ┌─────────────┐   ┌─────────────┐   ┌─────────────┐   ┌─────────────┐
│   Stage 0   │──▶│   Stage 1   │──▶│   Stage 2   │──▶│   Stage 3   │──▶│   Stage 4   │
│  Checkout   │   │Maven Build  │   │Security Scan│   │Image Build  │   │   Deploy    │
│             │   │             │   │             │   │             │   │             │
└─────────────┘   └─────────────┘   └─────────────┘   └─────────────┘   └─────────────┘
```

## Pipeline Parameters

### Required Parameters

| Parameter | Type | Options | Description |
|-----------|------|---------|-------------|
| `PROJECT_TO_BUILD` | Choice | `change-dashboard`, `risc`, `both` | Select which project(s) to build |
| `TARGET_ENVIRONMENT` | Choice | `dev`, `uat`, `prod` | Target deployment environment |

### Stage Control Parameters

| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| `STAGE_0_CHECKOUT` | Boolean | `true` | Enable/disable repository checkout |
| `STAGE_1_MAVEN_BUILD` | Boolean | `true` | Enable/disable Maven build stage |
| `STAGE_2_SECURITY_SCAN` | Boolean | `true` | Enable/disable security scanning |
| `STAGE_3_IMAGE_BUILD` | Boolean | `true` | Enable/disable Docker image build |
| `STAGE_4_DEPLOY` | Boolean | `true` | Enable/disable deployment stage |

### Build Options

| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| `SKIP_TESTS` | Boolean | `false` | Skip Maven tests during build |
| `SKIP_SAST` | Boolean | `false` | Skip SAST scanning |
| `PUSH_IMAGES` | Boolean | `true` | Push Docker images to registry |
| `WAIT_FOR_DEPLOYMENT` | Boolean | `true` | Wait for deployment completion |
| `PARALLEL_EXECUTION` | Boolean | `false` | Run projects in parallel |

### Compliance Parameters

| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| `CHANGE_ORDER` | String | _(empty)_ | SNOW change order (required for UAT/PROD) |

## Usage Examples

### 1. Development Build
```
PROJECT_TO_BUILD: both
TARGET_ENVIRONMENT: dev
All stages: enabled
PARALLEL_EXECUTION: true
```

### 2. UAT Deployment
```
PROJECT_TO_BUILD: change-dashboard
TARGET_ENVIRONMENT: uat
CHANGE_ORDER: CHG1234567
WAIT_FOR_DEPLOYMENT: true
```

### 3. Production Deployment
```
PROJECT_TO_BUILD: both
TARGET_ENVIRONMENT: prod
CHANGE_ORDER: CHG7654321
PARALLEL_EXECUTION: false
WAIT_FOR_DEPLOYMENT: true
```

### 4. Build Only (No Deployment)
```
PROJECT_TO_BUILD: risc
STAGE_4_DEPLOY: false
PUSH_IMAGES: true
```

### 5. Security Scan Only
```
PROJECT_TO_BUILD: both
STAGE_1_MAVEN_BUILD: false
STAGE_3_IMAGE_BUILD: false
STAGE_4_DEPLOY: false
```

## Stage Details

### Stage 0: Checkout
- Checks out the repository
- Makes pipeline scripts executable
- Validates required files exist
- Sets up workspace

### Stage 1: Maven Build
- Compiles Java source code
- Runs unit tests (unless skipped)
- Packages JAR files
- Creates build metadata
- Uses `stage-1-maven-build.sh`

### Stage 2: Security Scanning
- SAST (Static Application Security Testing)
- Dependency vulnerability scanning
- License compliance checking
- Creates security reports
- Uses `stage-2-security-scan.sh`

### Stage 3: Image Build & Push
- Builds Docker images using enterprise base images
- Tags images with version and git commit
- Pushes to Nexus and GCR registries
- Runs container security scans
- Uses `stage-3-image-build.sh`

### Stage 4: Deploy
- Deploys to target Kubernetes environment
- Uses Helm charts from gke-deployment
- Validates deployment health
- Updates ingress and services
- Uses `stage-4-deploy.sh`

## Environment Configuration

### Development (dev)
- **Namespace**: `ns-i15n-dev`
- **Registry**: `gcr.io/hsbc-9087302-unity-dev`
- **Domain**: `hsbc-9087302-unity-dev.dev.gcp.cloud.hk.hsbc`
- **Change Order**: Not required

### UAT (uat)
- **Namespace**: `ns-i15n-uat`
- **Registry**: `gcr.io/hsbc-9087302-unity-uat`
- **Domain**: `hsbc-9087302-unity-uat.uat.gcp.cloud.hk.hsbc`
- **Change Order**: Required

### Production (prod)
- **Namespace**: `ns-i15n-prod`
- **Registry**: `gcr.io/hsbc-9087302-unity-prod`
- **Domain**: `hsbc-9087302-unity-prod.prod.gcp.cloud.hk.hsbc`
- **Change Order**: Required

## Change Order Requirements

For UAT and Production deployments, a valid SNOW change order is required:

### Format
- Must follow pattern: `CHG1234567` (CHG + 7 digits)
- Example: `CHG0123456`

### Validation
- Pipeline validates change order format
- Deployment fails if change order is missing for UAT/PROD
- Change order is updated with deployment status

## Notifications

### Email Notifications
- **Failure**: Sent to build author and team
- **Success**: Sent for UAT/PROD deployments only
- **Includes**: Build status, duration, change order, URLs

### Slack Notifications
- **Channel**: `#unity-deployments`
- **Triggers**: All builds
- **Content**: Status, projects, environment

### SNOW Integration
- **Updates**: Change order work notes
- **Status**: Implementation status based on build result
- **Details**: Build information and URLs

## Artifacts and Reports

### Archived Artifacts
- `**/target/build-info.properties` - Build metadata
- `**/target/scan-results/**` - Security scan results
- `**/target/*.jar` - Built JAR files

### Published Reports
- **Test Results**: JUnit test reports
- **Security Scans**: HTML security scan reports
- **Build Logs**: Complete pipeline execution logs

## Troubleshooting

### Common Issues

#### 1. Change Order Validation Failed
```
Error: Change order is required for uat deployment
```
**Solution**: Provide a valid SNOW change order in CHG1234567 format

#### 2. Project Not Found
```
Error: Project directory 'project-name' not found
```
**Solution**: Ensure the project exists in the repository

#### 3. Helm Values Missing
```
Error: Helm values not found for project project-name
```
**Solution**: Create Helm values file at `gke-deployment/helm-charts/i15n-helmchart/project-name/values.yaml`

#### 4. Docker Build Failed
```
Error: Docker build failed with exit code 1
```
**Solution**: Check Dockerfile syntax and base image availability

#### 5. Deployment Timeout
```
Error: Deployment timeout or failed
```
**Solution**: Check Kubernetes cluster status and resource availability

### Debug Commands

#### Check Pipeline Status
```bash
# View build logs
kubectl logs -f deployment/jenkins -n jenkins

# Check pipeline workspace
ls -la /var/jenkins_home/workspace/
```

#### Validate Prerequisites
```bash
# Check tools
java -version
mvn -version
docker --version
kubectl version --client
helm version --client

# Check connectivity
kubectl cluster-info
```

#### Manual Stage Execution
```bash
# Run individual stages for debugging
./stage-1-maven-build.sh change-dashboard --skip-tests
./stage-2-security-scan.sh change-dashboard --skip-sast
./stage-3-image-build.sh change-dashboard --push
./stage-4-deploy.sh change-dashboard --environment dev --dry-run
```

## Best Practices

### 1. Development Workflow
- Use `dev` environment for feature development
- Enable parallel execution for faster builds
- Skip security scans for rapid iteration

### 2. UAT Workflow
- Always provide change order
- Run full pipeline with all stages
- Wait for deployment completion
- Validate application functionality

### 3. Production Workflow
- Use sequential execution for safety
- Require change order approval
- Enable all security scans
- Monitor deployment closely

### 4. Troubleshooting
- Use dry-run for deployment validation
- Check individual stage logs
- Validate prerequisites before running
- Use stage-specific parameters for debugging

## Integration with Existing Infrastructure

### devops-helper
- Maven builds use existing `actions.bash` scripts
- Security scans leverage existing tools
- Docker builds use enterprise base images

### gke-deployment
- Deployment uses existing `deploy.sh` script
- Helm charts from `i15n-helmchart` templates
- Namespace and service account configurations

### HSBC Enterprise
- Nexus repository integration
- GCR registry support
- SNOW change management
- Enterprise security compliance
