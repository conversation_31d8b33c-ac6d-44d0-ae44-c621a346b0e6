#!/usr/bin/env bash
set -euo pipefail
echo "<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<< GROOVY  >>>>>>>>>>>>>>>>>>>>>>>>>>>"
echo "[MGMT_HOST] starting groovy installaton"
echo "[MGMT_HOST] downloading groovy from nexus"
wget --user="vagrant" --password="vagrant" "https://efx-nexus.systems.uk.hsbc:8082/nexus/service/local/repositories/central/content/org/codehaus/groovy/groovy-binary/3.0.6/groovy-binary-3.0.6.zip"
mkdir -p $GROOVY_PATH
sudo mv $GROOVY_VERSION $GROOVY_PATH/$GROOVY_VERSION
sudo unzip $GROOVY_PATH/$GROOVY_VERSION 
ln -s $GROOVY_PATH/$GROOVY_VERSION  /usr/bin
echo "[MGMT_HOST]groovy installation was completed"
