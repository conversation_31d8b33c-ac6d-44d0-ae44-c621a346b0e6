variable "project_id" {
    type = string
}

variable "location" {
    type = string
}

variable "gcp_region" {
    type = string
}

variable "repository_id" {
    type = string
}

variable "description" {
    type = string
}

variable "kms_key_name" {
    type = string
}

variable "art_reg_reader_members" {
    type = list(string)
}

variable "art_reg_writer_members" {
    type = list(string)
}

variable "art_reg_admin_members" {
    type = list(string)
}

variable "format" {
    type = string
}