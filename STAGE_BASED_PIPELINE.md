# Stage-Based Pipeline Implementation

This document describes the modular, stage-based pipeline implementation for building and deploying `change-dashboard` and `risc` projects.

## Architecture Overview

The pipeline is divided into 4 distinct stages, each with its own script and responsibilities:

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Stage 1       │    │   Stage 2       │    │   Stage 3       │    │   Stage 4       │
│  Maven Build    │───▶│ Security Scan   │───▶│  Image Build    │───▶│     Deploy      │
│                 │    │                 │    │                 │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘    └─────────────────┘
```

## Stage Scripts

### 1. `stage-1-maven-build.sh`
**Purpose**: Compile, test, and package Java applications

**Features**:
- Uses existing devops-helper infrastructure
- Configurable test execution
- Validates JAR creation
- Creates build metadata for subsequent stages

**Usage**:
```bash
./stage-1-maven-build.sh change-dashboard
./stage-1-maven-build.sh risc --skip-tests --clean
```

**Options**:
- `--skip-tests`: Skip running tests
- `--clean`: Run clean before build
- `--offline`: Run Maven in offline mode

### 2. `stage-2-security-scan.sh`
**Purpose**: Perform security analysis on code and dependencies

**Features**:
- SAST (Static Application Security Testing)
- Dependency vulnerability scanning
- License compliance checking
- Configurable scan types

**Usage**:
```bash
./stage-2-security-scan.sh change-dashboard
./stage-2-security-scan.sh risc --skip-sast --force
```

**Options**:
- `--skip-sast`: Skip SAST scanning
- `--skip-deps`: Skip dependency scanning
- `--force`: Continue even if scans fail

### 3. `stage-3-image-build.sh`
**Purpose**: Build and optionally push Docker images

**Features**:
- Uses enterprise-approved base images
- Container security scanning
- Multi-registry support (Nexus + GCR)
- Automatic tagging

**Usage**:
```bash
./stage-3-image-build.sh change-dashboard --push
./stage-3-image-build.sh risc --tag latest --scan
```

**Options**:
- `--tag TAG`: Additional tag for the image
- `--push`: Push image to registry after build
- `--no-cache`: Build without using cache
- `--scan`: Run container security scan

### 4. `stage-4-deploy.sh`
**Purpose**: Deploy applications to Kubernetes using Helm

**Features**:
- Multi-environment support (dev/uat/prod)
- Uses existing gke-deployment infrastructure
- Health checks and status monitoring
- Rollback capabilities

**Usage**:
```bash
./stage-4-deploy.sh change-dashboard --environment dev
./stage-4-deploy.sh risc --environment uat --wait --dry-run
```

**Options**:
- `--environment`: Target environment (dev/uat/prod)
- `--namespace`: Target namespace
- `--dry-run`: Show what would be deployed
- `--wait`: Wait for deployment completion
- `--timeout`: Deployment timeout

## Master Pipeline Script

### `pipeline.sh`
**Purpose**: Orchestrate execution of multiple stages across multiple projects

**Features**:
- Sequential or parallel execution
- Selective stage execution
- Continue-on-failure option
- Comprehensive logging and reporting

**Usage**:
```bash
# Full pipeline for both projects
./pipeline.sh

# Specific stages only
./pipeline.sh --stages maven-build,image-build

# Single project
./pipeline.sh --projects change-dashboard

# Parallel execution with options
./pipeline.sh --parallel --continue --push --wait --environment uat
```

## Quick Start Script

### `quick-start.sh`
**Purpose**: Provide common pipeline execution patterns

**Commands**:
- `build-all`: Build both projects (maven + image)
- `deploy-dev`: Deploy to dev environment
- `deploy-uat`: Deploy to UAT environment
- `deploy-prod`: Deploy to production
- `full-dev`: Complete pipeline to dev
- `full-uat`: Complete pipeline to UAT
- `full-prod`: Complete pipeline to production
- `single PROJECT`: Run full pipeline for single project

**Usage**:
```bash
./quick-start.sh full-dev --parallel
./quick-start.sh single change-dashboard
./quick-start.sh deploy-prod --dry-run
```

## Stage Communication

Stages communicate through build metadata files:

### `target/build-info.properties`
```properties
# Maven build stage
project.name=change-dashboard
project.version=0.0.1-SNAPSHOT
build.status=success
jar.file=target/change-dashboard-0.0.1-SNAPSHOT.jar

# Security scan stage
scan.status=success
scan.sast.enabled=true

# Image build stage
image.status=success
image.gcr=gcr.io/hsbc-9087302-unity-dev/unity/i15n/change-dashboard:0.0.1-SNAPSHOT
image.push_status=success

# Deploy stage
deploy.status=success
deploy.environment=dev
deploy.namespace=ns-i15n-dev
```

## Benefits of Stage-Based Approach

### 1. **Modularity**
- Each stage can be run independently
- Easy to debug specific issues
- Reusable across different projects

### 2. **Flexibility**
- Skip stages as needed
- Run only specific stages
- Different options per stage

### 3. **CI/CD Integration**
- Each stage maps to CI/CD pipeline stages
- Easy to parallelize in CI systems
- Clear failure points

### 4. **Debugging**
- Isolated failure points
- Stage-specific logs
- Build metadata tracking

### 5. **Scalability**
- Easy to add new stages
- Project-agnostic design
- Parallel execution support

## Integration with Existing Infrastructure

### devops-helper Integration
- `stage-1-maven-build.sh` uses `devops-helper/src/devops/scripts/build/actions.bash mvn_build`
- `stage-2-security-scan.sh` uses `devops-helper/src/devops/scripts/build/actions.bash sast_scan`
- `stage-3-image-build.sh` uses `devops-helper/src/devops/scripts/build/actions.bash docker_build`

### gke-deployment Integration
- `stage-4-deploy.sh` uses `gke-deployment/deploy.sh`
- Leverages existing Helm charts in `gke-deployment/helm-charts/i15n-helmchart/`
- Uses existing namespace and service account configurations

### Jenkins Integration
Each stage can be mapped to Jenkins pipeline stages:

```groovy
pipeline {
    stages {
        stage('Maven Build') {
            steps {
                sh './stage-1-maven-build.sh ${PROJECT_NAME}'
            }
        }
        stage('Security Scan') {
            steps {
                sh './stage-2-security-scan.sh ${PROJECT_NAME}'
            }
        }
        stage('Image Build') {
            steps {
                sh './stage-3-image-build.sh ${PROJECT_NAME} --push'
            }
        }
        stage('Deploy') {
            steps {
                sh './stage-4-deploy.sh ${PROJECT_NAME} --environment ${ENVIRONMENT} --wait'
            }
        }
    }
}
```

## Error Handling and Recovery

### Stage Failures
- Each stage validates prerequisites
- Clear error messages with troubleshooting steps
- Build metadata tracks failure points
- Option to continue on non-critical failures

### Recovery Options
- Re-run individual stages
- Skip problematic stages
- Force continue with warnings
- Rollback deployment if needed

## Monitoring and Observability

### Build Metadata
- Comprehensive tracking in `build-info.properties`
- Timestamps for each stage
- Success/failure status
- Artifact locations

### Logging
- Stage-specific log sections
- Clear progress indicators
- Error highlighting
- Summary reports

### Health Checks
- Application health endpoints
- Kubernetes readiness/liveness probes
- Post-deployment validation
- Access URL verification

This stage-based approach provides a robust, flexible, and maintainable pipeline that integrates seamlessly with your existing HSBC infrastructure while providing modern CI/CD capabilities.
