#!/bin/sh
#api reference: https://grafana.com/docs/grafana/latest/developers/http_api/
#alert provisioning: https://grafana.com/docs/grafana/latest/developers/http_api/alerting_provisioning/

constructor() {
  if ! [ -z "${GF_SECURITY_ADMIN_USER__FILE}" ]; then
    export REQ_USERNAME=`cat $GF_SECURITY_ADMIN_USER__FILE`
  fi
  if ! [ -z "${GF_SECURITY_ADMIN_PASSWORD__FILE}" ]; then
    export REQ_PASSWORD=`cat $GF_SECURITY_ADMIN_PASSWORD__FILE`
  fi
}

datasource_handler() {
  curl -s -u ${REQ_USERNAME}:${REQ_PASSWORD} -X${REQ_METHOD} -H 'Content-Type: application/json' ${REQ_URL}
}

#########################################################
# main
#########################################################
constructor
datasource_handler