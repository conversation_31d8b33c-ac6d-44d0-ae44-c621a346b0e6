{{- $files := .Files }}
{{- $target := printf "" }}
{{- $target2 := printf "alertmanager/%s-%s/*.yaml" (.Values.target|lower) "nonprod"}}
{{- $target3 := printf "alertmanager/%s/*"  (.Values.target|lower)}}
{{- $custom_chart1 := printf "grafana/alertmanager/%s/*.yaml" (.Values.target|lower)}}
{{- $custom_chart2 := printf "grafana/alertmanager/%s-%s/*.yaml" (.Values.target|lower) "nonprod"}}
{{- if eq .Values.envType "PROD" }}
{{- $target2 = printf "alertmanager/%s-%s/*.yaml" (.Values.target|lower) "prod" }}
{{- $custom_chart2 = printf "grafana/alertmanager/%s-%s/*.yaml" (.Values.target|lower) "prod" }}
{{- end }}
{{- range tuple $target $target2 $target3 $custom_chart1 $custom_chart2 }}
{{- range $file, $_ :=  $files.Glob . }}
apiVersion: v1
kind: ConfigMap
metadata:
  name: grafana-config-alertmanager-{{$file|base|replace "." "-"|lower}}
  namespace: {{ $.Release.Namespace  }}
  labels:
    grafana_alertmanager: "1"
    {{- include "grafana.labels" $ | nindent 4 }}
data:
  {{$file|base}}: |{{ $files.Get $file |nindent 4}}
---
{{- end}}
{{- end}}