# RISC - Risk & Control Application

## Overview

RISC (Risk & Control) is a Spring Boot application that integrates with HSBC's ICE (Integrated Change Environment) system to manage and analyze change requests. The application provides REST APIs to retrieve, analyze, and validate change requests with quality checks.

## Project Structure

```
risc/
├── src/
│   ├── main/
│   │   ├── java/com/hsbc/ssv/bbd/risc/
│   │   │   ├── RiscApplication.java           # Main Spring Boot application
│   │   │   ├── ChangeRequestParser.java       # Parser for change request data
│   │   │   ├── DifyWorkflowRunner.java        # Workflow execution logic
│   │   │   ├── ICEChangeRequestReader.java    # ICE API integration
│   │   │   ├── SSLUtil.java                   # SSL utilities
│   │   │   ├── bean/
│   │   │   │   └── ChangeRequest.java         # Change request data model
│   │   │   ├── controller/
│   │   │   │   └── ICEChangeRequestController.java  # REST API endpoints
│   │   │   ├── service/
│   │   │   │   └── ICEService.java            # Business logic service
│   │   │   └── util/
│   │   │       └── EncryptionUtil.java        # Encryption utilities
│   │   └── resources/
│   │       └── application.properties         # Application configuration
├── pom.xml                                    # Maven configuration
└── .gitignore                                 # Git ignore rules
```

## Prerequisites

- **Java**: JDK 17 or higher (Project uses Java 17)
- **Maven**: 3.6+ (Tested with Maven 3.9.8)
- **Network Access**: Access to HSBC internal systems (ICE API)

## Dependencies

The application uses the following key dependencies:
- Spring Boot 3.2.12 (Web starter)
- JSON processing (org.json)
- OpenCSV for CSV handling
- Eclipse JGit for Git operations
- Spring Boot Test for testing

## Configuration

### Application Properties

The application is configured via src/main/resources/application.properties:

```
# Application settings
spring.application.name=risc
server.servlet.context-path=/risc

# ICE API Configuration
ice.api.username=ssv-bbd-ice-user
ice.api.password=[encrypted]
ice.api.base-url=https://ice.it.global.hsbc/ice/api/v4/changes
ice.api.fields=[extensive field list]
ice.api.offset=0
ice.api.limit=1000
ice.api.after-date=2025-05-26T00:00:00.675470Z
ice.api.before-date=2025-05-31T00:00:00.675470Z
ice.api.app-ids=11465671
ice.api.order-direction=Descending

# Dify API Configuration
dify.cr.quality.api=[encrypted]
```

### Environment Variables

You may need to set the following environment variable:
- app.secretKey: Secret key for encryption/decryption operations

## How to Build and Run

### 1. Clone the Repository

```
git clone https://stash.hk.hsbc/scm/unity-i15n-poc/risc.git
cd risc
```

### 2. Build the Application

```
mvn clean compile
```

### 3. Run Tests (if available)

```
mvn test
```

### 4. Package the Application

```
mvn clean package
```

This will create a JAR file in the target/ directory.

### 5. Run the Application

#### Option A: Using Maven Spring Boot Plugin
```
mvn spring-boot:run
```

#### Option B: Using the JAR file
```
java -jar target/risc-0.0.1-SNAPSHOT.jar
```

#### Option C: Using Maven with specific profile
```
mvn spring-boot:run -Dspring-boot.run.profiles=dev
```

## API Endpoints

The application exposes the following REST endpoints under /risc/crs:

### 1. List Change Requests
```
GET /risc/crs/list
```
**Parameters:**
- offset (optional, default: 0)
- limit (optional, default: 1000)
- afterDate (optional, defaults to current Monday)
- beforeDate (optional, defaults to next Monday)
- appIds (optional, default: 11465671)
- orderDirection (optional, default: Descending)

### 2. List Change Requests with Quality Check
```
GET /risc/crs/list-with-check
```

**Parameters:** Same as above, plus:
- refresh (optional, default: false)

### 3. Get Change Request Details
```
GET /risc/crs/details/{cr}
```

**Path Parameter:**
- cr: Change Request number

### 4. Check Change Request Quality
```
GET /risc/crs/check/{cr}
```

**Path Parameter:**
- cr: Change Request number

## Usage Examples

### Access the API
Once running, the application will be available at:
- Base URL: http://localhost:8080/risc
- Health check: http://localhost:8080/risc/actuator/health (if actuator is enabled)

### Example API Calls
```
# Get change requests for current week
curl "http://localhost:8080/risc/crs/list"

# Get specific change request details
curl "http://localhost:8080/risc/crs/details/CR123456"

# Check change request quality
curl "http://localhost:8080/risc/crs/check/CR123456"
```

## Development

### IDE Setup
- Import as Maven project
- Ensure Java 17+ is configured
- Set up Spring Boot run configuration

### Adding Dependencies
Use Maven to add new dependencies:
```
mvn dependency:tree  # View current dependencies
```

## Troubleshooting

### Common Issues

1. **Java Version Mismatch**
   - Ensure Java 17+ is installed and configured
   - Check JAVA_HOME environment variable

2. **Maven Build Failures**
   - Run mvn clean before building
   - Check internet connectivity for dependency downloads

3. **Application Startup Issues**
   - Verify application.properties configuration
   - Check if required environment variables are set
   - Ensure network access to ICE API endpoints

4. **SSL/TLS Issues**
   - The application includes SSLUtil.java for SSL handling
   - May need corporate certificates installed

### Logs
Application logs will show startup information and API call details. Check console output for debugging information.

## Security Notes

- The application contains encrypted passwords and API keys
- Ensure proper handling of sensitive configuration data
- Network access is required to HSBC internal systems
- SSL/TLS configuration may be required for production deployment

## Contact
For questions or issues, please contact the development team or refer to the project's internal documentation.
