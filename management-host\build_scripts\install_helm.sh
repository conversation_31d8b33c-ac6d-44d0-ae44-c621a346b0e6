#!/usr/bin/env bash
set -euo pipefail
echo "<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<< hel, >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>"
cd /opt/
echo "[Mgmt_Host] changed path to opt"
echo "[MGMT_HOST] downloading helm binary from digital nexus"
echo $PWD

helm_version=3.6.1
helm_plugins=/opt/helm/plugins

artifact=helm-${helm_version}-linux-amd64.tar.gz
echo "helm version:${helm_version}[${artifact}]"
#curl -L -X GET -u ${NEXUS_CREDS_USR}:${NEXUS_CREDS_PWD}  "https://nexus-digital.systems.uk.hsbc:8081/nexus/service/local/artifact/maven/redirect?r=digital-tooling&g=com.github&a=helm&v=3.2.4&e=tar.gz&c=linux-amd64" > ${artifact}
curl -L -XGET  https://hkl20090861.hc.cloud.hk.hsbc/devops/helm-v${helm_version}-linux-amd64.tar.gz > ${artifact}

echo "<<< Create helm directory >>>"
mkdir -p /opt/helm/plugins

echo "<<< Unzip helm archive >>>"
mv ${artifact} /opt/helm/${artifact}
cd /opt/helm/
sudo tar -xvzf ${artifact}
cp linux-amd64/helm helm
sudo rm -rf ${artifact}
sudo rm -rf linux-amd64


echo "installing helm secrets plugin + sops + age"
sudo curl -LSf https://hkl20090861.hc.cloud.hk.hsbc/devops/helm-secrets.tar.gz | tar -C ${helm_plugins} -xzf - 
sudo curl -LSf https://hkl20090861.hc.cloud.hk.hsbc/devops/age-latest-linux-amd64.tar.gz | tar -C /usr/local/bin/ -xzf- --strip-components=1 --wildcards "age/age*"
sudo curl https://hkl20090861.hc.cloud.hk.hsbc/devops/sops-v3.7.3.linux.amd64 -o /usr/local/bin/sops
sudo chown root:root /usr/local/bin/age* /usr/local/bin/sops
sudo chmod 755 /usr/local/bin/age* /usr/local/bin/sops
sudo chmod 777 -R /opt/helm

cat > helm.sh <<-'EOF'
export HELM_PLUGINS=/opt/helm/plugins
export SOPS_AGE_KEY_FILE=${HOME}/sops/age/keys.txt
if [[ -f "${SOPS_AGE_KEY_FILE}" ]]; then 
  export SOPS_AGE_RECIPIENTS=$(grep "key:" ${SOPS_AGE_KEY_FILE}|cut -f2 -d:|sed 's| ||g')
fi
EOF

sudo mv helm.sh /etc/profile.d/helm.sh
sudo chmod 644 /etc/profile.d/helm.sh

ln -s /opt/helm/helm /usr/bin
echo "helm installation completed"
