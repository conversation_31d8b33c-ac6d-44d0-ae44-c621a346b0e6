#!/usr/bin/env bash
# GCP project service account key will need to renewed every 60 days for Production and 30 days for Development
# otherwise this will be caught by the HGMS scanning
# https://alm-confluence.systems.uk.hsbc/confluence/display/GCP/HGMS+Violation+Rules
#
# This script is to demonstrate we can renew the service account key automatically
# https://cloud.google.com/iam/docs/creating-managing-service-account-keys#iam-service-account-keys-create-gcloud
#
# in order to renew the service account key, the service account must be in this role roles/iam.serviceAccountKeyAdmin
# https://cloud.google.com/iam/docs/understanding-roles#service-accounts-roles

# Enable stric mode
set -euo pipefail
trap 'echo "An error occured. Cleaning up..."' EXIT

################################################
# MAIN
################################################
project_id=$(gcloud config list --format 'value(core.project)')
if [ -z "${project_id}:-" ]; then
    echo >&2 "To run you must set project for gcloud locally to define target project for the shell host"
    exit 1
fi

# The image deployment service account whose service account key will be renewed by the service-account-review service account
export GCE_IMAGE_DEPLOYMENT_SA="gce-stage3-image-builder@${project_id}.iam.gserviceaccount.com"

# The service-account-review service account which only has roles/iam.serviceAccountKeyAdmin
# it will renew the service account key for image-deployment and itself
echo $PWD

declare -a SA_TO_ROTATE=(
    "runtime-gke@${project_id}.iam.gserviceaccount.com" \
    "gce-stage3-image-builder@${project_id}.iam.gserviceaccount.com" \
    "dmeshpd10-gcs@${project_id}.iam.gserviceaccount.com" \
    "terraform@${project_id}.iam.gserviceaccount.com" \
)

#TODO CLEVER WAY TO STORE /home/<USER>/ServiceAccountKeys
export GCE_IMAGE_DEPLOYMENT_SA_KEY_FILE="/home/<USER>/ServiceAccountKeys/gce-stage3-image-builder.json"

# Key before this date will be deleted
# The requirement from HGMS report is for GCP dev project, keys older than 30 days must be removed while for production, it is older then 60 days
# See rule 34 resourceViolationInvalidServiceAccountKey in here https://alm-confluence.systems.uk.hsbc/confluence/display/GCP/HGMS+Violation+Rules

gcloud iam service-accounts list --format 'table[no-heading](Email:sort=1)'| tr -d '\r\t' | while read SA
do
    
    if [[ " ${SA_TO_ROTATE[@]} " =~ " ${SA} " ]]; then
        echo "${SA}"    
        sh ./initial_setup/rotate_key_for_SA.sh "${SA}" 
    fi
done

chown -R jenbld:jenbld /home/<USER>/ServiceAccountKeys
chmod -R 755 /home/<USER>/ServiceAccountKeys

for i in "${SA_TO_ROTATE[@]}"
do
    echo "$i"
    SA_NAME_FOR_KEY=$(echo "$i" | cut -d @ -f 1)
    echo "$SA_NAME_FOR_KEY"
    SA_KEY_FILE="/home/<USER>/ServiceAccountKeys/$SA_NAME_FOR_KEY.json"
    #Copy newly generated key file to bucket
    gsutil cp ${SA_KEY_FILE} gs://$project_id-key-management/"$SA_NAME_FOR_KEY"/"$SA_NAME_FOR_KEY.json"
    #Copy old key to bucket for backup 
    gsutil cp ${SA_KEY_FILE}-BACKUP gs://$project_id-key-management/"$SA_NAME_FOR_KEY"/"$SA_NAME_FOR_KEY-BACKUP.json"
done