package com.hsbc.changedashboard.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

@JsonIgnoreProperties(ignoreUnknown = true)
public class PersonInfo {
    
    @JsonProperty("role")
    private String role;
    
    @JsonProperty("gender")
    private String gender;
    
    @JsonProperty("mobileNumber")
    private String mobileNumber;
    
    @JsonProperty("givenName")
    private String givenName;
    
    @JsonProperty("emailName")
    private String emailName;
    
    @JsonProperty("bcpTeam")
    private String bcpTeam;
    
    @JsonProperty("team")
    private Team team;
    
    @JsonProperty("url")
    private String url;
    
    @JsonProperty("windowsDomain")
    private String windowsDomain;
    
    @JsonProperty("squarePhotoUrl")
    private String squarePhotoUrl;
    
    @JsonProperty("primaryApplication")
    private String primaryApplication;
    
    @JsonProperty("phoneNumber")
    private String phoneNumber;
    
    @JsonProperty("employeeType")
    private String employeeType;
    
    @JsonProperty("windowsLogin")
    private String windowsLogin;
    
    @JsonProperty("grade")
    private String grade;
    
    @JsonProperty("name")
    private String name;
    
    @JsonProperty("location")
    private String location;
    
    @JsonProperty("staffID")
    private String staffID;
    
    @JsonProperty("email")
    private String email;

    // Constructors
    public PersonInfo() {}

    // Getters and Setters
    public String getRole() { return role; }
    public void setRole(String role) { this.role = role; }

    public String getGender() { return gender; }
    public void setGender(String gender) { this.gender = gender; }

    public String getMobileNumber() { return mobileNumber; }
    public void setMobileNumber(String mobileNumber) { this.mobileNumber = mobileNumber; }

    public String getGivenName() { return givenName; }
    public void setGivenName(String givenName) { this.givenName = givenName; }

    public String getEmailName() { return emailName; }
    public void setEmailName(String emailName) { this.emailName = emailName; }

    public String getBcpTeam() { return bcpTeam; }
    public void setBcpTeam(String bcpTeam) { this.bcpTeam = bcpTeam; }

    public Team getTeam() { return team; }
    public void setTeam(Team team) { this.team = team; }

    public String getUrl() { return url; }
    public void setUrl(String url) { this.url = url; }

    public String getWindowsDomain() { return windowsDomain; }
    public void setWindowsDomain(String windowsDomain) { this.windowsDomain = windowsDomain; }

    public String getSquarePhotoUrl() { return squarePhotoUrl; }
    public void setSquarePhotoUrl(String squarePhotoUrl) { this.squarePhotoUrl = squarePhotoUrl; }

    public String getPrimaryApplication() { return primaryApplication; }
    public void setPrimaryApplication(String primaryApplication) { this.primaryApplication = primaryApplication; }

    public String getPhoneNumber() { return phoneNumber; }
    public void setPhoneNumber(String phoneNumber) { this.phoneNumber = phoneNumber; }

    public String getEmployeeType() { return employeeType; }
    public void setEmployeeType(String employeeType) { this.employeeType = employeeType; }

    public String getWindowsLogin() { return windowsLogin; }
    public void setWindowsLogin(String windowsLogin) { this.windowsLogin = windowsLogin; }

    public String getGrade() { return grade; }
    public void setGrade(String grade) { this.grade = grade; }

    public String getName() { return name; }
    public void setName(String name) { this.name = name; }

    public String getLocation() { return location; }
    public void setLocation(String location) { this.location = location; }

    public String getStaffID() { return staffID; }
    public void setStaffID(String staffID) { this.staffID = staffID; }

    public String getEmail() { return email; }
    public void setEmail(String email) { this.email = email; }

    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Team {
        @JsonProperty("productOwnerEmail")
        private String productOwnerEmail;
        
        @JsonProperty("teambookId")
        private Long teambookId;
        
        @JsonProperty("assetClass")
        private String assetClass;
        
        @JsonProperty("podDescription")
        private String podDescription;
        
        @JsonProperty("url")
        private String url;
        
        @JsonProperty("productOwnerStaffId")
        private String productOwnerStaffId;
        
        @JsonProperty("businessLine")
        private String businessLine;
        
        @JsonProperty("name")
        private String name;
        
        @JsonProperty("symphonyRoom")
        private String symphonyRoom;
        
        @JsonProperty("id")
        private String id;
        
        @JsonProperty("distributionList")
        private String distributionList;
        
        @JsonProperty("productOwner")
        private String productOwner;
        
        @JsonProperty("programme")
        private String programme;

        public Team() {}

        // Getters and Setters for Team
        public String getProductOwnerEmail() { return productOwnerEmail; }
        public void setProductOwnerEmail(String productOwnerEmail) { this.productOwnerEmail = productOwnerEmail; }

        public Long getTeambookId() { return teambookId; }
        public void setTeambookId(Long teambookId) { this.teambookId = teambookId; }

        public String getAssetClass() { return assetClass; }
        public void setAssetClass(String assetClass) { this.assetClass = assetClass; }

        public String getPodDescription() { return podDescription; }
        public void setPodDescription(String podDescription) { this.podDescription = podDescription; }

        public String getUrl() { return url; }
        public void setUrl(String url) { this.url = url; }

        public String getProductOwnerStaffId() { return productOwnerStaffId; }
        public void setProductOwnerStaffId(String productOwnerStaffId) { this.productOwnerStaffId = productOwnerStaffId; }

        public String getBusinessLine() { return businessLine; }
        public void setBusinessLine(String businessLine) { this.businessLine = businessLine; }

        public String getName() { return name; }
        public void setName(String name) { this.name = name; }

        public String getSymphonyRoom() { return symphonyRoom; }
        public void setSymphonyRoom(String symphonyRoom) { this.symphonyRoom = symphonyRoom; }

        public String getId() { return id; }
        public void setId(String id) { this.id = id; }

        public String getDistributionList() { return distributionList; }
        public void setDistributionList(String distributionList) { this.distributionList = distributionList; }

        public String getProductOwner() { return productOwner; }
        public void setProductOwner(String productOwner) { this.productOwner = productOwner; }

        public String getProgramme() { return programme; }
        public void setProgramme(String programme) { this.programme = programme; }
    }
}
