import com.poc.hss.fasttrack.service.LookupService
import com.poc.hss.fasttrack.transform.model.LookupRequest
import com.poc.hss.fasttrack.transform.model.TransformerRecord
import io.vertx.core.json.JsonObject
import org.apache.logging.log4j.util.TriConsumer
import org.slf4j.Logger
import org.slf4j.LoggerFactory

import java.util.function.BiConsumer
import java.util.stream.Collectors

class UdmGcsLocation {

    private static final Logger logger = LoggerFactory.getLogger(UdmGcsLocation.class)

    static BiConsumer<List<TransformerRecord>, LookupService> queryAndBlendLocationData = (targetData, lookupService) -> {
        logger.info("Start Blending location data")
        String locationIds = getPlaceOfSafekeepingOrLocationIdsForLookup(targetData, lookupService)

        if (locationIds != null && locationIds.isEmpty()) {
            logger.info("Skipping location lookup request as placeOfSafekeeping and location Ids are empty or respective Location records are not exist.")
            return
        }
        String criteria = String.format("%s in (%s)", UdmGcsTransactionConstant._ID, locationIds)
        logger.debug("locationLookupRequest criteria: " + criteria)

        LookupRequest locationLookupRequest = UdmGcsTransactionHelper.createLookupRequest(criteria, UdmGcsTransactionConstant.GCS_LOCATION, UdmGcsTransactionConstant.LOCATION_FIELDS)

        Map<String, JsonObject> locationMap = lookupService.queryList(locationLookupRequest).stream()
                .collect(Collectors.toMap(json -> json.getString(UdmGcsTransactionConstant._ID), json -> json, (a, b) -> a))
        if (null == locationMap || locationMap.isEmpty()) {
            logger.info("locationLookupRequest failed or no record for Criteria : [" + criteria + "]")
            return
        }
        logger.debug("locationLookupRequest result for Criteria " + criteria + " is " + locationMap.toString())
        blendLocationData.accept(targetData, locationMap)
    }

    static BiConsumer<List<TransformerRecord>, Map<String, JsonObject>> blendLocationData = (targetData, locationMap) -> {
        targetData.forEach(record -> {
            String locationId = record.getData().getString(UdmGcsTransactionConstant.LOCATION_ID)
            String placeOfSafekeeping = record.getData().getString(UdmGcsTransactionConstant.PLACE_OF_SAFEKEEPING)
            if (locationMap.containsKey(placeOfSafekeeping) || locationMap.containsKey(locationId)) {
                JsonObject location = locationMap.get(locationId)
                locationDataMapping(location, record.getData())
            }
        })
    }

    static String getPlaceOfSafekeepingOrLocationIdsForLookup(List<TransformerRecord> targetData, LookupService lookupService) {
        return targetData.stream()
                .map(rec -> {
                    String placeOfSafekeeping = rec.getData().getString(UdmGcsTransactionConstant.PLACE_OF_SAFEKEEPING)?.trim()
                    String tradeId = rec.id
                    if (placeOfSafekeeping != null && !placeOfSafekeeping.isBlank()) {
                        String placeOfSafekeepingParam = placeOfSafekeeping.substring(0, 3)
                        String whereCondition = String.format("%s in (%s)", UdmGcsTransactionConstant._ID, String.format("'%s'", placeOfSafekeepingParam))
                        LookupRequest placeOfSafekeepingLookupRequest = UdmGcsTransactionHelper.createLookupRequest(whereCondition, UdmGcsTransactionConstant.GCS_LOCATION, UdmGcsTransactionConstant.LOCATION_FIELDS)
                        List<JsonObject> placeOfSafekeepingResultList = lookupService.queryList(placeOfSafekeepingLookupRequest)
                        logger.debug("TradeId:{}, placeOfSafekeepingRequest: {}, Result Size: {}", tradeId, placeOfSafekeepingLookupRequest.toString(), placeOfSafekeepingResultList.size())
                        if (placeOfSafekeepingResultList.size() > 0) {
                            return placeOfSafekeepingParam
                        }
                    }
                    logger.debug("TradeId:{}, placeOfSafekeeping: {} is either empty or no record exist in Location. returning location code : {} for lookup", tradeId, placeOfSafekeeping, rec.getData().getString(UdmGcsTransactionConstant.LOCATION_ID))
                    return rec.getData().getString(UdmGcsTransactionConstant.LOCATION_ID)
                })
                .map(id -> "'" + id + "'")
                .distinct()
                .collect(Collectors.joining(","))
    }

    static void locationDataMapping(JsonObject location, JsonObject output) {
        output.put("place_of_safekeeping_country_code", location?.getString("place_of_safekeeping_country_code")?:"")
        output.put("place_of_safekeeping_name", location?.getString("place_of_safekeeping_name")?:"")
    }

    static BiConsumer<List<TransformerRecord>, LookupService> queryAndBlendLocationDataForHolding = (targetData, lookupService) -> {
        logger.info("Start Blending location data for udm gcs holding");
        String locationIds = targetData.stream()
                .filter(rec -> rec.getData().getString(UdmGcsTransactionConstant.LOCATION_ID) != null || rec.getData().getString(UdmGcsTransactionConstant.LOCATION_ID) != "")
                .map(rec -> rec.getData().getString(UdmGcsTransactionConstant.LOCATION_ID))
                .map(id -> "'" + id + "'")
                .distinct()
                .collect(Collectors.joining(","))

        if (locationIds.isEmpty()) {
            logger.info("Skipping location lookup request as location ids are empty or respective trade records are not exist.")
            return
        }

        String criteria = String.format(UdmGcsTransactionConstant._ID + " in (%s)", locationIds)
        logger.debug("locationLookupRequest criteria: " + criteria)

        LookupRequest locationLookupRequest = UdmGcsTransactionHelper.createLookupRequest(criteria, UdmGcsTransactionConstant.GCS_LOCATION, UdmGcsTransactionConstant.LOCATION_FIELDS)

        Map<String, JsonObject> locationMap = lookupService.queryList(locationLookupRequest).stream()
                .collect(Collectors.toMap(json -> json.getString(UdmGcsTransactionConstant._ID), json -> json, (a, b) -> a))
        if (null == locationMap || locationMap.isEmpty()) {
            logger.info("locationLookupRequest failed or no record for Criteria : [" + criteria + "]")
            return
        }
        logger.debug("locationLookupRequest result for Criteria " + criteria + " is \n" + locationMap.toString())
        blendLocationData.accept(targetData, locationMap)
    }
}