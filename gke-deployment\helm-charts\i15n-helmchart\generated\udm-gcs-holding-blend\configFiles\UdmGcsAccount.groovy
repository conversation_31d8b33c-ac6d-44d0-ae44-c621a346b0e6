import com.poc.hss.fasttrack.service.LookupService
import com.poc.hss.fasttrack.transform.model.LookupRequest
import com.poc.hss.fasttrack.transform.model.TransformerRecord
import io.vertx.core.json.JsonObject
import org.slf4j.Logger
import org.slf4j.LoggerFactory

import java.util.function.BiConsumer
import java.util.stream.Collectors

class UdmGcsAccount {
    private static final Logger logger = LoggerFactory.getLogger(UdmGcsAccount.class);

    static BiConsumer<List<TransformerRecord>, LookupService> queryAndBlendAccountData = (targetData, lookupService) -> {
        logger.info("Start Blending Account data");
        String accountIds = targetData.stream()
                .filter(record -> record.getData().getString(UdmGcsTransactionConstant.ACCOUNT_ID) != null)
                .map(record -> record.getData().getString(UdmGcsTransactionConstant.ACCOUNT_ID))
                .map(accNo -> "'" + accNo + "'")
                .distinct()
                .collect(Collectors.joining(","))
        if (accountIds.isEmpty()) {
            logger.info("Skipping account lookup request as account ids are empty or respective trade records are not exist.")
            return
        }

        String criteria = String.format(UdmGcsTransactionConstant._ID + " in (%s)", accountIds)
        logger.debug("account lookup request criteria " + criteria)

        LookupRequest accountLookupRequest = UdmGcsTransactionHelper.createLookupRequest(criteria, UdmGcsTransactionConstant.GCS_ACCOUNT, UdmGcsTransactionConstant.ACCOUNT_FIELDS)
        Map<String, JsonObject> accountMap = lookupService.queryList(accountLookupRequest).stream()
                .collect(Collectors.toMap(json -> json.getString(UdmGcsTransactionConstant._ID), json -> json, (a, b) -> a))
        if (null == accountMap || accountMap.isEmpty()) {
            logger.debug("account lookup request failed or no record for Criteria : " + criteria)
            return
        }
        logger.debug("account lookup request result for Criteria " + criteria + " is \n" + accountMap.toString())
        blendAccount.accept(targetData, accountMap)
    }

    static BiConsumer<List<TransformerRecord>, Map<String, JsonObject>> blendAccount = (targetData, accountMap) -> {
        targetData.forEach(record -> {
            String accountId = record.getData().getString(UdmGcsTransactionConstant.ACCOUNT_ID)
            if (accountMap.containsKey(accountId)) {
                JsonObject account = accountMap.get(accountId)
                accountDataMapping(account, record.getData())
            }
        })
    }

    static void accountDataMapping(JsonObject account, JsonObject output) {
        output.put("account_region", account.getString("acc_region"))
        output.put("account_name", account.getString("acc_short_name"))
        output.put("acc_currency_code", account.getString("acc_currency_code"))
        output.put("acc_currency_role", account.getString("acc_currency_role"))
        output.put("acc_currency_name", account.getString("acc_currency_name"))
        output.put("hbfr_acc_indicator", account.getString("hbfr_acc_indicator"))
        output.put("legal_entity_sub_fund_indicator", account.getString("legal_entity_sub_fund_indicator"))
        output.put("legal_entity_id", account.getString("legal_entity_id"))
    }
}