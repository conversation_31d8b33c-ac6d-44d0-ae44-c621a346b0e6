#!/usr/bin/env bash
set -euo pipefail

echo "[MGMT_HOST] Started installating Docker version: $DOCKER_VERSION in /usr/bin. build_path: $BUILD_PATH"

#copying docker binaries to docker path
cd $BUILD_PATH/build_tools/
#wget --user=${NEXUS_CREDS_USR} --password=${NEXUS_CREDS_PWD} "https://gbmt-nexus.prd.fx.gbm.cloud.uk.hsbc/repository/maven-release/com/hsbc/mss/surv/comms/binaries/docker/20.10.18/docker-20.10.18.tgz"
wget --user=${NEXUS_CREDS_USR} --password=${NEXUS_CREDS_PWD} "https://hkl20090861.hc.cloud.hk.hsbc/devops/$DOCKER_VERSION"
sudo tar -xzvf $DOCKER_VERSION
sudo rm $DOCKER_VERSION
sudo chown root:root -R docker
sudo mv docker/* $DOCKER_PATH
echo "[MGMT_HOST] unzippped and removed docker tar file & moved docker to docker path $DOCKER_PATH"

echo "[MGMT_HOST]trying to start docker deamon"
#sudo dockerd &
sleep 10
#ps aux | grep dockerd
echo "[MGMT_HOST]docker deamon started successfully"

#docker info
echo "[MGMT_HOST] docker installation completed"
