{{- if and .Values.rbac.create (not .Values.rbac.namespaced) }}
kind: ClusterRoleBinding
apiVersion: rbac.authorization.k8s.io/v1
metadata:
  name: {{ template "grafana.fullname" . }}-clusterrolebinding
  labels:
    {{- include "grafana.labels" . | nindent 4 }}
{{- with .Values.annotations }}
  annotations:
{{ toYaml . | indent 4 }}
{{- end }}
subjects:
  - kind: ServiceAccount
    name: {{ template "grafana.serviceAccountName" . }}
    namespace: {{ template "grafana.namespace" . }}
roleRef:
  kind: ClusterRole
{{- if (not .Values.rbac.useExistingRole) }}
  name: {{ template "grafana.fullname" . }}-clusterrole
{{- else }}
  name: {{ .Values.rbac.useExistingRole }}
{{- end }}
  apiGroup: rbac.authorization.k8s.io
{{- end -}}
