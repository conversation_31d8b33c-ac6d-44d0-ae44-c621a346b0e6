import com.poc.hss.fasttrack.service.LookupService
import com.poc.hss.fasttrack.transform.model.LookupRequest
import com.poc.hss.fasttrack.transform.model.TransformerRecord
import io.vertx.core.json.JsonObject
import org.slf4j.Logger
import org.slf4j.LoggerFactory

import java.util.function.BiConsumer
import java.util.stream.Collectors

class UdmGcsClient{
    private static final Logger logger = LoggerFactory.getLogger(UdmGcsClient.class);

    static BiConsumer<List<TransformerRecord>, LookupService> queryAndBlendClientData = (targetData, lookupService) -> {
        logger.info("Start Blending Client data")
        String clientIds = targetData.stream()
                .filter(record -> record.getData().getString(UdmGcsTransactionConstant.LEGAL_ENTITY_ID) != null)
                .filter(record -> record.getData().getString(UdmGcsTransactionConstant.LEGAL_ENTITY_SUB_FUND_INDICATOR) != null)
                .filter(record -> record.getData().getString(UdmGcsTransactionConstant.LEGAL_ENTITY_ID).trim() != "")
                .filter(record -> record.getData().getString(UdmGcsTransactionConstant.LEGAL_ENTITY_SUB_FUND_INDICATOR).trim() != "")
                .map(record -> {
                    String legalEntityId = record.getData().getString(UdmGcsTransactionConstant.LEGAL_ENTITY_ID)?.trim()
                    String subFundIndicator =record.getData().getString(UdmGcsTransactionConstant.LEGAL_ENTITY_SUB_FUND_INDICATOR)?.trim()
                    String clientId = String.format("%s-%s", legalEntityId,subFundIndicator)
                    return clientId
                })
                .map(id -> "'" + id + "'")
                .distinct()
                .collect(Collectors.joining(","))

        if (clientIds.isEmpty()) {
            logger.info("Skipping clint lookup request as instrument price ids are empty or respective trade records are not exist.")
            return
        }

        String criteria = String.format(UdmGcsTransactionConstant._ID + " in (%s)", clientIds)
        logger.debug("Client lookup request criteria [" + criteria + "]")

        LookupRequest clientLookupRequest = UdmGcsTransactionHelper.createLookupRequest(criteria, UdmGcsTransactionConstant.GCS_CLIENT, UdmGcsTransactionConstant.CLIENT_FIELDS)

        Map<String, JsonObject> clientMap = lookupService.queryList(clientLookupRequest).stream()
                .collect(Collectors.toMap(json -> json.getString(UdmGcsTransactionConstant._ID), json -> json, (a, b) -> a))
        if (null == clientMap || clientMap.isEmpty()) {
            logger.debug("client lookup request failed or no record for Criteria : " + criteria)
            return
        }
        logger.debug("client lookup request result for Criteria " + criteria + " is \n" + clientMap.toString())
        blendClient.accept(targetData, clientMap)

    }

    static BiConsumer<List<TransformerRecord>, Map<String, JsonObject>> blendClient = (targetData, clientMap) -> {
        targetData.forEach(record -> {
            String legalEntityId = record.getData().getString(UdmGcsTransactionConstant.LEGAL_ENTITY_ID)?.trim()
            String subFundIndicator =record.getData().getString(UdmGcsTransactionConstant.LEGAL_ENTITY_SUB_FUND_INDICATOR)?.trim()
            if(legalEntityId != null && !legalEntityId.isEmpty() && subFundIndicator != null && !subFundIndicator.isEmpty()){
                String clientId = String.format("%s-%s", legalEntityId,subFundIndicator)
                if (clientMap.containsKey(clientId)) {
                    JsonObject client = clientMap.get(clientId)
                    clientDataMapping(client, record.getData())
                }
            }
        })
    }

    static void clientDataMapping(JsonObject client, JsonObject output) {
        output.put("client_service_location_code", client.getString("client_service_location_code"))
    }
}