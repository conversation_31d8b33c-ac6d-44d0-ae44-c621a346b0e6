json_data_path = ""
sql_proxy_zone = "asia-east2-c"
db_version = "POSTGRES_14"

multi_instance_sql_data = [
  {
    db_instance_name : "inm-prod-projection-52e12a",
    db_tier : "db-custom-2-8192",
    master_instance_name: null,
    db_databases : ["pipeline_data"],
    database_user_names : [
      { "username" : "<EMAIL>", "type" : "CLOUD_IAM_SERVICE_ACCOUNT" },
      { "username" : "<EMAIL>", "type" : "CLOUD_IAM_SERVICE_ACCOUNT" }
    ],
    delete_protection : false,
    retention_log_time : 1
    maintenance_window_day : 6 
    # UTC Time
    maintenance_window_hour : 2
    query_insights_enabled: true
  }
]