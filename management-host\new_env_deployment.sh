#!/usr/bin/env bash
set -euo pipefail

#please pass the iput vale either dev or prod like sh ./initial_setup/create_gcebucket dev
profile_env=$1
mig_or_not=$2

export GOOGLE_APPLICATION_CREDENTIALS="/c/Users/<USER>/hsbc-9087302-unity-prod.json"
export HTTPS_PROXY="http://googleapis-prod.gcp.cloud.hk.hsbc:3128"


echo $GOOGLE_APPLICATION_CREDENTIALS

echo "[MGMT_HOST] checking for default conf"
if [ ! -f "./initial_setup/var-${profile_env}.conf" ]; then
 echo "Parameter file var-${profile_env}.conf not found in initial_setup."
 exit 1
fi

source ./initial_setup/var-${profile_env}.conf

sh ./initial_setup/generate_sa_key.sh  ${profile_env}

echo "[GSUTIL] New Service account keys created secrets"

# sh ./initial_setup/create_gcsbucket.sh  ${profile_env}

echo "[GSUTIL] Bucket Created with secrets"

#run packer to do build the mgmt host
#packer build -timestamp-ui -on-error=cleanup -var-file=variables.json image.json

if [ ${profile_env} == dev ]
    then
        packer build -timestamp-ui -on-error=cleanup -var "BUILDSTAGE3=false" -var "STAGE=stage2" -var "ENVIRONMENT=uat"  -var "STASH_TOKEN=fee74e5f6e7601eaa27e0275093060236e33d15c" -var "NEXUS_CREDS_USR=GB-SVCAT-HKLXP" -var "NEXUS_CREDS_PWD=k84748UNuSwJtr" -var-file=./uat/variables.json ./uat/image.json

        echo "[PACKER] Image ${STAGE} for env ${profile_env} has been created"
    fi

if [${mig_or_not} == yes]
then
    sh ./initial_setup/create_template.sh ${profile_env}

    echo "[GCLOUD] Template created"

    sh ./initial_setup/create_mig.sh ${profile_env}
else
    sh ./initial_setup/create_mgmt_host.sh ${profile_env}
fi