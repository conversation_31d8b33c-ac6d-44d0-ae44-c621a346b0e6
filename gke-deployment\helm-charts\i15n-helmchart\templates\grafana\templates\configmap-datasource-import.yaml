{{- $files := .Files }}
{{- $target := printf "grafana/datasource/plaintext/*"}}
{{- range tuple $target "datasource/plaintext/*"}}
{{- range $file, $_ :=  $files.Glob . }}
apiVersion: v1
kind: ConfigMap
metadata:
  name: grafana-config-{{$file|base|replace "." "-"|replace "-yaml" ""|lower}}
  namespace: {{ $.Release.Namespace  }}
  labels:
    grafana_datasource: "1"
    {{- include "grafana.labels" $ | nindent 4 }}
data:
  {{$file|base}}: |{{ tpl ($files.Get $file) $|nindent 4}}
---
{{- end}}
{{- end}}
