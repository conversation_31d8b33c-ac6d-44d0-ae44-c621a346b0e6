FROM nexus3.systems.uk.hsbc:18096/com/hsbc/group/itid/es/dc/ubuntu/gcr-ubuntu-2404:latest
LABEL maintainer="Unity2.0 core engineering <<EMAIL>>"

RUN --mount=type=secret,id=aptconf,target=/etc/apt/auth.conf \
apt-get update \
&& apt-get install -y vim \
&& apt-get install -y less \
&& apt-get install -yqq inetutils-ping \
&& apt-get install -y net-tools \
&& apt-get install -y jq \
&& apt-get install -y nmap \
&& apt-get install -y zip \
&& apt-get install -y wget \
&& apt-get install -y curl \
&& rm -rf /var/lib/apt/lists/*

ARG artifacts_repo=https://hkl20090861.hc.cloud.hk.hsbc/devops
ARG artifact=grafana-enterprise-12.0.1.linux-amd64.tar.gz

# Reference Grafana offical dockerfile
# https://github.com/grafana/grafana/blob/main/packaging/docker/Dockerfile

ARG GF_UID="472"
ARG GF_GID="0"

ENV PATH=/usr/share/grafana/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin \
    GF_PATHS_CONFIG="/etc/grafana/grafana.ini" \
    GF_PATHS_DATA="/var/lib/grafana" \
    GF_PATHS_HOME="/usr/share/grafana" \
    GF_PATHS_LOGS="/var/log/grafana" \
    GF_PATHS_PLUGINS="/var/lib/grafana/plugins" \
    GF_PATHS_PROVISIONING="/etc/grafana/provisioning"

WORKDIR $GF_PATHS_HOME

# Download image from HSBC repo
RUN curl ${artifacts_repo}/${artifact} -o /tmp/grafana.tar.gz && \
    tar xzf /tmp/grafana.tar.gz --strip-components=1 -C $GF_PATHS_HOME && \
    rm -f /tmp/grafana.tar.gz

RUN if [ ! $(getent group "$GF_GID") ]; then \
      addgroup -S -g $GF_GID grafana; \
    fi

COPY ./run.sh /run.sh

RUN export GF_GID_NAME=$(getent group $GF_GID | cut -d':' -f1) && \
    mkdir -p "$GF_PATHS_HOME/.aws" && \
    adduser --system --uid $GF_UID --ingroup "$GF_GID_NAME" grafana && \
    mkdir -p "$GF_PATHS_PROVISIONING/datasources" \
             "$GF_PATHS_PROVISIONING/dashboards" \
             "$GF_PATHS_PROVISIONING/notifiers" \
             "$GF_PATHS_PROVISIONING/plugins" \
             "$GF_PATHS_PROVISIONING/access-control" \
             "$GF_PATHS_LOGS" \
             "$GF_PATHS_PLUGINS" \
             "$GF_PATHS_DATA" && \
    cp "$GF_PATHS_HOME/conf/sample.ini" "$GF_PATHS_CONFIG" && \
    cp "$GF_PATHS_HOME/conf/ldap.toml" /etc/grafana/ldap.toml && \
    chown -R "grafana:$GF_GID_NAME" "$GF_PATHS_DATA" "$GF_PATHS_HOME/.aws" "$GF_PATHS_LOGS" "$GF_PATHS_PLUGINS" "$GF_PATHS_PROVISIONING" /run.sh  && \
    chmod -R 777 "$GF_PATHS_DATA" "$GF_PATHS_HOME/.aws" "$GF_PATHS_LOGS" "$GF_PATHS_PLUGINS" "$GF_PATHS_PROVISIONING" && \
    mkdir /tmp/grafana-plugin && \
    cd /tmp/grafana-plugin && \
    wget -r -nd -nH --cut-dirs=2 -l1 -A "*.zip" ${artifacts_repo}/grafana-plugin && \
    ls *.zip | xargs -I{} unzip {} -d $GF_PATHS_PROVISIONING/plugins/ && \
    chown grafana $GF_PATHS_PROVISIONING/plugins && \
    ls -l $GF_PATHS_PROVISIONING/plugins/ && \
    rm -Rf /tmp/grafana-plugin

EXPOSE 3000

USER "$GF_UID"
ENTRYPOINT [ "/run.sh" ]
