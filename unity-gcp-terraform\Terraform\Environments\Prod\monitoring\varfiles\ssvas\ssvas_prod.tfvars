key               = "gcp"
unit              = "1"
label_value_type  = "STRING"
label_description = "metric for gcp instance"
api_key           = "ssvas-prod-key"
team_name         = "ssvas-prod"

channel_recipients = {
    display_name  = "ssvas_Prod_Monitoring_recipients"
    type = "webhook_basicauth"
    username = "BC000010001"
    url = "https://hap-api.hsbc.co.uk/api/sendAlert?@aboutSource=GCP"
    password = "DEWQ42@fgwer23"
}

//TODO: fill in
logs                = {
  SSV_AS_PROD_proxymity_holding_api_proxy_voting_au = {
    name                = "ssvas_Prod_Proxymity_Holding_API_Proxy_Voting_AU"
    filter              = "resource.type:\"k8s_container\" AND jsonPayload.message: \"Proxymity holding PV response for site au\" AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type          = "INT64"
    unit                = "1"
    display_name        = "SSV AS PROXYMITY HOLDINGS API VOLUME - Total proxymity holding request for proxy voting"
    labels              = []
    label_extractors    = {}
  },
  SSV_AS_PROD_proxymity_holding_api_proxy_voting_nz = {
    name                = "ssvas_Prod_Proxymity_Holding_API_Proxy_Voting_NZ"
    filter              = "resource.type:\"k8s_container\" AND jsonPayload.message: \"Proxymity holding PV response for site nz\" AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type          = "INT64"
    unit                = "1"
    display_name        = "SSV AS PROXYMITY HOLDINGS API VOLUME - Total proxymity holding request for proxy voting"
    labels              = []
    label_extractors    = {}
  },
  SSV_AS_PROD_proxymity_holding_api_proxy_voting_hk = {
    name                = "ssvas_Prod_Proxymity_Holding_API_Proxy_Voting_HK"
    filter              = "resource.type:\"k8s_container\" AND jsonPayload.message: \"Proxymity holding PV response for site hk\" AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type          = "INT64"
    unit                = "1"
    display_name        = "SSV AS PROXYMITY HOLDINGS API VOLUME - Total proxymity holding request for proxy voting"
    labels              = []
    label_extractors    = {}
  },
  SSV_AS_PROD_proxymity_holder_api_proxy_voting_au = {
    name                = "ssvas_Prod_Proxymity_Holder_API_Proxy_Voting_AU"
    filter              = "resource.type:\"k8s_container\" AND jsonPayload.message: \"Proxymity holder PV response for site au\" AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type          = "INT64"
    unit                = "1"
    display_name        = "SSV AS PROXYMITY HOLDINGS API VOLUME - Total proxymity holding request for proxy voting"
    labels              = []
    label_extractors    = {}
  },
  SSV_AS_PROD_proxymity_holder_api_proxy_voting_nz = {
      name                = "ssvas_Prod_Proxymity_Holder_API_Proxy_Voting_NZ"
      filter              = "resource.type:\"k8s_container\" AND jsonPayload.message: \"Proxymity holder PV response for site nz\" AND resource.labels.namespace_name: \""
      metric_kind         = "DELTA"
      value_type          = "INT64"
      unit                = "1"
      display_name        = "SSV AS PROXYMITY HOLDINGS API VOLUME - Total proxymity holding request for proxy voting"
      labels              = []
      label_extractors    = {}
  },
  SSV_AS_PROD_proxymity_holder_api_proxy_voting_hk = {
      name                = "ssvas_Prod_Proxymity_Holder_API_Proxy_Voting_HK"
      filter              = "resource.type:\"k8s_container\" AND jsonPayload.message: \"Proxymity holder PV response for site hk\" AND resource.labels.namespace_name: \""
      metric_kind         = "DELTA"
      value_type          = "INT64"
      unit                = "1"
      display_name        = "SSV AS PROXYMITY HOLDINGS API VOLUME - Total proxymity holding request for proxy voting"
      labels              = []
      label_extractors    = {}
  },
  SSV_AS_PROD_proxymity_holding_api_proxy_voting = {
    name                = "ssvas_Prod_Proxymity_Holding_API_Proxy_Voting"
    filter              = "resource.type:\"k8s_container\" AND jsonPayload.message: \"Received proxymity holding request with intent P\" AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type          = "INT64"
    unit                = "1"
    display_name        = "SSV AS PROXYMITY HOLDINGS API VOLUME - Total proxymity holding request for proxy voting"
    labels              = []
    label_extractors    = {}
  },
  SSV_AS_PROD_proxymity_holding_api_disclosure_au = {
    name                = "ssvas_Prod_Proxymity_Holding_API_Disclosure_AU"
    filter              = "resource.type:\"k8s_container\" AND jsonPayload.message: \"Received proxymity holding request with intent D and intermediaryId HKBAAU2S\" AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type          = "INT64"
    unit                = "1"
    display_name        = "SSV AS PROXYMITY HOLDINGS API VOLUME - Received proxymity holding request for disclosure AU"
    labels              = []
    label_extractors    = {}
  },
  SSV_AS_PROD_proxymity_holder_api_proxy_voting = {
    name                = "ssvas_Prod_Proxymity_Holder_API_Proxy_Voting"
    filter              = "resource.type:\"k8s_container\" AND jsonPayload.message: \"Received proxymity holder request with intent P\" AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type          = "INT64"
    unit                = "1"
    display_name        = "SSV AS PROXYMITY HOLDERS API VOLUME - Total proxymity holder request for proxy voting"
    labels              = []
    label_extractors    = {}
  },
  SSV_AS_PROD_proxymity_holder_api_disclosure_au = {
    name                = "ssvas_Prod_Proxymity_Holder_API_Disclosure_AU"
    filter              = "resource.type:\"k8s_container\" AND jsonPayload.message: \"Received proxymity holder request with intent D and intermediaryId HKBAAU2S\" AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type          = "INT64"
    unit                = "1"
    display_name        = "SSV AS PROXYMITY HOLDERS API VOLUME - Received proxymity holder request for disclosure AU"
    labels              = []
    label_extractors    = {}
  },
  SSV_AS_PROD_gcuk_proxymity_holdings_api_volume_PH = {
    name                = "SSV_AS_PROD_HoldingApi_Prod_GCUK_Proxymity_Holdings_Api_Volume_PH"
    filter              = "resource.type:\"k8s_container\" AND jsonPayload.message: \"Received GCS proxymity holding GCUK market request\" AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type           = "INT64"
    unit                = "1"
    display_name        = "GCUK PROXYMITY HOLDINGS API VOLUME PH - Received GCS proxymity holding GCUK market request"
    labels              = []
    label_extractors    = {}
  },
  SSV_AS_PROD_gcuk_proxymity_holders_api_volume_PH = {
    name                = "SSV_AS_PROD_HoldingApi_Prod_GCUK_Proxymity_Holders_Api_Volume_PH"
    filter              = "resource.type:\"k8s_container\" AND jsonPayload.message: \"Received GCS proxymity holder GCUK market request\" AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type           = "INT64"
    unit                = "1"
    display_name        = "GCUK PROXYMITY HOLDERS API VOLUME PH - Received GCS proxymity holder GCUK market request"
    labels              = []
    label_extractors    = {}
  },
  SSV_AS_PROD_gcuk_proxymity_holdings_api_response_time_over_thirty_sec_volume_PH = {
    name                = "SSV_AS_PROD_HoldingApi_Prod_GCUK_Proxymity_Holdings_Api_Response_Time_Over_Thirty_Sec_Volume_PH"
    filter              = "resource.type:\"k8s_container\" AND jsonPayload.message: \"Proxymity holding gcs GCUK market request processing took longer than 30 seconds\" AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type           = "INT64"
    unit                = "1"
    display_name        = "GCUK PROXYMITY HOLDINGS API VOLUME PH - Proxymity holding gcs GCUK market request processing took longer than 30 seconds"
    labels              = []
    label_extractors    = {}
  },
  SSV_AS_PROD_gcuk_proxymity_holders_api_response_time_over_thirty_sec_volume_PH = {
    name                = "SSV_AS_PROD_HoldingApi_Prod_GCUK_Proxymity_Holders_Api_Response_Time_Over_Thirty_Sec_Volume_PH"
    filter              = "resource.type:\"k8s_container\" AND jsonPayload.message: \"GCS Proxymity holder GCUK market request processing took longer than 30 seconds\" AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type           = "INT64"
    unit                = "1"
    display_name        = "GCUK PROXYMITY HOLDERS API VOLUME PH - GCS Proxymity holder GCUK market request processing took longer than 30 seconds"
    labels              = []
    label_extractors    = {}
  },
  SSV_AS_PROD_gce_proxymity_holdings_api_volume_PH = {
    name                = "SSV_AS_PROD_HoldingApi_Prod_GCE_Proxymity_Holdings_Api_Volume_PH"
    filter              = "resource.type:\"k8s_container\" AND jsonPayload.message: \"Received GCS proxymity holding GCE market request\" AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type           = "INT64"
    unit                = "1"
    display_name        = "GCE PROXYMITY HOLDINGS API VOLUME PH - Received GCS proxymity holding GCE market request"
    labels              = []
    label_extractors    = {}
  },
  SSV_AS_PROD_gce_proxymity_holders_api_volume_PH = {
    name                = "SSV_AS_PROD_HoldingApi_Prod_GCE_Proxymity_Holders_Api_Volume_PH"
    filter              = "resource.type:\"k8s_container\" AND jsonPayload.message: \"Received GCS proxymity holder GCE market request\" AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type           = "INT64"
    unit                = "1"
    display_name        = "GCE PROXYMITY HOLDERS API VOLUME PH - Received GCS proxymity holder GCE market request"
    labels              = []
    label_extractors    = {}
  },
  SSV_AS_PROD_gce_proxymity_holdings_api_response_time_over_thirty_sec_volume_PH = {
    name                = "SSV_AS_PROD_HoldingApi_Prod_GCE_Proxymity_Holdings_Api_Response_Time_Over_Thirty_Sec_Volume_PH"
    filter              = "resource.type:\"k8s_container\" AND jsonPayload.message: \"Proxymity holding gcs GCE market request processing took longer than 30 seconds\" AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type           = "INT64"
    unit                = "1"
    display_name        = "GCE PROXYMITY HOLDINGS API VOLUME PH - Proxymity holding gcs GCE market request processing took longer than 30 seconds"
    labels              = []
    label_extractors    = {}
  },
  SSV_AS_PROD_gce_proxymity_holders_api_response_time_over_thirty_sec_volume_PH = {
    name                = "SSV_AS_PROD_HoldingApi_Prod_GCE_Proxymity_Holders_Api_Response_Time_Over_Thirty_Sec_Volume_PH"
    filter              = "resource.type:\"k8s_container\" AND jsonPayload.message: \"GCS Proxymity holder GCE market request processing took longer than 30 seconds\" AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type           = "INT64"
    unit                = "1"
    display_name        = "GCE PROXYMITY HOLDERS API VOLUME PH - GCS Proxymity holder GCE market request processing took longer than 30 seconds"
    labels              = []
    label_extractors    = {}
  },
  custom_api_err = {
    name                = "ssvas_Prod_Custom_API_internal_error"
    filter              = "resource.type:\"k8s_container\" AND jsonPayload.message: \"UNY2ERR500\" and resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type          = "INT64"
    unit                = "1"
    display_name        = "Custom API internal error - Unexcepted error was raised during the invocation of a custom API"
    labels              = []
    label_extractors    = {}
  },
  pipeline_app_err = {
    name                = "ssvas_Prod_Pipeline_application_fatal_error"
    filter              = "resource.type:\"k8s_container\" AND jsonPayload.message: \"UNY2ERR999\" and resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type          = "INT64"
    unit                = "1"
    display_name        = "Unexpected error has caused the Unity 2.0 ETL pipelines to fail - container would be restarted"
    labels              = []
    label_extractors    = {}
  },
  SSV_AS_PROD_proxymity_holdings_api_volume = {
    name                = "SSV_AS_PROD_Proxymity_Holdings_API_Volume"
    filter              = "resource.type:\"k8s_container\" AND jsonPayload.message: \"Received proxymity holding request\" AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type          = "INT64"
    unit                = "1"
    display_name        = "SSV AS PROXYMITY HOLDINGS API VOLUME - Received proxymity holding request"
    labels              = []
    label_extractors    = {}
  },
  SSV_AS_PROD_proxymity_holders_api_volume = {
    name                = "SSV_AS_PROD_Proxymity_Holders_Api_Volume"
    filter              = "resource.type:\"k8s_container\" AND jsonPayload.message: \"Received proxymity holder request\" AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type          = "INT64"
    unit                = "1"
    display_name        = "SSV AS PROXYMITY HOLDERS API VOLUME - Received proxymity holder request"
    labels              = []
    label_extractors    = {}
  },
  SSV_AS_PROD_proxymity_holdings_api_response_time_over_thirty_sec = {
    name                = "SSV_AS_PROD_Proxymity_Holding_API_Response_Time_Over_Thirty_Sec"
    filter              = "resource.type:\"k8s_container\" AND jsonPayload.message: \"Proxymity holding request processing took longer than 30 seconds\" AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type          = "INT64"
    unit                = "1"
    display_name        = "SSV AS PROXYMITY HOLDINGS API VOLUME - Proxymity holding request processing took longer than 30 seconds"
    labels              = []
    label_extractors    = {}
  },
  SSV_AS_PROD_proxymity_holders_api_response_time_over_thirty_sec = {
    name                = "SSV_AS_PROD_Proxymity_Holders_Api_Response_Time_Over_Thirty_Sec"
    filter              = "resource.type:\"k8s_container\" AND jsonPayload.message: \"Proxymity holder request processing took longer than 30 seconds\" AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type          = "INT64"
    unit                = "1"
    display_name        = "SSV AS PROXYMITY HOLDERS API VOLUME - Proxymity holder request processing took longer than 30 seconds"
    labels              = []
    label_extractors    = {}
  }
}

metrics = {
  Sql_Connections = {
    name                = "SQL Connections - Google Cloud CloudSQl PostGreSQL - Connections"
    object_name          = "sqlcon"
    alert_severity       = "WARNING"
    combiner            = "OR"
    filter              = "metric.type=\"cloudsql.googleapis.com/database/network/connections\" AND resource.type=\"cloudsql_database\" AND resource.label.database_id=has_substring(\""
    duration            = "120s"
    comparison          = "COMPARISON_GT"
    cross_series_reducer= ""
    alignment_period    = "120s"
    per_series_aligner  = "ALIGN_MAX"
    threshold           = 20
    trigger_count       = 1
    group_by_fields     =  ["resource.label.database_id"]
    api_key             = ""
    enabled             = true
  },
  Sql_cpu_utilization = {
    name                = "SQL CPU Utilization - Google Cloud CloudSQl PostGreSQL - SQL CPU Utilization"
    object_name          = "sqlcpu"
    alert_severity       = "WARNING"
    combiner            = "OR"
    filter              = "metric.type=\"cloudsql.googleapis.com/database/cpu/utilization\" AND resource.type=\"cloudsql_database\" AND resource.label.database_id=has_substring(\""
    duration            = "180s"
    comparison          = "COMPARISON_GT"
    cross_series_reducer= ""
    alignment_period    = "60s"
    per_series_aligner  = "ALIGN_MEAN"
    threshold           = 0.9
    trigger_count       = 1
    group_by_fields     =  ["resource.label.database_id"]
    api_key             = ""
    enabled             = true
  },
  Sql_disk_io_reads = {
    name                = "SQL Disk I/O Reads > 120 /s - Google Cloud CloudSQl PostGreSQL - SQL Disk I/O Reads"
    object_name          = "sqldicread"
    alert_severity       = "WARNING"
    combiner            = "OR"
    filter              = "metric.type=\"cloudsql.googleapis.com/database/disk/read_ops_count\" AND resource.type=\"cloudsql_database\" AND resource.label.database_id=has_substring(\""
    duration            = "120s"
    comparison          = "COMPARISON_GT"
    cross_series_reducer= ""
    alignment_period    = "120s"
    per_series_aligner  = "ALIGN_RATE"
    threshold           = 120
    trigger_count       = 1
    group_by_fields     =  ["resource.label.database_id"]
    api_key             = ""
    enabled             = true
  },
  Sql_disk_io_writes = {
    name                = "SQL Disk I/O Writes > 90 /s - Google Cloud CloudSQl PostGreSQL - SQL Disk I/O Writes"
    object_name          = "sqldiscwrite"
    alert_severity       = "WARNING"
    combiner            = "OR"
    filter              = "metric.type=\"cloudsql.googleapis.com/database/disk/write_ops_count\" AND resource.type=\"cloudsql_database\" AND resource.label.database_id=has_substring(\""
    duration            = "300s"
    comparison          = "COMPARISON_GT"
    cross_series_reducer= ""
    alignment_period    = "300s"
    per_series_aligner  = "ALIGN_RATE"
    threshold           = 90
    trigger_count       = 1
    group_by_fields     =  ["resource.label.database_id"]
    api_key             = ""
    enabled             = true
  },
  Sql_disk_utilization = {
    name                = "SQL Disk Utilization  - Google Cloud CloudSQl PostGreSQL - SQL Disk Utilization"
    object_name          = "sqldiscut"
    alert_severity       = "WARNING"
    combiner            = "OR"
    filter              = "metric.type=\"cloudsql.googleapis.com/database/disk/utilization\" AND resource.type=\"cloudsql_database\" AND resource.label.database_id=has_substring(\""
    duration            = "120s"
    comparison          = "COMPARISON_GT"
    cross_series_reducer= ""
    alignment_period    = "120s"
    per_series_aligner  = "ALIGN_PERCENT_CHANGE"
    threshold           = 0.8
    trigger_count       = 1
    group_by_fields     =  ["resource.label.database_id"]
    api_key             = ""
    enabled             = true
  },
  Sql_memory_utilization = {
    name                = "SQL Memory Utilization  - Google Cloud CloudSQl PostGreSQL - SQL Memory Utilization"
    object_name          = "sqlmem"
    alert_severity       = "WARNING"
    combiner            = "OR"
    filter              = "metric.type=\"cloudsql.googleapis.com/database/memory/utilization\" AND resource.type=\"cloudsql_database\" AND resource.label.database_id=has_substring(\""
    duration            = "300s"
    comparison          = "COMPARISON_GT"
    cross_series_reducer= ""
    alignment_period    = "300s"
    per_series_aligner  = "ALIGN_PERCENT_CHANGE"
    threshold           = 0.8
    trigger_count       = 1
    group_by_fields     =  ["resource.label.database_id"]
    api_key             = ""
    enabled             = true
  },
  Sql_send_byte_rate = {
    name                = "SQL Byte Rate > 80MBytes/s - Google Cloud CloudSQl PostGreSQL - SQL Byte Rate"
    object_name          = "sqlbytesrate"
    alert_severity       = "WARNING"
    combiner            = "OR"
    filter              = "metric.type=\"cloudsql.googleapis.com/database/network/sent_bytes_count\" AND resource.type=\"cloudsql_database\" AND resource.label.database_id=has_substring(\""
    duration            = "120s"
    comparison          = "COMPARISON_GT"
    cross_series_reducer= ""
    alignment_period    = "120s"
    per_series_aligner  = "ALIGN_RATE"
    threshold           = 80000000
    trigger_count       = 1
    group_by_fields     = ["resource.label.database_id"]
    api_key             = ""
    enabled             = true
  },
  Gke_containers_cpu = {
    name                = "GKE Container CPU - Google GKE Container - GKE Container CPU utilization"
    object_name          = "gkecpu"
    alert_severity       = "WARNING"
    combiner            = "OR"
    filter              = "metric.type=\"kubernetes.io/container/cpu/limit_utilization\" AND resource.type=\"k8s_container\" AND resource.label.namespace_name=has_substring(\""
    duration            = "300s"
    comparison          = "COMPARISON_GT"
    cross_series_reducer= ""
    alignment_period    = "300s"
    per_series_aligner  = "ALIGN_MEAN"
    threshold           = 0.8
    trigger_count       = 1
    group_by_fields     = ["resource.label.container_name"]
    api_key             = ""
    enabled             = true
  },
  Gke_containers_memory = {
    name                = "GKE Container Memory - Google GKE Container - GKE Container memory limit utilization"
    object_name          = "gkemem"
    alert_severity       = "WARNING"
    combiner            = "OR"
    filter              = "metric.type=\"kubernetes.io/container/memory/limit_utilization\" AND resource.type=\"k8s_container\" AND resource.label.namespace_name=has_substring(\""
    duration            = "300s"
    comparison          = "COMPARISON_GT"
    cross_series_reducer= ""
    alignment_period    = "300s"
    per_series_aligner  = "ALIGN_MEAN"
    threshold           = 0.8
    trigger_count       = 1
    group_by_fields     = ["resource.label.container_name"]
    api_key             = ""
    enabled             = true
  },
  Gke_containers_restarts = {
    name                = "GKE Container Restarts - Google GKE Container - GKE Container Restarts"
    object_name          = "gkerestarts"
    alert_severity       = "WARNING"
    combiner            = "OR"
    filter              = "metric.type=\"kubernetes.io/container/restart_count\" AND resource.type=\"k8s_container\" AND resource.label.namespace_name=has_substring(\""
    duration            = "120s"
    comparison          = "COMPARISON_GT"
    cross_series_reducer= "REDUCE_SUM"
    alignment_period    = "120s"
    per_series_aligner  = "ALIGN_RATE"
    threshold           = 3
    trigger_count       = 1
    group_by_fields     = ["resource.label.container_name"]
    api_key             = ""
    enabled             = true
   },
 Custom_API_internal_error = {
    name                = "Custom API internal error - Unexcepted error was raised during the invocation of a custom API"
    object_name          = "custapierr"
    alert_severity       = "WARNING"
    combiner            = "OR"
    filter              = "metric.type=\"logging.googleapis.com/user/ssvas_Prod_Custom_API_internal_error\" AND resource.type=\"k8s_container\" AND resource.label.namespace_name=has_substring(\""
    duration            = "60s"
    comparison          = "COMPARISON_GT"
    cross_series_reducer= "REDUCE_COUNT"
    alignment_period    = "60s"
    per_series_aligner  = "ALIGN_DELTA"
    threshold           = 0
    trigger_count       = 1
    group_by_fields     = ["metric.labels.log","resource.label.namespace_name"]
    api_key             = ""
    enabled             = true
  },
   Pipeline_api_internal_error = {
    name                = "Pipeline application fatal error - Unexpected error has caused the Unity 2.0 ETL pipelines to fail"
    object_name          = "pipapiinterr"
    alert_severity       = "WARNING"
    combiner            = "OR"
    filter              = "metric.type=\"logging.googleapis.com/user/ssvas_Prod_Pipeline_application_fatal_error\" AND resource.type=\"k8s_container\" AND resource.label.namespace_name=has_substring(\""
    duration            = "60s"
    comparison          = "COMPARISON_GT"
    cross_series_reducer= "REDUCE_COUNT"
    alignment_period    = "60s"
    per_series_aligner  = "ALIGN_DELTA"
    threshold           = 0
    trigger_count       = 1
    group_by_fields     = ["metric.labels.log","resource.label.namespace_name"]
    api_key             = ""
    enabled             = true
  },
   Sql_server_is_down = {
    name                = "CloudSQL is Down - Unexpected error caused Cloud SQL to be down"
    object_name          = "sql-server-is-down"
    alert_severity       = "WARNING"
    combiner            = "OR"
    filter              = "metric.type=\"cloudsql.googleapis.com/database/up\" resource.type=\"cloudsql_database\" AND resource.label.database_id=has_substring(\""
    duration            = "0s"
    comparison          = "COMPARISON_LT"
    cross_series_reducer= ""
    alignment_period    = "60s"
    per_series_aligner  = "ALIGN_MEAN"
    threshold           = 1
    trigger_count       = 1
    group_by_fields     = []
    api_key             = ""
    enabled             = true
  },
  Sql_server_available_for_failover = {
    name                = "CloudSQL is Down - Database Server failover unavaliable"
    object_name          = "sql-failover-is-down"
    alert_severity       = "WARNING"
    combiner            = "OR"
    filter              = "metric.type=\"cloudsql.googleapis.com/database/up\" resource.type=\"cloudsql_database\" AND resource.label.database_id=has_substring(\""
    duration            = "180s"
    comparison          = "COMPARISON_LT"
    cross_series_reducer= ""
    alignment_period    = "60s"
    per_series_aligner  = "ALIGN_MEAN"
    threshold           = 1
    trigger_count       = 1
    group_by_fields     = []
    api_key             = ""
    enabled             = true
  },
  Sql_server_uptime = {
    name                = "CloudSQL is Down - Database Server down for more than 5 min"
    object_name          = "sql-uptime"
    alert_severity       = "WARNING"
    combiner            = "OR"
    filter              = "metric.type=\"cloudsql.googleapis.com/database/uptime\" resource.type=\"cloudsql_database\" AND resource.label.database_id=has_substring(\""
    duration            = "300s"
    comparison          = "COMPARISON_LT"
    cross_series_reducer= ""
    alignment_period    = "60s"
    per_series_aligner  = "ALIGN_MEAN"
    threshold           = 1
    trigger_count       = 1
    group_by_fields     = []
    api_key             = ""
    enabled             = true
  },
    Sql_server_state = {
    name                = "CloudSQL failed - Database Server is in state failed for 3 minutes"
    object_name          = "sql-state"
    alert_severity       = "WARNING"
    combiner            = "OR"
    filter              = "metric.type=\"cloudsql.googleapis.com/database/instance_state\" resource.type=\"cloudsql_database\" AND metric.label.state=\"FAILED\" AND resource.label.database_id=has_substring(\""
    duration            = "180s"
    comparison          = "COMPARISON_GT"
    cross_series_reducer= ""
    alignment_period    = "60s"
    per_series_aligner  = "ALIGN_COUNT_TRUE"
    threshold           = 0
    trigger_count       = 1
    group_by_fields     = []
    api_key             = ""
    enabled             = true
  }
}
