{"groups": [{"name": "api", "type": "com.hsbc.changedashboard.config.ApiConfig", "sourceType": "com.hsbc.changedashboard.config.ApiConfig"}, {"name": "api.ice", "type": "com.hsbc.changedashboard.config.ApiConfig$Ice", "sourceType": "com.hsbc.changedashboard.config.ApiConfig", "sourceMethod": "getIce()"}, {"name": "api.jira", "type": "com.hsbc.changedashboard.config.ApiConfig$Jira", "sourceType": "com.hsbc.changedashboard.config.ApiConfig", "sourceMethod": "getJira()"}, {"name": "api.poddy", "type": "com.hsbc.changedashboard.config.ApiConfig$Poddy", "sourceType": "com.hsbc.changedashboard.config.ApiConfig", "sourceMethod": "getPoddy()"}], "properties": [{"name": "api.ice.app-ids", "type": "java.lang.String", "sourceType": "com.hsbc.changedashboard.config.ApiConfig$Ice"}, {"name": "api.ice.authorization", "type": "java.lang.String", "sourceType": "com.hsbc.changedashboard.config.ApiConfig$Ice"}, {"name": "api.ice.base-url", "type": "java.lang.String", "sourceType": "com.hsbc.changedashboard.config.ApiConfig$Ice"}, {"name": "api.ice.fields", "type": "java.lang.String", "sourceType": "com.hsbc.changedashboard.config.ApiConfig$Ice"}, {"name": "api.jira.authorization", "type": "java.lang.String", "sourceType": "com.hsbc.changedashboard.config.ApiConfig$Jira"}, {"name": "api.jira.base-url", "type": "java.lang.String", "sourceType": "com.hsbc.changedashboard.config.ApiConfig$Jira"}, {"name": "api.jira.cookie", "type": "java.lang.String", "sourceType": "com.hsbc.changedashboard.config.ApiConfig$Jira"}, {"name": "api.poddy.base-url", "type": "java.lang.String", "sourceType": "com.hsbc.changedashboard.config.ApiConfig$Poddy"}], "hints": []}