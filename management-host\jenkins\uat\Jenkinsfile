static final boolean C_TRUE = true
static final boolean C_FALSE = false
static final String C_SUCCESS = 'SUCCESS'
static final String C_FAILURE = 'FAILURE'
static final String C_NULL = null
static final String C_YES = 'Yes'
static final String C_NO = 'No'
static final String packer_dir = "/usr/bin"

pipeline {    
    agent  { 
        label "unity-dev-jenkins-agent"    
    }
    environment {
	    STASH_TOKEN= credentials('github-connect')   
    }
    stages {

        stage('Select target enviroment') {
            steps {
                script {
                    v_userInput = input id: 'v_userInput', message: 'Env', ok: 'Next', 
                                                parameters: [choice(choices: ["dev","uat", "prod"]  , description: 'Hi, what is your target env?', name: 'targetenv')
                                                            ]
                    env.v_targetEnv = v_userInput?:''
                    env.v_targetEnv = env.v_targetEnv.trim()
                    echo "Selected target env : \'${env.v_targetEnv}\'"
                }
           } 
        }

        stage('Select the operation') {
            steps {
                script {
                    v_userInput = input id: 'v_userInput', message: 'Operations', ok: 'Next', 
                                                parameters: [choice(choices: ["Create template","Create mig","Build image", "Rolling update on mig","Rotate sa keys"]  , description: 'Hi, what would you like to perform?', name: 'gcpoperations')
                                                            ]
                    env.v_selectedOperation = v_userInput?:''
                    env.v_selectedOperation = env.v_selectedOperation.trim()
                    echo "Selected Operation : \'${env.v_selectedOperation}\'"
                }
           } 
        }

        stage('Build image') {
            when {
                environment ignoreCase: "${C_TRUE}", name: 'v_selectedOperation', value: 'Build image'
                beforeOptions true
            }
            steps {
                script {
                    withCredentials([
                        [$class: 'UsernamePasswordMultiBinding', credentialsId: 'nexus3_connect', usernameVariable: 'NEXUSUSERNAME', passwordVariable: 'NEXUSPASSWORD'],
                        [$class: 'StringBinding', credentialsId: 'github-token', variable: 'STASH_TOKEN']
                    ]) {       
                        def BUILDSTAGE3 = input message: 'Stage 3', ok: 'Select',
                            parameters: [choice(name: 'BUILDSTAGE3', choices: 'true\nfalse', description: 'Select if image should be stage 3')]

                        def STAGE = input message: 'Select stage', ok: 'Select',
                            parameters: [choice(name: 'STAGE', choices: 'stage2\nstage3', description: 'Image stage')]

                        this.sh """${packer_dir}/packer build -timestamp-ui -on-error=cleanup \
                        -var "BUILDSTAGE3=${BUILDSTAGE3}"  \
                        -var "STAGE=${STAGE}"  \
                        -var "NEXUS_CREDS_USR=$NEXUSUSERNAME" \
                        -var "NEXUS_CREDS_PWD=$NEXUSPASSWORD" \
                        -var "STASH_TOKEN=$STASH_TOKEN" \
                        -var-file=${env.v_targetEnv}/variables.json ${env.v_targetEnv}/image.json"""
          
                    }                    
                }
            }
        }

        stage('Create template') {
            when {
                environment ignoreCase: "${C_TRUE}", name: 'v_selectedOperation', value: 'Create template'
                beforeOptions true
            }
            steps {
                script {
                    this.sh "chmod 755 ./initial_setup/create_template.sh; ./initial_setup/create_template.sh ${env.v_targetEnv}"  
                }
            }
        }

        stage('Create mig') {
            when {
                environment ignoreCase: "${C_TRUE}", name: 'v_selectedOperation', value: 'Create mig'
                beforeOptions true
            }
            steps {
                script {
                    this.sh "chmod 755 ./initial_setup/create_mig.sh; ./initial_setup/create_mig.sh  ${env.v_targetEnv}"
                }
            }
        }

        stage('Rolling update on mig') {
            when {
                environment ignoreCase: "${C_TRUE}", name: 'v_selectedOperation', value: 'Rolling update on mig'
                beforeOptions true
            }
            steps {
                script {

                    def userInput = input(
                    id: 'userInput', message: 'Please provide mig name:',
                    parameters: [
                            string(defaultValue: 'VERSION',
                                    description: 'Provide mig name',
                                    name: 'mig')
                    ])

                    this.echo "executing spinningup instance script"
                    this.sh "gcloud compute instance-groups managed  wait-until --stable ${userInput}  --zone asia-east2-b"
                    this.sh "gcloud compute instance-groups managed rolling-action replace ${userInput} --max-unavailable 1 --zone asia-east2-b"
                }
            }
        }

        stage('Rotate sa keys') {
            when {
                environment ignoreCase: "${C_TRUE}", name: 'v_selectedOperation', value: 'Rotate sa keys'
                beforeOptions true
            }
            steps {
                script {
                    this.sh "chmod 755 ./initial_setup/rotate_keys.sh; ./initial_setup/rotate_keys.sh  ${env.v_targetEnv}"
                }
            }
        }
    }
    post {
        success {
            script {
                echo "Sucess begins..."   
            }
        }
        unsuccessful {
            script {
                echo "Rollback begins..."      
            }
        }       
        cleanup {
            script {
                cleanWs()
            }
        }
    }
}