#!/usr/bin/env bash
set -euo pipefail
set -x

profile_env=$1

echo "[MGMT_HOST] checking for default conf"
if [ ! -f "./initial_setup/var-${profile_env}.conf" ]; then
 echo "Parameter file var-${profile_env}.conf not found in initial_setup."
 exit 1
fi

source ./initial_setup/var-${profile_env}.conf

NEXUS_CREDS_USR=$2
NEXUS_CREDS_PSW=$3
nexusImageName=$4
releaseImageName=$5
releaseVersion=$6
sourceRepo=${7:-"nexus3-hk-prod"}
targetRepo=${8:-"gcr-io"}

function docker_login() {
if [ ! -z ${NEXUS_CREDS_USR} ] && [ ! -z ${NEXUS_CREDS_PSW} ]; then
docker login ${NEXUSREGISTRY_PROD} --username ${NEXUS_CREDS_USR} --password-stdin <<< ${NEXUS_CREDS_PSW}
docker login ${NEXUSREGISTRY_UAT} --username ${NEXUS_CREDS_USR} --password-stdin <<< ${NEXUS_CREDS_PSW}
docker login ${NEXUSREGISTRY_DEV} --username ${NEXUS_CREDS_USR} --password-stdin <<< ${NEXUS_CREDS_PSW}
docker login ${NEXUSREGISTRY_UK_PROD} --username ${NEXUS_CREDS_USR} --password-stdin <<< ${NEXUS_CREDS_PSW}
docker login ${NEXUSREGISTRY_UK_UAT} --username ${NEXUS_CREDS_USR} --password-stdin <<< ${NEXUS_CREDS_PSW}
docker login ${NEXUSREGISTRY_UK_DEV} --username ${NEXUS_CREDS_USR} --password-stdin <<< ${NEXUS_CREDS_PSW}
fi
gcloud auth activate-service-account --key-file $HOME/ServiceAccountKeys/${SERVICE_ACCOUNT_NAME}.json;
gcloud auth configure-docker ${CONTAINERREGISTRY} --project ${PROJECT_ID} --quiet
docker login ${CONTAINERREGISTRY}
}

function cleanup(){
if [ ! -z ${NEXUS_CREDS_USR} ] && [ ! -z ${NEXUS_CREDS_PSW} ]; then
docker logout  ${NEXUSREGISTRY_PROD}
docker logout  ${NEXUSREGISTRY_UAT}
docker logout  ${NEXUSREGISTRY_DEV}
docker logout  ${NEXUSREGISTRY_UK_PROD}
docker logout  ${NEXUSREGISTRY_UK_UAT}
docker logout  ${NEXUSREGISTRY_UK_DEV}
docker logout  ${CONTAINERREGISTRY}
fi
echo $DOCKER_CONFIG
if [[ -f $DOCKER_CONFIG/config.json ]]; then
  rm -f $DOCKER_CONFIG/config.json
fi
}

nexusFullImageName=$NEXUSREGISTRY$NEXUS_PATH$nexusImageName
gcrFullImageName=$CONTAINERREGISTRY$GCR_PATH$releaseImageName

HTTPS_PROXY=googleapis-${profile_env}.gcp.cloud.uk.hsbc:3128

case ${sourceRepo} in
  "nexus3-hk-dev") 
     source_image_fullname=${NEXUSREGISTRY_DEV}${NEXUS_PATH}${nexusImageName}:${releaseVersion}
     ;;
  "nexus3-hk-prod") 
     source_image_fullname=${NEXUSREGISTRY_PROD}${NEXUS_PATH}${nexusImageName}:${releaseVersion}
     ;;
  "nexus3-uk-dev") 
     source_image_fullname=${NEXUSREGISTRY_UK_DEV}${NEXUS_PATH}${nexusImageName}:${releaseVersion}
     ;;     
  "nexus3-uk-prod") 
     source_image_fullname=${NEXUSREGISTRY_UK_PROD}${NEXUS_PATH}${nexusImageName}:${releaseVersion}
     ;;          
  "gcr-io") 
     source_image_fullname=${CONTAINERREGISTRY}${GCR_PATH}${releaseImageName}:${releaseVersion}
     ;;  
esac
case ${targetRepo} in
  "nexus3-hk-dev") 
     target_image_fullname=${NEXUSREGISTRY_DEV}${NEXUS_PATH}${nexusImageName}:${releaseVersion}
     ;;
  "nexus3-hk-prod") 
     target_image_fullname=${NEXUSREGISTRY_PROD}${NEXUS_PATH}${nexusImageName}:${releaseVersion}
     ;;
  "nexus3-uk-dev") 
     target_image_fullname=${NEXUSREGISTRY_UK_DEV}${NEXUS_PATH}${nexusImageName}:${releaseVersion}
     ;;     
  "nexus3-uk-prod") 
     target_image_fullname=${NEXUSREGISTRY_UK_PROD}${NEXUS_PATH}${nexusImageName}:${releaseVersion}
     ;;          
  "gcr-io") 
     target_image_fullname=${CONTAINERREGISTRY}${GCR_PATH}${releaseImageName}:${releaseVersion}
     ;;
  "gcr-io/hsbc-12064156-evlayer-dev")      
     target_image_fullname=${CONTAINERREGISTRY}${GCR_EVLAYER_PATH}${releaseImageName}:${releaseVersion}
     ;;
  "gcr-io/hsbc-12064156-evlayer1-dev")      
     target_image_fullname=${CONTAINERREGISTRY}${GCR_EVLAYER1_PATH}${releaseImageName}:${releaseVersion}
     ;;
  "gcr-io/hsbc-11465671-unyin1-dev") 
     target_image_fullname=${CONTAINERREGISTRY}${GCR_PATH_IN1}${releaseImageName}:${releaseVersion}
     ;;
  "gcr-io/hsbc-11465671-unyin2-dev") 
     target_image_fullname=${CONTAINERREGISTRY}${GCR_PATH_IN2}${releaseImageName}:${releaseVersion}
     ;; 
  "gcr-io/hsbc-11465671-unyin1-prod") 
     target_image_fullname=${CONTAINERREGISTRY}${GCR_PATH_IN1}${releaseImageName}:${releaseVersion}
     ;;
  "gcr-io/hsbc-11465671-unyin2-prod") 
     target_image_fullname=${CONTAINERREGISTRY}${GCR_PATH_IN2}${releaseImageName}:${releaseVersion}
     ;; 
esac

[[ "\${DEBUG_MODE:-false}" == "true" ]] && set -x

##########################################################################
#docker login
##########################################################################
docker_login
##########################################################################
echo source_image_fullname:${source_image_fullname}
echo target_image_fullname:${target_image_fullname}
export DOCKER_CLI_EXPERIMENTAL=enabled
if [[ "${targetRepo}" = "nexus3-hk-prod" ]] || [[ "${targetRepo}" = "nexus3-uk-prod" ]] ; then 
  set +e
  docker manifest inspect ${target_image_fullname} 
  rc=$?
  set -e
  if [[ ${rc} -eq 1 ]]; then 
    docker pull ${source_image_fullname}
    docker tag ${source_image_fullname} ${target_image_fullname}
    if [[ "${targetRepo}" == "gcr-io-evlayer" ]]; then 
      gcloud auth activate-service-account --key-file $HOME/ServiceAccountKeys/${GCR_EVLAYER_PROJECT_SERVICE_ACCOUNT}.json;
      gcloud auth configure-docker ${CONTAINERREGISTRY} --project ${GCR_EVLAYER_PROJECT} --quiet
      docker login ${CONTAINERREGISTRY}
    fi
    if [[ "${targetRepo}" == "gcr-io-evlayer1" ]]; then 
      gcloud auth activate-service-account --key-file $HOME/ServiceAccountKeys/${GCR_EVLAYER1_PROJECT_SERVICE_ACCOUNT}.json;
      gcloud auth configure-docker ${CONTAINERREGISTRY} --project ${GCR_EVLAYER1_PROJECT} --quiet
      docker login ${CONTAINERREGISTRY}
    fi
    docker push ${target_image_fullname} 
    docker rmi ${source_image_fullname}
    docker rmi ${target_image_fullname}
  else
    echo Image Exist [skipping push][${target_image_fullname}]
  fi
else 
  docker pull ${source_image_fullname}
  docker tag ${source_image_fullname} ${target_image_fullname}
  if [[ "${targetRepo}" == "gcr-io-evlayer" ]]; then 
    gcloud auth activate-service-account --key-file $HOME/ServiceAccountKeys/${GCR_EVLAYER_PROJECT_SERVICE_ACCOUNT}.json;
    gcloud auth configure-docker ${CONTAINERREGISTRY} --project ${GCR_EVLAYER_PROJECT} --quiet
    docker login ${CONTAINERREGISTRY}
  fi
  if [[ "${targetRepo}" == "gcr-io-evlayer1" ]]; then 
    gcloud auth activate-service-account --key-file $HOME/ServiceAccountKeys/${GCR_EVLAYER1_PROJECT_SERVICE_ACCOUNT}.json;
    gcloud auth configure-docker ${CONTAINERREGISTRY} --project ${GCR_EVLAYER1_PROJECT} --quiet
    docker login ${CONTAINERREGISTRY}
  fi
  docker push ${target_image_fullname}
  docker rmi ${source_image_fullname}
  docker rmi ${target_image_fullname}
fi
##########################################################################
#docker logout
##########################################################################
cleanup
##########################################################################
