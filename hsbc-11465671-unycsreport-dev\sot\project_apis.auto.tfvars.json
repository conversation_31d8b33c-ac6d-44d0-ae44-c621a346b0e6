{"enabled_api_services": ["artifactregistry.googleapis.com", "autoscaling.googleapis.com", "bigquery.googleapis.com", "bigquerymigration.googleapis.com", "bigquerystorage.googleapis.com", "binaryauthorization.googleapis.com", "cloudresourcemanager.googleapis.com", "compute.googleapis.com", "container.googleapis.com", "containeranalysis.googleapis.com", "containerfilesystem.googleapis.com", "containerthreatdetection.googleapis.com", "dns.googleapis.com", "essentialcontacts.googleapis.com", "iam.googleapis.com", "iamcredentials.googleapis.com", "logging.googleapis.com", "monitoring.googleapis.com", "networkconnectivity.googleapis.com", "oslogin.googleapis.com", "pubsub.googleapis.com", "recommender.googleapis.com", "servicenetworking.googleapis.com", "serviceusage.googleapis.com", "sqladmin.googleapis.com", "storage-api.googleapis.com", "storage-component.googleapis.com"]}