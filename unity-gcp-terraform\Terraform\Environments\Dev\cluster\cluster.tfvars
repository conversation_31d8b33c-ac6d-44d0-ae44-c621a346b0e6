#
image_project = "hsbc-9087302-unity-dev"

#TODO: are these 2 variables still needed?
tls_cert_key_path      = "./Certificates/Dev/tls.key"
internal_tls_cert_path = "./Certificates/Dev/lepexp_dev_int_chain.cer"
#

proxy_nginx_server_key  = "../configs/Proxy_config/Certs/server.key"
proxy_nginx_server_cert = "../configs/Proxy_config/Certs/server.pem"
proxy_nginx_ca          = "../configs/Proxy_config/Certs/ca.pem"

# image_builder_sa_email    = "<EMAIL>"

proxy_subnetwork = "cinternal-vpc3-asia-east2-gkenodes3"

# << GKE Variables >>

gke_cluster_name_prefix           = "gke-t2"
gke_subnetwork                    = "cinternal-vpc3-asia-east2-gkenodes3"
gke_cluster_secondary_range_name  = "gkepods"
gke_services_secondary_range_name = "gkeservices"
gke_master_ipv4_cidr_block        = "*************/28"
gke_release_channel               = "STABLE"
project_supernet_cidr             = "***********/16"
istio_enabled                     = false
gke_pod_security_policy           = false
gke_service_account_id            = "runtime-gke"

gke_oauth_scopes = [
  "https://www.googleapis.com/auth/logging.write",
  "https://www.googleapis.com/auth/monitoring",
  "https://www.googleapis.com/auth/devstorage.read_only"
  #"storage-ro",
  #"logging-write",
  #"monitoring",
]

gke_node_locations = [
  "asia-east2-a",
  "asia-east2-b",
  "asia-east2-c"
]

node_pools_config = {
  node_pool_one_config = {
    machine_type = "e2-standard-16"
    tags = [
      "fwtag-gke-nodepool",
    "fwtag-proxy-access"]
    disk_size_gb = 250
    # Node disk size in GB
    node_count = 1
    # Node acount per zone
    min_node_count = 1
    # Min node acount per zone
    max_node_count = 50
    # Max node acount per zone
    type_of_gpu_attached = ""
    # GPU type
    gpu_count = 0
    # GPU count
    node_pool_name = "0"
  }
}

max_pods_per_node = 64

gke_proxy_tags = [
  "cmbsp05-dev",
  "fwtag-all-dev-out-alm-github",
  "fwtag-all-dev-out-alm-nexus-3-uat",
  "t-all-dev-all-in-ilb-healthcheck",
  "t-hsbc-cmbsp-sb-all-in-vpc1",
  "t-6320774-dev-in-all-jenkinsslave-ssh",
  "t-all-dev-in-drn-jumpservers",
  "t-dev-all-out-drn-proxy",
  "tt-9820327-cmbsp05-130-178-55-232-32-a-i-all",
  "tt-9820327-cmbsp05-130-178-57-232-32-a-i-all",
  "fwtag-all-dev-out-alm-nexus-3-production",
  "fwtag-all-dev-out-efx-nexus-3-production",
  "tt-1115139-bizanalyser-10-0-0-0-8-a-i-rdp",
  "tt-9820327-cmbsp05-10-91-0-0-18-a-i-all",
  "tt-9820327-cmbsp05-10-91-0-0-17-a-i-cidmzproxy",
  "tt-1141993-iuploafwtag-proxy-accessd-0-0-0-0-0-a-i-ssh",
  "tt-9820327-cmbsp05-10-91-0-0-17-a-i-cidmzproxy",
]

# << Proxy Inblound Variables >>
proxy_service_account_id  = "runtime-proxy"
proxy_inbound_machine_type = "n1-standard-2"
proxy_inbound_ip_protocol = "TCP"
mig_version               = 1
proxy_nginx_conf_file     = "../configs/Proxy_config/nginx.conf"

proxy_inbound_tcp_health_check = {
  check_interval_sec  = 60
  timeout_sec         = 30
  healthy_threshold   = 1
  unhealthy_threshold = 5
  port                = 443
}

# << Proxy Outbound Variables >>
proxy_name_prefix          = "gke-proxy"
proxy_outbound_ip_protocol = "TCP"
proxy_outbound_target_size = 1
forward_proxy_config_path  = "../configs/Proxy_config/squid.conf"

proxy_outbound_tcp_health_check = {
  check_interval_sec  = 60
  timeout_sec         = 30
  healthy_threshold   = 1
  unhealthy_threshold = 5
  port                = 3128
}

proxy_outbound_autoscaling_policy = {
  min_replicas               = 2
  max_replicas               = 10
  cooldown_period            = 30
  cpu_utilization            = 0.8
  load_balancing_utilization = 0.8
}

proxy_combined_disk = [
  {
    auto_delete  = true
    boot         = true
    image_family = "hsbc-sp-rhel-8-squid-fproxy-mutable-dev"
    type         = "pd-standard"
    size_gb      = 60
    mode         = "READ_WRITE"
}]

proxy_kubectl_disk = [
  {
    auto_delete  = true
    boot         = true
    image_family = "hsbc-sp-rhel-8-squid-fproxy-immutable-kubectl-dev"
    type         = "pd-standard"
    size_gb      = 60
    mode         = "READ_WRITE"
}]

proxy_firewall_tags = [
  "fwtag-gke-proxy"
]

internal_fproxy_exclusive_tags = [
  "tt-9087302-unity-onprem-a-e-all",
  "tt-6320774-vpchost-asia-cinternal-drn-a-i-ssh"
]

internal_rproxy_exclusive_tags = [
]

# << Proxy kubectl Variables >>
proxy_kubectl_name_prefix = "gke-kubectl"
kubectl_proxy_config_path = "../configs/Proxy_config/kubectl.conf"
