variable "project" {
  type = string
}
variable "region" {
  type = string
}

################################################################# SQL Variables #####################################################################

variable "database_user_names" {
  description = "set user for database"
  type        = list(object({ username : string, type : string }))
  #example: [
  # {username: "<EMAIL>", type:"CLOUD_IAM_SERVICE_ACCOUNT"},
  # {username: "projection-sql", type:"BUILT_IN"},
  #]
}

variable "db_version" {
  type = string
}

variable "db_tier" {
  type    = string
  default = "db-f1-micro"
}

variable "db_instance_name" {
  type = string
}

variable "sql_network" {
  type = string
}

variable "sql_proxy_zone" {
  type = string
}

variable "kms_key_sql_link" {
  type = string
}

variable "db_databases" {
  type = list(string)
}

variable "db_availability_type" {
  type = string
}

variable "db_backup_configuration_location" {
  type = string
}

variable "point_in_time_recovery" {
  type        = bool
  default     = false
  description = "point in time recovery option to enable on instance"
}

variable "db_flags" {
  type        = list(object({ name : string, value : string }))
  description = "List of flags to set"
  default = [
    { name = "cloudsql.iam_authentication", value = "on" },
    { name = "cloudsql.enable_pgaudit", value = "on" },
    { name = "pgaudit.log", value = "all" },
    { name = "max_connections", value = "1000" },
  ]
}

variable "deletion_policy" {
  description = "ABANDON or null"
  default     = null
}

variable "deletion_protection" {
  type = string
}

variable "transaction_log_retention_days" {
  type = number
}

variable "maintenance_window_day" {
  type = number
  default = 6
}

variable "maintenance_window_hour" {
  type = number
  default = 2
}

variable "query_insights_enabled" {
  type        = bool
  default     = false
}

variable "master_instance_name" {
  type = string
  default = null
}