{{- $files := .Files }}
{{- $target := printf "grafana/datasource/encrypted/*.yaml.dec"}}
{{- range tuple $target "datasource/encrypted/*.yaml.dec"}}
{{ range $file, $_ :=  $files.Glob .}}
apiVersion: v1
kind: Secret 
metadata:
  name: grafana-config-{{$file|base|replace "." "-"|replace "-yaml-dec" ""}}
  namespace: {{ $.Release.Namespace  }}
  labels:
    grafana_datasource: "1"
    {{- include "grafana.labels" $ | nindent 4 }}
data:
  {{$file|base|replace ".yaml.dec" ".yaml"}}: {{tpl ($files.Get $file) $ |b64enc}}
---
{{- end}}
{{- end}}
