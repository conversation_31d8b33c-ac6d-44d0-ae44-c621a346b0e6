import com.poc.hss.fasttrack.transform.AbstractTransformer
import com.poc.hss.fasttrack.transform.model.TransformerBatchInputContext
import com.poc.hss.fasttrack.transform.model.TransformerRecord
import io.vertx.core.json.JsonObject
import org.slf4j.Logger
import org.slf4j.LoggerFactory

import java.util.stream.Collectors

class CustomerInfoTransformer extends AbstractTransformer {
    JsonObject constructRecord(JsonObject source) {
        return new JsonObject()
                .put("site", source.getString("SITE")?.trim())
                .put("ZGCTCD", source.getString("ZGCTCD")?.trim())
                .put("ZGDCG", source.getString("ZGDCG")?.trim())
                .put("ZGDCB", source.getString("ZGDCB")?.trim())
                .put("ZGDCS", source.getString("ZGDCS")?.trim())
                .put("ZGCSCN", source.getString("ZGCSCN")?.trim())
                .put("ZGCSFN", source.getString("ZGCSFN")?.trim())
                .put("ZGGHCL", source.getString("ZGGHCL")?.trim())
                .put("customer_id", String.format("%s-%s-%s-%03d-%06d",
                        source.getString("SITE","").trim(),
                        source.getString("ZGCTCD", "").trim(),
                        source.getString("ZGDCG", "").trim(),
                        Integer.parseInt(source.getString("ZGDCB", "0").trim()),
                        Integer.parseInt(source.getString("ZGDCS", "0").trim()),
                ).toUpperCase())
    }

    private static final Logger logger = LoggerFactory.getLogger(CustomerInfoTransformer.class)

    @Override
    List<TransformerRecord> batchTransform(TransformerBatchInputContext context) {
        List<TransformerRecord> records = context.getRecords()
        return records.stream()
                .filter(rec -> isValidEventRecord(context, rec))
                .map(rec -> applySite(rec))
                .map({
                    rec ->
                        JsonObject source = rec.getData();
                        return rec.from(rec).data(constructRecord(source)).build()
                }).filter(Objects::nonNull).collect(Collectors.toList())
    }

    boolean isValidEventRecord(TransformerBatchInputContext context, TransformerRecord record) {
        if (record != null && record.getData() != null) {
            JsonObject source = record.getData()
            if (isKOPSEnv(context)) {
                if (source != null && source.getString("SITE") != null && "a1p" == source.getString("SITE").trim().toLowerCase() && "kr" == source.getString("ZGCTCD").trim().toLowerCase()) {
                    return true
                } else {
                    return false
                }
            }
            return true
        }
        return false
    }

    def isKOPSEnv(TransformerBatchInputContext context) {
        context.getEnv().getProperty("KAFKA_CONSUMER_GROUP").containsIgnoreCase("kops-") ? true : false;
    }

    private TransformerRecord applySite(TransformerRecord record) {
        if (record != null && record.getData() != null) {
            JsonObject source = record.getData()
            if (source != null && source.getString("SITE") != null && "a1p" == source.getString("SITE").trim().toLowerCase() && "kr" == source.getString("ZGCTCD").trim().toLowerCase()) {
                source.put("SITE", "kr")
                return record
            } else {
                return record
            }
        }
    }
}