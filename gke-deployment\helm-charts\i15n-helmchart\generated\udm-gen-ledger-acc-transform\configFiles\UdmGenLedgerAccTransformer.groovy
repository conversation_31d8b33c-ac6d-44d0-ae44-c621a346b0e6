

import com.poc.hss.fasttrack.transform.AbstractTransformer
import com.poc.hss.fasttrack.transform.model.TransformerBatchInputContext
import com.poc.hss.fasttrack.transform.model.TransformerRecord
import io.vertx.core.json.JsonObject
import org.slf4j.Logger
import org.slf4j.LoggerFactory

import java.util.stream.Collectors
import java.util.stream.Stream

class UdmGenLedgerAccTransformer extends AbstractTransformer {

    JsonObject constructRecord(JsonObject source){
        new JsonObject()
                .put("SITE", source.getString("GECTCD","").trim().toUpperCase())
                .put("GEACB", source.getString("GEACB","").trim())
                .put("GEACS", source.getString("GEACS","").trim())
                .put("GEACX", source.getString("GEACX","").trim())
                .put("GEAC2N", source.getString("GEAC2N","").trim())
                .put("GEACSN", source.getString("GEACSN","").trim())
                .put("GEACTP", source.getString("GEACTP","").trim())
                .put("GEBKSI", source.getString("GEBKSI","").trim())
                .put("GEBLOP", source.getString("GEBLOP","").trim())
                .put("GECABS", source.getString("GECABS","").trim())
                .put("GECBBH", source.getString("GECBBH","").trim())
                .put("GECBBK", source.getString("GECBBK","").trim())
                .put("GECLBS", source.getString("GECLBS","").trim())
                .put("GECTCD", source.getString("GECTCD","").trim())
                .put("GECYCD", source.getString("GECYCD","").trim())
                .put("GEDLTN", source.getString("GEDLTN","").trim())
                .put("GEDLUP", source.getString("GEDLUP","").trim())
                .put("GEDPCD", source.getString("GEDPCD","").trim())
                .put("GEDROP", source.getString("GEDROP","").trim())
                .put("GEDTAO", source.getString("GEDTAO","").trim())
                .put("GEDTLS", source.getString("GEDTLS","").trim())
                .put("GEDTNS", source.getString("GEDTNS","").trim())
                .put("GEEXCD", source.getString("GEEXCD","").trim())
                .put("GEGABS", source.getString("GEGABS","").trim())
                .put("GEGAT1", source.getString("GEGAT1","").trim())
                .put("GEGAT2", source.getString("GEGAT2","").trim())
                .put("GEGLBS", source.getString("GEGLBS","").trim())
                .put("GEGMAB", source.getString("GEGMAB","").trim())
                .put("GEISMR", source.getString("GEISMR","").trim())
                .put("GEKSAI", source.getString("GEKSAI","").trim())
                .put("GELLBL", source.getString("GELLBL","").trim())
                .put("GEMDFL", source.getString("GEMDFL","").trim())
                .put("GENOCR", source.getString("GENOCR","").trim())
                .put("GEOPIT", source.getString("GEOPIT","").trim())
                .put("GERLBL", source.getString("GERLBL","").trim())
                .put("GERTPD", source.getString("GERTPD","").trim())
                .put("GESERL", source.getString("GESERL","").trim())
                .put("GESMCY", source.getString("GESMCY","").trim())
                .put("GETLUP", source.getString("GETLUP","").trim())
                .put("GETRIN", source.getString("GETRIN","").trim())
                .put("GEVLOC", source.getString("GEVLOC","").trim())
                .put("tranc_id", String.format("%s-%s-%03d-%06d-%03d",source.getString("GECTCD","").trim(),source.getString("GEGMAB","").trim(),
                        getInteger("GEACB",source),getInteger("GEACS",source),getInteger("GEACX",source)))
    }

    static Integer getInteger( String key,JsonObject obj) {
        String result = obj.getString(key);
        if (result != null && !result.trim().equals("")) {
            return new BigDecimal(result).intValueExact();
        } else {
            return 0;
        }
    }

    private static final Logger logger = LoggerFactory.getLogger(UdmGenLedgerAccTransformer.class)
    @Override
    List<TransformerRecord> batchTransform(TransformerBatchInputContext context) {
        List<TransformerRecord> records = context.getRecords()
        return records.stream()
                .filter(rec -> validData(rec,context))
                .map(rec -> rec.from(rec).data(constructRecord(rec.getData())).build())
                .collect(Collectors.toList())
    }
    boolean validData(TransformerRecord rec,TransformerBatchInputContext context){
        if(isKOPSEnv(context)){// now only QA&KW are deployed to kops, GLACMSP don't have gca data
            return ["QA","KW","KR","GCA"].contains(rec.getData().getString("GECTCD","").trim().toUpperCase())
        }else{//EG OM are not on board now, QA/KW don't have data visa to deploy them to gcp
            return !["QA","KW","EG"].contains(rec.getData().getString("GECTCD","").trim().toUpperCase())
        }
    }


    def isKOPSEnv(TransformerBatchInputContext context) {
        context.getEnv().getProperty("KAFKA_CONSUMER_GROUP").containsIgnoreCase("kops-") ? true : false;
    }
}

