import hudson.model.ParameterDefinition
import hudson.model.JobProperty
import groovy.json.*
import jenkins.model.*
import java.io.*
import java.io.File
import groovy.io.FileType
def AGENT = "unity-prod-jenkins-agent" 

pipeline {    
    agent { label env.AGENT_LABEL ?: AGENT }

    environment {
        // Fix github connection so it points to env specific variable  
        STASH_TOKEN= credentials('STASH_TOKEN')   
        NEXUS_CREDS = credentials('NEXUS3_CONNECT')
    }

    stages {
        stage ('Promote image to the production contaier registry') { 
	        steps {
                script {
                    def imagesData = "${imagesList}"

                    echo """Images to promote ${imagesData}"""

                    imagesData.readLines().each{
                        echo """Promoting image: ${it}"""
                        String[] nameAndVersion;
                        nameAndVersion = it.split(':');
                        
                        sh(""" chmod 755 ./image_promotion/promote_image.sh; ./image_promotion/promote_image.sh prod ${NEXUS_CREDS_USR} ${NEXUS_CREDS_PSW} ${nameAndVersion[0]} ${nameAndVersion[0]} ${nameAndVersion[1]} ${sourceRepo} ${targetRepo}""")
                    }
                }
          }
        }
    }
   
    post {
        /*unsuccessful {
            mail bcc: '',
                body: 'ERROR occured in automatic mgmt host rotation occured. Please inspect relevant Jenkins pipeline. Regards DevOps Team',
                cc: '',
                from: 'unity_cloud_devops@noreply',
                replyTo: '',
                subject: "ERROR in automatic mgmt host rotation notification",
                to: '<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>'
        } */      
        cleanup {
            script {
                cleanWs()
            }
        }
    }		

}