def AGENT = 'unity-dev-jenkins-agent'
pipeline {
    agent { label env.AGENT_LABEL ?: AGENT }

    parameters {
        text(name: "changeOrder", description: "SNOW change order number")
        text(name: "unityReleaseVersion", description: "unity_release_version is used for LTTD calculation: documentation: https://gbmt-confluence.prd.fx.gbm.cloud.uk.hsbc/x/p0vnSQ")
    }
    
    environment {
        ICE_AUTH_TOKEN = credentials('ICE_AUTH_TOKEN')
    }
    
    stages {
        stage('Verify Change Order') {
            when {
                expression { params.changeOrder != ''}
            }
            steps {
                script {
                    changeOrder = "${params.changeOrder}"
                    statusCode = sh returnStatus: true, script:
                                """if [ "$changeOrder" != "" ]
                                then
                                    CR_Response=\$(curl -X 'GET' 'https://cr-minion.uk.hsbc/api/v3/read?changeRequest=$changeOrder&withChangeTasks=false&withPamTasks=false' -H 'accept: application/json')

                                    if [ \$(jq -r '.statusCode' <<< \$CR_Response) != "200" ]
                                    then
                                        echo 'Change Request returning error in CR Minion... proceeding without validation'
                                        exit 0
                                    fi

                                    if [ \$(jq -r '.approvalStatus' <<< \$CR_Response) != "Approved" ]
                                    then
                                        echo 'Change Request is not approved. Aborting the build.'
                                        exit 2
                                    fi
                                else
                                    echo 'Change Request field is mandatory. Please re-run the job after providing a valid Change Request.'
                                    exit 1
                                fi
                                echo 'Change Request is validated'"""

                    if ( statusCode != 0 ) {
                        currentBuild.result = "ABORTED"
                        if ( statusCode == 1 ) {
                            error("Empty Change Request")
                        } else if ( statusCode == 2 ) {
                            error("Change Request not approved")
                        }
                    }
                }
            }
            
        }
        stage('Git Checkout gke-deployment Repo') {
            when {
                expression { params.changeOrder != ''}
            }
            steps {
                script {
                    this.sh """if [ -d "gke-deployment" ]; then 
                                 cd gke-deployment; 
                                 git pull; 
                                 cd ..
                               else 
                                 git clone -b core-PROD https://stash.hk.hsbc/scm/unity-i15n-poc/gke-deployment.git; 
                               fi"""
                }
            }
        }
        stage('Upload Source Code Diff URL') {
            when {
                expression { params.changeOrder != ''}
            }
            steps {
                script {
                    dir ("gke-deployment") {
                        
                        // run generateSourceDiffURL.sh
                        this.sh """chmod +x $WORKSPACE/pipeline-config-generation/src/main/resources/jenkins-default/generateSourceDiffURL.sh
                        $WORKSPACE/pipeline-config-generation/src/main/resources/jenkins-default/generateSourceDiffURL.sh ${params.unityReleaseVersion}"""
                        
                        statusCode = sh returnStatus: true, script:
                                    """status_code=\$(curl --write-out "%{http_code}\\n" --silent --output /dev/null \
                                    -X 'PATCH' -H 'Authorization: Basic ${ICE_AUTH_TOKEN}' 'https://ice.it.global.hsbc/ice/api/v1/changes/${params.changeOrder}' -H 'accept: */*' -H 'Content-Type: application/json' -d @artifact.ice.json.updated)
                                    if [ \$status_code -ne 204 ]
                                    then
                                        if [ \$status_code -eq 400 ]
                                        then
                                            echo 'A request to update a field source from GSD was made, or the request body JSON describing the patch was invalid'
                                        elif [ \$status_code -eq 404 ]
                                        then
                                            echo 'The CR does not exist'
                                        fi
                                        exit 1
                                    fi"""
                        
                        if ( statusCode != 0 ) {
                            unstable(message: "ICE update failed: please refer to previous log for details" )
                        } 
                    }
                }
            }
        }
    }
}