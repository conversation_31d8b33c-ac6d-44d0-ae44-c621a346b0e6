#!/usr/bin/env bash
set -euo pipefail

#please pass the iput vale either dev or prod like sh ./initial_setup/create_gcebucket dev
profile_env=$1


echo "[MGMT_HOST] checking for default conf"
if [ ! -f "./initial_setup/var-${profile_env}.conf" ]; then
 echo "Parameter file var-${profile_env}.conf not found in initial_setup."
 exit 1
fi

source ./initial_setup/var-${profile_env}.conf

echo "executing ${profile_env} "

gcloud compute instances create ${MGMT_HOST_NAME}-${CURRENT_TIME} --machine-type=${MACHINE_TYPE} --project ${PROJECT_ID} \
 --network-interface subnet=$SUBNET,no-address \
 --network-interface subnet=$SUBNET2,no-address \
 --zone ${ZONE} \
 --image-family $IMAGE_FAMILY --tags ${TAGS} --boot-disk-size=${DISK_SIZE} \
--image-project=${PROJECT_ID} --service-account=${SERVICE_ACCOUNT_NAME}@${PROJECT_ID}.iam.gserviceaccount.com --scopes=${SCOPES} \
--boot-disk-kms-key projects/${CMEK_PROJECT}/locations/${REGION}/keyRings/computeEngine/cryptoKeys/HSMcomputeEngine
