package com.hsbc.changedashboard.service;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest
class DateUtilServiceTest {

    @Autowired
    private DateUtilService dateUtilService;

    @Test
    void testGetCurrentWeekMonday() {
        String currentWeekMonday = dateUtilService.getCurrentWeekMonday();
        assertNotNull(currentWeekMonday);
        assertTrue(currentWeekMonday.matches("\\d{4}-\\d{2}-\\d{2}T00:00:00\\.000000Z"));
        System.out.println("Current week Monday: " + currentWeekMonday);
    }

    @Test
    void testGetNextWeekMonday() {
        String nextWeekMonday = dateUtilService.getNextWeekMonday();
        assertNotNull(nextWeekMonday);
        assertTrue(nextWeekMonday.matches("\\d{4}-\\d{2}-\\d{2}T00:00:00\\.000000Z"));
        System.out.println("Next week Monday: " + nextWeekMonday);
    }

    @Test
    void testDateDifference() {
        String currentWeekMonday = dateUtilService.getCurrentWeekMonday();
        String nextWeekMonday = dateUtilService.getNextWeekMonday();
        
        // Next week Monday should be after current week Monday
        assertTrue(nextWeekMonday.compareTo(currentWeekMonday) > 0);
    }
}
