<?xml version="1.0" encoding="UTF-8"?>
<testsuite xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="https://maven.apache.org/surefire/maven-surefire-plugin/xsd/surefire-test-report-3.0.xsd" version="3.0" name="com.hsbc.changedashboard.ChangeDashboardApplicationTests" time="2.758" tests="1" errors="0" skipped="0" failures="0">
  <properties>
    <property name="java.specification.version" value="21"/>
    <property name="sun.cpu.isalist" value="amd64"/>
    <property name="sun.jnu.encoding" value="Cp1252"/>
    <property name="java.class.path" value="C:\eddiewtchoi\workspace\ide\git\change-dashboard\target\test-classes;C:\eddiewtchoi\workspace\ide\git\change-dashboard\target\classes;C:\sandbox\maven\repository\org\springframework\boot\spring-boot-starter-web\3.2.0\spring-boot-starter-web-3.2.0.jar;C:\sandbox\maven\repository\org\springframework\boot\spring-boot-starter\3.2.0\spring-boot-starter-3.2.0.jar;C:\sandbox\maven\repository\org\springframework\boot\spring-boot\3.2.0\spring-boot-3.2.0.jar;C:\sandbox\maven\repository\org\springframework\boot\spring-boot-autoconfigure\3.2.0\spring-boot-autoconfigure-3.2.0.jar;C:\sandbox\maven\repository\org\springframework\boot\spring-boot-starter-logging\3.2.0\spring-boot-starter-logging-3.2.0.jar;C:\sandbox\maven\repository\ch\qos\logback\logback-classic\1.4.11\logback-classic-1.4.11.jar;C:\sandbox\maven\repository\ch\qos\logback\logback-core\1.4.11\logback-core-1.4.11.jar;C:\sandbox\maven\repository\org\apache\logging\log4j\log4j-to-slf4j\2.21.1\log4j-to-slf4j-2.21.1.jar;C:\sandbox\maven\repository\org\apache\logging\log4j\log4j-api\2.21.1\log4j-api-2.21.1.jar;C:\sandbox\maven\repository\org\slf4j\jul-to-slf4j\2.0.9\jul-to-slf4j-2.0.9.jar;C:\sandbox\maven\repository\jakarta\annotation\jakarta.annotation-api\2.1.1\jakarta.annotation-api-2.1.1.jar;C:\sandbox\maven\repository\org\yaml\snakeyaml\2.2\snakeyaml-2.2.jar;C:\sandbox\maven\repository\org\springframework\boot\spring-boot-starter-json\3.2.0\spring-boot-starter-json-3.2.0.jar;C:\sandbox\maven\repository\com\fasterxml\jackson\datatype\jackson-datatype-jdk8\2.15.3\jackson-datatype-jdk8-2.15.3.jar;C:\sandbox\maven\repository\com\fasterxml\jackson\module\jackson-module-parameter-names\2.15.3\jackson-module-parameter-names-2.15.3.jar;C:\sandbox\maven\repository\org\springframework\boot\spring-boot-starter-tomcat\3.2.0\spring-boot-starter-tomcat-3.2.0.jar;C:\sandbox\maven\repository\org\apache\tomcat\embed\tomcat-embed-core\10.1.16\tomcat-embed-core-10.1.16.jar;C:\sandbox\maven\repository\org\apache\tomcat\embed\tomcat-embed-websocket\10.1.16\tomcat-embed-websocket-10.1.16.jar;C:\sandbox\maven\repository\org\springframework\spring-web\6.1.1\spring-web-6.1.1.jar;C:\sandbox\maven\repository\org\springframework\spring-beans\6.1.1\spring-beans-6.1.1.jar;C:\sandbox\maven\repository\org\springframework\spring-webmvc\6.1.1\spring-webmvc-6.1.1.jar;C:\sandbox\maven\repository\org\springframework\spring-aop\6.1.1\spring-aop-6.1.1.jar;C:\sandbox\maven\repository\org\springframework\spring-context\6.1.1\spring-context-6.1.1.jar;C:\sandbox\maven\repository\org\springframework\spring-expression\6.1.1\spring-expression-6.1.1.jar;C:\sandbox\maven\repository\org\springframework\boot\spring-boot-starter-actuator\3.2.0\spring-boot-starter-actuator-3.2.0.jar;C:\sandbox\maven\repository\org\springframework\boot\spring-boot-actuator-autoconfigure\3.2.0\spring-boot-actuator-autoconfigure-3.2.0.jar;C:\sandbox\maven\repository\org\springframework\boot\spring-boot-actuator\3.2.0\spring-boot-actuator-3.2.0.jar;C:\sandbox\maven\repository\io\micrometer\micrometer-observation\1.12.0\micrometer-observation-1.12.0.jar;C:\sandbox\maven\repository\io\micrometer\micrometer-commons\1.12.0\micrometer-commons-1.12.0.jar;C:\sandbox\maven\repository\io\micrometer\micrometer-jakarta9\1.12.0\micrometer-jakarta9-1.12.0.jar;C:\sandbox\maven\repository\io\micrometer\micrometer-core\1.12.0\micrometer-core-1.12.0.jar;C:\sandbox\maven\repository\org\hdrhistogram\HdrHistogram\2.1.12\HdrHistogram-2.1.12.jar;C:\sandbox\maven\repository\org\latencyutils\LatencyUtils\2.0.3\LatencyUtils-2.0.3.jar;C:\sandbox\maven\repository\org\springframework\boot\spring-boot-starter-validation\3.2.0\spring-boot-starter-validation-3.2.0.jar;C:\sandbox\maven\repository\org\apache\tomcat\embed\tomcat-embed-el\10.1.16\tomcat-embed-el-10.1.16.jar;C:\sandbox\maven\repository\org\hibernate\validator\hibernate-validator\8.0.1.Final\hibernate-validator-8.0.1.Final.jar;C:\sandbox\maven\repository\jakarta\validation\jakarta.validation-api\3.0.2\jakarta.validation-api-3.0.2.jar;C:\sandbox\maven\repository\org\jboss\logging\jboss-logging\3.5.3.Final\jboss-logging-3.5.3.Final.jar;C:\sandbox\maven\repository\com\fasterxml\classmate\1.6.0\classmate-1.6.0.jar;C:\sandbox\maven\repository\com\fasterxml\jackson\core\jackson-databind\2.15.3\jackson-databind-2.15.3.jar;C:\sandbox\maven\repository\com\fasterxml\jackson\core\jackson-annotations\2.15.3\jackson-annotations-2.15.3.jar;C:\sandbox\maven\repository\com\fasterxml\jackson\core\jackson-core\2.15.3\jackson-core-2.15.3.jar;C:\sandbox\maven\repository\com\fasterxml\jackson\datatype\jackson-datatype-jsr310\2.15.3\jackson-datatype-jsr310-2.15.3.jar;C:\sandbox\maven\repository\org\springframework\boot\spring-boot-starter-test\3.2.0\spring-boot-starter-test-3.2.0.jar;C:\sandbox\maven\repository\org\springframework\boot\spring-boot-test\3.2.0\spring-boot-test-3.2.0.jar;C:\sandbox\maven\repository\org\springframework\boot\spring-boot-test-autoconfigure\3.2.0\spring-boot-test-autoconfigure-3.2.0.jar;C:\sandbox\maven\repository\com\jayway\jsonpath\json-path\2.8.0\json-path-2.8.0.jar;C:\sandbox\maven\repository\org\slf4j\slf4j-api\2.0.9\slf4j-api-2.0.9.jar;C:\sandbox\maven\repository\jakarta\xml\bind\jakarta.xml.bind-api\4.0.1\jakarta.xml.bind-api-4.0.1.jar;C:\sandbox\maven\repository\jakarta\activation\jakarta.activation-api\2.1.2\jakarta.activation-api-2.1.2.jar;C:\sandbox\maven\repository\net\minidev\json-smart\2.5.0\json-smart-2.5.0.jar;C:\sandbox\maven\repository\net\minidev\accessors-smart\2.5.0\accessors-smart-2.5.0.jar;C:\sandbox\maven\repository\org\ow2\asm\asm\9.3\asm-9.3.jar;C:\sandbox\maven\repository\org\assertj\assertj-core\3.24.2\assertj-core-3.24.2.jar;C:\sandbox\maven\repository\net\bytebuddy\byte-buddy\1.14.10\byte-buddy-1.14.10.jar;C:\sandbox\maven\repository\org\awaitility\awaitility\4.2.0\awaitility-4.2.0.jar;C:\sandbox\maven\repository\org\hamcrest\hamcrest\2.2\hamcrest-2.2.jar;C:\sandbox\maven\repository\org\junit\jupiter\junit-jupiter\5.10.1\junit-jupiter-5.10.1.jar;C:\sandbox\maven\repository\org\junit\jupiter\junit-jupiter-api\5.10.1\junit-jupiter-api-5.10.1.jar;C:\sandbox\maven\repository\org\opentest4j\opentest4j\1.3.0\opentest4j-1.3.0.jar;C:\sandbox\maven\repository\org\junit\platform\junit-platform-commons\1.10.1\junit-platform-commons-1.10.1.jar;C:\sandbox\maven\repository\org\apiguardian\apiguardian-api\1.1.2\apiguardian-api-1.1.2.jar;C:\sandbox\maven\repository\org\junit\jupiter\junit-jupiter-params\5.10.1\junit-jupiter-params-5.10.1.jar;C:\sandbox\maven\repository\org\junit\jupiter\junit-jupiter-engine\5.10.1\junit-jupiter-engine-5.10.1.jar;C:\sandbox\maven\repository\org\junit\platform\junit-platform-engine\1.10.1\junit-platform-engine-1.10.1.jar;C:\sandbox\maven\repository\org\mockito\mockito-core\5.7.0\mockito-core-5.7.0.jar;C:\sandbox\maven\repository\net\bytebuddy\byte-buddy-agent\1.14.10\byte-buddy-agent-1.14.10.jar;C:\sandbox\maven\repository\org\objenesis\objenesis\3.3\objenesis-3.3.jar;C:\sandbox\maven\repository\org\mockito\mockito-junit-jupiter\5.7.0\mockito-junit-jupiter-5.7.0.jar;C:\sandbox\maven\repository\org\skyscreamer\jsonassert\1.5.1\jsonassert-1.5.1.jar;C:\sandbox\maven\repository\com\vaadin\external\google\android-json\0.0.20131108.vaadin1\android-json-0.0.20131108.vaadin1.jar;C:\sandbox\maven\repository\org\springframework\spring-core\6.1.1\spring-core-6.1.1.jar;C:\sandbox\maven\repository\org\springframework\spring-jcl\6.1.1\spring-jcl-6.1.1.jar;C:\sandbox\maven\repository\org\springframework\spring-test\6.1.1\spring-test-6.1.1.jar;C:\sandbox\maven\repository\org\xmlunit\xmlunit-core\2.9.1\xmlunit-core-2.9.1.jar;C:\sandbox\maven\repository\org\springframework\boot\spring-boot-configuration-processor\3.2.0\spring-boot-configuration-processor-3.2.0.jar;"/>
    <property name="java.vm.vendor" value="Eclipse Adoptium"/>
    <property name="sun.arch.data.model" value="64"/>
    <property name="user.variant" value=""/>
    <property name="java.vendor.url" value="https://adoptium.net/"/>
    <property name="user.timezone" value="Asia/Shanghai"/>
    <property name="org.jboss.logging.provider" value="slf4j"/>
    <property name="os.name" value="Windows 10"/>
    <property name="java.vm.specification.version" value="21"/>
    <property name="sun.java.launcher" value="SUN_STANDARD"/>
    <property name="user.country" value="US"/>
    <property name="sun.boot.library.path" value="C:\sandbox\devtools\java\jdk-21.0.4+7\bin"/>
    <property name="sun.java.command" value="C:\Users\<USER>\AppData\Local\Temp\surefire962031461374633185\surefirebooter-20250605180341385_3.jar C:\Users\<USER>\AppData\Local\Temp\surefire962031461374633185 2025-06-05T18-03-40_365-jvmRun1 surefire-20250605180341385_1tmp surefire_0-20250605180341385_2tmp"/>
    <property name="jdk.debug" value="release"/>
    <property name="surefire.test.class.path" value="C:\eddiewtchoi\workspace\ide\git\change-dashboard\target\test-classes;C:\eddiewtchoi\workspace\ide\git\change-dashboard\target\classes;C:\sandbox\maven\repository\org\springframework\boot\spring-boot-starter-web\3.2.0\spring-boot-starter-web-3.2.0.jar;C:\sandbox\maven\repository\org\springframework\boot\spring-boot-starter\3.2.0\spring-boot-starter-3.2.0.jar;C:\sandbox\maven\repository\org\springframework\boot\spring-boot\3.2.0\spring-boot-3.2.0.jar;C:\sandbox\maven\repository\org\springframework\boot\spring-boot-autoconfigure\3.2.0\spring-boot-autoconfigure-3.2.0.jar;C:\sandbox\maven\repository\org\springframework\boot\spring-boot-starter-logging\3.2.0\spring-boot-starter-logging-3.2.0.jar;C:\sandbox\maven\repository\ch\qos\logback\logback-classic\1.4.11\logback-classic-1.4.11.jar;C:\sandbox\maven\repository\ch\qos\logback\logback-core\1.4.11\logback-core-1.4.11.jar;C:\sandbox\maven\repository\org\apache\logging\log4j\log4j-to-slf4j\2.21.1\log4j-to-slf4j-2.21.1.jar;C:\sandbox\maven\repository\org\apache\logging\log4j\log4j-api\2.21.1\log4j-api-2.21.1.jar;C:\sandbox\maven\repository\org\slf4j\jul-to-slf4j\2.0.9\jul-to-slf4j-2.0.9.jar;C:\sandbox\maven\repository\jakarta\annotation\jakarta.annotation-api\2.1.1\jakarta.annotation-api-2.1.1.jar;C:\sandbox\maven\repository\org\yaml\snakeyaml\2.2\snakeyaml-2.2.jar;C:\sandbox\maven\repository\org\springframework\boot\spring-boot-starter-json\3.2.0\spring-boot-starter-json-3.2.0.jar;C:\sandbox\maven\repository\com\fasterxml\jackson\datatype\jackson-datatype-jdk8\2.15.3\jackson-datatype-jdk8-2.15.3.jar;C:\sandbox\maven\repository\com\fasterxml\jackson\module\jackson-module-parameter-names\2.15.3\jackson-module-parameter-names-2.15.3.jar;C:\sandbox\maven\repository\org\springframework\boot\spring-boot-starter-tomcat\3.2.0\spring-boot-starter-tomcat-3.2.0.jar;C:\sandbox\maven\repository\org\apache\tomcat\embed\tomcat-embed-core\10.1.16\tomcat-embed-core-10.1.16.jar;C:\sandbox\maven\repository\org\apache\tomcat\embed\tomcat-embed-websocket\10.1.16\tomcat-embed-websocket-10.1.16.jar;C:\sandbox\maven\repository\org\springframework\spring-web\6.1.1\spring-web-6.1.1.jar;C:\sandbox\maven\repository\org\springframework\spring-beans\6.1.1\spring-beans-6.1.1.jar;C:\sandbox\maven\repository\org\springframework\spring-webmvc\6.1.1\spring-webmvc-6.1.1.jar;C:\sandbox\maven\repository\org\springframework\spring-aop\6.1.1\spring-aop-6.1.1.jar;C:\sandbox\maven\repository\org\springframework\spring-context\6.1.1\spring-context-6.1.1.jar;C:\sandbox\maven\repository\org\springframework\spring-expression\6.1.1\spring-expression-6.1.1.jar;C:\sandbox\maven\repository\org\springframework\boot\spring-boot-starter-actuator\3.2.0\spring-boot-starter-actuator-3.2.0.jar;C:\sandbox\maven\repository\org\springframework\boot\spring-boot-actuator-autoconfigure\3.2.0\spring-boot-actuator-autoconfigure-3.2.0.jar;C:\sandbox\maven\repository\org\springframework\boot\spring-boot-actuator\3.2.0\spring-boot-actuator-3.2.0.jar;C:\sandbox\maven\repository\io\micrometer\micrometer-observation\1.12.0\micrometer-observation-1.12.0.jar;C:\sandbox\maven\repository\io\micrometer\micrometer-commons\1.12.0\micrometer-commons-1.12.0.jar;C:\sandbox\maven\repository\io\micrometer\micrometer-jakarta9\1.12.0\micrometer-jakarta9-1.12.0.jar;C:\sandbox\maven\repository\io\micrometer\micrometer-core\1.12.0\micrometer-core-1.12.0.jar;C:\sandbox\maven\repository\org\hdrhistogram\HdrHistogram\2.1.12\HdrHistogram-2.1.12.jar;C:\sandbox\maven\repository\org\latencyutils\LatencyUtils\2.0.3\LatencyUtils-2.0.3.jar;C:\sandbox\maven\repository\org\springframework\boot\spring-boot-starter-validation\3.2.0\spring-boot-starter-validation-3.2.0.jar;C:\sandbox\maven\repository\org\apache\tomcat\embed\tomcat-embed-el\10.1.16\tomcat-embed-el-10.1.16.jar;C:\sandbox\maven\repository\org\hibernate\validator\hibernate-validator\8.0.1.Final\hibernate-validator-8.0.1.Final.jar;C:\sandbox\maven\repository\jakarta\validation\jakarta.validation-api\3.0.2\jakarta.validation-api-3.0.2.jar;C:\sandbox\maven\repository\org\jboss\logging\jboss-logging\3.5.3.Final\jboss-logging-3.5.3.Final.jar;C:\sandbox\maven\repository\com\fasterxml\classmate\1.6.0\classmate-1.6.0.jar;C:\sandbox\maven\repository\com\fasterxml\jackson\core\jackson-databind\2.15.3\jackson-databind-2.15.3.jar;C:\sandbox\maven\repository\com\fasterxml\jackson\core\jackson-annotations\2.15.3\jackson-annotations-2.15.3.jar;C:\sandbox\maven\repository\com\fasterxml\jackson\core\jackson-core\2.15.3\jackson-core-2.15.3.jar;C:\sandbox\maven\repository\com\fasterxml\jackson\datatype\jackson-datatype-jsr310\2.15.3\jackson-datatype-jsr310-2.15.3.jar;C:\sandbox\maven\repository\org\springframework\boot\spring-boot-starter-test\3.2.0\spring-boot-starter-test-3.2.0.jar;C:\sandbox\maven\repository\org\springframework\boot\spring-boot-test\3.2.0\spring-boot-test-3.2.0.jar;C:\sandbox\maven\repository\org\springframework\boot\spring-boot-test-autoconfigure\3.2.0\spring-boot-test-autoconfigure-3.2.0.jar;C:\sandbox\maven\repository\com\jayway\jsonpath\json-path\2.8.0\json-path-2.8.0.jar;C:\sandbox\maven\repository\org\slf4j\slf4j-api\2.0.9\slf4j-api-2.0.9.jar;C:\sandbox\maven\repository\jakarta\xml\bind\jakarta.xml.bind-api\4.0.1\jakarta.xml.bind-api-4.0.1.jar;C:\sandbox\maven\repository\jakarta\activation\jakarta.activation-api\2.1.2\jakarta.activation-api-2.1.2.jar;C:\sandbox\maven\repository\net\minidev\json-smart\2.5.0\json-smart-2.5.0.jar;C:\sandbox\maven\repository\net\minidev\accessors-smart\2.5.0\accessors-smart-2.5.0.jar;C:\sandbox\maven\repository\org\ow2\asm\asm\9.3\asm-9.3.jar;C:\sandbox\maven\repository\org\assertj\assertj-core\3.24.2\assertj-core-3.24.2.jar;C:\sandbox\maven\repository\net\bytebuddy\byte-buddy\1.14.10\byte-buddy-1.14.10.jar;C:\sandbox\maven\repository\org\awaitility\awaitility\4.2.0\awaitility-4.2.0.jar;C:\sandbox\maven\repository\org\hamcrest\hamcrest\2.2\hamcrest-2.2.jar;C:\sandbox\maven\repository\org\junit\jupiter\junit-jupiter\5.10.1\junit-jupiter-5.10.1.jar;C:\sandbox\maven\repository\org\junit\jupiter\junit-jupiter-api\5.10.1\junit-jupiter-api-5.10.1.jar;C:\sandbox\maven\repository\org\opentest4j\opentest4j\1.3.0\opentest4j-1.3.0.jar;C:\sandbox\maven\repository\org\junit\platform\junit-platform-commons\1.10.1\junit-platform-commons-1.10.1.jar;C:\sandbox\maven\repository\org\apiguardian\apiguardian-api\1.1.2\apiguardian-api-1.1.2.jar;C:\sandbox\maven\repository\org\junit\jupiter\junit-jupiter-params\5.10.1\junit-jupiter-params-5.10.1.jar;C:\sandbox\maven\repository\org\junit\jupiter\junit-jupiter-engine\5.10.1\junit-jupiter-engine-5.10.1.jar;C:\sandbox\maven\repository\org\junit\platform\junit-platform-engine\1.10.1\junit-platform-engine-1.10.1.jar;C:\sandbox\maven\repository\org\mockito\mockito-core\5.7.0\mockito-core-5.7.0.jar;C:\sandbox\maven\repository\net\bytebuddy\byte-buddy-agent\1.14.10\byte-buddy-agent-1.14.10.jar;C:\sandbox\maven\repository\org\objenesis\objenesis\3.3\objenesis-3.3.jar;C:\sandbox\maven\repository\org\mockito\mockito-junit-jupiter\5.7.0\mockito-junit-jupiter-5.7.0.jar;C:\sandbox\maven\repository\org\skyscreamer\jsonassert\1.5.1\jsonassert-1.5.1.jar;C:\sandbox\maven\repository\com\vaadin\external\google\android-json\0.0.20131108.vaadin1\android-json-0.0.20131108.vaadin1.jar;C:\sandbox\maven\repository\org\springframework\spring-core\6.1.1\spring-core-6.1.1.jar;C:\sandbox\maven\repository\org\springframework\spring-jcl\6.1.1\spring-jcl-6.1.1.jar;C:\sandbox\maven\repository\org\springframework\spring-test\6.1.1\spring-test-6.1.1.jar;C:\sandbox\maven\repository\org\xmlunit\xmlunit-core\2.9.1\xmlunit-core-2.9.1.jar;C:\sandbox\maven\repository\org\springframework\boot\spring-boot-configuration-processor\3.2.0\spring-boot-configuration-processor-3.2.0.jar;"/>
    <property name="sun.cpu.endian" value="little"/>
    <property name="user.home" value="C:\Users\<USER>\sandbox\devtools\java\jdk-21.0.4+7"/>
    <property name="file.separator" value="\"/>
    <property name="basedir" value="C:\eddiewtchoi\workspace\ide\git\change-dashboard"/>
    <property name="java.vm.compressedOopsMode" value="Zero based"/>
    <property name="line.separator" value="&#10;"/>
    <property name="java.vm.specification.vendor" value="Oracle Corporation"/>
    <property name="java.specification.name" value="Java Platform API Specification"/>
    <property name="FILE_LOG_CHARSET" value="UTF-8"/>
    <property name="java.awt.headless" value="true"/>
    <property name="surefire.real.class.path" value="C:\Users\<USER>\AppData\Local\Temp\surefire962031461374633185\surefirebooter-20250605180341385_3.jar"/>
    <property name="user.script" value=""/>
    <property name="sun.management.compiler" value="HotSpot 64-Bit Tiered Compilers"/>
    <property name="java.runtime.version" value="21.0.4+7-LTS"/>
    <property name="user.name" value="43402948"/>
    <property name="stdout.encoding" value="Cp1252"/>
    <property name="path.separator" value=";"/>
    <property name="os.version" value="10.0"/>
    <property name="java.runtime.name" value="OpenJDK Runtime Environment"/>
    <property name="file.encoding" value="UTF-8"/>
    <property name="java.vm.name" value="OpenJDK 64-Bit Server VM"/>
    <property name="java.vendor.version" value="Temurin-21.0.4+7"/>
    <property name="localRepository" value="C:\sandbox\maven\repository"/>
    <property name="java.vendor.url.bug" value="https://github.com/adoptium/adoptium-support/issues"/>
    <property name="java.io.tmpdir" value="C:\Users\<USER>\AppData\Local\Temp\"/>
    <property name="java.version" value="21.0.4"/>
    <property name="user.dir" value="C:\eddiewtchoi\workspace\ide\git\change-dashboard"/>
    <property name="os.arch" value="amd64"/>
    <property name="java.vm.specification.name" value="Java Virtual Machine Specification"/>
    <property name="PID" value="48960"/>
    <property name="sun.os.patch.level" value=""/>
    <property name="CONSOLE_LOG_CHARSET" value="UTF-8"/>
    <property name="native.encoding" value="Cp1252"/>
    <property name="java.library.path" value="C:\sandbox\devtools\java\jdk-21.0.4+7\bin;C:\Windows\Sun\Java\bin;C:\Windows\system32;C:\Windows;C:\Windows\CCM\;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;c:\Program Files (x86)\Sennheiser\SenncomSDK\;C:\Program Files (x86)\SafeCom\SafeComPrintClient;C:\Windows\system32\config\systemprofile\AppData\Local\Microsoft\WindowsApps;C:\Program Files (x86)\Adaptiva\AdaptivaClient\bin\x32;C:\Program Files (x86)\Adaptiva\AdaptivaClient\bin\x64;C:\Program Files\Git-2.41.0\cmd;C:\Program Files\Microsoft VS Code\bin;C:\Users\<USER>\go\bin;C:\sandbox\devtools\golang\1.20\go\bin;C:\sandbox\devtools\google-cloud-sdk\239.0.0\google-cloud-sdk\bin;C:\sandbox\devtools\node\node-v15.14.0-win-x64\;C:\sandbox\devtools\lein;C:\sandbox\devtools\gradle\gradle-4.6\bin;C:\sandbox\devtools\maven\apache-maven-3.9.8\bin;C:\sandbox\devtools\git\2.44.0\usr\bin;C:\sandbox\devtools\git\2.44.0\bin;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\sandbox\devtools\postgresql\14.2-1\pgsql\bin;;c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\github.copilot-chat\debugCommand;."/>
    <property name="java.vm.info" value="mixed mode, sharing"/>
    <property name="stderr.encoding" value="Cp1252"/>
    <property name="java.vendor" value="Eclipse Adoptium"/>
    <property name="java.vm.version" value="21.0.4+7-LTS"/>
    <property name="sun.io.unicode.encoding" value="UnicodeLittle"/>
    <property name="java.class.version" value="65.0"/>
    <property name="LOGGED_APPLICATION_NAME" value="[change-dashboard] "/>
  </properties>
  <testcase name="contextLoads" classname="com.hsbc.changedashboard.ChangeDashboardApplicationTests" time="0.907">
    <system-out><![CDATA[18:03:42.491 [main] INFO org.springframework.test.context.support.AnnotationConfigContextLoaderUtils -- Could not detect default configuration classes for test class [com.hsbc.changedashboard.ChangeDashboardApplicationTests]: ChangeDashboardApplicationTests does not declare any static, non-private, non-final, nested classes annotated with @Configuration.
18:03:42.569 [main] INFO org.springframework.boot.test.context.SpringBootTestContextBootstrapper -- Found @SpringBootConfiguration com.hsbc.changedashboard.ChangeDashboardApplication for test class com.hsbc.changedashboard.ChangeDashboardApplicationTests

  .   ____          _            __ _ _
 /\\ / ___'_ __ _ _(_)_ __  __ _ \ \ \ \
( ( )\___ | '_ | '_| | '_ \/ _` | \ \ \ \
 \\/  ___)| |_)| | | | | || (_| |  ) ) ) )
  '  |____| .__|_| |_|_| |_\__, | / / / /
 =========|_|==============|___/=/_/_/_/
 :: Spring Boot ::                (v3.2.0)

2025-06-05T18:03:42.911+08:00  INFO 48960 --- [change-dashboard] [           main] c.h.c.ChangeDashboardApplicationTests    : Starting ChangeDashboardApplicationTests using Java 21.0.4 with PID 48960 (started by 43402948 in C:\eddiewtchoi\workspace\ide\git\change-dashboard)
2025-06-05T18:03:42.912+08:00 DEBUG 48960 --- [change-dashboard] [           main] c.h.c.ChangeDashboardApplicationTests    : Running with Spring Boot v3.2.0, Spring v6.1.1
2025-06-05T18:03:42.912+08:00  INFO 48960 --- [change-dashboard] [           main] c.h.c.ChangeDashboardApplicationTests    : No active profile set, falling back to 1 default profile: "default"
2025-06-05T18:03:43.765+08:00  INFO 48960 --- [change-dashboard] [           main] o.s.b.a.w.s.WelcomePageHandlerMapping    : Adding welcome page: class path resource [static/index.html]
2025-06-05T18:03:44.086+08:00  INFO 48960 --- [change-dashboard] [           main] o.s.b.a.e.web.EndpointLinksResolver      : Exposing 1 endpoint(s) beneath base path '/actuator'
2025-06-05T18:03:44.124+08:00  INFO 48960 --- [change-dashboard] [           main] c.h.c.ChangeDashboardApplicationTests    : Started ChangeDashboardApplicationTests in 1.44 seconds (process running for 2.199)
]]></system-out>
    <system-err><![CDATA[WARNING: A Java agent has been loaded dynamically (C:\sandbox\maven\repository\net\bytebuddy\byte-buddy-agent\1.14.10\byte-buddy-agent-1.14.10.jar)
WARNING: If a serviceability tool is in use, please run with -XX:+EnableDynamicAgentLoading to hide this warning
WARNING: If a serviceability tool is not in use, please run with -Djdk.instrument.traceUsage for more information
WARNING: Dynamic loading of agents will be disallowed by default in a future release
]]></system-err>
  </testcase>
</testsuite>