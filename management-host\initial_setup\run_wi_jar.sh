#!/usr/bin/env bash

set -euo

env_code=$1
project_id=`gcloud config list --format 'value(core.project)'`

cp -R /tmp/build/workdir/unity-gcp-terraform .

cd unity-gcp-terraform
mkdir -p Credentials
cd Credentials
echo "credentials path: $(pwd)}"

gsutil cp "gs://${project_id}-key-management/gce-stage3-image-builder/gce-stage3-image-builder.json" .
gsutil cp "gs://${project_id}-key-management/runtime-gke/runtime-gke.json" .

touch kubeconfig

image_builder_credentials_path=$(realpath "gce-stage3-image-builder.json")
runtime_gke_credentials_path=$(realpath "runtime-gke.json")
kubeconfig_path=$(realpath "kubeconfig")

echo "downloaded credentials files:\n image builder:${image_builder_credentials_path}\n runtime gke:${runtime_gke_credentials_path}\nkubeconfig:${kubeconfig_path}"

cd ../..

cd "unity-gcp-terraform/groovy_scripts"

echo "running secret refresh in $(pwd)"

mkdir -p target
cd target

cp /opt/wi_creator/groovy_scripts-1.0-SNAPSHOT-jar-with-dependencies.jar .

echo "running secret refresh in $(pwd)"

java -Dhost=REMOTE \
-Denv=${env_code} \
-Dcluster=UNITY \
-Dgoogle_default_credentials_file_path="$image_builder_credentials_path" \
-Dgoogle_workload_identity_credentials_path="$runtime_gke_credentials_path" \
-cp groovy_scripts-1.0-SNAPSHOT-jar-with-dependencies.jar \
unity.scripts.ReplaceServiceAccountKey

