import com.poc.hss.fasttrack.transform.AbstractTransformer
import com.poc.hss.fasttrack.transform.model.TransformerBatchInputContext
import com.poc.hss.fasttrack.transform.model.TransformerRecord
import io.vertx.core.json.JsonObject
import org.slf4j.Logger
import org.slf4j.LoggerFactory

import java.util.stream.Collectors

class NationalHolidayTransformer extends AbstractTransformer {
    JsonObject constructRecord(JsonObject source){
        return new JsonObject()
                .put("site", source.getString("SITE")?.trim())
                .put("X3CTCD", source.getString("X3CTCD")?.trim())
                .put("X3HOLA", source.getString("X3HOLA")?.trim())
                .put("X3YYMM", source.getString("X3YYMM")?.trim())
                .put("X3HLDY", source.getString("X3HLDY")?.trim())
    }

    private static final Logger logger = LoggerFactory.getLogger(NationalHolidayTransformer.class)

    @Override
    List<TransformerRecord> batchTransform(TransformerBatchInputContext context) {
        List<TransformerRecord> records = context.getRecords()
        return records.stream().map({
            rec ->
                JsonObject source = rec.getData();
                return rec.from(rec).data(constructRecord(source)).build()
        }).collect(Collectors.toList())
    }
}