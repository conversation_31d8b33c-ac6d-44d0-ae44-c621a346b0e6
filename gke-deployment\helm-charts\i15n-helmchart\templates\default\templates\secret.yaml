{{- $folderpath := printf "%s/secretFiles/*" .Values.nameOverride }}
{{- $secretFiles := .Files.Glob $folderpath  }}
{{- if $secretFiles }}
apiVersion: v1
kind: Secret
metadata:
  name: {{ include "i15n-helmchart.fullname" . }}
  namespace: {{ .Values.namespace }}
  labels:
    {{- include "i15n-helmchart.labels" . | nindent 4 }}
data:
{{- $path := printf "%s/secretFiles/*" .Values.nameOverride }}
{{ (.Files.Glob $path).AsSecrets | indent 2 }}

{{- end }}
