{{- if .Values.customServiceAccount.create -}}
apiVersion: v1
kind: ServiceAccount
metadata:
  labels:
    {{- include "kube-state-metrics.labels" . | indent 4 }}
  name: {{ template "kube-state-metrics.serviceAccountName" . }}
  namespace: {{ template "kube-state-metrics.namespace" . }}
{{- if .Values.customServiceAccount.annotations }}
  annotations:
{{ toYaml .Values.customServiceAccount.annotations | indent 4 }}
{{- end }}
imagePullSecrets:
{{ toYaml .Values.customServiceAccount.imagePullSecrets | indent 2 }}
{{- end -}}
