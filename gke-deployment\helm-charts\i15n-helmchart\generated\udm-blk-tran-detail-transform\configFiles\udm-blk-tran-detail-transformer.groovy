

import com.poc.hss.fasttrack.transform.AbstractTransformer
import com.poc.hss.fasttrack.transform.model.TransformerBatchInputContext
import com.poc.hss.fasttrack.transform.model.TransformerRecord
import io.vertx.core.json.JsonObject
import org.slf4j.Logger
import org.slf4j.LoggerFactory

import java.util.stream.Collectors

class BulkTradingTransactionDetailTransformer extends AbstractTransformer {
    JsonObject constructRecord(JsonObject source){
        return new JsonObject()
                .put("site", source.getString("SITE")?.trim())
                .put("TBTXFT", source.getString("TBTXFT")?.trim())
                .put("TBTXFS", source.getString("TBTXFS")?.trim())
                .put("TBTXFQ", source.getString("TBTXFQ")?.trim())
                .put("TBTXFX", source.getString("TBTXFX")?.trim())
                .put("TBPLRF", source.getString("TBPLRF")?.trim())
                .put("TBBTPC", source.getString("TBBTPC")?.trim())
                .put("TBNOCT", source.getString("TBNOCT")?.trim())
                .put("event_source", source.getString("FILE_NAME", "")?.trim())
                .put("transaction_id",String.format("%s-%s-%06d-%02d-%01d-%08d",source.getString("SITE")?.trim().toUpperCase(),source.getString("TBTXFT")?.trim(),getInteger("TBTXFS",source)
                        ,getInteger("TBTXFQ",source),getInteger("TBTXFX",source),getInteger("TBTRDT",source)))
                .put("partition_key",String.format("%s-%s-%06d-%02d-%01d",source.getString("SITE")?.trim().toUpperCase(),source.getString("TBTXFT")?.trim(),getInteger("TBTXFS",source)
                        ,getInteger("TBTXFQ",source),getInteger("TBTXFX",source)))
    }

    static Integer getInteger( String key,JsonObject obj) {
        String result = obj.getString(key);
        if (result != null && !result.trim().equals("")) {
            return new BigDecimal(result).intValueExact();
        } else {
            return 0;
        }
    }

    private static final Logger logger = LoggerFactory.getLogger(BulkTradingTransactionDetailTransformer.class)

    @Override
    List<TransformerRecord> batchTransform(TransformerBatchInputContext context) {
        List<TransformerRecord> records = context.getRecords()
        return records.stream().map({
            rec ->
                JsonObject source = rec.getData();
                JsonObject transformedSource = constructRecord(source);
                return rec.from(rec).data(transformedSource).kafkaPartitionKey(transformedSource.getString("partition_key")).build()
        }).collect(Collectors.toList())
    }
}
