#!/bin/bash
set -euo pipefail

# Master build and deployment script for change-dashboard and risc
echo "=== Building and Deploying change-dashboard and risc ==="

# Configuration
PROJECTS=("change-dashboard" "risc")
BUILD_ONLY=false
DEPLOY_ONLY=false
ENVIRONMENT="dev"
NAMESPACE=""

# Function to print usage
usage() {
    echo "Usage: $0 [options]"
    echo ""
    echo "Options:"
    echo "  --build-only        Only build projects, skip deployment"
    echo "  --deploy-only       Only deploy projects, skip build"
    echo "  --environment ENV   Target environment: dev|uat|prod (default: dev)"
    echo "  --namespace NS      Target namespace (default: auto-detected from environment)"
    echo "  --help, -h          Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0                                    # Build and deploy to dev"
    echo "  $0 --build-only                      # Only build projects"
    echo "  $0 --deploy-only --environment uat   # Only deploy to UAT"
    echo "  $0 --environment prod                # Build and deploy to production"
    exit 1
}

# Parse arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --build-only)
            BUILD_ONLY=true
            DEPLOY_ONLY=false
            shift
            ;;
        --deploy-only)
            BUILD_ONLY=false
            DEPLOY_ONLY=true
            shift
            ;;
        --environment)
            ENVIRONMENT="$2"
            shift 2
            ;;
        --namespace)
            NAMESPACE="$2"
            shift 2
            ;;
        --help|-h)
            usage
            ;;
        *)
            echo "Error: Unknown option $1"
            usage
            ;;
    esac
done

# Validate environment
case "${ENVIRONMENT}" in
    "dev"|"uat"|"prod")
        ;;
    *)
        echo "Error: Invalid environment '${ENVIRONMENT}'. Must be dev, uat, or prod"
        exit 1
        ;;
esac

# Set default namespace if not provided
if [ -z "${NAMESPACE}" ]; then
    NAMESPACE="ns-i15n-${ENVIRONMENT}"
fi

echo "Configuration:"
echo "  Build Only: ${BUILD_ONLY}"
echo "  Deploy Only: ${DEPLOY_ONLY}"
echo "  Environment: ${ENVIRONMENT}"
echo "  Namespace: ${NAMESPACE}"
echo "  Projects: ${PROJECTS[*]}"

# Build phase
if [ "${BUILD_ONLY}" = "true" ] || [ "${DEPLOY_ONLY}" = "false" ]; then
    echo ""
    echo "=== BUILD PHASE ==="
    
    for project in "${PROJECTS[@]}"; do
        echo ""
        echo "--- Building ${project} ---"

        if [ -f "build-project.sh" ]; then
            chmod +x "build-project.sh"
            ./build-project.sh "${project}"

            if [ $? -eq 0 ]; then
                echo "✅ Build successful for ${project}"
            else
                echo "❌ Build failed for ${project}"
                exit 1
            fi
        else
            echo "❌ Generic build script not found: build-project.sh"
            exit 1
        fi
    done
    
    echo ""
    echo "✅ All builds completed successfully"
fi

# Deploy phase
if [ "${DEPLOY_ONLY}" = "true" ] || [ "${BUILD_ONLY}" = "false" ]; then
    echo ""
    echo "=== DEPLOYMENT PHASE ==="
    
    # Check prerequisites
    echo "Checking deployment prerequisites..."
    
    # Check kubectl
    if ! command -v kubectl &> /dev/null; then
        echo "❌ kubectl not found. Please install kubectl"
        exit 1
    fi
    
    # Check helm
    if ! command -v helm &> /dev/null; then
        echo "❌ helm not found. Please install helm"
        exit 1
    fi
    
    # Check cluster connectivity
    echo "Checking cluster connectivity..."
    kubectl cluster-info --request-timeout=10s > /dev/null || {
        echo "❌ Cannot connect to Kubernetes cluster"
        echo "Please ensure kubectl is configured and you have access to the cluster"
        exit 1
    }
    
    echo "✅ Prerequisites check passed"
    
    for project in "${PROJECTS[@]}"; do
        echo ""
        echo "--- Deploying ${project} ---"

        if [ -f "deploy-project.sh" ]; then
            chmod +x "deploy-project.sh"
            ./deploy-project.sh "${project}" --environment "${ENVIRONMENT}" --namespace "${NAMESPACE}"

            if [ $? -eq 0 ]; then
                echo "✅ Deployment successful for ${project}"
            else
                echo "❌ Deployment failed for ${project}"
                exit 1
            fi
        else
            echo "❌ Generic deployment script not found: deploy-project.sh"
            exit 1
        fi
    done
    
    echo ""
    echo "✅ All deployments completed successfully"
fi

echo ""
echo "=== SUMMARY ==="
echo "Projects processed: ${PROJECTS[*]}"
if [ "${BUILD_ONLY}" = "false" ] && [ "${DEPLOY_ONLY}" = "false" ]; then
    echo "✅ Build and deployment completed successfully for all projects"
elif [ "${BUILD_ONLY}" = "true" ]; then
    echo "✅ Build completed successfully for all projects"
elif [ "${DEPLOY_ONLY}" = "true" ]; then
    echo "✅ Deployment completed successfully for all projects"
fi

echo ""
echo "Access URLs (if ingress is enabled):"
case "${ENVIRONMENT}" in
    "dev")
        echo "  change-dashboard: https://change-dashboard.hsbc-9087302-unity-dev.dev.gcp.cloud.hk.hsbc"
        echo "  risc: https://risc.hsbc-9087302-unity-dev.dev.gcp.cloud.hk.hsbc"
        ;;
    "uat")
        echo "  change-dashboard: https://change-dashboard.hsbc-9087302-unity-uat.uat.gcp.cloud.hk.hsbc"
        echo "  risc: https://risc.hsbc-9087302-unity-uat.uat.gcp.cloud.hk.hsbc"
        ;;
    "prod")
        echo "  change-dashboard: https://change-dashboard.hsbc-9087302-unity-prod.prod.gcp.cloud.hk.hsbc"
        echo "  risc: https://risc.hsbc-9087302-unity-prod.prod.gcp.cloud.hk.hsbc"
        ;;
esac
