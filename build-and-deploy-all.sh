#!/bin/bash
set -euo pipefail

# Master build and deployment script for change-dashboard and risc
echo "=== Building and Deploying change-dashboard and risc ==="

# Configuration
PROJECTS=("change-dashboard" "risc")
BUILD_ONLY=${1:-false}
DEPLOY_ONLY=${2:-false}

# Function to print usage
usage() {
    echo "Usage: $0 [build-only|deploy-only]"
    echo "  build-only  : Only build projects, skip deployment"
    echo "  deploy-only : Only deploy projects, skip build"
    echo "  (no args)   : Build and deploy both projects"
    exit 1
}

# Parse arguments
if [ $# -gt 1 ]; then
    usage
fi

if [ $# -eq 1 ]; then
    case $1 in
        "build-only")
            BUILD_ONLY=true
            DEPLOY_ONLY=false
            ;;
        "deploy-only")
            BUILD_ONLY=false
            DEPLOY_ONLY=true
            ;;
        *)
            usage
            ;;
    esac
fi

echo "Configuration:"
echo "  Build Only: ${BUILD_ONLY}"
echo "  Deploy Only: ${DEPLOY_ONLY}"
echo "  Projects: ${PROJECTS[*]}"

# Build phase
if [ "${BUILD_ONLY}" = "true" ] || [ "${DEPLOY_ONLY}" = "false" ]; then
    echo ""
    echo "=== BUILD PHASE ==="
    
    for project in "${PROJECTS[@]}"; do
        echo ""
        echo "--- Building ${project} ---"
        
        if [ -f "build-${project}.sh" ]; then
            chmod +x "build-${project}.sh"
            ./build-${project}.sh
            
            if [ $? -eq 0 ]; then
                echo "✅ Build successful for ${project}"
            else
                echo "❌ Build failed for ${project}"
                exit 1
            fi
        else
            echo "❌ Build script not found: build-${project}.sh"
            exit 1
        fi
    done
    
    echo ""
    echo "✅ All builds completed successfully"
fi

# Deploy phase
if [ "${DEPLOY_ONLY}" = "true" ] || [ "${BUILD_ONLY}" = "false" ]; then
    echo ""
    echo "=== DEPLOYMENT PHASE ==="
    
    # Check prerequisites
    echo "Checking deployment prerequisites..."
    
    # Check kubectl
    if ! command -v kubectl &> /dev/null; then
        echo "❌ kubectl not found. Please install kubectl"
        exit 1
    fi
    
    # Check helm
    if ! command -v helm &> /dev/null; then
        echo "❌ helm not found. Please install helm"
        exit 1
    fi
    
    # Check cluster connectivity
    echo "Checking cluster connectivity..."
    kubectl cluster-info --request-timeout=10s > /dev/null || {
        echo "❌ Cannot connect to Kubernetes cluster"
        echo "Please ensure kubectl is configured and you have access to the cluster"
        exit 1
    }
    
    echo "✅ Prerequisites check passed"
    
    for project in "${PROJECTS[@]}"; do
        echo ""
        echo "--- Deploying ${project} ---"
        
        if [ -f "deploy-${project}.sh" ]; then
            chmod +x "deploy-${project}.sh"
            ./deploy-${project}.sh
            
            if [ $? -eq 0 ]; then
                echo "✅ Deployment successful for ${project}"
            else
                echo "❌ Deployment failed for ${project}"
                exit 1
            fi
        else
            echo "❌ Deployment script not found: deploy-${project}.sh"
            exit 1
        fi
    done
    
    echo ""
    echo "✅ All deployments completed successfully"
fi

echo ""
echo "=== SUMMARY ==="
echo "Projects processed: ${PROJECTS[*]}"
if [ "${BUILD_ONLY}" = "false" ] && [ "${DEPLOY_ONLY}" = "false" ]; then
    echo "✅ Build and deployment completed successfully for all projects"
elif [ "${BUILD_ONLY}" = "true" ]; then
    echo "✅ Build completed successfully for all projects"
elif [ "${DEPLOY_ONLY}" = "true" ]; then
    echo "✅ Deployment completed successfully for all projects"
fi

echo ""
echo "Access URLs (if ingress is enabled):"
echo "  change-dashboard: https://change-dashboard.hsbc-9087302-unity-dev.dev.gcp.cloud.hk.hsbc"
echo "  risc: https://risc.hsbc-9087302-unity-dev.dev.gcp.cloud.hk.hsbc"
