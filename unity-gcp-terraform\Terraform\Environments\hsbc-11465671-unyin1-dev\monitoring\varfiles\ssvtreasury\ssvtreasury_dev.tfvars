key               = "gcp"
unit              = "1"
label_value_type  = "STRING"
label_description = "metric for gcp instance"
api_key           = "ssvtreasury-dev-key"
team_name         = "ssvtreasury-dev"

channel_recipients = {
    display_name  = "ssvtreasury_Dev_Monitoring_recipients" 
    type = "webhook_basicauth"
    username = "f0851c183aac47568707a0d29697ab1f"
    url = "https://if-api.hsbc.co.uk/api/alertapi-baseauth?@aboutSource=GCP"
    password = "2e43F52B6701457fAf6431afA3F26Ad0"
}

//TODO: fill in
logs                = {
  custom_api_err = {
    name                = "ssvtreasury_Dev_Custom_API_internal_error"
    filter              = "resource.type:\"k8s_container\" AND jsonPayload.message: \"UNY2ERR500\" and resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type          = "INT64"
    unit                = "1"
    display_name        = "Custom API internal error - Unexcepted error was raised during the invocation of a custom API"
    labels              = []
    label_extractors    = {}
  },
  pipeline_app_err = {
    name                = "ssvtreasury_Dev_Pipeline_application_fatal_error"
    filter              = "resource.type:\"k8s_container\" AND jsonPayload.message: \"UNY2ERR999\" and resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type          = "INT64"
    unit                = "1"
    display_name        = "Unexpected error has caused the Unity 2.0 ETL pipelines to fail - container would be restarted"
    labels              = []
    label_extractors    = {}
  }
}

metrics = {
  Sql_Connections = {
    name                = "SQL Connections - Google Cloud CloudSQl PostGreSQL - Connections"
    object_name          = "sqlcon"
    alert_severity       = "WARNING"     
    combiner            = "OR"
    filter              = "metric.type=\"cloudsql.googleapis.com/database/network/connections\" AND resource.type=\"cloudsql_database\" AND resource.label.database_id=has_substring(\""
    duration            = "120s"
    comparison          = "COMPARISON_GT"
    cross_series_reducer= ""
    alignment_period    = "120s"
    per_series_aligner  = "ALIGN_MAX"
    threshold           = 20
    trigger_count       = 1
    group_by_fields     =  ["resource.label.database_id"]
    api_key             = ""
    enabled             = true
  },
  Sql_cpu_utilization = {
    name                = "SQL CPU Utilization - Google Cloud CloudSQl PostGreSQL - SQL CPU Utilization"
    object_name          = "sqlcpu"
    alert_severity       = "WARNING"     
    combiner            = "OR"
    filter              = "metric.type=\"cloudsql.googleapis.com/database/cpu/utilization\" AND resource.type=\"cloudsql_database\" AND resource.label.database_id=has_substring(\""
    duration            = "180s"
    comparison          = "COMPARISON_GT"
    cross_series_reducer= ""
    alignment_period    = "60s"
    per_series_aligner  = "ALIGN_MEAN"
    threshold           = 0.9
    trigger_count       = 1
    group_by_fields     =  ["resource.label.database_id"]
    api_key             = ""
    enabled             = true
  },
  Sql_disk_io_reads = {
    name                = "SQL Disk I/O Reads > 120 /s - Google Cloud CloudSQl PostGreSQL - SQL Disk I/O Reads"
    object_name          = "sqldicread"
    alert_severity       = "WARNING"     
    combiner            = "OR"
    filter              = "metric.type=\"cloudsql.googleapis.com/database/disk/read_ops_count\" AND resource.type=\"cloudsql_database\" AND resource.label.database_id=has_substring(\""
    duration            = "120s"
    comparison          = "COMPARISON_GT"
    cross_series_reducer= ""
    alignment_period    = "120s"
    per_series_aligner  = "ALIGN_RATE"
    threshold           = 120
    trigger_count       = 1
    group_by_fields     =  ["resource.label.database_id"]
    api_key             = ""
    enabled             = true
  },
  Sql_disk_io_writes = {
    name                = "SQL Disk I/O Writes > 90 /s - Google Cloud CloudSQl PostGreSQL - SQL Disk I/O Writes"
    object_name          = "sqldiscwrite"
    alert_severity       = "WARNING"     
    combiner            = "OR"
    filter              = "metric.type=\"cloudsql.googleapis.com/database/disk/write_ops_count\" AND resource.type=\"cloudsql_database\" AND resource.label.database_id=has_substring(\""
    duration            = "300s"
    comparison          = "COMPARISON_GT"
    cross_series_reducer= ""
    alignment_period    = "300s"
    per_series_aligner  = "ALIGN_RATE"
    threshold           = 90
    trigger_count       = 1
    group_by_fields     =  ["resource.label.database_id"]
    api_key             = ""
    enabled             = true
  },
  Sql_disk_utilization = {
    name                = "SQL Disk Utilization  - Google Cloud CloudSQl PostGreSQL - SQL Disk Utilization"
    object_name          = "sqldiscut"
    alert_severity       = "WARNING"     
    combiner            = "OR"
    filter              = "metric.type=\"cloudsql.googleapis.com/database/disk/utilization\" AND resource.type=\"cloudsql_database\" AND resource.label.database_id=has_substring(\""
    duration            = "120s"
    comparison          = "COMPARISON_GT"
    cross_series_reducer= ""
    alignment_period    = "120s"
    per_series_aligner  = "ALIGN_PERCENT_CHANGE"
    threshold           = 0.8
    trigger_count       = 1
    group_by_fields     =  ["resource.label.database_id"]
    api_key             = ""
    enabled             = true
  },
  Sql_memory_utilization = {
    name                = "SQL Memory Utilization  - Google Cloud CloudSQl PostGreSQL - SQL Memory Utilization"
    object_name          = "sqlmem"
    alert_severity       = "WARNING"     
    combiner            = "OR"
    filter              = "metric.type=\"cloudsql.googleapis.com/database/memory/utilization\" AND resource.type=\"cloudsql_database\" AND resource.label.database_id=has_substring(\""
    duration            = "300s"
    comparison          = "COMPARISON_GT"
    cross_series_reducer= ""
    alignment_period    = "300s"
    per_series_aligner  = "ALIGN_PERCENT_CHANGE"
    threshold           = 0.8
    trigger_count       = 1
    group_by_fields     =  ["resource.label.database_id"]
    api_key             = ""
    enabled             = true
  },
  Sql_send_byte_rate = {
    name                = "SQL Byte Rate > 80MBytes/s - Google Cloud CloudSQl PostGreSQL - SQL Byte Rate"
    object_name          = "sqlbytesrate"
    alert_severity       = "WARNING"     
    combiner            = "OR"
    filter              = "metric.type=\"cloudsql.googleapis.com/database/network/sent_bytes_count\" AND resource.type=\"cloudsql_database\" AND resource.label.database_id=has_substring(\""
    duration            = "120s"
    comparison          = "COMPARISON_GT"
    cross_series_reducer= ""
    alignment_period    = "120s"
    per_series_aligner  = "ALIGN_RATE"
    threshold           = 80000000
    trigger_count       = 1
    group_by_fields     = ["resource.label.database_id"]
    api_key             = ""
    enabled             = true
  },
  Gke_containers_cpu = {
    name                = "GKE Container CPU - Google GKE Container - GKE Container CPU utilization"
    object_name          = "gkecpu"
    alert_severity       = "WARNING"     
    combiner            = "OR"
    filter              = "metric.type=\"kubernetes.io/container/cpu/limit_utilization\" AND resource.type=\"k8s_container\" AND resource.label.namespace_name=has_substring(\""
    duration            = "300s"
    comparison          = "COMPARISON_GT"
    cross_series_reducer= ""
    alignment_period    = "300s"
    per_series_aligner  = "ALIGN_MEAN"
    threshold           = 0.8
    trigger_count       = 1
    group_by_fields     = ["resource.label.container_name"]
    api_key             = ""
    enabled             = true
  },  
  Gke_containers_memory = {
    name                = "GKE Container Memory - Google GKE Container - GKE Container memory limit utilization"
    object_name          = "gkemem"
    alert_severity       = "WARNING"     
    combiner            = "OR"
    filter              = "metric.type=\"kubernetes.io/container/memory/limit_utilization\" AND resource.type=\"k8s_container\" AND resource.label.namespace_name=has_substring(\""
    duration            = "300s"
    comparison          = "COMPARISON_GT"
    cross_series_reducer= ""
    alignment_period    = "300s"
    per_series_aligner  = "ALIGN_MEAN"
    threshold           = 0.8
    trigger_count       = 1
    group_by_fields     = ["resource.label.container_name"]
    api_key             = ""
    enabled             = true
  },
  Gke_containers_restarts = {
    name                = "GKE Container Restarts - Google GKE Container - GKE Container Restarts"
    object_name          = "gkerestarts"
    alert_severity       = "WARNING"     
    combiner            = "OR"
    filter              = "metric.type=\"kubernetes.io/container/restart_count\" AND resource.type=\"k8s_container\" AND resource.label.namespace_name=has_substring(\""
    duration            = "120s"
    comparison          = "COMPARISON_GT"
    cross_series_reducer= "REDUCE_SUM"
    alignment_period    = "120s"
    per_series_aligner  = "ALIGN_RATE"
    threshold           = 3
    trigger_count       = 1
    group_by_fields     = ["resource.label.container_name"]
    api_key             = ""
    enabled             = true
   },
 Custom_API_internal_error = {
    name                = "Custom API internal error - Unexcepted error was raised during the invocation of a custom API"
    object_name          = "custapierr"
    alert_severity       = "WARNING"
    combiner            = "OR"
    filter              = "metric.type=\"logging.googleapis.com/user/ssvtreasury_Dev_Custom_API_internal_error\" AND resource.type=\"k8s_container\" AND resource.label.namespace_name=has_substring(\""
    duration            = "60s"
    comparison          = "COMPARISON_GT"
    cross_series_reducer= "REDUCE_COUNT"
    alignment_period    = "60s"
    per_series_aligner  = "ALIGN_DELTA"
    threshold           = 0
    trigger_count       = 1
    group_by_fields     = ["metric.labels.log","resource.label.namespace_name"]
    api_key             = ""
    enabled             = true
  },
   Pipeline_api_internal_error = {
    name                = "Pipeline application fatal error - Unexpected error has caused the Unity 2.0 ETL pipelines to fail"
    object_name          = "pipapiinterr"
    alert_severity       = "WARNING"
    combiner            = "OR"
    filter              = "metric.type=\"logging.googleapis.com/user/ssvtreasury_Dev_Pipeline_application_fatal_error\" AND resource.type=\"k8s_container\" AND resource.label.namespace_name=has_substring(\""
    duration            = "60s"
    comparison          = "COMPARISON_GT"
    cross_series_reducer= "REDUCE_COUNT"
    alignment_period    = "60s"
    per_series_aligner  = "ALIGN_DELTA"
    threshold           = 0
    trigger_count       = 1
    group_by_fields     = ["metric.labels.log","resource.label.namespace_name"]
    api_key             = ""
    enabled             = true
  },
   Sql_server_is_down = {
    name                = "CloudSQL is Down - Unexpected error caused Cloud SQL to be down"
    object_name          = "sql-server-is-down"
    alert_severity       = "WARNING"
    combiner            = "OR"
    filter              = "metric.type=\"cloudsql.googleapis.com/database/up\" resource.type=\"cloudsql_database\" AND resource.label.database_id=has_substring(\""
    duration            = "0s"
    comparison          = "COMPARISON_LT"
    cross_series_reducer= ""
    alignment_period    = "60s"
    per_series_aligner  = "ALIGN_MEAN"
    threshold           = 1
    trigger_count       = 1
    group_by_fields     = []
    api_key             = ""
    enabled             = true
  },
  Sql_server_available_for_failover = {
    name                = "CloudSQL is Down - Database Server failover unavaliable"
    object_name          = "sql-failover-is-down"
    alert_severity       = "WARNING"
    combiner            = "OR"
    filter              = "metric.type=\"cloudsql.googleapis.com/database/up\" resource.type=\"cloudsql_database\" AND resource.label.database_id=has_substring(\""
    duration            = "180s"
    comparison          = "COMPARISON_LT"
    cross_series_reducer= ""
    alignment_period    = "60s"
    per_series_aligner  = "ALIGN_MEAN"
    threshold           = 1
    trigger_count       = 1
    group_by_fields     = []
    api_key             = ""
    enabled             = true
  },
  Sql_server_uptime = {
    name                = "CloudSQL is Down - Database Server down for more than 5 min"
    object_name          = "sql-uptime"
    alert_severity       = "WARNING"
    combiner            = "OR"
    filter              = "metric.type=\"cloudsql.googleapis.com/database/uptime\" resource.type=\"cloudsql_database\" AND resource.label.database_id=has_substring(\""
    duration            = "300s"
    comparison          = "COMPARISON_LT"
    cross_series_reducer= ""
    alignment_period    = "60s"
    per_series_aligner  = "ALIGN_MEAN"
    threshold           = 1
    trigger_count       = 1
    group_by_fields     = []
    api_key             = ""
    enabled             = true
  },
    Sql_server_state = {
    name                = "CloudSQL failed - Database Server is in state failed for 3 minutes"
    object_name          = "sql-state"
    alert_severity       = "WARNING"
    combiner            = "OR"
    filter              = "metric.type=\"cloudsql.googleapis.com/database/instance_state\" resource.type=\"cloudsql_database\" AND metric.label.state=\"FAILED\" AND resource.label.database_id=has_substring(\""
    duration            = "180s"
    comparison          = "COMPARISON_GT"
    cross_series_reducer= ""
    alignment_period    = "60s"
    per_series_aligner  = "ALIGN_COUNT_TRUE"
    threshold           = 0
    trigger_count       = 1
    group_by_fields     = []
    api_key             = ""
    enabled             = true
  }
}