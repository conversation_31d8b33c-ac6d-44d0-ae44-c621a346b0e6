

import com.poc.hss.fasttrack.transform.AbstractTransformer
import com.poc.hss.fasttrack.transform.model.TransformerBatchInputContext
import com.poc.hss.fasttrack.transform.model.TransformerRecord
import io.vertx.core.json.JsonObject
import org.slf4j.Logger
import org.slf4j.LoggerFactory

import java.util.stream.Collectors

class UdmLocSecRdtranTransformer extends AbstractTransformer {

    private static final Logger logger = LoggerFactory.getLogger(UdmLocSecRdtranTransformer.class);

    JsonObject constructRecord(JsonObject source) {
        new JsonObject()
                .put("site", source.getString("SITE", "").trim().toUpperCase())
                .put("T8TXFT", source.getString("T8TXFT", "").trim().toUpperCase())
                .put("T8TXFS", source.getString("T8TXFS", "").trim().toUpperCase())
                .put("T8TXFQ", source.getString("T8TXFQ", "").trim().toUpperCase())
                .put("T8TXFX", source.getString("T8TXFX", "").trim().toUpperCase())
                .put("T8_ETI", source.getString("T8_ETI", "").trim().toUpperCase())
                .put("T8_FAI", source.getString("T8_FAI", "").trim().toUpperCase())
                .put("T8_FA1", source.getString("T8_FA1", "").trim().toUpperCase())
                .put("T8_FA2", source.getString("T8_FA2", "").trim().toUpperCase())
                .put("T8_FA3", source.getString("T8_FA3", "").trim().toUpperCase())
                .put("T8_FA4", source.getString("T8_FA4", "").trim().toUpperCase())
    }

    @Override
    List<TransformerRecord> batchTransform(TransformerBatchInputContext context) {
        List<TransformerRecord> records = context.getRecords()
        logger.info("=====first record, _id:{} data:{}",records.get(0).getId(),records.get(0).getData())
        return records.stream()
                .map(rec -> rec.from(rec).data(constructRecord(rec.getData())).build())
                .collect(Collectors.toList())
    }
}
