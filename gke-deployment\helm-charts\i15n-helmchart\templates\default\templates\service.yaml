{{- if .Values.service.enabled }}
apiVersion: v1
kind: Service
metadata:
  name: {{ include "i15n-helmchart.fullname" . }}
  namespace: {{ .Values.namespace }}
{{- if .Values.ingress.enabled }}
  annotations:
    networking.gke.io/load-balancer-type: "Internal"
    cloud.google.com/neg: '{"ingress":true}'
{{- end }}
  labels:
    {{- include "i15n-helmchart.labels" . | nindent 4 }}
spec:
  type: {{ .Values.service.type }}
  ports:
    - port: {{ .Values.service.port }}
      targetPort: {{ .Values.service.targetPort }}
      protocol: TCP
      name: http
  selector:
    {{- include "i15n-helmchart.selectorLabels" . | nindent 4 }}
{{- end }}