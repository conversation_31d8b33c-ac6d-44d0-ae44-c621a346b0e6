def AGENT = 'unity-dev-jenkins-agent'
def charts = []
def undeployCharts = []
def deploymentEnv = ''
def rolloutStatusCallbackUrl = ''
def rolloutStatusId = ''
def namespaces = []
def deployControlCharts = []
def deployControlUndeployCharts = []
def releaseCharts = []
def releaseUndeployCharts = []
def allowedUndeployCharts = []

def updateDeploymentBody = [:]
pipeline {
    agent { label env.AGENT_LABEL ?: AGENT }
    stages {
        stage('Connect GCP') {
            steps {
                script {
                    env.v_project = this.sh(
                            script: """gcloud config list --format 'value(core.project)'""",
                            returnStdout: true
                    ).trim()
                    env.v_clusterName = this.sh(
                            script: """gcloud container clusters list --filter="NAME:gke-t2-vpc3" --region=asia-east2 --format 'value(NAME)'""",
                            returnStdout: true
                    ).trim()
                    env.v_kubeCtlLib = this.sh(
                            script: """gcloud compute addresses list --filter="NAME:gke-kubectl-vpc3" --format 'value(ADDRESS)'""",
                            returnStdout: true
                    ).trim()
                }
            }
        }
        stage('Set Properties') {
            steps {
                script {
                    echo "ON BRANCH: ${env.GIT_BRANCH} "

                    dir('helm-charts/i15n-helmchart/generated/') {
                        if (fileExists('.env')) {
                            def envString = readFile('.env')
                            deploymentEnv = envString.split(',')[0]
                        }

                        if (fileExists('.version')) {
                            version = readFile('.version')
                            currentBuild.displayName = version
                        }

                        if (fileExists('.rolloutStatusCallbackUrl')) {
                            rolloutStatusCallbackUrl = readFile('.rolloutStatusCallbackUrl')
                        }
                    }
                    
                    echo "checking for deployment control"
                    cloneDeploymentControl(params.changeOrder)

                    if ("${chartsList}".isEmpty()) {

                        echo "chartsList is empty, calling checkoutDeploymentControl"

                        if (fileExists('gke-deployment-control')) {
                          dir('gke-deployment-control') {
                          if (fileExists('.chartList')) {
                              def chartsList = readFile('.chartList')
                              chartsList.readLines().each {
                                  String[] split = it.split(',');
                                  for (chart in split) {
                                    if (chart.trim()) {
                                      deployControlCharts.add(chart)
                                    }
                                  }
                              }
                           }
                         }
                        }

                        dir('helm-charts/i15n-helmchart/generated/') {
                            if (fileExists('.chartList')) {
                                def chartsList = readFile('.chartList')
                                chartsList.readLines().each {
                                    String[] split = it.split(',');
                                    for (chart in split) {
                                        releaseCharts.add(chart)
                                    }
                                }
                            }
                        }

                        if (deployControlCharts.isEmpty()) {
                            charts = releaseCharts
                        } else {
                            charts = deployControlCharts.findAll { it in releaseCharts }
                        }

                    } else if ("${chartsList}".equalsIgnoreCase("ALL")) {
                        dir('helm-charts/i15n-helmchart/generated/') {
                            foundFiles = findFiles()
                        }
                        for (f in foundFiles) {
                            if (f.directory) {
                                charts.add(f.name)
                            }
                        }
                    } else {
                        "${chartsList}".readLines().each {
                            String[] chartsList;
                            chartsList = it.split(',');
                            for (chart in chartsList) {
                                charts.add(chart)
                            }
                        }
                    }

                    if ("${undeployChartsList}".isEmpty()) {

                        echo "undeployChartsList is empty, calling checkoutDeploymentControl"

                        if (fileExists('gke-deployment-control')) {
                          dir('gke-deployment-control') {
                          if (fileExists('.undeployChartList')) {
                              def chartsList = readFile('.undeployChartList')
                              chartsList.readLines().each {
                                  String[] split = it.split(',');
                                  for (chart in split) {
                                    if (chart.trim()) {
                                      deployControlUndeployCharts.add(chart)
                                    }
                                  }
                              }
                           }
                         }
                        }

                        dir('helm-charts/i15n-helmchart/generated/') {
                            if (fileExists('.undeployChartList')) {
                                def undeployChartsList = readFile('.undeployChartList')
                                undeployChartsList.readLines().each {
                                    String[] split = it.split(',');
                                    for (chart in split) {
                                        releaseUndeployCharts.add(chart)
                                    }
                                }
                            }
                        }
                        
                        if (deployControlUndeployCharts.isEmpty()) {
                            undeployCharts = releaseUndeployCharts
                        } else {
                            allowedUndeployCharts = releaseCharts + releaseUndeployCharts
                            undeployCharts = deployControlUndeployCharts.findAll { it in allowedUndeployCharts }
                        }

                    } else if ("${undeployChartsList}".equalsIgnoreCase("ALL")) {
                        dir('helm-charts/i15n-helmchart/generated/') {
                            foundFiles = findFiles()
                        }
                        for (f in foundFiles) {
                            if (f.directory) {
                                undeployCharts.add(f.name)
                            }
                        }
                    } else {
                        "${undeployChartsList}".readLines().each {
                            String[] undeployChartsList;
                            undeployChartsList = it.split(',');
                            for (chart in undeployChartsList) {
                                undeployCharts.add(chart)
                            }
                        }
                    }

                    dir('helm-charts/i15n-helmchart/generated/') {
                        if (fileExists('.namespace')) {
                            namespaces.add(readFile('.namespace'))
                        }
                    }

                    charts = sortCharts(charts)
                    echo "charts: ${charts}."
                    echo "releaseCharts: ${releaseCharts}."
                    echo "releaseUndeployCharts: ${releaseUndeployCharts}."
                    echo "deployControlCharts: ${deployControlCharts}."
                    echo "deployControlUndeployCharts: ${deployControlUndeployCharts}."
                    echo "undeployCharts: ${undeployCharts}."                    
                    echo "namespaces: ${namespaces}."
                    echo "rolloutStatusCallbackUrl ${rolloutStatusCallbackUrl}"
                    def jobUserId
                    wrap([$class: 'BuildUser']) {
                        jobUserId = "${BUILD_USER_ID}"
                    }
                    echo "jobUserId ${jobUserId}"
                    jobUserId = jobUserId == "scmChange" ? "SYSTEM" : jobUserId
                    rolloutStatusId = startRollout(deploymentEnv, rolloutStatusCallbackUrl, charts, jobUserId)
                }
            }
        }
        stage('Undeploy helm charts') {
            steps {
                script {
                    for (chart in undeployCharts) {
                        for (namespace in namespaces) {
                            echo "Executing undeployment script for: ${chart} in ${namespace}"
                            retry(3) {
                                this.sh """set +x;
                                        gcloud container clusters get-credentials ${env.v_clusterName} --region=asia-east2;
                                        export HTTPS_PROXY=${env.v_kubeCtlLib}:3128;
                                        chmod +x ./undeploy.sh;
                                        ./undeploy.sh ${chart} ${namespace}"""
                            }
                        }
                    }
                }
            }
        }
        stage('Deploy helm charts') {
            steps {
                script {
                    def parallelBatches = charts.collate(10);
                    for (parallelBatch in parallelBatches) {
                        def tasks = [:]
                        for (tmpChart in parallelBatch) {
                            def chart = tmpChart
                            tasks["${chart}"] = {
                                for (namespace in namespaces) {
                                    echo "Downloading custom datasource configuration from gcp bucket"
                                    if (chart == "grafana") {
                                        this.sh """set -x;
                                          set +e;
                                          tree ./helm-charts/i15n-helmchart/generated/grafana
                                          if [[ ! -f "./helm-charts/i15n-helmchart/generated/grafana/datasource/plaintext/datasource.yaml" ]] ; then
                                             mkdir -p ./helm-charts/i15n-helmchart/generated/grafana/datasource/plaintext
                                             if [[ \$(gsutil ls gs://${v_project}-app-runtime-dependencies/secrets/env/gcp/grafana/${namespace}/datasource/plaintext/datasource.yaml 2> /dev/null) ]]; then
                                               gsutil cp gs://${v_project}-app-runtime-dependencies/secrets/env/gcp/grafana/${namespace}/datasource/plaintext/datasource.yaml ./helm-charts/i15n-helmchart/generated/grafana/datasource/plaintext/
                                               rm -f ./helm-charts/i15n-helmchart/templates/grafana/datasource/plaintext/datasource.yaml
                                             elif [[ \$(gsutil ls gs://${v_project}-app-runtime-dependencies/secrets/env/gcp/grafana/default/datasource/plaintext/datasource.yaml 2> /dev/null) ]]; then
                                               gsutil cp gs://${v_project}-app-runtime-dependencies/secrets/env/gcp/grafana/default/datasource/plaintext/datasource.yaml ./helm-charts/i15n-helmchart/generated/grafana/datasource/plaintext/
                                               rm -f ./helm-charts/i15n-helmchart/templates/grafana/datasource/plaintext/datasource.yaml
                                             fi
                                          else
                                            rm -f ./helm-charts/i15n-helmchart/templates/grafana/datasource/plaintext/datasource.yaml
                                          fi
                                          tree ./helm-charts/i15n-helmchart/generated/grafana"""
                                    }
                                    echo "Executing deployment script for: ${chart} in ${namespace}"
                                    retry(3) {
                                        this.sh """set +x;
                                                gcloud container clusters get-credentials ${env.v_clusterName} --region=asia-east2;
                                                export HTTPS_PROXY=${env.v_kubeCtlLib}:3128;
                                                chmod +x ./deploy.sh;
                                                ./deploy.sh ${chart} ${namespace} 5"""
                                    }
                                    updateDeploymentRollout(rolloutStatusId, rolloutStatusCallbackUrl, chart, 'In_Progress', 'Rollout in progress', updateDeploymentBody)
                                }
                            }
                        }
                        parallel tasks
                    }
                }
            }
        }
        stage('Wait until rollout complete') {
            options {
                timeout(time: 15, unit: "MINUTES")
            }
            steps {
                script {
                    def parallelBatches = charts.collate(10);
                    for (parallelBatch in parallelBatches) {
                        def tasks = [:]
                        for (tmpChart in parallelBatch) {
                            def chart = tmpChart
                            tasks["${chart}"] = {
                                try {
                                    for (namespace in namespaces) {
                                        echo "Checking rollout status for: ${chart} in ${namespace}"
                                        retry(3) {
                                            this.sh """set +x;
                                                gcloud container clusters get-credentials ${env.v_clusterName} --region=asia-east2;
                                                export HTTPS_PROXY=${env.v_kubeCtlLib}:3128;
                                                kubectl rollout status deployment ${chart} -n ${namespace} """
                                        }
                                        updateDeploymentRollout(rolloutStatusId, rolloutStatusCallbackUrl, chart, 'Success', 'Rollout Successfully', updateDeploymentBody)
                                    }
                                } catch (org.jenkinsci.plugins.workflow.steps.FlowInterruptedException e) {
                                    updateDeploymentRollout(rolloutStatusId, rolloutStatusCallbackUrl, chart, 'Fail', 'Rollout timeout', updateDeploymentBody)
                                    unstable(message: "Timeout reached, please check the status in GKE directly")
                                } catch (Throwable e) {
                                    updateDeploymentRollout(rolloutStatusId, rolloutStatusCallbackUrl, chart, 'Fail', e.getCause(), updateDeploymentBody)
                                    unstable(message: "Caught unexpected Exception: ${e.toString()}")
                                }
                            }
                        }
                        parallel tasks
                    }
                }
            }
        }
    }
    post {
        success {
            script {
                finishRollout(rolloutStatusId, rolloutStatusCallbackUrl, 'Success', 'Rollout successfully', updateDeploymentBody)
            }
        }
        unsuccessful {
            finishRollout(rolloutStatusId, rolloutStatusCallbackUrl, 'Fail', 'Rollout failed, please check Jenkins job url for more details.', updateDeploymentBody)
        }
    }
}

def startRollout(env, rolloutStatusCallbackUrl, charts, jobUserId) {
    def deploymentBody = "["
    charts.eachWithIndex { item, index ->
        deploymentBody = deploymentBody + """
        {
            "name": "${item}",
            "status": "Not_Started",
            "statusDescription": "Deployment not started"
        }
        """
        if (index != (charts.size() - 1)) {
            deploymentBody = deploymentBody + ','
        }
    }

    deploymentBody = deploymentBody + ']'


    def requestBodyPayload = """
        {
            "jobId":"${BUILD_ID}",
            "jobUrl": "${BUILD_URL}",
            "overallProgress": 0,
            "overallStatus": "In_Progress",
            "overallStatusDescription": "Rollout in progress",
            "envName": "${env}",
            "deployments" : ${deploymentBody},
            "createdBy": "${jobUserId}"
        }
    """
    println('requestPayload: ' + requestBodyPayload)
    try {
        def response
        retry(5) {
            response = httpRequest httpMode: 'PUT', contentType: 'APPLICATION_JSON', requestBody: requestBodyPayload, ignoreSslErrors: true, url: rolloutStatusCallbackUrl, timeout: 5
            println('Status: ' + response.status)
            println('Content: ' + response.content)
            def responseObj = readJSON text: response.content
            return responseObj['id']
        }
    } catch (Exception e) {
        echo "Caught unexpected Exception when updating rollout status: ${e.toString()}"
        return ''
    }
}

def finishRollout(rolloutStatusId, rolloutStatusCallbackUrl, status, statusDescription, updateDeploymentBody) {
    def updateDeploymentBodyList = updateDeploymentBody.collect { it -> it.value }
    def requestBodyPayload = """
        {
            "id": "${rolloutStatusId}",
            "overallStatus" : "${status}",
            "overallStatusDescription": "${statusDescription}",
            "deployments" : ${updateDeploymentBodyList}
        }
    """
    println('requestPayload: ' + requestBodyPayload)
    try {
        def response
        retry(5) {
            response = httpRequest httpMode: 'PATCH', contentType: 'APPLICATION_JSON', requestBody: requestBodyPayload, ignoreSslErrors: true, url: rolloutStatusCallbackUrl, timeout: 5
            println('Status: ' + response.status)
            println('Content: ' + response.content)
        }
    } catch (Exception e) {
        echo "Caught unexpected Exception when updating rollout status: ${e.toString()}"
        return ''
    }
}


def updateDeploymentRollout(rolloutStatusId, rolloutStatusCallbackUrl, deployment, status, statusDescription, updateDeploymentBody) {
    updateDeploymentBody["${deployment}"] = """
    {
            "name": "${deployment}",
            "status": "${status}",
            "statusDescription": "${statusDescription}"
    }
    """
    def updateDeploymentBodyList = updateDeploymentBody.collect { it -> it.value }
    def requestBodyPayload = """
        {
            "id": "${rolloutStatusId}",
            "deployments" : ${updateDeploymentBodyList}
        }
    """
    println('requestPayload: ' + requestBodyPayload)
    try {
        def response
        retry(5) {
            response = httpRequest httpMode: 'PATCH', contentType: 'APPLICATION_JSON', requestBody: requestBodyPayload, ignoreSslErrors: true, url: rolloutStatusCallbackUrl, timeout: 5
            println('Status: ' + response.status)
            println('Content: ' + response.content)
        }
    } catch (Exception e) {
        echo "Caught unexpected Exception when updating rollout status: ${e.toString()}"
        return ''
    }
}

@NonCPS
def sortCharts(List<String> charts) {
    // prioritize data lookup and cache
    return charts.toSorted{a, b -> sortChartComparator(a, b)}
}

@NonCPS
def sortChartComparator(String a , String b){
    if ((a.equals("data-lookup") || a.equals("cache")) && (b.equals("data-lookup") || b.equals("cache"))) {
        return 0
    }

    if (a.equals("data-lookup") || a.equals("cache")) {
        return -1
    }
    if (b.equals("data-lookup") || b.equals("cache")) {
        return 1
    }
    return a.compareTo(b)
}

def cloneDeploymentControl(changeOrder) {
    def repoUrl = 'https://gbmt-bitbucket.prd.fx.gbm.cloud.uk.hsbc/projects/UNITY-I15N-POC/repos/gke-deployment-control'
    def branchExists = this.sh(
        script: """curl -s -o /dev/null -w "%{http_code}" ${repoUrl}/raw/.chartList?at=refs%2Fheads%2F${changeOrder}""",
        returnStdout: true
    ).trim()

    if (branchExists == '200') {
        this.sh "git clone --depth 1 --branch ${changeOrder} https://gbmt-bitbucket.prd.fx.gbm.cloud.uk.hsbc/scm/unity-i15n-poc/gke-deployment-control.git"        
    } else {
        echo "Branch ${changeOrder} does not exist in ${repoUrl}"
    }
}