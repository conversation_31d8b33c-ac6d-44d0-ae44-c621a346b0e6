ARG JAVA_BASE_IMAGE=nexus3.systems.uk.hsbc:18096/com/hsbc/group/itid/es/mw/java/azuljava-jre-ubuntu-17:17.46
FROM ${JAVA_BASE_IMAGE}
USER root

RUN --mount=type=secret,id=aptconf,target=/etc/apt/auth.conf \
apt-get update \
&& apt-get install -y vim \
&& apt-get install -y less \
&& apt-get install -yqq inetutils-ping \
&& apt-get install -y net-tools \
&& apt-get install -y jq \
&& apt-get install -y nmap \
&& apt-get install -y zip \
&& apt-get install -y curl \
&& rm -rf /var/lib/apt/lists/*

ARG owner=confluent
ARG group=confluent
ARG mount=/opt/disk1
ARG artifacts_repo=https://hkl20090861.hc.cloud.hk.hsbc/devops/confluent
ARG confluent_artifact=confluent-community-7.5.7.tar
ARG kafka_lag_exporter_artifact=kafka-lag-exporter-*******.zip
ARG install_dir=${mount}/hss/apps/confluent
RUN groupadd -g 40001  ${group} && \
    useradd -l -u 40001 -g ${group} -m ${owner} && \
    mkdir -p ${mount} && \
    ln -sf ${mount}/hss /hss && \
    mkdir -p ${install_dir}/ops &&  \
    cd ${install_dir} && \
    curl ${artifacts_repo}/${confluent_artifact} -o ${install_dir}/confluent.tar && \
    tar -xf ${install_dir}/confluent.tar && \
    ln -sf $(ls -d /hss/apps/confluent/confluent-*) ${install_dir}/current && \
    mkdir -p ${install_dir}/monitor && \
    curl ${artifacts_repo}/${kafka_lag_exporter_artifact} -o ${install_dir}/kafka-lag-exporter.zip && \ 
    unzip -o ${install_dir}/kafka-lag-exporter.zip -d ${install_dir}/monitor/ && \
    ln -sf $(find ${install_dir}/monitor -type d -name "kafka-lag-exporter*") ${install_dir}/monitor/kafka-lag-exporter && \
    curl ${artifacts_repo}/yq-4.13.4.gz | tar -xz && \
    mv yq_linux_amd64 /usr/local/bin/yq && \
    echo '#!/bin/bash' > ${install_dir}/ops/docker-entrypoint && \
    echo ncat -l -k -v 0.0.0.0 8080 >> ${install_dir}/ops/docker-entrypoint && \
    chmod 755 ${install_dir}/ops/docker-entrypoint && \
    chown -R ${owner}:${owner} /opt/disk1 && \
    rm -f ${install_dir}/confluent.tar ${install_dir}/kafka-lag-exporter.zip

COPY --chown=${owner}:${owner} docker-entrypoint ${install_dir}/ops/docker-entrypoint
COPY --chown=${owner}:${owner} target/kafka-operation-tool*.jar /opt/disk1/hss/apps/confluent/ops/kafka-operation-tool.jar
COPY --chown=${owner}:${owner} support.sh /opt/disk1/hss/apps/confluent/ops/support.sh
RUN chmod 755 ${install_dir}/ops/docker-entrypoint
RUN chmod 755 ${install_dir}/ops/support.sh

USER ${owner}
WORKDIR ${mount}/hss/apps/confluent/ops
ENV PATH=$PATH:${mount}/hss/apps/confluent/current/bin:.
STOPSIGNAL SIGINT
ENTRYPOINT [ "bash", "docker-entrypoint" ]
