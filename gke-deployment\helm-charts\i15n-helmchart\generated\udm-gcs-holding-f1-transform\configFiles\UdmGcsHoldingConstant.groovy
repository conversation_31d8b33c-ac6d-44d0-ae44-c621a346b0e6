class UdmGcsHoldingConstant {

    public static final String GCS_ACCOUNT = "GCS_ACCOUNT"
    public static final String GCS_LOCATION = "GCS_LOCATION"
    public static final String GCS_INSTRUMENT = "GCS_INSTRUMENT"

    public static final String _ID = "_id"
    public static final String SOURCE_SYSTEM_NAME = "source_system_name"
    public static final String ISIN = "isin"
    public static final String SECURITY_ACCOUNT_NUMBER = "security_account_number"
    public static final String LOCATION_CODE = "location_code"
    public static final String REGISTRATION_CODE = "registration_code"
    public static final String SETTLED_BALANCE_QUANTITY = "settled_balance_quantity"
    public static final String TRADED_BALANCE_QUANTITY = "traded_balance_quantity"
    public static final String SETTLED_BALANCE_SECURITY_CURRENCY = "settled_balance_security_currency"
    public static final String SETTLED_BALANCE_REPORT_CURRENCY = "settled_balance_report_currency"
    public static final String TRADED_BALANCE_SECURITY_CURRENCY = "traded_balance_security_currency"
    public static final String TRADED_BALANCE_REPORT_CURRENCY = "traded_balance_report_currency"
    public static final String SETTLED_MARKET_VALUE_IN_SECURITY_CURRENCY = "settled_market_value_in_security_currency"
    public static final String SETTLED_MARKET_VALUE_IN_REPORT_CURRENCY= "settled_market_value_in_report_currency"
    public static final String TRADED_MARKET_VALUE_IN_SECURITY_CURRENCY = "traded_market_value_in_security_currency"
    public static final String TRADED_MARKET_VALUE_IN_REPORT_CURRENCY = "traded_market_value_in_report_currency"
    public static final String AVAILABLE_BALANCE = "available_balance"
    public static final String PENDING_CORPORATE_ACTION_RECEIPT_BALANCE = "pending_corporate_action_receipt_balance"
    public static final String PENDING_CORPORATE_ACTION_DELIVERY_BALANCE = "pending_corporate_action_delivery_balance"
    public static final String PENDING_DELIVERY_BALANCE = "pending_delivery_balance"
    public static final String PENDING_RECEIPT_BALANCE = "pending_receipt_balance"
    public static final String RESTRICTED_BALANCE = "restricted_balance"
    public static final String ON_LOAN_BALANCE = "on_loan_balance"
    public static final String OUT_OF_REGISTRATION_BALANCE = "out_of_registration_balance"
    public static final String PENDING_ON_LOAN_DELIVERY_BALANCE = "pending_on_loan_delivery_balance"
    public static final String BLOCKED_BALANCE = "blocked_balance"
    public static final String BLOCKED_CORPORATE_ACTION_BALANCE = "blocked_corporate_action_balance"
    public static final String PENDING_BORROWED_RECEIPT_BALANCE = "pending_borrowed_receipt_balance"
    public static final String PENDING_BORROWED_DELIVERY_BALANCE = "pending_borrowed_delivery_balance"
    public static final String PENDING_ON_LOAN_RECEIPT_BALANCE = "pending_on_loan_receipt_balance"
    public static final String IN_TRANSSHIPMENT_BALANCE = "in_transshipment_balance"
    public static final String STATUS_ACTIVE = "active"
    public static final String STATUS_INACTIVE = "inactive"
    public static final String BUSINESS_DATE = "business_date"
    public static final String REGISTRATION_DESCRIPTION = "registration_description"
    public static final String SECURITY_PRICE_CURRENCY = "security_price_currency"
    public static final String SECURITY_PRICE = "security_price"
    public static final String SYSTEM_COUNTRY_CODE = "system_country_code"
    public static final String FX_RATE = "fx_rate"
    public static final String ASSOCIATED_SUB_ACCOUNT = "associated_sub_account"
    public static final String GB_HBEU="GB-HBEU"
    public static final String GB_HBFR="GB-HBFR"
    public static final String GCUK="gcuk"
    public static final String GCE="gce"
    public static final String POSITION_ID = "pos_id"
    public static final String GCS_HOLDING = "GCS_HOLDING"
    public static final String GCSF1="GCSF1"
    public static final String GCSF5="GCSF5"
    public static final String ZERO_DATE_VALUE="0000-00-00"

    public static final List<String> GCS_HOLDING_FIELDS = Collections.unmodifiableList(
            new ArrayList<String>() {
                {
                    add("_id")
                    add("acc_region")
                    add("acc_short_name")
                    add("acc_currency_code")
                    add("acc_currency_role")
                    add("acc_currency_name")
                }
            })

    public static final List<String> LOCATION_FIELDS = Collections.unmodifiableList(
            new ArrayList<String>() {
                {
                    add("_id")
                    add("place_of_safekeeping_country_code")
                    add("place_of_safekeeping_name")
                }
            })

    public static final List<String> INSTRUMENT_FIELDS = Collections.unmodifiableList(
            new ArrayList<String>() {
                {
                    add("_id")
                    add("description")
                    add("type")
                    add("ccy")
                    add("qty_unit")
                    add("isin")
                    add("sedol")
                    add("cusip")
                    add("registrar_id")
                    add("registrar_desc")
                    add("maturity_date")
                }
            })

    public static List<String> getBalanceConstants() {
        return Arrays.asList(AVAILABLE_BALANCE, PENDING_CORPORATE_ACTION_RECEIPT_BALANCE, PENDING_CORPORATE_ACTION_DELIVERY_BALANCE,
                PENDING_DELIVERY_BALANCE, PENDING_RECEIPT_BALANCE, RESTRICTED_BALANCE, ON_LOAN_BALANCE, OUT_OF_REGISTRATION_BALANCE,
                PENDING_ON_LOAN_DELIVERY_BALANCE, BLOCKED_BALANCE, BLOCKED_CORPORATE_ACTION_BALANCE, PENDING_BORROWED_RECEIPT_BALANCE,
                PENDING_BORROWED_DELIVERY_BALANCE, PENDING_ON_LOAN_RECEIPT_BALANCE,IN_TRANSSHIPMENT_BALANCE
        )
    }

    public static final CURRENCY_MAP = [
            "AED" : "DIRAHAMS",
            "AOA" : "ANGOLA KWANZA",
            "ALL" : "ALBANIA LEK",
            "AMD" : "ARMENIAN DRAM",
            "ARS" : "ARGENTINA PESO",
            "ATS" : "AUSTRIAN SCHILLING",
            "AUD" : "AUSTRALIAN DOLLAR",
            "BDT" : "BANGLADESH TAKA",
            "BEC" : "BELGIAN FRANC - DUMMY",
            "BEF" : "BELGIAN FRANC",
            "BGN" : "BULGARIA LEVA",
            "BHD" : "BAHRAINI DINAR",
            "BMD" : "BERMUDIAN DOLLAR",
            "BND" : "BRUNEI DOLLAR",
            "BRL" : "BRAZILIAN REAL",
            "BSD" : "BAHAMIAN DOLLAR",
            "BWP" : "BOTSWANA-PULA",
            "CAD" : "CANADIAN DOLLAR",
            "CHF" : "SWISS FRANC",
            "CLP" : "CHILEAN PESO",
            "CNH" : "OFF SHORE CHINA CNH",
            "CNY" : "YUAN RENMINBI",
            "COP" : "COLOMBIAN PESO",
            "CYP" : "CYPRUS POUND",
            "CZK" : "CZECH KORUNA",
            "DEM" : "DEUTSCHE MARK",
            "DKK" : "DANISH KRONER",
            "DZD" : "ALGERIAN DINAR",
            "EEK" : "ESTONIA-KROON",
            "EGP" : "EGYPTIAN POUND",
            "ESP" : "SPANISH PESETA",
            "ETB" : "ETHIOPIAN BIRR",
            "EUR" : "EURO CURRENCY",
            "FIM" : "FINNISH MARKKA",
            "FJD" : "FIJI DOLLAR",
            "FRF" : "FRENCH FRANC",
            "GBP" : "POUND STERLING",
            "GGP" : "GUERNSEY POUNDS",
            "GHC" : "GHANA-CEDI",
            "GHS" : "GHANA CEDI",
            "HKD" : "HONG KONG DOLLAR",
            "HRK" : "KUNA",
            "HUF" : "FORINT",
            "IDR" : "INDONESIAN RUPIAH",
            "IEP" : "IRISH POUND",
            "ILS" : "SHEKEL",
            "IMP" : "ISLE OF MAN, POUNDS",
            "INR" : "INDIAN RUPEE",
            "IQD" : "IRAQI DINAR",
            "ISK" : "ICELAND KRONA",
            "ITL" : "ITALIAN LIRA",
            "JEP" : "JERSEY POUNDS",
            "JOD" : "JORDANIAN DINAR",
            "JPY" : "JAPANESE YEN",
            "KES" : "KENYAN SHILLING",
            "KRW" : "WON",
            "KWD" : "KUWAITI DINAR",
            "KYD" : "CAYMAN ISLANDS DOLLAR",
            "KZT" : "TENGE",
            "LAK" : "LAOTIAN KIP",
            "LBP" : "LEBANESE POUND",
            "LKR" : "SRI LANKA RUPEE",
            "LTL" : "LITHUANIAN LITAS",
            "LVL" : "LATVIVAN LATS",
            "MAD" : "MOROCCAN DIRHAM",
            "MOP" : "MACAU PATACA",
            "MTL" : "MALTESE LIRA",
            "MUR" : "MAURITIUS RUPEE",
            "MVR" : "RUFIYAA",
            "MWK" : "KWACHA",
            "MXN" : "MEXICAN NUEVO PESO",
            "MYR" : "MALAYSIAN RINGGIT",
            "NAD" : "NAMIBIA DOLLAR",
            "NGN" : "NAIRA",
            "NLG" : "NETHERLANDS GUILDER",
            "NOK" : "NORWEGIAN KRONER",
            "NZD" : "NEW ZEALAND DOLLAR",
            "OMR" : "OMANI RIAL",
            "PEN" : "NUEVO SOL",
            "PGK" : "KINA",
            "PHP" : "PHILIPPINE PESO",
            "PKR" : "PAKISTAN RUPEE",
            "PLN" : "ZLOTY",
            "QAR" : "QATAR RIYAL",
            "QTY" : "QUANTITY",
            "RMB" : "YUAN RENMINBI",
            "RON" : "ROMANIAN LEU",
            "RUB" : "RUSSIAN RUBLE (NEW)",
            "SAR" : "SAUDI ARABIA RIYAL",
            "SEK" : "SWEDISH KRONER",
            "SGD" : "SINGAPORE DOLLAR",
            "SIT" : "TOLAR",
            "SKK" : "SLOVAK KORUNA",
            "SPL" : "SEBORGA LUIGINI",
            "SVC" : "EL SALVADOR COLON",
            "SZL" : "LILANGENI",
            "THB" : "THAI BAHT",
            "TJS" : "TAJIKISTAN SOMONI",
            "TND" : "TUNISIAN DINAR",
            "TRY" : "TURKISH LIRA",
            "TVD" : "TUVALU DOLLARS",
            "TWD" : "NEW TAIWAN DOLLAR",
            "TZS" : "TANZANIAN SHILLING",
            "UAH" : "UKRAINIAN HRYVNIA",
            "UGX" : "UGANDA SHILLING",
            "USD" : "US DOLLAR1",
            "UYU" : "PESO URUGUAYO",
            "VEB" : "BOLIVAR",
            "VEF" : "VENEZUELA BOLIVAR FUERTE",
            "VND" : "VIETNAM DONG",
            "XAG" : "SILVER",
            "XAL" : "ALUMINIUM",
            "XAU" : "GOLD",
            "XCU" : "COPPER",
            "XCV" : "COPPER",
            "XEU" : "EUROPEAN CURRENCY UNIT",
            "XIR" : "IRIDIUM",
            "XOF" : "CFA FRANC BCEAO",
            "XPD" : "PALLADIUM", //Found in Dev Env. Not found in ghss MENA / GC Asia Ccy table
            "XPT" : "PLATINUM", //Found in Dev Env. Not found in ghss MENA / GC Asia Ccy table
            "YER" : "YEMENI RIAL", //Found in Dev Env. Not found in ghss MENA / GC Asia Ccy table
            "ZAR" : "SOUTH AFRICAN RAND",
            "ZMK" : "KWACHA",
            "ZMW" : "ZAMBIAN KWACHA",
            "ZWD" : "ZIMBABWE DOLLAR",
            "ZWL" : "ZIMBABWE DOLLAR",
            "SCR" : "SEYCHELLES RUPEE",
            "BBD" : "BARBADOS DOLLAR",
            "GTQ" : "QUETZAL",
            "HNL" : "LEMPIRA",
            "MXV" : "MEXICAN UNIDAD DE INVERSION (UDI)",
            "CUC" : "PESO CONVERTIBLE",
            "SLL" : "LEONE",
            "SDG" : "SUDANESE POUND",
            "CUP" : "CUBAN PESO",
            "GMD" : "DALASI",
            "RSD" : "SERBIAN DINAR",
            "DOP" : "DOMINICAN PESO",
            "UYI" : "URUGUAY PESO EN UNIDADES INDEXADAS (URUIURUI)",
            "KMF" : "COMORO FRANC",
            "FKP" : "FALKLAND ISLANDS POUND",
            "GEL" : "LARI",
            "CVE" : "CABO VERDE ESCUDO",
            "TOP" : "Pa’anga",
            "AZN" : "AZERBAIJANIAN MANAT",
            "BTN" : "NGULTRUM",
            "GNF" : "GUINEA FRANC",
            "ERN" : "NAKFA",
            "MZN" : "MOZAMBIQUE METICAL",
            "XPF" : "CFP FRANC",
            "UZS" : "UZBEKISTAN SUM",
            "LYD" : "LIBYAN DINAR",
            "PYG" : "GUARANI",
            "JMD" : "JAMAICAN DOLLAR",
            "COU" : "UNIDAD DE VALOR REAL",
            "PAB" : "BALBOA",
            "USN" : "US DOLLAR (NEXT DAY)",
            "KGS" : "SOM",
            "SOS" : "SOMALI SHILLING",
            "VUV" : "VATU",
            "XAF" : "CFA FRANC BEAC",
            "LRD" : "LIBERIAN DOLLAR",
            "DJF" : "DJIBOUTI FRANC",
            "CHE" : "WIR EURO",
            "GYD" : "GUYANA DOLLAR",
            "CHW" : "WIR FRANC",
            "KHR" : "RIEL",
            "RWF" : "RWANDA FRANC",
            "LSL" : "LOTI",
            "MMK" : "KYAT",
            "SYP" : "SYRIAN POUND",
            "GIP" : "GIBRALTAR POUND",
            "CRC" : "COSTA RICAN COLON",
            "XCD" : "EAST CARIBBEAN DOLLAR",
            "ANG" : "NETHERLANDS ANTILLEAN GUILDER",
            "HTG" : "GOURDE",
            "SRD" : "SURINAM DOLLAR",
            "TTD" : "TRINIDAD AND TOBAGO DOLLAR",
            "AFN" : "AFGHANI",
            "AWG" : "ARUBAN FLORIN",
            "NPR" : "NEPALESE RUPEE",
            "MNT" : "TUGRIK",
            "SBD" : "SOLOMON ISLANDS DOLLAR",
            "BIF" : "BURUNDI FRANC",
            "MGA" : "MALAGASY ARIARY",
            "BZD" : "BELIZE DOLLAR",
            "BAM" : "CONVERTIBLE MARK",
            "SSP" : "SOUTH SUDANESE POUND",
            "NIO" : "CORDOBA ORO",
            "WST" : "TALA",
            "TMT" : "TURKMENISTAN NEW MANAT",
            "CLF" : "UNIDAD DE FOMENTO",
            "KPW" : "NORTH KOREAN WON",
            "BOB" : "BOLIVIANO",
            "BOV" : "MVDOL",
            "MKD" : "DENAR",
            "MDL" : "MOLDOVAN LEU",
            "IRR" : "IRANIAN RIAL",
            "CDF" : "CONGOLESE FRANC",
            "SHP" : "SAINT HELENA POUND",
            "BYN" : "BELARUSIAN RUBLE",
            "MRU" : "OUGUIYA",
            "STN" : "DOBRA",
            "VES" : "BOLÍVAR SOBERANO",
            "UYW" : "UNIDAD PREVISIONAL",
            "XXX" : "NO CURRENCY",
            "XTS" : "TESTING CODE",
            "NOT_APPLICABLE" : "N/A",
            "UNDEFINED" : "UNKNOWN"
    ]
}

