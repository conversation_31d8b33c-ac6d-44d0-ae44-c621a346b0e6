#!/bin/bash

function print_usage(){
  echo bash $(basename $0) options
  echo options:
  echo --vedsdk-bearer-token
  echo --vedsdk-username
  echo --vedsdk-password
  echo --vedsdk-clientid
  echo --vedsdk-scope
  echo --subdomain
  echo --override-subdomain
  echo --setup-kubeconfig
  echo --kubeconfig
  echo --domain
  echo --namespace
  echo --use-wildcard [0|1]
  echo bash $(basename $0) --subdomain=vpc3 --domain=hsbc-9087302-unity-dev.dev.gcp.cloud.hk.hsbc --vedsdk-bearer-token=iUf1XpvojXhX/RzCT5TScw==
}

function options() {
while [ "$1" != "" ];
do
  _param_=`printf "%s\n" $1 | awk -F= '{print $1}'`
  _value_=`printf "%s\n" $1 | sed 's/^[^=]*=//g'`
  if [[ $_value_ == $_param_ ]]; then
    shift
    _value_=$1
  fi
  case ${_param_} in
  --vedsdk-bearer-token)
    bearer_token="${_value_}"
    ;;
  --vedsdk-username)
    vedsdk_username="${_value_}"
    ;;
  --vedsdk-password)
    vedsdk_password="${_value_}"
    ;;
  --vedsdk-clientid)
    vedsdk_clientid="${_value_}"
    ;;
  --vedsdk-scope)
    vedsdk_scope="${_value_}"
    ;;
  --subdomain)
    subdomain_name="${_value_}"
    ;;
  --namespace)
    namespace="$(echo ${_value_}|sed 's|,|\||g')"
    ;;
  --domain)
    domain_name="${_value_}"
    ;;
  --clusterproject)
    clusterproject="${_value_}"
    ;;     
  --clustername)
    clustername="${_value_}"
    ;; 
  --clusterregion)
    clusterregion="${_value_}"
    ;;
  --kubeconfig)
    kubeconfig="${_value_}"
    ;;
  --kubeproxy)
    kubeproxy="${_value_}"
    ;;
  --setup-kubeconfig)
    setup_kubeconfig="${_value_}"
    ;;
  --override-subdomain)
    override_subdomain="${_value_}"
    ;;
  --upload-bucket)
    upload_bucket="${_value_}"
    ;;
  --use-wildcard)
    use_wildcard="${_value_}"
    ;;
  --help)
    print_usage
    exit
    ;;
  esac
  shift;
done
}

function setup_kubeconfig(){
  set -x 
  export KUBECONFIG=${root}/kubeconfig.conf
  if [[ ! -z "${clustername}" ]]; then 
    gcloud container clusters get-credentials ${clustername} --region=${clusterregion} --project=${clusterproject}
  else 
    gcloud container clusters list --format='value(name,zone)' | grep ${subdomain_name} | while read cluster region ; do 
      gcloud container clusters get-credentials ${cluster} --region=${region}
    done
  fi 
  if [[ -z "${kubeproxy}" ]] ; then 
    export KUBE_PROXY=$(gcloud compute addresses list --filter="NAME:gke-kubectl-${subdomain_name}" --format='value(ADDRESS)'):3128
  else
    export KUBE_PROXY=${kubeproxy}
  fi
  export HTTPS_PROXY=${KUBE_PROXY}
  set +x
}

function vedsdk_authorize(){
local response
response=$(curl \
  --silent \
  --location \
  --request POST \
  --header "Content-Type: application/json" \
  -o rs -w "%{http_code}" \
  "${vedsdk}/vedauth/authorize"  \
  -d '{ "username": "'${vedsdk_username}'", "password": "'${vedsdk_password}'", "scope": "'${vedsdk_scope}'", "client_id":"'${vedsdk_clientid}'"}')
if [[ $response -ne 200 ]]; then 
  bearer_token=""
  cat rs | jq .
else 
  bearer_token=$(cat rs | jq -r ".access_token")
fi
rm -f rs
}

function constructor(){
root=$(readlink -f $(dirname $0))
setup_kubeconfig=0
namespace=".*"
vedsdk=https://venafi-prod-wk.systems.uk.hsbc
vedsdk_scope="certificate:manage"
vedsdk_clientid="1TDCL"
export no_proxy=${no_proxy},systems.uk.hsbc
error_cnt=0
}

###############################################
# main
###############################################
constructor
options "$@"

echo namespace:filter:${namespace}
if [ -z "${bearer_token}" ]; then 
  if [ ! -z "${vedsdk_username}" ] && [ ! -z "${vedsdk_password}" ]; then  
    vedsdk_authorize    
  fi
fi

#iterate to create tls secrets
[[ -z "${bearer_token}" ]] && echo fatal: unable to access to vedsdk[${vedsdk}]. exit 1 && exit 1 || echo authenticated to vedsdk
[[ ${setup_kubeconfig} -eq 1 ]] && setup_kubeconfig
[[ ! -z "${kubeconfig}" ]] && export KUBECONFIG=${kubeconfig}
echo KUBECONFIG=$KUBECONFIG
 while read namespace; do 
 project=$(echo $namespace|sed -e 's|^ns-||g' -e 's|\-apps||g')
 [[ ! -z "${subdomain_name}" ]] && _subdomain_=${project}.${subdomain_name} || _subdomain_=${project}
 [[ ! -z "${override_subdomain}" ]] && _subdomain_=${override_subdomain} 
 bash ${root}/create-certificate.sh --subdomain-name=${_subdomain_} --domain-name=${domain_name} --bearer-token=${bearer_token} --use-wildcard=${use_wildcard:-1}
 if [ $? -eq 0 ]; then 
   keyfile=$(find ${root}/cert-request/${project}* -name "server.key")
   pemfile=$(find ${root}/cert-request/${project}* -name "server.pem")
   cafile=$(find ${root}/cert-request/${project}* -name "ca.crt")
   set -x
   kubectl create secret tls ingress-tls-secret --key=${keyfile} --cert=${pemfile} -n ${namespace} --dry-run="client" -oyaml | kubectl apply -f - 
   kubectl create secret generic ingress-tls-ca-secret --from-file=${cafile} -n ${namespace} --dry-run="client" -oyaml | kubectl apply -f - 
   set +x  
   if [ ! -z "${upload_bucket}" ]; then    
     connected_project=$(gcloud config list --format 'value(core.project)')
     #only upload if connected project = cluster project
     if [[ "${connected_project}" == "${clusterproject:-$connected_project}" ]]; then
       export HTTPS_PROXY_ORIGINAL=$HTTPS_PROXY
       unset HTTPS_PROXY
       gsutil cp ${keyfile} ${upload_bucket}/${project}/
       gsutil cp ${pemfile} ${upload_bucket}/${project}/   
       export HTTPS_PROXY=${HTTPS_PROXY_ORIGINAL}
     fi
   fi 
  else
    ((error_cnt++))
  fi 
done <<< $(kubectl get namespace | egrep "ns-.*-apps|monitoring-system|ccat-.*-apps" | egrep "${namespace}" | awk '{print $1}')
if [[ ${error_cnt}  -gt 0 ]]; then
  exit 1
fi


