variable "region" {
  type = string
}

variable "kms_project" {
  type = string
}

variable "project_network" {
  type = string
}

variable "project" {
  type = string
}

variable "environment_type" {
  type = string
}

variable "json_data_path" {
  type = string
}

variable "multi_instance_sql_data" {
  type = list(object({
    db_instance_name : string,
    db_tier : string,
    db_databases : list(string),
    database_user_names : list(object({ username : string, type : string })),
    delete_protection : bool,
    retention_log_time : number,
    maintenance_window_day : number,
    maintenance_window_hour : number,
    query_insights_enabled: bool,
    master_instance_name: string
  }))
}


variable "sql_proxy_zone" {
  type = string
}

variable "db_version" {
  type = string
}

variable "master_instance_name" {
  type = string
  default = null
}

variable "db_backup_configuration_location" {
  type = string
  default = "asia-east2"
}