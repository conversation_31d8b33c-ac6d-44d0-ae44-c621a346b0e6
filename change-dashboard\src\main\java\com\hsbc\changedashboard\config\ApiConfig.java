package com.hsbc.changedashboard.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Component;

@Configuration
@EnableConfigurationProperties
@ConfigurationProperties(prefix = "api")
@Component
public class ApiConfig {
    
    private Ice ice = new Ice();
    private Jira jira = new Jira();
    private Poddy poddy = new Poddy();

    public Ice getIce() { return ice; }
    public void setIce(Ice ice) { this.ice = ice; }

    public Jira getJira() { return jira; }
    public void setJira(Jira jira) { this.jira = jira; }

    public Poddy getPoddy() { return poddy; }
    public void setPoddy(Poddy poddy) { this.poddy = poddy; }

    public static class Ice {
        private String baseUrl;
        private String authorization;
        private String appIds;
        private String fields;

        public String getBaseUrl() { return baseUrl; }
        public void setBaseUrl(String baseUrl) { this.baseUrl = baseUrl; }

        public String getAuthorization() { return authorization; }
        public void setAuthorization(String authorization) { this.authorization = authorization; }

        public String getAppIds() { return appIds; }
        public void setAppIds(String appIds) { this.appIds = appIds; }

        public String getFields() { return fields; }
        public void setFields(String fields) { this.fields = fields; }
    }

    public static class Jira {
        private String baseUrl;
        private String authorization;
        private String cookie;

        public String getBaseUrl() { return baseUrl; }
        public void setBaseUrl(String baseUrl) { this.baseUrl = baseUrl; }

        public String getAuthorization() { return authorization; }
        public void setAuthorization(String authorization) { this.authorization = authorization; }

        public String getCookie() { return cookie; }
        public void setCookie(String cookie) { this.cookie = cookie; }
    }

    public static class Poddy {
        private String baseUrl;

        public String getBaseUrl() { return baseUrl; }
        public void setBaseUrl(String baseUrl) { this.baseUrl = baseUrl; }
    }
}
