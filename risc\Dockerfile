ARG JAVA_BASE_IMAGE=nexus3.systems.uk.hsbc:18096/com/hsbc/group/itid/es/mw/java/mw-azuljava-v17:17.36
FROM ${JAVA_BASE_IMAGE}
ARG version
# copy the packaged jar file into our docker image
USER root

RUN --mount=type=secret,id=aptconf,target=/etc/apt/auth.conf \
apt-get update \
&& apt-get install -y curl \
&& rm -rf /var/lib/apt/lists/*

COPY target/risc-${version}.jar /risc.jar
RUN chown javadm:javgrp /risc.jar

# Health check endpoint (assuming Spring Boot Actuator will be added)
HEALTHCHECK --interval=30s --timeout=3s --start-period=60s --retries=3 \
  CMD curl -f http://localhost:8080/actuator/health || exit 1

USER javadm
EXPOSE 8080
ENTRYPOINT ["java", "-jar", "/risc.jar"]
