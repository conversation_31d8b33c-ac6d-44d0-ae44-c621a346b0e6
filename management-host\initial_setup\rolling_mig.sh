#!/usr/bin/env bash
set -euo pipefail

profile_env=$1

echo "[MGMT_HOST] checking for default conf"
if [ ! -f "./initial_setup/var-${profile_env}.conf" ]; then
 echo "Parameter file var-${profile_env}.conf not found in initial_setup."
 exit 1
fi
source ./initial_setup/var-${profile_env}.conf

mig_name=`gcloud compute instance-groups managed list --filter="gce-mig-mgmt-host-${profile_env}" --format="value(NAME)"`

gcloud compute instance-groups managed  wait-until --stable ${mig_name}  --region ${REGION}

gcloud compute instance-groups managed rolling-action replace  ${mig_name} --region ${REGION}
