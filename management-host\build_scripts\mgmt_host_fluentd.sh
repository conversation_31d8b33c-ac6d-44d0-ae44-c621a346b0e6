#!/usr/bin/env bash
echo "$0 - started"
set -euo pipefail
cat >> /etc/google-fluentd/config.d/mgm.conf<<HELLO
<source>
@type tail
format none
path /tmp/consoleoutput
pos_file /var/lib/google-fluentd/pos/filenet_jenkinsbuild-log.pos
tag filenet_jenkinsbuild-log.tag
</source>
HELLO
echo "service google-fluentd restart"	
cat >> /etc/google-fluentd/config.d/sakey.conf<<HELLO
<source>
@type tail
format none
path /tmp/sakeyconsoleoutput
pos_file /var/lib/google-fluentd/pos/filenet_sakey-log.pos
tag filenet_sakey-log.tag
</source>
HELLO
sudo service google-fluentd restart	
echo "$0 - ended"