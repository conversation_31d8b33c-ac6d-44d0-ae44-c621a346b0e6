

import com.poc.hss.fasttrack.transform.AbstractTransformer
import com.poc.hss.fasttrack.transform.model.TransformerBatchInputContext
import com.poc.hss.fasttrack.transform.model.TransformerRecord
import io.vertx.core.json.JsonObject
import org.apache.commons.lang3.exception.ExceptionUtils
import org.slf4j.Logger
import org.slf4j.LoggerFactory

import java.util.stream.Collectors

class SiCancellationControlTransformer extends AbstractTransformer {
    JsonObject constructRecord(JsonObject source){
        try{
            return new JsonObject()
                    .put("site", source.getString("SITE")?.trim())
                    .put("CCTXFT", source.getString("CCTXFT")?.trim())
                    .put("CCTXFS", source.getString("CCTXFS")?.trim())
                    .put("CCTXFQ", source.getString("CCTXFQ")?.trim())
                    .put("CCTXFX", source.getString("CCTXFX")?.trim())
                    .put("CCTRDT", source.getString("CCTRDT")?.trim())
                    .put("CCSDLC", source.getString("CCSDLC")?.trim())
                    .put("CCSICI", source.getString("CCSICI")?.trim())
                    .put("transaction_id", String.format("%s-%s-%06d-%02d-%01d-%08d",source.getString("SITE")?.trim().toUpperCase(),source.getString("CCTXFT")?.trim(),
                            getInteger(source.getString("CCTXFS")?.trim()),getInteger(source.getString("CCTXFQ")?.trim()),
                            getInteger(source.getString("CCTXFX")?.trim()),getInteger(source.getString("CCTRDT")?.trim())))
                    .put("partition_key", String.format("%s-%s-%06d-%02d-%01d",source.getString("SITE")?.trim().toUpperCase(),source.getString("CCTXFT")?.trim(),
                            getInteger(source.getString("CCTXFS")?.trim()),getInteger(source.getString("CCTXFQ")?.trim()),
                            getInteger(source.getString("CCTXFX")?.trim())))
                    .put("event_source", source.getString("FILE_NAME", "")?.trim())
        }catch(Exception e){
            logger.error("constructRecord failed! data:{} error:{}",source, ExceptionUtils.getStackTrace(e))
            throw e
        }
    }

    static Integer getInteger(String key) {
        if (key != null && !key.trim().equals("")) {
            return new BigDecimal(key).intValueExact();
        } else {
            return 0;
        }
    }

    private static final Logger logger = LoggerFactory.getLogger(SiCancellationControlTransformer.class)

    @Override
    List<TransformerRecord> batchTransform(TransformerBatchInputContext context) {
        List<TransformerRecord> records = context.getRecords()
        return records.stream().map({
            rec ->
                JsonObject source = rec.getData();
                JsonObject transformedSource = constructRecord(source)
                return rec.from(rec).data(transformedSource).kafkaPartitionKey(transformedSource.getString("partition_key")).build()
        }).collect(Collectors.toList())
    }
}

