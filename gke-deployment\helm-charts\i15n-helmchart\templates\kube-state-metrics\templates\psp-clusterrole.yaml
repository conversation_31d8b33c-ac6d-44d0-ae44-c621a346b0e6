{{- if and .Values.podSecurityPolicy.enabled .Values.customRbac.create -}}
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    {{- include "kube-state-metrics.labels" . | indent 4 }}
  name: psp-{{ template "kube-state-metrics.fullname" . }}
rules:
{{- $kubeTargetVersion := default .Capabilities.KubeVersion.GitVersion .Values.kubeTargetVersionOverride }}
{{- if semverCompare "> 1.15.0-0" $kubeTargetVersion }}
- apiGroups: ['policy']
{{- else }}
- apiGroups: ['extensions']
{{- end }}
  resources: ['podsecuritypolicies']
  verbs:     ['use']
  resourceNames:
  - {{ template "kube-state-metrics.fullname" . }}
{{- end }}
