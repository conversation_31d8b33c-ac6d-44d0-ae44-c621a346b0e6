#!/usr/bin/env bash
set -euo pipefail

echo "<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<< MAVEN >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>"
echo "[MGMT_HOST] starting maven installation"

if id "maven" >/dev/null 2>&1; then
    echo "[MGMT_HOST] maven user exists"    
else
    echo "[MGMT_HOST] maven user does not exist"
    sudo useradd maven 
    echo "[MGMT_HOST] created maven user "
fi
sudo usermod -a -G root maven
echo "[MGMT_HOST] added maven user to root"

sudo mkdir -p $MAVEN_PATH
sudo mkdir -p $MAVEN_PATH/bin
echo "[MGMT_HOST] created maven directory"

#downloading maven from nexus
cd $BUILD_PATH/build_tools/
echo "[MGMT_HOST] downloading maven from nexus"
wget --user=${NEXUS_CREDS_USR} --password=${NEXUS_CREDS_PWD} "https://hkl20090861.hc.cloud.hk.hsbc/devops/$MAVEN_BINARY"
sudo tar xf $MAVEN_BINARY -C ${MAVEN_PATH}/
sudo rm -rf $MAVEN_BINARY
echo "[MGMT_HOST] unzip and moved maven to maven path and completed maven installation"

echo "[MGMT_HOST] linking maven to /usr/bin"
sudo ln -s $MAVEN_PATH/$MAVEN_VERSION/bin/mvn /usr/bin


#assigning permissions to terraform user
sudo chown -R root:root $MAVEN_PATH
sudo chmod -R 777 $MAVEN_PATH
echo "[MGMT_HOST] assigning permissions to maven user"


echo "[MGMT_HOST] Add mvn configuration"
if [ ! -f ~/.m2/settings.xml ]
then
    mkdir -p ~/.m2
    cat > ~/.m2/settings.xml <<"SUBEOF"
<?xml version="1.0" encoding="UTF-8"?>
<settings xmlns="http://maven.apache.org/SETTINGS/1.0.0"
          xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
          xsi:schemaLocation="http://maven.apache.org/SETTINGS/1.0.0 http://maven.apache.org/xsd/settings-1.0.0.xsd">
        <mirrors>
                <mirror>
                        <id>efx.nexus</id>
                        <mirrorOf>*</mirrorOf>
                        <url>https://efx-nexus.systems.uk.hsbc:8082/nexus/content/groups/public/</url>
                </mirror>
        </mirrors>
        <servers>
                <server>
                        <id>efx.nexus</id>
                        <username>fxsalesdev</username>
                        <password>F5884048e84b5c02fdd9979833d4d0e&amp;</password>
                </server>
                <server>
                        <id>nexus.releases</id>
                        <username>fxsalesdev</username>
                        <password>F5884048e84b5c02fdd9979833d4d0e&amp;</password>
                </server>
                <server>
                        <id>nexus.snapshots</id>
                        <username>fxsalesdev</username>
                        <password>F5884048e84b5c02fdd9979833d4d0e&amp;</password>
                </server>
        </servers>
        <profiles>
                <profile>
                        <id>nexus</id>
                        <!--Enable snapshots for the built in central repo to direct -->
                        <!--all requests to nexus via the mirror -->
                        <repositories>
                                <repository>
                                        <id>central</id>
                                        <url>http://central</url>
                                        <releases><enabled>true</enabled></releases>
                                        <snapshots><enabled>true</enabled></snapshots>
                                </repository>
                        </repositories>
                        <pluginRepositories>
                                <pluginRepository>
                                        <id>central</id>
                                        <url>http://central</url>
                                        <releases><enabled>true</enabled></releases>
                                        <snapshots><enabled>true</enabled></snapshots>
                                </pluginRepository>
                        </pluginRepositories>
                </profile>
        </profiles>
        <activeProfiles>
                <!--make the profile active all the time -->
                <activeProfile>nexus</activeProfile>
        </activeProfiles>
</settings>
SUBEOF
        cat > ~/.m2/toolchains.xml <<"SUBEOF"
<toolchains>
        <!-- This file was auto-generated by the dev-tools script and any changes you make may be overwritten -->
    <toolchain>
       <type>jdk</type>
       <provides>
           <version>1.8</version>
           <vendor>openjdk</vendor>
           <id>openjdk18</id>
       </provides>
       <configuration>
          <jdkHome>/usr/lib/jvm/java-1.8.0-openjdk-1.8.0.292.b10-1.el7_9.x86_64</jdkHome>
       </configuration>
    </toolchain>
</toolchains>
SUBEOF
fi

mkdir /home/<USER>/.m2
cp ~/.m2/settings.xml /home/<USER>/.m2/settings.xml
cp ~/.m2/toolchains.xml /home/<USER>/.m2/toolchains.xml
chown jenbld -R /home/<USER>/.m2
chmod 777 -R /home/<USER>/.m2

echo "[MGMT_HOST] completed maven installation"