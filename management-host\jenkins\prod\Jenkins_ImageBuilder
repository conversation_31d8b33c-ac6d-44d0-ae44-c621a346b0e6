static final boolean C_TRUE = true
static final boolean C_FALSE = false
static final String C_SUCCESS = 'SUCCESS'
static final String C_FAILURE = 'FAILURE'
static final String C_NULL = null
static final String C_YES = 'Yes'
static final String C_NO = 'No'
static final String packer_dir = "/usr/bin"
def AGENT = "unity-prod-jenkins-agent" 

pipeline {    
    agent { label env.AGENT_LABEL ?: AGENT }

    environment {
        // Fix github connection so it points to env specific variable  
        STASH_TOKEN= credentials('STASH_TOKEN')   
        GITHUB_TOKEN= credentials('GITHUB_TOKEN')   
    }

    stages {
            stage('Build image') {
                steps {
                    script {
                        withCredentials([
                            [$class: 'UsernamePasswordMultiBinding', credentialsId: 'NEXUS3_CONNECT', usernameVariable: 'NEXUSUSERNAME', passwordVariable: 'NEXUSPASSWORD'],
                            [$class: 'StringBinding', credentialsId: 'STASH_TOKEN', variable: 'STASH_TOKEN']
                        ]) {       
                            this.sh """${packer_dir}/packer build -timestamp-ui -on-error=cleanup \
                            -var "BUILDSTAGE3=false"  \
                            -var "STAGE=stage2"  \
                            -var "NEXUS_CREDS_USR=$NEXUSUSERNAME" \
                            -var "NEXUS_CREDS_PWD=$NEXUSPASSWORD" \
                            -var "STASH_TOKEN=$STASH_TOKEN" \
                            -var-file=dev/variables.json dev/image.json"""

                            this.sh """${packer_dir}/packer build -timestamp-ui -on-error=cleanup \
                            -var "BUILDSTAGE3=true"  \
                            -var "STAGE=stage3"  \
                            -var "NEXUS_CREDS_USR=$NEXUSUSERNAME" \
                            -var "NEXUS_CREDS_PWD=$NEXUSPASSWORD" \
                            -var "STASH_TOKEN=$STASH_TOKEN" \
                            -var "GITHUB_TOKEN=$GITHUB_TOKEN" \
                            -var-file=dev/variables.json dev/image.json"""
                
                    }                    
                }
            }
        }
    }

    post {
        unsuccessful {
            mail bcc: '',
                body: 'ERROR occured in automatic mgmt host image building occured. Please inspect relevant Jenkins pipeline. Regards DevOps Team',
                cc: '',
                from: 'unity_cloud_devops@noreply',
                replyTo: '',
                subject: "ERROR in automatic mgmt host image building  notification",
                to: '<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>'
        }       
        cleanup {
            script {
                cleanWs()
            }
        }
    }		

} 