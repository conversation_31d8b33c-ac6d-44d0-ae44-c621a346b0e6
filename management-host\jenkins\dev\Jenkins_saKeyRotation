def AGENT = "unity-dev-jenkins-agent"

def initEnvironment() {
  script {
    withCredentials([gitUsernamePassword(credentialsId: 'STASH_ACCESS', gitToolName: 'git-tool')]) {

        static final String C_MINUTES = 'MINUTES'
        static final boolean C_TRUE = true
        static final boolean C_FALSE = false
        static final String C_SUCCESS = 'SUCCESS'
        static final String C_FAILURE = 'FAILURE'
        static final String C_NULL = null
        static final String C_YES = 'Yes'
        static final String C_NO = 'No'
        static final String C_CONFIG_JSON_FILE_PATH = "./unity-gcp-terraform/Jenkins/Configs/hsbc-gcp-config.json"
        static final String C_GROOVY_METHOD_FILE_PATH = "./unity-gcp-terraform/Jenkins/Methods/JenkinsMethods.groovy"
        static final String c_workspace_path = "${env.WORKSPACE}"
        static final C_THIS = this
                
        this.sh """
                echo Downloading cluster configuration and helper function 
                rm -Rf ./unity-gcp-terraform                 
                git config user.name $GIT_USERNAME;
                git config user.email "<>";                  
                git clone --depth 1 --branch feature.jo/BBDRPT-7231-Implementation_the_automated_key_refresh_process_multiproject_GCP --single-branch  https://stash.hk.hsbc/scm/uoci/unity-gcp-terraform.git
                """
        jenkinsMethodHelper = load("${env.WORKSPACE}/${C_GROOVY_METHOD_FILE_PATH}")
        jenkinsMethodHelper.v_configJSONObject = jenkinsMethodHelper.createFileJSONObject(C_THIS, C_CONFIG_JSON_FILE_PATH, C_TRUE)


        
        jenkinsMethodHelper.initializeEnvironment(v_envFolder="${project_gcp_env}")

        clusterName = jenkinsMethodHelper.v_clusterName       
        clusterProject = jenkinsMethodHelper.v_clusterProject
        clusterRegion = jenkinsMethodHelper.v_clusterRegion
        kubectlProxy = jenkinsMethodHelper.v_kubectlProxy
        serviceAccountKey = jenkinsMethodHelper.v_salocation
        serviceAccountEMail=jenkinsMethodHelper.v_gcpBuildServiceAccount
        clusterLocation = kubectlProxy.split(":")[0].split("\\.")[-2]

        jenkinsMethodHelper.initializeEnvironment(v_envFolder="${master_project_gcp_env}")
        masterClusterName = jenkinsMethodHelper.v_clusterName       
        masterClusterProject = jenkinsMethodHelper.v_clusterProject
        masterClusterRegion = jenkinsMethodHelper.v_clusterRegion
        masterKubectlProxy = jenkinsMethodHelper.v_kubectlProxy
        masterServiceAccountKey = jenkinsMethodHelper.v_salocation
        masterCmekProject = jenkinsMethodHelper.v_cmek_project
        
        
        

        fileEnvPrefix = "${project_gcp_env}_${master_project_gcp_env}"

        this.sh """
                  rm -Rf ./unity-gcp-terraform                
                  cat > /tmp/.${project_target}.${fileEnvPrefix}.env <<-'EOT'
                  function authenticate() {

                    local _authMethod=\${1}
                    local _project_target=\${2}
                    local _clusterProject=\${3}
                    local _clusterRegion=\${4}
                    local _clusterName=\${5}
                    local _kubectlProxy=\${6}
                    

                    case \${_authMethod} in 
                    IAM-DIRECT)
                       unset HTTPS_PROXY
                       export KUBECONFIG=/tmp/.\${_project_target}.\${_clusterProject}.\${_clusterRegion}.\${_clusterName}.kube
                       gcloud container clusters get-credentials \${_clusterName} --region=\${_clusterRegion} --project=\${_clusterProject}	
                  	   export HTTPS_PROXY=\${_kubectlProxy}
                  	   ;;
                    KUBECONFIG)
                  	  case \${_project_target} in 
                  	  KOPS)
                  	   unset HTTPS_PROXY
                       export KUBECONFIG=./jenkins-admin.hk-kops-u2kopd01;		   
                  	   ;;
                  	  GKE)
                  	   export KUBECONFIG=${KUBECONFIG}
                  	   export HTTPS_PROXY=\${_kubectlProxy}
                        ;;			   
                  	  esac
                      ;;				 
                    esac
                  }
                 
                  function gcloud_authenticate() {

                    local _clusterProject=\${1}
                    local _serviceAccountKey=\${2}
                    local _serviceAccountEMail=\${3}
                    local _accessToken=\${4}

                    local _project_env=\$(echo \${_clusterProject} | rev | cut -f1 -d- | rev) 

                    local _gcp_project=\$(gcloud config list --format 'value(core.project)')
                    if [[  "\${_gcp_project}" != "\${_clusterProject}" ]]; then 

                      export CLOUDSDK_CONFIG=\$HOME/.cloudsdk/\${_clusterProject}
                      unset HTTPS_PROXY
                      if [[ -n "\$_accessToken" ]]; then
                            local create_key_url=\"https://iam.googleapis.com/v1/projects/\${_clusterProject}/serviceAccounts/\${_serviceAccountEMail}/keys\"
                            # Make the API request
                        
                            local response=\$(curl --proxy googleapis-\${_project_env}.gcp.cloud.hk.hsbc:3128 -s -X POST "\${create_key_url}" \\
                            -H "Authorization: Bearer \${_accessToken}" \\
                            -H "Content-Type: application/json" \\
                            -d '{"privateKeyType": "TYPE_GOOGLE_CREDENTIALS_FILE"}')

                            # Create key for sa and activate this servise account
                            # Step 2: Decode the privateKeyData from the response

                            local _decoded_key=\$(echo "\${response}" | jq -r '.privateKeyData' | base64 --decode)
                            

                            # Check if the key was decoded successfully
                            if [ -z "\${_decoded_key}" ]; then
                                log_message "Failed to generate key in the correct format."
                                return 1
                            fi

                            # Save the decoded key to a JSON file
                            echo \${_decoded_key} | jq '.' > \${_serviceAccountKey}

                            echo "Key created successfully for \${_serviceAccountEMail} and saved to \${_serviceAccountKey}"
                      fi
                      local _active_service_account=\$(gcloud auth list --filter=status:ACTIVE --format="value(account)")
                      local _authenticate_account=\$(cat \${_serviceAccountKey}| jq -r '.client_email')
                      
                      if [[ \${_active_service_account} != "\${_authenticate_account}" ]]; then                             
                        gcloud config set proxy/address googleapis-\${_project_env}.gcp.cloud.hk.hsbc
                        gcloud config set proxy/port 3128
                        gcloud config set proxy/type http_no_tunnel

                        # Sync token
                        gcloud config list
                        sleep 10

                        gcloud auth activate-service-account --key-file=\${_serviceAccountKey}
                        gcloud config set project \${_clusterProject}
                      fi
                    fi 
                    gcloud config list
                 }
EOT
                  echo "---------------------------------------------------------------------------"
                  cat /tmp/.${project_target}.${fileEnvPrefix}.env
                  echo "---------------------------------------------------------------------------"
                  """
        
        }
    }   
}



pipeline {    
    agent { label env.AGENT_LABEL ?: AGENT }
    
    parameters {
        choice(name: 'project_target', choices: ['GKE'], description: 'Project target')
        string(name: 'master_project_gcp_env', defaultValue: "hsbc-9087302-unity-dev", description: 'Master project for Unity')
        choice(name: 'project_gcp_env', choices: ['hsbc-9087302-unity-dev','hsbc-********-unyin1-dev','hsbc-********-unyin2-dev','hsbc-********-unyeu1-dev','hsbc-********-unybitlm-dev','hsbc-********-unycfs-dev','hsbc-********-unyfaasia-dev','hsbc-********-unyempf-dev','hsbc-********-unity-dev','hsbc-********-unyquartz-dev','hsbc-********-unyclientconn-dev','hsbc-********-unyas-dev','hsbc-********-unycsreport-dev','hsbc-********-unytreasury-dev','hsbc-********-unyirm-dev'], description: "project gcp project")
        string(name: "authMethod", defaultValue: "IAM-DIRECT", description: "GCP ONLY - GKE cluster Authentication Method") 
        string(name: 'serviceAccounts', defaultValue: 'runtime-gke')
        string(name: 'accessToken', defaultValue: "", description: 'OAUTH 2.0 key for admin key SA ( gcloud auth print-access-token )' )
    }
    stages {
        stage("Initialize Environment Variables") {
            options {
                timeout(time: 8, unit: "MINUTES")
            }
            steps {
                script {
                   initEnvironment()
                }
            }
        } 

        stage('Sa Key Rotation') {
            steps {
                script {
                    this.sh """
                        set -e
                        source /tmp/.${project_target}.${fileEnvPrefix}.env

                        echo "GCP authentication"
                        gcloud_authenticate ${clusterProject} ${serviceAccountKey} ${serviceAccountEMail} ${accessToken}

                        chmod 755 ./initial_setup/rotate_keys_for_SA.sh; 
                        echo "Running script rotate_keys_for_SA.sh"
                        ./initial_setup/rotate_keys_for_SA.sh --project_id=${clusterProject} --path_folder_keys="/home/<USER>/ServiceAccountKeys/" --service_account_admin=gce-stage3-image-builder@${clusterProject}.iam.gserviceaccount.com  --service_accounts=${serviceAccounts};

                        gcloud_authenticate ${masterClusterProject} ${masterServiceAccountKey} "" ""
                        chmod 755 ./initial_setup/stotre_keys_for_SA.sh
                        echo "Running script stotre_keys_for_SA.sh"
                        ./initial_setup/stotre_keys_for_SA.sh --key_project_id=${clusterProject} --path_folder_keys="/home/<USER>/ServiceAccountKeys/" --master_project=${masterClusterProject} --cmek_project=${masterCmekProject} --region=${masterClusterRegion}


                        chmod 755 ./initial_setup/resync_key_to_unity_onprem.sh
                        echo "Running script resync_key_to_unity_onprem.sh"
                        if [[ "${masterClusterProject}" == "${clusterProject}" ]]; then
                            ./initial_setup/resync_key_to_unity_onprem.sh;
                        fi
                    """
                }
            }
        }
        stage('Docker secrets update'){
            when {
                expression  {
                    currentBuild.result == null ||  currentBuild.result == 'SUCCESS'
                }
            }
            steps{
                script{
                     this.sh """
                        set -e
                        source /tmp/.${project_target}.${fileEnvPrefix}.env

                        echo "GCP authentication"
                        gcloud_authenticate ${clusterProject} ${serviceAccountKey} "" ""
                        authenticate ${authMethod} ${project_target} ${clusterProject} ${clusterRegion} ${clusterName} ${kubectlProxy}

                        echo "Running script rotate_docker_secrets_for_key.sh"
                        chmod 755 ./initial_setup/rotate_docker_secrets_for_key.sh
                        ./initial_setup/rotate_docker_secrets_for_key.sh --project_id=${clusterProject} --path_folder_keys="/home/<USER>/ServiceAccountKeys/" --service_account=runtime-gke --secret_name=docker-secret
                    """
                }
            }
        }
    }
    post {  
        success {
            script {
                DATETIME_TAG = java.time.LocalDateTime.now()
                def emailBody = """
                        Automatic key rotaion took place on Unity GCP ${project_gcp_env} project  
                        
                        Job Name: ${env.JOB_NAME}
                        Build number: ${env.BUILD_NUMBER}
                        URL to the build ${env.BUILD_URL}

                        You can find generated keys in bucket: https://console.cloud.google.com/storage/browser/${master_project_gcp_env}-key-management 
                        
                        
                        Best regards,  
                        Unity cloud devops team
                        """
                def emailSubject = "Automatic SA key rotation notification Unity GCP ${project_gcp_env} - ${DATETIME_TAG}"
                mail bcc: '', 
                    body: emailBody, 
                    cc: '', 
                    from: 'unity_cloud_devops@noreply', 
                    replyTo: '', 
                    subject: emailSubject, 
                    to: '<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>'
            }
        }    
        failure {
            echo "Pipeline failed"
        }
        cleanup {
            script {
                cleanWs()
            }
        }
    }
}
