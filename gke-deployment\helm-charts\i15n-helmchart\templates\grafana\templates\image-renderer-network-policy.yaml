{{- if and (.Values.imageRenderer.enabled) (.Values.imageRenderer.networkPolicy.limitIngress) }}
---
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: {{ template "grafana.fullname" . }}-image-renderer-ingress
  namespace: {{ template "grafana.namespace" . }}
  annotations:
    comment: Limit image-renderer ingress traffic from grafana
spec:
  podSelector:
    matchLabels:
      {{- include "grafana.imageRenderer.selectorLabels" . | nindent 6 }}
      {{- if .Values.imageRenderer.podLabels }}
        {{ toYaml .Values.imageRenderer.podLabels | nindent 6 }}
      {{- end }}

  policyTypes:
    - Ingress
  ingress:
    - ports:
        - port: {{ .Values.imageRenderer.service.port }}
          protocol: TCP
      from:
        - namespaceSelector:
            matchLabels:
              name: {{ template "grafana.namespace" . }}
          podSelector:
            matchLabels:
              {{- include "grafana.selectorLabels" . | nindent 14 }}
              {{- if .Values.podLabels }}
                {{ toYaml .Values.podLabels | nindent 14 }}
              {{- end }}
{{ end }}

{{- if and (.Values.imageRenderer.enabled) (.Values.imageRenderer.networkPolicy.limitEgress) }}
---
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: {{ template "grafana.fullname" . }}-image-renderer-egress
  namespace: {{ template "grafana.namespace" . }}
  annotations:
    comment: Limit image-renderer egress traffic to grafana
spec:
  podSelector:
    matchLabels:
      {{- include "grafana.imageRenderer.selectorLabels" . | nindent 6 }}
      {{- if .Values.imageRenderer.podLabels }}
        {{ toYaml .Values.imageRenderer.podLabels | nindent 6 }}
      {{- end }}

  policyTypes:
    - Egress
  egress:
    # allow dns resolution
    - ports:
        - port: 53
          protocol: UDP
        - port: 53
          protocol: TCP
    # talk only to grafana
    - ports:
        - port: {{ .Values.service.port }}
          protocol: TCP
      to:
        - namespaceSelector:
            matchLabels:
              name: {{ template "grafana.namespace" . }}
          podSelector:
            matchLabels:
              {{- include "grafana.selectorLabels" . | nindent 14 }}
              {{- if .Values.podLabels }}
                {{ toYaml .Values.podLabels | nindent 14 }}
              {{- end }}
{{ end }}
