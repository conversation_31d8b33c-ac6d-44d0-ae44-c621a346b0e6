pipeline {    
    agent  { 
        label "unity-dev-jenkins-agent"    
    }
    environment {
	    STASH_TOKEN= credentials('github-connect')
    }
     parameters 
    {

    }
    stages {
        stage('gcloud auth activate') {
	        steps{
		        withCredentials([[$class: 'StringBinding', credentialsId: 'uat_img_builder_path', variable: 'gcp_sa_key']]) {
                        this.sh(returnStdout: true, script: "gcloud auth activate-service-account --key-file ${gcp_sa_key}")
                        this.sh 'export GOOGLE_APPLICATION_CREDENTIALS=$gcp_sa_key'
	                }	   
	        }
	      }	  
        stage ('rolling out the management host mig') { 
	        steps {
              sh 'chmod 755 ./initial_setup/rolling_mig.sh'
              sh './initial_setup/rolling_mig.sh uat'
          }
        }
    }		
}