#!/bin/bash
project_id=`gcloud config list --format 'value(core.project)'`

if [ "${project_id}" == "" ]
then
    echo "To run you must set project for gcloud locally to define target project for the shell host"
    exit 255
fi

echo "[MGMT_HOST]service account key is copying from gcs is started"
cd $HOME

cd /root
echo "changed to root directory"
echo $PWD

mkdir .ssh
chmod -R 700 .ssh

if id "jenbld" >/dev/null 2>&1; then
    echo "[MGMT_HOST] jenbld user exists"
else
    sudo useradd jenbld
    echo "[MGMT_HOST] jenbld user does not exist, created jenbld user"
    #sudo usermod -aG root jenbld
    echo "[MGMT_HOST] added jenbld to root group & given permission to jenkins path"    
fi

cd /home/<USER>

mkdir ServiceAccountKeys
chmod -R 774 ServiceAccountKeys
cd ServiceAccountKeys

#SERVICE_ACCOUNT_LIST=("gce-stage3-image-builder" "terraform" "connector-cloudsql" "k8s-vault-data" "big-query-audit-log-sa")

#for i in "${SERVICE_ACCOUNT_LIST[@]}"
#do
#    gsutil cp gs://"${project_id}"-key-management/"${i}"/"${i}".json .
#done

sudo chown jenbld:jenbld /home/<USER>/ServiceAccountKeys/*
sudo chown jenbld:jenbld /home/<USER>/ServiceAccountKeys

ls -la
echo "completed"