ARG JAVA_BASE_IMAGE=nexus3.systems.uk.hsbc:18096/com/hsbc/group/itid/es/mw/java/mw-azuljava-v17:17.36
FROM ${JAVA_BASE_IMAGE}
ARG image_name
ARG version
# copy the packaged jar file into our docker image
USER root

RUN --mount=type=secret,id=aptconf,target=/etc/apt/auth.conf \
apt-get update \
&& apt-get install -y curl \
&& rm -rf /var/lib/apt/lists/*

COPY target/${image_name}-${version}.jar /${image_name}.jar
RUN chown javadm:javgrp /${image_name}.jar

LABEL maintainer="Unity2 DevOps Team" \
      image_name="${image_name}" \
      version="${version}" \
      description="Docker image for ${image_name} version ${version}"

USER javadm
EXPOSE 8080
ENTRYPOINT ["java", "-jar", "/${image_name}.jar"]