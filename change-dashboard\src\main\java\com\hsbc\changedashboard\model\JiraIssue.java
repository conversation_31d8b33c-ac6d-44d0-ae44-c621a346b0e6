package com.hsbc.changedashboard.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

@JsonIgnoreProperties(ignoreUnknown = true)
public class JiraIssue {
    
    @JsonProperty("expand")
    private String expand;
    
    @JsonProperty("id")
    private String id;
    
    @JsonProperty("self")
    private String self;
    
    @JsonProperty("key")
    private String key;
    
    @JsonProperty("fields")
    private JiraFields fields;

    // Constructors
    public JiraIssue() {}

    // Getters and Setters
    public String getExpand() { return expand; }
    public void setExpand(String expand) { this.expand = expand; }

    public String getId() { return id; }
    public void setId(String id) { this.id = id; }

    public String getSelf() { return self; }
    public void setSelf(String self) { this.self = self; }

    public String getKey() { return key; }
    public void setKey(String key) { this.key = key; }

    public JiraFields getFields() { return fields; }
    public void setFields(JiraFields fields) { this.fields = fields; }

    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class JiraFields {
        @JsonProperty("summary")
        private String summary;

        @JsonProperty("creator")
        private JiraUser creator;

        public JiraFields() {}

        public String getSummary() { return summary; }
        public void setSummary(String summary) { this.summary = summary; }

        public JiraUser getCreator() { return creator; }
        public void setCreator(JiraUser creator) { this.creator = creator; }
    }

    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class JiraUser {
        @JsonProperty("name")
        private String name;

        @JsonProperty("displayName")
        private String displayName;

        @JsonProperty("emailAddress")
        private String emailAddress;

        public JiraUser() {}

        public String getName() { return name; }
        public void setName(String name) { this.name = name; }

        public String getDisplayName() { return displayName; }
        public void setDisplayName(String displayName) { this.displayName = displayName; }

        public String getEmailAddress() { return emailAddress; }
        public void setEmailAddress(String emailAddress) { this.emailAddress = emailAddress; }
    }
}
