def namespaces = []
def clusters = []
/* required environment variable
AGENT
*/

pipeline {
    agent { label env.AGENT_LABEL ?: AGENT }
    stages {
        stage('get cluster configuration - kubeconfig') {
            steps {
                script {
                    env.v_project = this.sh(
                            script: """gcloud config list --format 'value(core.project)'""",
                            returnStdout: true
                    ).trim()
                    env.v_kubehome = "$HOME/kubeconfig/${clusterEnv}"
                    this.sh """mkdir -p ${v_kubehome};gsutil -m cp -r gs://${v_project}-app-runtime-dependencies/secrets/env/${clusterEnv}/kubeconfig/* ${v_kubehome}/;"""
                    v_gcpKeyBucketAppDep = "${v_project}-key-management"
                }
            }
        }
        stage('Set Properties') {
            steps {
                script {
                    echo "ON BRANCH: ${env.GIT_BRANCH} "

                    dir('helm-charts/i15n-helmchart/generated/') {
                        if (fileExists('.namespace')) {
                            namespaces.add(readFile('.namespace'))
                        }
                    }

                    clusters = "${clusterName}".split(",")
                    echo "namespaces: ${namespaces}."
                }
            }
        }
        stage('Set Active Cluster Pod Scaling') {
            steps {
                script {
                    for (namespace in namespaces) {
                        //Stop inactive clusters
                        for (cluster in clusters) {
                            if (params.activeClusters != null && !activeClusters.contains("${cluster}")) {
                                scalePodsCommand = "kubectl get deployment --namespace ${namespace} --no-headers -o=custom-columns='NAME:.metadata.name' | xargs -I{} kubectl scale --replicas=0 --namespace ${namespace} deployment/{} "
                                echo "Executing scaling script for: ${scalePodsCommand}"
                                retry(3) {
                                    this.sh """set +x;
                                            export KUBECONFIG=${v_kubehome}/${namespace}.${cluster};
                                            ${scalePodsCommand};
                                            if [ \$(kubectl get crd scaledobjects.keda.sh --no-headers | wc -l) -gt 0 ]; then
                                               kubectl get scaledobject -o custom-columns='NAME:.metadata.name' --no-headers | while read o; do
                                                 kubectl annotate scaledobjects \$o autoscaling.keda.sh/paused=true --overwrite
                                               done
                                            fi
                                            kubectl get deployment --namespace ${namespace};
                                            """
                                }
                            }
                        }
                        //need to add wait?
                        //Start active clusters
                        for (cluster in clusters) {
                            if (params.activeClusters == null || activeClusters.contains("${cluster}")) {
                                scalePodsCommand ="kubectl get deployment --namespace ${namespace} --no-headers -o=custom-columns='NAME:.metadata.name' | xargs -I{} kubectl scale --current-replicas=0 --replicas=1 --namespace ${namespace} deployment/{} || true"
                                echo "Executing scaling script for: ${scalePodsCommand}"
                                retry(3) {
                                    this.sh """set +x;
                                            export KUBECONFIG=${v_kubehome}/${namespace}.${cluster};
                                            ${scalePodsCommand};
                                            if [ \$(kubectl get crd scaledobjects.keda.sh --no-headers | wc -l) -gt 0 ]; then
                                               kubectl get scaledobject -o custom-columns='NAME:.metadata.name' --no-headers | while read o; do
                                                 kubectl annotate scaledobjects \$o autoscaling.keda.sh/paused=false --overwrite
                                               done
                                            fi
                                            kubectl get deployment --namespace ${namespace};
                                            """
                                }
                            }
                        }
                    }
                }
            }
        }
        stage('Wait until rollout complete') {
            options {
                timeout(time: 15, unit: "MINUTES")
            }
            steps {
                script {
                    try {
                      for (namespace in namespaces) {
                        for (cluster in clusters) {
                          echo "Checking rollout status in ${namespace}.${cluster}"
                          retry(3) {
                              this.sh """set +x;
                                  export KUBECONFIG=${v_kubehome}/${namespace}.${cluster};
                                  kubectl get deployment -o custom-columns=NAME:metadata.name --no-headers | while read c; do
                                  kubectl rollout status deployment \$c -n ${namespace}; done """
                          }
                        }
                      }
                    } catch (org.jenkinsci.plugins.workflow.steps.FlowInterruptedException e) {
                        unstable(message: "Timeout reached, please check the status in GKE directly")
                    } catch (Throwable e) {
                        unstable(message: "Caught unexpected Exception: ${e.toString()}")
                    }
                }
            }
        }
    }
    post {
      success {
        echo "operation completed [succeeded]"
      }
      unsuccessful{
        echo "operation completed [failed]"
      }
    }
}
