static final boolean C_TRUE = true
static final boolean C_FALSE = false
static final String C_SUCCESS = 'SUCCESS'
static final String C_FAILURE = 'FAILURE'
static final String C_NULL = null
static final String C_YES = 'Yes'
static final String C_NO = 'No'
static final String packer_dir = "/usr/bin"

pipeline {    
    agent  { 
        label "unity-dev-jenkins-agent"    
    }
    environment {
	    STASH_TOKEN= credentials('github-connect')   
    }

    stages {
        stage('gcloud auth activate') {
	        steps{
                script{
		            withCredentials([[$class: 'StringBinding', credentialsId: 'uat_img_builder_path', variable: 'gcp_sa_key']]) {
                        this.sh(returnStdout: true, script: "gcloud auth activate-service-account --key-file ${gcp_sa_key}")
                        this.sh 'export GOOGLE_APPLICATION_CREDENTIALS=$gcp_sa_key'
	                }	
                }	   
	        }
	      }
        stage('GCE image Builder') {
            steps {
                script {
                    withCredentials([
                       [$class: 'UsernamePasswordMultiBinding', credentialsId: 'uat_nexus3_connect', usernameVariable: 'NEXUSUSERNAME', passwordVariable: 'NEXUSPASSWORD'],
                        [$class: 'StringBinding', credentialsId: 'uat_gihub_connect', variable: 'STASH_TOKEN']
                    ]) {
                        this.sh "source ./initial_setup/var-uat.conf"

                        this.sh """${packer_dir}/packer build -timestamp-ui -on-error=cleanup \
                        -var "BUILDSTAGE3=true"  \
                        -var "STAGE=stage3"  \
                        -var "NEXUS_CREDS_USR=$NEXUSUSERNAME" \
                        -var "NEXUS_CREDS_PWD=$NEXUSPASSWORD" \
                        -var "STASH_TOKEN=$STASH_TOKEN" \
                        -var-file=uat/variables.json uat/image.json"""          
                    }                    
                }
            }
        }
    }

    post {
        success {
            script {
                echo "Sucess begins..."   
            }
        }
        unsuccessful {
            script {
                echo "Rollback begins..."      
            }
        }       
        cleanup {
            script {
                cleanWs()
            }
        }
    }
}