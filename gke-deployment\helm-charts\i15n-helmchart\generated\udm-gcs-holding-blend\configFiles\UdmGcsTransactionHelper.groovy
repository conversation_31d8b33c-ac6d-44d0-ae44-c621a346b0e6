import com.poc.hss.fasttrack.transform.model.LookupRequest
import io.vertx.core.json.JsonObject
import org.apache.commons.lang3.StringUtils
import org.apache.poi.ss.formula.functions.T
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import java.text.DecimalFormat
import java.time.LocalDateTime
import java.time.ZoneId
import java.time.ZoneOffset
import java.time.ZonedDateTime
import java.time.format.DateTimeFormatter
import java.util.function.Predicate
import java.util.regex.Pattern

class UdmGcsTransactionHelper {
    private static final Logger logger = LoggerFactory.getLogger(UdmGcsTransactionHelper.class);

    static Predicate<List<T>> isListNotNullAndEmpty = record -> null != record && !record.isEmpty();

    static LookupRequest createLookupRequest(String criteria, String tableName, List<String> fields) {
        return LookupRequest.builder()
                .accessSchemaName(tableName)
                .criteria(criteria)
                .fields(fields)
                .build()
    }

    static String deriveHbcTransactionIdentification(JsonObject msg) {
        return String.format("%s-%s-%s", msg.getString("country_code")?.trim(),
                msg.getString("account_region")?.trim(),
                msg.getString("transaction_reference")?.trim())
    }

    static String deriveLocationCodeOrDescription(JsonObject msg, String locationCountryCode, String returnField) {
        if (locationCountryCode != "GB") {
            return ""
        } else if (returnField != "") {
            return formatOutputFieldNullToEmpty(returnField)
        } else {
            return msg.getString("country_loc_code")
        }
    }

    static String deriveLocalCustodianCodeOrDescription(JsonObject msg, String locationCountryCode, String returnField) {
        if (locationCountryCode == "GB") {
            return ""
        } else if (returnField != "") {
            return formatOutputFieldNullToEmpty(returnField)
        } else {
            return msg.getString("country_loc_code")
        }
    }

    static String deriveSafekeepingAccountIdentification(String secAccID) {
        if (secAccID.isEmpty() || secAccID == null) {
            return ""
        } else {
            final String[] parts = secAccID.split("-")
            String countryCode = parts[2] == "HBEU" ? "GB" : "FR"
            return String.format("%s %s %s", countryCode, parts[2], parts[3])
        }
    }

    static String deriveCounterpartyDataSourceSchemeCodeOrType(JsonObject msg,
                                                               String idType,
                                                               String type,
                                                               String securityMovementTypeValue,
                                                               String returnField) {
        if (idType == "Brkr") {
            if (type != "B" || type != "LOCAL") {
                return formatOutputFieldNullToEmpty(returnField)
            }
        } else {
            String securityMovementType = deriveSecuritiesMovementType(msg);
            if (securityMovementType == securityMovementTypeValue) {
                if (type != "B" || type != "LOCAL") {
                    return formatOutputFieldNullToEmpty(returnField)
                }
            }
        }
        return ""
    }

    static String deriveLocalCustodianName(JsonObject msg) {
        String placeOfSafekeepingCountryCode = msg.getString("place_of_safekeeping_country_code")?.trim();
        if (placeOfSafekeepingCountryCode == "GB") {
            return "HSBC Bank Plc";
        } else {
            return formatOutputFieldNullToEmpty(msg.getString("place_of_safekeeping_name")?.trim());
        }
    }

    static String deriveSecuritiesMovementType(JsonObject msg) {
        String transactionType = msg.getString("transaction_type");
        String direction = TransactionType.getFromType(transactionType).getDirection();
        if (direction == "R") {
            return "RECE";
        } else if (direction == "D") {
            return "DELI";
        } else {
            return "";
        }
    }

    static String deriveSourceSystemTransactionType(String transactionType) {
        return TransactionType.getFromType(transactionType).getDescription();
    }

    static String deriveCounterpartyBic(JsonObject msg, String idType,
                                        String securityMovementTypeValue,
                                        String counterpartyType,
                                        String counterpartyName,
                                        String counterpartySwiftCode) {
        if (idType == "Brkr") {
            if (counterpartyType == "B") {
                return formatOutputFieldNullToEmpty(counterpartyName)
            } else {
                return formatOutputFieldNullToEmpty(counterpartySwiftCode);
            }
        } else {
            String securityMovementType = deriveSecuritiesMovementType(msg);
            if (securityMovementType == securityMovementTypeValue) {
                if (counterpartyType == "B") {
                    return formatOutputFieldNullToEmpty(counterpartyName)
                } else {
                    return formatOutputFieldNullToEmpty(counterpartySwiftCode)
                }
            }
            return "";
        }
    }

    static String deriveExternalTradeStatus(JsonObject msg) {
        String allegedTradIndicator = msg.getString("alleged_trad_ind", "")?.trim();
        String tradeStatus = msg.getString("trade_status", "")?.trim();

        if (allegedTradIndicator.equalsIgnoreCase("false")) {
            return formatOutputFieldNullToEmpty(tradeStatus)
        } else {
            if (tradeStatus.equalsIgnoreCase("CANCELLED")) {
                return "ALLEGED CANCELLED"
            } else {
                return "ALLEGED"
            }
        }
    }

    static String deriveCounterpartyFields(JsonObject msg,
                                           String securityMovementTypeValue,
                                           String returnField) {
        String securityMovementType = deriveSecuritiesMovementType(msg);
        if (securityMovementType == securityMovementTypeValue) {
            return formatOutputFieldNullToEmpty(returnField);
        } else {
            return "";
        }
    }

    static String deriveFxRateToAccountBaseCurrencyAndFxReversalRate(String fxIndicator, String fxRate) {
        if (!StringUtils.isEmpty(fxRate)) {
            Double number = Double.parseDouble(fxRate)
            if (number > 0 && !StringUtils.isEmpty(fxIndicator)) {
                switch (fxIndicator) {
                    case "M":
                        return fxRate
                    case "D":
                        Double rate = Double.parseDouble(fxRate)
                        return String.format("%.6f", (1 / rate));
                }
            }
        }
        return ""
    }


    static String deriveAccountBaseCurrencySettlementAmount(String amount) {
        if (amount != null) {
            if (amount != "" || !amount.isEmpty()) {
                Double amt = Double.parseDouble(amount)
                if (amt > 0) return String.valueOf(amt)
                else return ""
            } else {
                return ""
            }
        } else {
            return ""
        }
    }

    static String deriveSettlementAmount(JsonObject msg) {
        String settlementAmount = msg.getString("settlement_amount")
        if (settlementAmount != "" || !settlementAmount.isEmpty()) {
            Double settlementAmt = Double.parseDouble(settlementAmount)
            if (settlementAmt > 0) return String.valueOf(settlementAmt)
        }
        return ""
    }

    static String deriveSellerOrBuyerAccountIdentification(JsonObject msg,
                                                           String securityMovementTypeValue,
                                                           String returnField) {
        String securitiesSettlementType = deriveSecuritiesSettlementType(msg)
        if (securitiesSettlementType != "Internal Settlement") {
            String securityMovementType = deriveSecuritiesMovementType(msg);
            if (securityMovementType == securityMovementTypeValue) {
                return formatOutputFieldNullToEmpty(returnField);
            }
        }
        return ""
    }

    static String deriveSecuritiesSettlementType(JsonObject msg) {
        String locationCountryCode = msg.getString("place_of_safekeeping_country_code")
        String buyerSellerSafekeepingAccNo = msg.getString("buyer_seller_safekeeping_acc_no")
        String placeOfSafekeeping = msg.getString("place_of_safekeeping") != null && msg.getString("place_of_safekeeping")?.length() != 0
                ? msg.getString("place_of_safekeeping")?.substring(0, 3) : ""
        String brokerIdName = msg.getString(UdmGcsTransactionConstant.COUNTERPARTY_BROKER_NAME)
        String buyerSellerIdName = msg.getString(UdmGcsTransactionConstant.COUNTERPARTY_BUYER_SELLER_NAME)
        String transactionType = msg.getString("transaction_type")
        String registration = msg.getString("registration")

        List<String> nameList = List.of("NFIT", "2701")
        List<String> transactionTypeList = List.of("RFP", "RVP", "DFP", "DVP", "SLN", "SLR", "STOCK BORROW", "STOCK BORROW RETURN");
        List<String> cstNameList = List.of("BH01MSS1", "2701MSS1", "4JQAQNCMN", "9FMAYMLL9", "BFMAYMLL8", "LNKAVBBB1", "4SMAYNCMC", "AFMAYGEC6", "0LQAQNCMP", "HDMAYGEC9", "BRUAANCMS", "7IUAANCMW", "HQUAGCSTD", "KJQAQNCMO", "BH01ANCMO", "9AUAGCSTB", "ASMAYNCMC", "BH01APPL1", "FQUAANCMX", "1AUAGCSTF","KKUAAMIG1")
        if (locationCountryCode != "GB") {
            if (buyerSellerSafekeepingAccNo != "" && (nameList.contains(brokerIdName) || nameList.contains(buyerSellerIdName)))
                return "Internal Settlement"
            else if (transactionTypeList.contains(transactionType))
                return "External Settlement"
            else return "Non Settlement"
        } else if (locationCountryCode == "GB") {
            if (buyerSellerSafekeepingAccNo != "" && (
                    (placeOfSafekeeping == "CGO" && (List.of("5676", "7676").contains(brokerIdName) || List.of("5676", "7676").contains(buyerSellerIdName)))
                            || (placeOfSafekeeping == "CST" && (cstNameList.contains(join(brokerIdName,registration)) || cstNameList.contains(join(buyerSellerIdName,registration))))
            )) {
                return "Internal Settlement"
            } else if (transactionTypeList.contains(transactionType)) {
                return "External Settlement"
            } else return "Non Settlement"
        }
        return "";
    }

    static String deriveReasonCodeDescriptionOrInformation(JsonObject msg, String externalTradeStatus) {
        if (externalTradeStatus == "SETTLED") {
            return "This trade is in a settled status, no further action required."
        } else if (externalTradeStatus == "MATCHED") {
            return "This trade is now in a matched status."
        } else {
            String SwiftStatus1Reason1Code = formatOutputFieldNullToEmpty(msg.getString("swift_status_1_rsncode_1"))
            String AdditionalCancellationReasonInformation = deriveUnexecutionReasonCodeAndInformation("CANCEL_REASON_INFO", msg.getString("trade_status"), msg.getString("remarks"))
            String AdditionalUnexecutionReasonInformation = deriveUnexecutionReasonCodeAndInformation("REASON_INFO", msg.getString("trade_status"), msg.getString("remarks"))
            String SwiftStatus1Reason1CodeDescription = formatOutputFieldNullToEmpty(msg.getString("swift_status_1_rsncode_1_desc"))

            if (SwiftStatus1Reason1Code == "") {
                return getReasonCode(externalTradeStatus)
            } else if (SwiftStatus1Reason1Code.contains("NARR")) {
                if (AdditionalCancellationReasonInformation != "")
                    return AdditionalCancellationReasonInformation
                else if (AdditionalUnexecutionReasonInformation != "")
                    return AdditionalUnexecutionReasonInformation
                else
                    return getReasonCode(externalTradeStatus)
            } else {
                return SwiftStatus1Reason1CodeDescription
            }
        }
    }

    static String getReasonCode(String externalTradeStatus) {
        if (externalTradeStatus == "CANCELLED") {
            return "This instruction has been cancelled, please contact a HSBC representative for further details."
        } else if (externalTradeStatus == "UNMATCHED") {
            return "The matching process for this trade is in progress, please contact a HSBC representative for further details."
        } else if (externalTradeStatus == "FAILED") {
            return "This trade has failed, please contact a HSBC representative for further details."
        } else if (externalTradeStatus == "ALLEGED") {
            return "The trade is now in alleged status."
        } else if (externalTradeStatus == "ALLEGED CANCELLED") {
            return "This alleged trade has been cancelled, please contact a HSBC representative for further details"
        }
    }

    static String derivePayment(JsonObject msg) {
        String transactionType = msg.getString("transaction_type")
        if (transactionType == "RVP" || transactionType == "DVP")
            return "APMT"
        else
            return "FREE"
    }

    static String deriveSourceOfInstructionType(String tradeSource, String tradeInstructionSrc) {
        if (tradeSource == "ONLINE") {
            switch (tradeInstructionSrc) {
                case "S":
                    return "ONLINE - SWIFT"
                case "F":
                    return "ONLINE - Fax"
                case "M":
                    return "ONLINE - 599"
                case "E":
                    return "CORPAC"
                case "C":
                    return "ONLINE - Contra"
                case "I":
                    return "ONLINE - Internal"
                case "O":
                    return "ONLINE - Other"
                default:
                    return "ONLINE - Manual"
            }
        } else if (tradeSource == "SWIFT") {
            if (tradeInstructionSrc == "H")
                return "HSBCNet"
            else
                return "SWIFT"
        } else
            return tradeSource
    }

    static String deriveCounterpartyIdentification(String BrokerIdType, String brokerName) {
        if (BrokerIdType == "LOCAL") {
            return brokerName
        }
        return ""
    }

    static String deriveNoChangeOfBeneficialOwnershipIndicator(String changeOfBeneficiaryOwnership) {
        if (changeOfBeneficiaryOwnership == "YBEN")
            return "N"
        else if (changeOfBeneficiaryOwnership == "NBEN")
            return "Y"
    }

    static String deriveAssociatedAccountIdentification(String type, JsonObject msg) {
        String placeOfSafekeeping = msg.getString("place_of_safekeeping")
        String subAccountNumber = msg.getString("associated_sub_acc")
        switch (type) {
            case "ACC_ID":
                if (placeOfSafekeeping == "CST" || placeOfSafekeeping == "CGO") {
                    final String[] parts = subAccountNumber.split("-", 2)
                    if (parts.length == 2)
                        return parts[0]
                } else
                    return subAccountNumber

            case "SUB_ACC_ID":
                if (placeOfSafekeeping == "CST" || placeOfSafekeeping == "CGO") {
                    final String[] parts = subAccountNumber.split("-", 2)
                    if (parts.length == 2)
                        return parts[1]
                } else
                    return ""
        }
    }

    static String derivePlaceOfTradeMicorTypeCode(String type, String placeofTrade) {
        final String[] parts = placeofTrade.split("/", 2)
        if (type == "TradMic" && parts.length == 2)
            return parts[1]
        else if (type == "TradTypeCode" && parts.length == 2)
            return parts[0]
        else
            ""
    }


    static String deriveUnexecutionReasonCodeAndInformation(String type, String tradeStatus, String returnFields) {
        if ((type.equalsIgnoreCase("REASON_CODE") || type.equalsIgnoreCase("REASON_INFO"))
                && (tradeStatus == "UNMATCHED" || tradeStatus == "FAILED"))
            return returnFields
        if ((type.equalsIgnoreCase("CANCEL_REASON_CODE") || type.equalsIgnoreCase("CANCEL_REASON_INFO"))
                && tradeStatus == "CANCELLED")
            return returnFields
        return ""
    }


    static String deriveGenerateDepositoryInstructionIndicator(JsonObject msg) {
        String subCustSwiftSntind = msg.getString("sub_cust_swift_sntind")
        String depoInstructionInd = msg.getString("depo_instruction_ind")
        String locationCountryCode = msg.getString("place_of_safekeeping_country_code")
        if (locationCountryCode != "GB") {
            if (subCustSwiftSntind == "Y")
                return "Auto Generation"
            else if (subCustSwiftSntind == "N")
                return "Not Generate"
        } else if (locationCountryCode == "GB") {
            if (depoInstructionInd == "Y")
                return "Auto Generation"
            else if (depoInstructionInd == "N")
                return "Not Generate"
        }
        return ""
    }

    static String deriveTradeMaintenanceStatus(JsonObject msg) {
        String locationCountryCode = msg.getString("place_of_safekeeping_country_code")
        String sourceSystemStatus = msg.getString("source_system_status")
        String subStringSourceStatus = (sourceSystemStatus != null && sourceSystemStatus?.length() != 0) ? sourceSystemStatus?.substring(0, 3) : ""
        String sourceSystemState = msg.getString("source_system_state")
        String tradeSource = msg.getString("trade_source")
        String captureUserId = msg.getString("capture_user_identification")
        String pendingCancelationIndicator = msg.getString("pending_cancelation_indicator")
        String cancellationOriginatorType = msg.getString("cancellation_originator_type")
        String preVerificationIndicator = msg.getString("pre_verification_indicator")
        String securitiesSettlementType = deriveSecuritiesSettlementType(msg)
        if (locationCountryCode == "GB" && securitiesSettlementType == "External Settlement" && (sourceSystemStatus == "UNVX" || sourceSystemStatus == "UNVU") && sourceSystemState == "OPEN" && tradeSource == "CREST" && cancellationOriginatorType == "")
            return "TradCaptrdCSDAllgmt"
        else if (sourceSystemStatus == "UNVX" && sourceSystemState == "OPEN" && captureUserId == "PJ04" && cancellationOriginatorType == "")
            return "TradCaptrdInErr"
        else if (sourceSystemStatus == "UNVX" && sourceSystemState == "OPEN" && captureUserId != "PJ04" && cancellationOriginatorType == "") {
            if (preVerificationIndicator != "P")
                return "TradCaptrdPdgApprvl"
            else if (preVerificationIndicator == "P")
                return "TradCaptrdPdgPreVrfctn"
        } else if (subStringSourceStatus != "UNV" && sourceSystemState == "OPEN" && (pendingCancelationIndicator == "Y" || pendingCancelationIndicator == "P")) {
            if (pendingCancelationIndicator == "P")
                return "TradCancPdgRvsl"
            else if (pendingCancelationIndicator == "Y")
                return "TradCancPdgConf"
        } else if (subStringSourceStatus == "UNV" && sourceSystemState == "OPEN" && cancellationOriginatorType != "")
            return "TradCancPdgActn"
        else if (sourceSystemState == "CANCL")
            return "TradCanc"
        else
            return "TradCaptrd"
    }

    static String deriveTradeMatchingStatus(JsonObject msg) {
        String securitiesSettlementType = deriveSecuritiesSettlementType(msg)
        String locationCountryCode = msg.getString("place_of_safekeeping_country_code")
        String substringplaceOfSafekeeping = msg.getString("place_of_safekeeping") != null && msg.getString("place_of_safekeeping")?.length() != 0
                ? msg.getString("place_of_safekeeping")?.substring(0, 3) : ""
        String sourceSystemStatus = msg.getString("source_system_status")
        String sourceSystemState = msg.getString("source_system_state")
        String subCustSwiftSntin = msg.getString("sub_cust_swift_sntind")
        String swiftStatus1Rsncode1 = msg.getString("swift_status_1_rsncode_1")
        String subStringswiftStatus1Rsncode1 = (swiftStatus1Rsncode1 != null && swiftStatus1Rsncode1?.length() != 0) ? swiftStatus1Rsncode1?.substring(swiftStatus1Rsncode1?.length() - 4, swiftStatus1Rsncode1?.length()) : ""
        String cancellationOriginatorType = msg.getString("cancellation_originator_type")
        String transactionType = msg.getString("transaction_type")
        String depoInstructionInd = msg.getString("depo_instruction_ind")
        if (locationCountryCode != "GB" && securitiesSettlementType == "Internal Settlement" && sourceSystemState == "OPEN" &&
                (sourceSystemStatus == "VERX" || sourceSystemStatus == "CHKX" ) && subCustSwiftSntin == "N" && cancellationOriginatorType == "" && swiftStatus1Rsncode1 == "") {
            return "TradToBeMtchdNtrnly"
        } else if (securitiesSettlementType == "External Settlement" && sourceSystemState == "OPEN" && sourceSystemStatus == "VERX" && cancellationOriginatorType == "" && swiftStatus1Rsncode1 == "" &&
                ((locationCountryCode != "GB" && subCustSwiftSntin == "N" || transactionType == "SLN") ||
                        (locationCountryCode == "GB" && (substringplaceOfSafekeeping == "CST" ||
                                substringplaceOfSafekeeping == "CGO" ||
                                substringplaceOfSafekeeping == "MMI" ||
                                substringplaceOfSafekeeping == "DBV") &&
                                depoInstructionInd == "N"))) {
            return "TradToBeMtchdXtrnly"
        } else if (locationCountryCode != "GB" && securitiesSettlementType != "Non Settlement" && sourceSystemState == "OPEN" &&
                (sourceSystemStatus == "ADVX" || sourceSystemStatus == "VERX" || sourceSystemStatus == "CHKX") &&
                subCustSwiftSntin == "Y" && cancellationOriginatorType == "" && swiftStatus1Rsncode1 == "") {
            return "TradMtchgReqdSbCtdn"
        } else if (locationCountryCode == "GB" && sourceSystemState == "OPEN" && cancellationOriginatorType == "" &&
                ((securitiesSettlementType == "External Settlement" &&
                        (sourceSystemStatus == "VERX" ||
                                sourceSystemStatus == "ADVX" ||
                                sourceSystemStatus == "CHKX") &&
                        (substringplaceOfSafekeeping == "CST" ||
                                substringplaceOfSafekeeping == "CGO" ||
                                substringplaceOfSafekeeping == "MMI" ||
                                substringplaceOfSafekeeping == "DBV") &&
                        depoInstructionInd == "Y") ||
                        (securitiesSettlementType == "Internal Settlement" && (sourceSystemStatus == "VERX" || sourceSystemStatus == "CHKX") &&
                                (substringplaceOfSafekeeping == "CST" ||
                                        substringplaceOfSafekeeping == "CGO" ||
                                        substringplaceOfSafekeeping == "MMI" ||
                                        substringplaceOfSafekeeping == "DBV")))) {
            return "TradMtchgReqdCSD"
        } else if ((sourceSystemState == "OPEN"
                && List.of("VERM", "ADVM").contains(sourceSystemStatus)
                && cancellationOriginatorType == "")) {
            return "TradMtchd"
        } else if (locationCountryCode == "GB" && sourceSystemState == "OPEN" && sourceSystemStatus == "VERX" &&
                (substringplaceOfSafekeeping != "CST" &&
                        substringplaceOfSafekeeping != "CGO" &&
                        substringplaceOfSafekeeping != "MMI" &&
                        substringplaceOfSafekeeping != "DBV") &&
                cancellationOriginatorType == "") {
            return "NoMtchgReqrd"
        } else if (sourceSystemState == "OPEN" &&
                (sourceSystemStatus == "VERU" ||
                        sourceSystemStatus == "VERX" ||
                        sourceSystemStatus == "CHKU" ||
                        sourceSystemStatus == "CHKX" ||
                        sourceSystemStatus == "ADVU" ||
                        sourceSystemStatus == "ADVX" ||
                        sourceSystemStatus == "UNVU") &&
                subStringswiftStatus1Rsncode1 == "CMIS" &&
                cancellationOriginatorType == "") {
            return "CtrPtyInstrMssng"
        } else if (sourceSystemState == "OPEN" &&
                (((sourceSystemStatus == "VERX" ||
                        sourceSystemStatus == "ADVX") &&
                        swiftStatus1Rsncode1 != "") ||
                        (sourceSystemStatus == "VERU" ||
                                sourceSystemStatus == "ADVU" ||
                                sourceSystemStatus == "UNVU")) &&
                (subStringswiftStatus1Rsncode1 != "LACK" &&
                        subStringswiftStatus1Rsncode1 != "MONY" &&
                        subStringswiftStatus1Rsncode1 != "CMIS" &&
                        subStringswiftStatus1Rsncode1 != "LALO") &&
                cancellationOriginatorType == ""){
            return "TradMsmtchd"
        } else {
            return null
        }
    }

    static String deriveTradeStockSettlementStatus(JsonObject msg, String deliverCode, String receiveCode) {
        String locationCountryCode = msg.getString("place_of_safekeeping_country_code")
        String sourceSystemStatus = msg.getString("source_system_status")
        String subStringsourceSystemStatus = (sourceSystemStatus != null && sourceSystemStatus?.length() != 0) ? sourceSystemStatus?.substring(0, 3) : ""
        String sourceSystemState = msg.getString("source_system_state")?.trim()
        String securitiesMovementType = deriveSecuritiesMovementType(msg)?.trim()
        String transactionType = msg.getString("transaction_type")?.trim()
        String swiftStatus1Rsncode1 = msg.getString("swift_status_1_rsncode_1")?.trim()
        String subStringSwiftStatus1Rsncode1 = (swiftStatus1Rsncode1 != null && swiftStatus1Rsncode1?.length() != 0) ? swiftStatus1Rsncode1?.substring(swiftStatus1Rsncode1?.length() - 4, swiftStatus1Rsncode1?.length()) : ""
        String cancellationOriginatorType = msg.getString("cancellation_originator_type")
        String placeOfSafekeeping = msg.getString("place_of_safekeeping")
        String subStringPlaceOfSafekeeping = (placeOfSafekeeping != null && placeOfSafekeeping?.length() != 0) ? placeOfSafekeeping?.substring(0, 3) : ""
        if (sourceSystemState == "FINAL" && (subStringsourceSystemStatus == "SET" || subStringsourceSystemStatus == "DEP" || subStringsourceSystemStatus == "DEL" || subStringsourceSystemStatus == "DVD"))
            return "StockSttld"
        else if (sourceSystemState == "SPLIT")
            return "StockPrtlySttld"
        else if (locationCountryCode == "GB" && sourceSystemState == "OPEN" && subStringsourceSystemStatus == "SET")
            return "StockSttldPdgCertActn"
        else if (locationCountryCode == "GB" && sourceSystemState == "OPEN" &&
                (subStringsourceSystemStatus == "CIH" || subStringsourceSystemStatus == "REG" || subStringsourceSystemStatus == "DIS") &&
                (subStringPlaceOfSafekeeping != "UNT" && subStringPlaceOfSafekeeping != "TBD") &&
                cancellationOriginatorType == "")
            return "CertRegnInPrgrs"
        else if (locationCountryCode == "GB" && sourceSystemState == "OPEN" &&
                (subStringsourceSystemStatus == "WTH" || subStringsourceSystemStatus == "BRP") &&
                (subStringPlaceOfSafekeeping != "UNT" && subStringPlaceOfSafekeeping != "TBD") &&
                cancellationOriginatorType == "")
            return "CertDlvryPdgConf"
        else {
            if (securitiesMovementType == receiveCode)
                return ""
            else if (securitiesMovementType == deliverCode) {
                if (sourceSystemState == "OPEN" && subStringsourceSystemStatus == "UNV" &&
                        (transactionType == "DVP" ||
                                transactionType == "DFP" ||
                                transactionType == "SLN" ||
                                transactionType == "STOCK BORROW RETURN" ||
                                transactionType == "ETD DEL"))
                    return "StockUallctd"
                else if (sourceSystemState == "OPEN" &&
                        (subStringsourceSystemStatus == "VER" || subStringsourceSystemStatus == "ADV") &&
                        (transactionType == "DVP" ||
                                transactionType == "DFP" ||
                                transactionType == "SLN" ||
                                transactionType == "STOCK BORROW RETURN" ||
                                transactionType == "ETD DEL") &&
                        ((subStringSwiftStatus1Rsncode1 != "LACK" &&  subStringSwiftStatus1Rsncode1 != "LALO")
                                || swiftStatus1Rsncode1 == ""))
                    return "StockAllctd"
                else if ((sourceSystemState == "OPEN" &&
                        subStringsourceSystemStatus == "CHK" &&
                        (transactionType == "DVP" ||
                                transactionType == "DFP" ||
                                transactionType == "SLN" ||
                                transactionType == "STOCK BORROW RETURN" ||
                                transactionType == "ETD DEL")) ||
                        (sourceSystemState == "OPEN" &&
                                (sourceSystemStatus == "UNVU" ||
                                        sourceSystemStatus == "UNVX" ||
                                        sourceSystemStatus == "VERU" ||
                                        sourceSystemStatus == "VERX") &&
                                (transactionType == "DVP" ||
                                        transactionType == "DFP" ||
                                        transactionType == "SLN" ||
                                        transactionType == "STOCK BORROW RETURN" ||
                                        transactionType == "ETD DEL") &&
                                (subStringSwiftStatus1Rsncode1 == "LACK" ||
                                        subStringSwiftStatus1Rsncode1 == "LALO")))
                    return "StockNsffcnt"
            } else
                return ""
        }
        return null
    }

    static String deriveTradeCashSettlementStatus(JsonObject msg, String deliverCode, String receiveCode) {
        String locationCountryCode = msg.getString("place_of_safekeeping_country_code")
        String payment = derivePayment(msg)
        String sourceSystemStatus = msg.getString("source_system_status")
        String subStringsourceSystemStatus = (sourceSystemStatus != null && sourceSystemStatus?.length() != 0) ? sourceSystemStatus?.substring(0, 3) : ""
        String sourceSystemState = msg.getString("source_system_state")
        String transactionType = msg.getString("transaction_type")
        String securitiesMovementType = deriveSecuritiesMovementType(msg)
        String swiftStatus1Rsncode1 = msg.getString("swift_status_1_rsncode_1")
        String subStringSwiftStatus1Rsncode1 = (swiftStatus1Rsncode1 != null && swiftStatus1Rsncode1?.length() != 0) ? swiftStatus1Rsncode1?.substring(swiftStatus1Rsncode1?.length() - 4, swiftStatus1Rsncode1?.length()) : ""
        String fxInstructionStatus = msg.getString("fx_instruction_status")
        String cancellationOriginatorType = msg.getString("cancellation_originator_type")
        String placeOfSafekeeping = msg.getString("place_of_safekeeping")
        String subStringPlaceOfSafekeeping = (placeOfSafekeeping != null && placeOfSafekeeping?.length() != 0) ? placeOfSafekeeping?.substring(0, 3) : ""
        if (payment == "FREE") {
            return ""
        } else if (payment == "APMT") {
            if (sourceSystemState == "FINAL" && (subStringsourceSystemStatus == "SET" || subStringsourceSystemStatus == "DEP" || subStringsourceSystemStatus == "DEL" || subStringsourceSystemStatus == "DVD") && (fxInstructionStatus == "N" || fxInstructionStatus == "C" || fxInstructionStatus == "I" || fxInstructionStatus == "B")) {
                if (fxInstructionStatus == "N" || fxInstructionStatus == "C") {
                    return "CshSttld"
                } else if (fxInstructionStatus == "I" || fxInstructionStatus == "B") {
                    return "CshSttldPdgFxActn"
                }
            } else if (sourceSystemState == "SPLIT") {
                return "CshPrtlySttld"
            } else if (locationCountryCode == "GB" && sourceSystemState == "OPEN" && cancellationOriginatorType == "" && subStringsourceSystemStatus == "CIH") {
                return "TradPmtInPrgrs"
            } else if (locationCountryCode == "GB" && sourceSystemState == "OPEN" && cancellationOriginatorType == "" && (subStringsourceSystemStatus == "REG" || subStringsourceSystemStatus == "DIS") && (subStringPlaceOfSafekeeping == "UNT" || subStringPlaceOfSafekeeping == "TBD")) {
                return "TradPmtConf"
            } else if (locationCountryCode == "GB" && sourceSystemState == "OPEN" && cancellationOriginatorType == "" && (subStringsourceSystemStatus == "WTH" || subStringsourceSystemStatus == "BRP") && (subStringPlaceOfSafekeeping == "UNT" || subStringPlaceOfSafekeeping == "TBD")) {
                return "TradPmtPdgBrkr"
            } else {
                if (securitiesMovementType == deliverCode)
                    return ""
                else if (securitiesMovementType == receiveCode) {
                    if (sourceSystemState == "OPEN" && subStringsourceSystemStatus == "UNV" && transactionType == "RVP") {
                        return "CshUallctd"

                    } else if (sourceSystemState == "OPEN" &&
                            (subStringsourceSystemStatus == "VER" || subStringsourceSystemStatus == "ADV") &&
                            (swiftStatus1Rsncode1 == "" || subStringSwiftStatus1Rsncode1 != "MONY") &&
                            transactionType == "RVP"){
                        return "CshAllctd"
                    } else if (sourceSystemState == "OPEN" &&
                            (sourceSystemStatus == "UNVU" || sourceSystemStatus == "UNVX" || sourceSystemStatus == "VERU" || sourceSystemStatus == "VERX") &&
                            transactionType == "RVP" &&
                            subStringSwiftStatus1Rsncode1 == "MONY") {
                        return "CshNsffcnt"
                    }
                } else
                    return ""
            }
        }
        return null
    }

    static String deriveTradeHoldStatus(JsonObject msg) {
        String sourceSystemState = msg.getString("source_system_state")?.trim()
        String sourceSystemStatus = msg.getString("source_system_status")?.trim()
        String subStringsourceSystemStatus = (sourceSystemStatus != null && sourceSystemStatus?.length() != 0) ? sourceSystemStatus?.substring(0, 3) : ""
        String swiftStatus1Rsncode1 = msg.getString("swift_status_1_rsncode_1")?.trim()
        String subStringSwiftStatus1Rsncode1 = (swiftStatus1Rsncode1 != null && swiftStatus1Rsncode1?.length() != 0) ? swiftStatus1Rsncode1?.substring(swiftStatus1Rsncode1?.length() - 4, swiftStatus1Rsncode1?.length()) : ""
        String snctnsInd = msg.getString("snctns_Ind")
        if (sourceSystemState == "OPEN" && snctnsInd == "IMPOSED")
            return "TradOnHldDueToSnctns"
        else if ((sourceSystemState == "OPEN" && subStringsourceSystemStatus == "CHK") ||
                (sourceSystemState == "OPEN" &&
                        (sourceSystemStatus == "VERU" || sourceSystemStatus == "VERX" || sourceSystemStatus == "UNVU") &&
                        (subStringSwiftStatus1Rsncode1 == "MONY" || subStringSwiftStatus1Rsncode1 == "LACK" || subStringSwiftStatus1Rsncode1 == "LALO")))
            return "TradOnHldByHsbc"
        else
            return ""
    }

    static String deriveOwningBusinessName(JsonObject msg){
        String siteCode = msg.getString("site").toUpperCase()
        String localCustodianCode = deriveLocalCustodianCodeOrDescription(msg,
                msg.getString("place_of_safekeeping_country_code"),
                msg.getString("place_of_safekeeping"))
        if(siteCode == "GCUK"){
            if(localCustodianCode != "")
                return "GC UK"
            else
                return "DCC UK"
        }else if(siteCode == "GCE"){
            return "GC EU"
        }
    }

    static String deriveInternalAccountTransferAccountIdentification(JsonObject msg) {
        String securitiesSettlementType = deriveSecuritiesSettlementType(msg)
        String buyerSellerSafekeepingAccNo = msg.getString("buyer_seller_safekeeping_acc_no")
        if (securitiesSettlementType == "Internal Settlement")
            return buyerSellerSafekeepingAccNo
        else
            return ""
    }

    static String deriveCrossBusinessTransactionIdentification(JsonObject msg){
        String owningBusinessName = deriveOwningBusinessName(msg)
        String systemTransactionIdentification = msg.getString("transaction_reference")
        if(owningBusinessName == "GC UK")
            return systemTransactionIdentification
        else
            return ""
    }

    static String deriveSuppressNostroMessageIndicator(String indicator) {
        if (indicator == "Y")
            return "N"
        else if (indicator == "N")
            return "Y"
    }

    static String deriveCurrentSourceFinalLocationCode(String placeOfSafekeepingCountryCode, String returnValue) {
        if (placeOfSafekeepingCountryCode != "GB")
            return ""
        else
            return returnValue
    }

    static String deriveCurrentSourceFinalLocalCustodianCode(String placeOfSafekeepingCountryCode, String returnValue) {
        if (placeOfSafekeepingCountryCode != "GB")
            return returnValue
        else
            return ""
    }

    static String deriveAmountOrPriceFields(String input, int beforeDecimal, int afterDecimal) {
        if (!StringUtils.isEmpty(input)) {
            Double number = Double.parseDouble(input)
            return number > 0 ? formatFieldWithDecimalPlaces(number, beforeDecimal, afterDecimal) : ""
        }
        return ""
    }

    static String deriveNumericField(String input) {
        if (!StringUtils.isEmpty(input)) {
            Double number = Double.parseDouble(input)
            return number > 0 ? number : ""
        }
    }

    static String join(String firstInput, String secondInput){
            return String.format("%s%s", firstInput,secondInput)
    }

    static String deriveTransactionReferenceAmount(String input, String settlementQuantity, DecimalFormat decimalFormat) {
        if (!StringUtils.isEmpty(input) && !StringUtils.isEmpty("settlementQuantity")) {
            Double number = Double.parseDouble(input)
            Double quantity = Double.parseDouble(settlementQuantity)
            return formatAmountInDecimal((quantity*number), decimalFormat)
        }
    }

    static String formatAmountInDecimal(Double input, DecimalFormat decimalFormat) {
            return input > 0 ? decimalFormat.format(input) : ""
    }

    static String formatOutputField(String value) {
        return value == null || value.isEmpty() ? null : value
    }

    static String formatOutputFieldNullToEmpty(Object value) {
        return value == null || value == "null" ? "" : value
    }

    static String formatFieldWithDecimalPlaces(Double input, int beforeDecimal, int afterDecimal) {
        return String.format("%" + beforeDecimal + "." + afterDecimal + "f", input)
    }

    static String convert2UTC(JsonObject json, String field) {
        String timeStr = json.getString(field)?.trim()
        if (StringUtils.isEmpty(timeStr))
            return ""
        try {
            ZonedDateTime systemZoneDateTime = LocalDateTime.parse(timeStr, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")).atZone(ZoneId.of("Europe/London"));
            ZonedDateTime utcDateTime = systemZoneDateTime.withZoneSameInstant(ZoneId.of("UTC"));
            return DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss").format(utcDateTime.withZoneSameInstant(ZoneId.of("UTC")).toLocalDateTime());
        } catch (Exception e) {
            logger.error("[UdmGcsTransactionHelper]convert2UTC failed! _id:{} field:{} timeStr:{} var:{} error:{}", json.getString("_id"), field, timeStr, ZoneId.systemDefault(), e.getMessage())
            return ""
        }
    }

    enum TransactionType {
        RECEIVE_FREE("RFP", "R", "RECEIVE FREE"),
        RECEIVE_VERSUS_PAYMENT("RVP", "R", "RECEIVE VERSUS PAYMENT"),
        DELIVER_FREE("DFP", "D", "DELIVER FREE"),
        DELIVER_VERSUS_PAYMENT("DVP", "D", "DELIVER VERSUS PAYMENT"),
        DEPOSITORY_DEPOSIT("DEP", "R", "DEPOSITORY DEPOSIT"),
        DEPOSITORY_WITHDRAW("WDL", "D", "DEPOSITORY WITHDRAW"),
        TRANSFER("TRANSFER", "SR", "TRANSFER"),
        TEMP_WITHDRAW("TEMP WITHDRAW", "D", "TEMP WITHDRAW"),
        INTERNAL_MOVE("INTERNAL MOVE", "SL", "INTERNAL MOVE"),
        PLEDGE("PLEDGE", "SL", "PLEDGE"),
        PLEDGE_RELEASE("PLEDGE RELEASE", "SL", "PLEDGE RELEASE"),
        SEGREGATE("SEGREGATE", "SL", "SEGREGATE"),
        SEGREGATE_RELEASE("SEGREGATE RELEASE", "SL", "SEGREGATE RELEASE"),
        STOCK_LOAN("SLN", "D", "STOCK LOAN"),
        STOCK_LOAN_RETURN("SLR", "R", "STOCK LOAN RETURN"),
        NON_TRADE("NON-TRADE", "R", "NON TRADE"),
        REDEMPTION("REDEMPTION", "D", "REDEMPTION"),
        SWITCH_TO("SWITCH TO", "D", "SWITCH TO"),
        SWITCH_FROM("SWITCH FROM", "R", "SWITCH FROM"),
        STOCK_DIVIDEND_REC("STOCK DIVIDEND REC", "R", "STOCK DIVIDEND REC"),
        WITHDRAW_REORG("WITHDRAW REORG", "D", "WITHDRAW REORG"),
        CORPORATE_RECEIVE("CORPORATE RECEIVE", "R", "CORPORATE RECEIVE"),
        CORPORATE_DELIVER("CORPORATE DELIVER", "D", "CORPORATE DELIVER"),
        BTO_RECEIVE("BTO RECEIVE", "R", "BTO RECEIVE"),
        BTO_DELIVER("BTO DELIVER", "D", "BTO DELIVER"),
        EDC_RELO("EDC RELO", "SL", "EDC RELO"),
        ETD_RELO("ETD RELO", "SL", "ETD RELO"),
        ETD_DEL("ETD DEL", "D", "ETD DEL"),
        EDC_REC("EDC REC", "R", "EDC REC"),
        STOCK_BORROW("STOCK BORROW", "R", "STOCK BORROW"),
        STOCK_BORROW_RETURN("STOCK BORROW RETURN", "D", "STOCK BORROW RETURN"),
        NET_RECEIVE("NET RECEIVE", "R", "NET RECEIVE"),
        NET_DELIVER("NET DELIVER", "D", "NET DELIVER"),
        UNDEFINED("UNDEFINED", "U", "UNDEFINED")

        private final String type;
        private final String direction;
        private final String description;

        private TransactionType(String type, String direction, String description) {
            this.type = type;
            this.direction = direction;
            this.description = description;

        }

        String getType() {
            return this.type;
        }

        String getDirection() {
            return this.direction;
        }

        String getDescription() {
            return this.description;
        }

        static TransactionType getFromType(String inputType) {
            List<TransactionType> transactionTypeValues = List.of(values());

            return transactionTypeValues.stream()
                    .filter { transactionType -> (inputType == transactionType.type) }
                    .map { transactionType -> transactionType }
                    .findFirst()
                    .orElse(UNDEFINED);
        }
    }
}