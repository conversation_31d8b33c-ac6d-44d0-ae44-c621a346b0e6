#!/usr/bin/env bash
set -euo pipefail

#please pass the iput vale either dev or prod like sh ./initial_setup/create_gcebucket dev
profile_env=$1

echo "[MGMT_HOST] checking for default conf"
if [ ! -f "./initial_setup/var-${profile_env}.conf" ]; then
 echo "Parameter file var-${profile_env}.conf not found in initial_setup."
 exit 1
fi

source ./initial_setup/var-${profile_env}.conf

PROJECT_ID=$PROJECT_ID
REGION=$REGION

echo "[MGMT_HOST] creating  bucket for Management Host"
gsutil mb -l $REGION -p $PROJECT_ID gs://$PROJECT_ID-key-management/

gsutil kms encryption -k projects/${CMEK_PROJECT}/locations/${REGION}/keyRings/cloudStorage/cryptoKeys/cloudStorage -w gs://${PROJECT_ID}-key-management/

echo "[MGMT_HOST] creating  bucket for Management Host"
gsutil mb -l $REGION -p $PROJECT_ID gs://$PROJECT_ID-terraform-state-bucket/

gsutil kms encryption -k projects/${CMEK_PROJECT}/locations/${REGION}/keyRings/cloudStorage/cryptoKeys/cloudStorage -w gs://$PROJECT_ID-terraform-state-bucket/


echo "[MGMT_HOST] creating and uploading sa key to gs://$PROJECT_ID-key-management/"

IFS=', ' read -r -a sa_list <<< $SERVICE_ACCOUNT_LIST

for i in "${sa_list[@]}"
do
    SA_KEY=$"$HOME/ServiceAccountKeys/${i}.json"

    gsutil -m cp "${SA_KEY}" gs://$PROJECT_ID-key-management/"${i}"/
done

gsutil cp -r Certs/${profile_env}/* gs://$PROJECT_ID-key-management/Certificates/
gsutil cp -r terraform/.terraform.tar.gz gs://$PROJECT_ID-key-management/

echo "[MGMT_HOST] listing files in gs://$PROJECT_ID-mgmt-host/"
gsutil ls gs://$PROJECT_ID-key-management/