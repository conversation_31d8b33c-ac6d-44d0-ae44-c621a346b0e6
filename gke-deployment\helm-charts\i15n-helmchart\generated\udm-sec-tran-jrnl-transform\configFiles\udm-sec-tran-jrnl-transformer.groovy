import com.poc.hss.fasttrack.transform.AbstractTransformer
import com.poc.hss.fasttrack.transform.model.TransformerBatchInputContext
import com.poc.hss.fasttrack.transform.model.TransformerRecord
import io.vertx.core.json.JsonObject
import org.slf4j.Logger
import org.slf4j.LoggerFactory

import java.util.stream.Collectors

class SecuritiesTransactionJournalTransformer extends AbstractTransformer {
    JsonObject constructRecord(JsonObject source){
        if(!source.getString("JHPSTS")?.equals("P")){
            return null
        }
        return new JsonObject()
                .put("site", source.getString("SITE")?.trim())
                .put("JHCREF", source.getString("JHCREF")?.trim())
                .put("JHXREF", source.getString("JHXREF")?.trim())
                .put("JHTCAC", source.getString("JHTCAC")?.trim())
                .put("JHDLUP", source.getString("JHDLUP")?.trim())
                .put("JHTLUP", source.getString("JHTLUP")?.trim())
                .put("JHTXFT", source.getString("JHTXFT")?.trim())
                .put("JHTXFS", source.getString("JHTXFS")?.trim())
                .put("JHTXFQ", source.getString("JHTXFQ")?.trim())
                .put("JHTXFX", source.getString("JHTXFX")?.trim())
                .put("JHSFTP", source.getString("JHSFTP")?.trim())
                .put("JHPSTS", source.getString("JHPSTS")?.trim())
                .put("event_source", source.getString("FILE_NAME", "")?.trim())
                .put("transaction_id",String.format("%s-%s-%06d-%02d-%01d-%08d",source.getString("SITE")?.trim().toUpperCase(),source.getString("JHTXFT")?.trim(),getInteger("JHTXFS",source)
                        ,getInteger("JHTXFQ",source),getInteger("JHTXFX",source),getInteger("JHTRDT",source)))
                .put("partition_key",String.format("%s-%s-%06d-%02d-%01d",source.getString("SITE")?.trim().toUpperCase(),source.getString("JHTXFT")?.trim(),getInteger("JHTXFS",source)
                        ,getInteger("JHTXFQ",source),getInteger("JHTXFX",source)))
    }

    static Integer getInteger( String key,JsonObject obj) {
        String result = obj.getString(key);
        if (result != null && !result.trim().equals("")) {
            return new BigDecimal(result).intValueExact();
        } else {
            return 0;
        }
    }

    private static final Logger logger = LoggerFactory.getLogger(SecuritiesTransactionJournalTransformer.class)

    @Override
    List<TransformerRecord> batchTransform(TransformerBatchInputContext context) {
        List<TransformerRecord> records = context.getRecords()
        return records.stream().map({
            rec ->
                JsonObject source = rec.getData()
                JsonObject transformedSource = constructRecord(source)
                if(transformedSource == null){
                    return null
                }
                return rec.from(rec).data(transformedSource).kafkaPartitionKey(transformedSource.getString("partition_key")).build()
        }).filter(Objects::nonNull).collect(Collectors.toList())
    }
}
