#!/usr/bin/env bash
set -euo pipefail
echo "<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<< kubernetes >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>"
cd /opt/
echo "[Mgmt_Host] changed path to opt"
wget --user=${NEXUS_CREDS_USR} --password=${NEXUS_CREDS_PWD} "https://nexus3.systems.uk.hsbc:8081/nexus/repository/misc-foss-n3p/com/google/kubectl/1.12.0/kubectl-1.12.0-linux.zip"
echo "[MGMT_HOST]downloading package from nexus"
echo $PWD
unzip kubectl-1.12.0-linux.zip
mv kubectl-1.12.0 kubectl
chmod 777 kubectl


ln -s /opt/kubectl /usr/bin
echo "kubectl installation completed"