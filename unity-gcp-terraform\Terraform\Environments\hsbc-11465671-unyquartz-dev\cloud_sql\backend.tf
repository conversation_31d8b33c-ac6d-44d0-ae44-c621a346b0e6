terraform {
  required_providers {
    google = {
      source  = "hashicorp/google"
      version = ">= 4.1.0"
    }
    google-beta = {
      source  = "hashicorp/google-beta"
      version = "~> 4.1.0"
    }
    random = {
      source  = "hashicorp/random"
      version = "~> 2.2.0"
    }
    template = {
      source  = "hashicorp/template"
      version = "~> 2.1.2"
    }
    tls = {
      source  = "hashicorp/tls"
      version = "~> 2.1.0"
    }
  }
  backend "gcs" {
    bucket = "hsbc-11465671-unyquartz-dev-terraform-state-bucket"
    prefix = "cloud_sql"
  }
}