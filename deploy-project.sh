#!/bin/bash
set -euo pipefail

# Generic deployment script for projects using gke-deployment
# Usage: ./deploy-project.sh <project-name> [options]

# Function to print usage
usage() {
    echo "Usage: $0 <project-name> [options]"
    echo ""
    echo "Arguments:"
    echo "  project-name    Name of the project to deploy (e.g., change-dashboard, risc)"
    echo ""
    echo "Options:"
    echo "  --namespace     Target namespace (default: ns-i15n-dev)"
    echo "  --environment   Target environment: dev|uat|prod (default: dev)"
    echo "  --dry-run       Show what would be deployed without actually deploying"
    echo "  --force         Force deployment even if validation fails"
    echo "  --help, -h      Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 change-dashboard"
    echo "  $0 risc --namespace ns-i15n-uat --environment uat"
    echo "  $0 change-dashboard --dry-run"
    exit 1
}

# Parse arguments
if [ $# -lt 1 ]; then
    echo "Error: Project name is required"
    usage
fi

PROJECT_NAME="$1"
shift

# Default configuration
NAMESPACE="ns-i15n-dev"
ENVIRONMENT="dev"
DRY_RUN=false
FORCE=false
MAX_HISTORY="5"
POST_ACTION="start"
ADDITIONAL_OPTIONS=""

# Parse options
while [[ $# -gt 0 ]]; do
    case $1 in
        --namespace)
            NAMESPACE="$2"
            shift 2
            ;;
        --environment)
            ENVIRONMENT="$2"
            shift 2
            ;;
        --dry-run)
            DRY_RUN=true
            shift
            ;;
        --force)
            FORCE=true
            shift
            ;;
        --help|-h)
            usage
            ;;
        *)
            echo "Error: Unknown option $1"
            usage
            ;;
    esac
done

# Environment-specific configurations
case "${ENVIRONMENT}" in
    "dev")
        NAMESPACE="${NAMESPACE:-ns-i15n-dev}"
        GCP_PROJECT="hsbc-9087302-unity-dev"
        ;;
    "uat")
        NAMESPACE="${NAMESPACE:-ns-i15n-uat}"
        GCP_PROJECT="hsbc-9087302-unity-uat"
        ;;
    "prod")
        NAMESPACE="${NAMESPACE:-ns-i15n-prod}"
        GCP_PROJECT="hsbc-9087302-unity-prod"
        ;;
    *)
        echo "Error: Invalid environment '${ENVIRONMENT}'. Must be dev, uat, or prod"
        exit 1
        ;;
esac

echo "=== Deploying ${PROJECT_NAME} to GKE ==="
echo "Configuration:"
echo "  Project: ${PROJECT_NAME}"
echo "  Environment: ${ENVIRONMENT}"
echo "  Namespace: ${NAMESPACE}"
echo "  GCP Project: ${GCP_PROJECT}"
echo "  Dry Run: ${DRY_RUN}"

# Validate project exists
if [ ! -d "${PROJECT_NAME}" ]; then
    echo "Error: Project directory '${PROJECT_NAME}' not found"
    exit 1
fi

# Check if gke-deployment directory exists
if [ ! -d "gke-deployment" ]; then
    echo "Error: gke-deployment directory not found"
    echo "Please ensure you're running this script from the repository root"
    exit 1
fi

# Check if Helm values exist
VALUES_FILE="gke-deployment/helm-charts/i15n-helmchart/${PROJECT_NAME}/values.yaml"
if [ ! -f "${VALUES_FILE}" ]; then
    echo "Error: Helm values file not found: ${VALUES_FILE}"
    echo "Please ensure the Helm values are created first"
    exit 1
fi

echo ""
echo "=== Pre-deployment Validation ==="

# Verify kubectl is available
if ! command -v kubectl &> /dev/null; then
    echo "Error: kubectl not found. Please install kubectl"
    exit 1
fi

# Verify helm is available
if ! command -v helm &> /dev/null; then
    echo "Error: helm not found. Please install helm"
    exit 1
fi

# Verify kubectl connectivity
echo "Checking kubectl connectivity..."
if ! kubectl cluster-info --request-timeout=10s > /dev/null 2>&1; then
    echo "Error: Cannot connect to Kubernetes cluster"
    echo "Please ensure kubectl is configured and you have access to the cluster"
    exit 1
fi
echo "✅ Kubectl connectivity verified"

# Get current context
CURRENT_CONTEXT=$(kubectl config current-context)
echo "Current kubectl context: ${CURRENT_CONTEXT}"

# Verify namespace exists or create it
echo "Checking namespace ${NAMESPACE}..."
if kubectl get namespace "${NAMESPACE}" > /dev/null 2>&1; then
    echo "✅ Namespace ${NAMESPACE} exists"
else
    if [ "${DRY_RUN}" = "true" ]; then
        echo "⚠️  Namespace ${NAMESPACE} does not exist (would be created)"
    else
        echo "⚠️  Namespace ${NAMESPACE} does not exist, creating..."
        kubectl create namespace "${NAMESPACE}"
        echo "✅ Namespace ${NAMESPACE} created"
    fi
fi

# Verify service account exists
SERVICE_ACCOUNT="ns-i15n-${ENVIRONMENT}-sa"
echo "Checking service account ${SERVICE_ACCOUNT}..."
if kubectl get serviceaccount "${SERVICE_ACCOUNT}" -n "${NAMESPACE}" > /dev/null 2>&1; then
    echo "✅ Service account ${SERVICE_ACCOUNT} exists"
else
    echo "⚠️  Service account ${SERVICE_ACCOUNT} not found in namespace ${NAMESPACE}"
    if [ "${FORCE}" = "false" ]; then
        echo "Use --force to proceed anyway, or create the service account first"
        exit 1
    fi
fi

# Verify docker secret exists
echo "Checking docker secret..."
if kubectl get secret docker-secret -n "${NAMESPACE}" > /dev/null 2>&1; then
    echo "✅ Docker secret exists"
else
    echo "⚠️  Docker secret not found in namespace ${NAMESPACE}"
    if [ "${FORCE}" = "false" ]; then
        echo "Use --force to proceed anyway, or create the docker secret first"
        exit 1
    fi
fi

# Validate Helm values
echo "Validating Helm values..."
if helm template "${PROJECT_NAME}" -f "${VALUES_FILE}" gke-deployment/helm-charts/i15n-helmchart/templates/default/ > /dev/null 2>&1; then
    echo "✅ Helm values validation passed"
else
    echo "❌ Helm values validation failed"
    echo "Please check the Helm values file: ${VALUES_FILE}"
    exit 1
fi

# Show what will be deployed
echo ""
echo "=== Deployment Preview ==="
echo "Helm Chart: gke-deployment/helm-charts/i15n-helmchart/templates/default/"
echo "Values File: ${VALUES_FILE}"
echo "Release Name: ${PROJECT_NAME}"
echo "Namespace: ${NAMESPACE}"

if [ "${DRY_RUN}" = "true" ]; then
    echo ""
    echo "=== DRY RUN: Showing what would be deployed ==="
    cd gke-deployment
    helm template "${PROJECT_NAME}" -f "helm-charts/i15n-helmchart/${PROJECT_NAME}/values.yaml" helm-charts/i15n-helmchart/templates/default/ --namespace "${NAMESPACE}"
    cd ..
    echo ""
    echo "=== DRY RUN COMPLETED ==="
    echo "To actually deploy, run without --dry-run flag"
    exit 0
fi

# Actual deployment
echo ""
echo "=== Deploying with Helm ==="

# Change to gke-deployment directory
cd gke-deployment

# Make deploy.sh executable
chmod +x ./deploy.sh

# Run deployment using existing deploy.sh script
echo "Executing: ./deploy.sh ${PROJECT_NAME} ${NAMESPACE} ${MAX_HISTORY} ${POST_ACTION} '${ADDITIONAL_OPTIONS}'"
./deploy.sh "${PROJECT_NAME}" "${NAMESPACE}" "${MAX_HISTORY}" "${POST_ACTION}" "${ADDITIONAL_OPTIONS}"

DEPLOY_EXIT_CODE=$?

if [ ${DEPLOY_EXIT_CODE} -eq 0 ]; then
    echo ""
    echo "=== Deployment Completed Successfully ==="
    echo "Application: ${PROJECT_NAME}"
    echo "Namespace: ${NAMESPACE}"
    echo "Environment: ${ENVIRONMENT}"
    
    echo ""
    echo "=== Post-deployment Status ==="
    
    # Check deployment status
    echo "Deployment status:"
    kubectl get deployment "${PROJECT_NAME}" -n "${NAMESPACE}" 2>/dev/null || echo "  Deployment not found"
    
    # Check pods
    echo ""
    echo "Pod status:"
    kubectl get pods -l app.kubernetes.io/name="${PROJECT_NAME}" -n "${NAMESPACE}" 2>/dev/null || echo "  No pods found"
    
    # Check service
    echo ""
    echo "Service status:"
    kubectl get service "${PROJECT_NAME}" -n "${NAMESPACE}" 2>/dev/null || echo "  Service not found"
    
    # Check ingress
    echo ""
    echo "Ingress status:"
    if kubectl get ingress "${PROJECT_NAME}" -n "${NAMESPACE}" > /dev/null 2>&1; then
        INGRESS_HOST=$(kubectl get ingress "${PROJECT_NAME}" -n "${NAMESPACE}" -o jsonpath='{.spec.rules[0].host}' 2>/dev/null || echo "unknown")
        echo "  Ingress URL: https://${INGRESS_HOST}"
        kubectl get ingress "${PROJECT_NAME}" -n "${NAMESPACE}"
    else
        echo "  No ingress configured"
    fi
    
    echo ""
    echo "✅ Deployment successful!"
    
else
    echo ""
    echo "❌ Deployment failed with exit code ${DEPLOY_EXIT_CODE}"
    echo ""
    echo "Troubleshooting steps:"
    echo "1. Check the Helm values file: ${VALUES_FILE}"
    echo "2. Verify the Docker image exists and is accessible"
    echo "3. Check namespace permissions and service account"
    echo "4. Review the deployment logs above for specific errors"
    exit 1
fi

# Return to original directory
cd ..
