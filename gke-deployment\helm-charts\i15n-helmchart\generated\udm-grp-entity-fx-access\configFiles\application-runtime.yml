---
spring:
  datasource:
    url: "********************************/${DB_NAME}"
    username: "${DB_USER}"
    password: "${DB_PASSWORD}"
  jpa:
    show-sql: false
    generate-ddl: true
    properties:
      hibernate:
        dialect: "org.hibernate.dialect.PostgreSQLDialect"
        hbm2ddl:
          auto: "none"
  application:
    name: "udm-grp-entity-fx-access"
kafkaConfig:
  auto.offset.reset: "latest"
  bootstrap.servers: "kafka-0.kafka-internal.ns-core-prod-apps.svc.cluster.local:9093,kafka-1.kafka-internal.ns-core-prod-apps.svc.cluster.local:9093,kafka-2.kafka-internal.ns-core-prod-apps.svc.cluster.local:9093"
  group.id: "${KAFKA_CONSUMER_GROUP}"
  schema.registry.url: "https://hkl20077608.hc.cloud.hk.hsbc:8082/"
  security.protocol: "SSL"
  ssl.keystore.location: "/hss/apps/certs/unity-microservices.jks"
  ssl.keystore.password: "${KAFKA_KEYSTORE_PASSWORD}"
  ssl.truststore.location: "/hss/apps/certs/unity-microservices.ts"
  ssl.truststore.password: "${KAFKA_TRUSTSTORE_PASSWORD}"
management:
  endpoints:
    web:
      exposure:
        include: "health,prometheus"
  metrics:
    tags:
      application: "${spring.application.name}"
  tracing:
    enabled: true
cache:
  provider: "kafka"
  address: "http://cache"
  topic: "unity2-PROD-core-cache-metric"
projectId: "12bb8fb6-ba3d-4615-9b0b-f4f5cb38656f"
accesses:
- persistenceEnabled: true
  sourceTopic: "unity2-PROD-core-transform-udm-grp-entity-fx-out"
  tableName: "udm-grp-entity-fx"
  columns:
  - name: "_trace_id"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "_id"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "created_by"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "created_time_stamp"
    dataType: "timestamp"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "deleted_by"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "deleted_time_stamp"
    dataType: "timestamp"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "updated_by"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "updated_time_stamp"
    dataType: "timestamp"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "_batch_id"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "OPGPID"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "OPEYID"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "ODCTCD"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "ODGMAB"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "ODACB"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "ODACS"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "ODACX"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "ODTYCC"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "ODDCIN"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "ODAFCY"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "ODATCY"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "ODALFR"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "ODACOI"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "ODDLUP"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "site"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  script: "SET SCHEMA '12bb8fb6_ba3d_4615_9b0b_f4f5cb38656f'; CREATE INDEX IF NOT\
    \ EXISTS \"udm-grp-entity-fx-comp-idx\" ON \"12bb8fb6_ba3d_4615_9b0b_f4f5cb38656f\"\
    .\"udm-grp-entity-fx\" (\"ODACB\", \"ODACS\", \"ODACX\", \"ODAFCY\", \"ODATCY\"\
    , \"ODCTCD\", \"ODDCIN\", \"ODGMAB\", \"ODTYCC\", \"OPEYID\", \"OPGPID\");  CREATE\
    \ MATERIALIZED VIEW IF NOT EXISTS \"12bb8fb6_ba3d_4615_9b0b_f4f5cb38656f\".\"\
    AO_TPC_CLIENT_ACCOUNT_MAPPING\"  AS  SELECT a._id,     b.group_id,     b.entity_id,\
    \     a.sec_account_number,     b.customer_id,     a.business_line,     b.client_legal_entity\
    \    FROM \"12bb8fb6_ba3d_4615_9b0b_f4f5cb38656f\".\"SEC_ACCOUNT\" a,     ( SELECT\
    \ a_1.group_id,             a_1.entity_id,             a_1.customer_id,      \
    \       b_1.client_legal_entity            FROM ( SELECT \"CUSTOMER\".group_id,\
    \                     \"CUSTOMER\".entity_id,                     \"CUSTOMER\"\
    ._id AS customer_id                    FROM \"12bb8fb6_ba3d_4615_9b0b_f4f5cb38656f\"\
    .\"CUSTOMER\"                   WHERE \"CUSTOMER\".site = 'hk'::text AND (\"CUSTOMER\"\
    ._id = ANY (ARRAY['HK-HK-HSBC-111-277653'::text, 'HK-HK-HSBC-510-868888'::text,\
    \ 'HK-HK-HSBC-511-233116'::text, 'HK-HK-HSBC-567-255401'::text, 'HK-HK-HSBC-567-778683'::text,\
    \ 'HK-HK-HSBC-720-101286'::text, 'HK-HK-HSBC-730-297900'::text, 'HK-HK-HSBC-730-532090'::text,\
    \ 'HK-HK-HSBC-741-240683'::text, 'HK-HK-HSBC-808-196133'::text, 'HK-HK-HSBC-848-390126'::text,\
    \ 'HK-HK-HSBC-848-801213'::text, 'HK-HK-HSBC-808-192785'::text, 'HK-HK-HSBC-741-147672'::text])))\
    \ a_1,             ( SELECT                         CASE                     \
    \        WHEN length(TRIM(BOTH FROM \"substring\"(\"STANDING_DATA\".code, 1, 4)))\
    \ = 1 THEN lpad(TRIM(BOTH FROM \"substring\"(\"STANDING_DATA\".code, 1, 4)), 2,\
    \ '0'::text)                             ELSE TRIM(BOTH FROM \"substring\"(\"\
    STANDING_DATA\".code, 1, 4))                         END AS group_id,        \
    \                 CASE                             WHEN length(TRIM(BOTH FROM\
    \ \"substring\"(\"STANDING_DATA\".code, 5, 4))) = 1 THEN lpad(TRIM(BOTH FROM \"\
    substring\"(\"STANDING_DATA\".code, 5, 4)), 2, '0'::text)                    \
    \         ELSE TRIM(BOTH FROM \"substring\"(\"STANDING_DATA\".code, 5, 4))   \
    \                      END AS entity_id,                     \"STANDING_DATA\"\
    .alias_id_type AS client_legal_entity                    FROM \"12bb8fb6_ba3d_4615_9b0b_f4f5cb38656f\"\
    .\"STANDING_DATA\"                   WHERE \"STANDING_DATA\".site = 'hk'::text\
    \ AND (\"STANDING_DATA\".key_id = ANY (ARRAY['V#'::text, 'V%'::text]))) b_1  \
    \         WHERE a_1.group_id = b_1.group_id AND a_1.entity_id = b_1.entity_id)\
    \ b   WHERE a.customer_id = b.customer_id;  ALTER TABLE \"12bb8fb6_ba3d_4615_9b0b_f4f5cb38656f\"\
    .\"AO_TPC_CLIENT_ACCOUNT_MAPPING\"     OWNER TO uny_role_owner;  GRANT ALL ON\
    \ TABLE \"12bb8fb6_ba3d_4615_9b0b_f4f5cb38656f\".\"AO_TPC_CLIENT_ACCOUNT_MAPPING\"\
    \ TO uny_role_owner; GRANT INSERT, SELECT, UPDATE, DELETE ON TABLE \"12bb8fb6_ba3d_4615_9b0b_f4f5cb38656f\"\
    .\"AO_TPC_CLIENT_ACCOUNT_MAPPING\" TO uny_role_user_app; GRANT INSERT, SELECT,\
    \ UPDATE, DELETE ON TABLE \"12bb8fb6_ba3d_4615_9b0b_f4f5cb38656f\".\"AO_TPC_CLIENT_ACCOUNT_MAPPING\"\
    \ TO uny_role_user_ff; GRANT SELECT ON TABLE \"12bb8fb6_ba3d_4615_9b0b_f4f5cb38656f\"\
    .\"AO_TPC_CLIENT_ACCOUNT_MAPPING\" TO uny_role_user_ro;  CREATE TABLE IF NOT EXISTS\
    \ \"12bb8fb6_ba3d_4615_9b0b_f4f5cb38656f\".insufficient_holding (     _id text\
    \ primary key COLLATE pg_catalog.\"default\",     sec_account text COLLATE pg_catalog.\"\
    default\",     instrument text COLLATE pg_catalog.\"default\",     location text\
    \ COLLATE pg_catalog.\"default\",     registration text COLLATE pg_catalog.\"\
    default\",     client_legal_entity text COLLATE pg_catalog.\"default\",     unity_holding\
    \ jsonb,     transactions jsonb,     insufficient_holding jsonb );  CREATE INDEX\
    \ IF NOT EXISTS \"insufficient_holding-composite-idx\" ON \"12bb8fb6_ba3d_4615_9b0b_f4f5cb38656f\"\
    .\"insufficient_holding\" (\"sec_account\", \"instrument\", \"location\", \"registration\"\
    );  insert into \"12bb8fb6_ba3d_4615_9b0b_f4f5cb38656f\".insufficient_holding(_id,insufficient_holding)\
    \ values('STATUS','{\"status\":\"Ready\"}') on conflict do nothing;  CREATE TABLE\
    \ IF NOT EXISTS \"12bb8fb6_ba3d_4615_9b0b_f4f5cb38656f\".\"AO_TPC_TUPLES\" ( \
    \    processed_date text COLLATE pg_catalog.\"default\",     securities_account_id\
    \ text COLLATE pg_catalog.\"default\",     location text COLLATE pg_catalog.\"\
    default\",     registration text COLLATE pg_catalog.\"default\",     instrument_id\
    \ text COLLATE pg_catalog.\"default\",     client_legal_entity text COLLATE pg_catalog.\"\
    default\" )  TABLESPACE pg_default;  ALTER TABLE IF EXISTS \"12bb8fb6_ba3d_4615_9b0b_f4f5cb38656f\"\
    .\"AO_TPC_TUPLES\"     OWNER to uny_role_owner;  REVOKE ALL ON TABLE \"12bb8fb6_ba3d_4615_9b0b_f4f5cb38656f\"\
    .\"AO_TPC_TUPLES\" FROM uny_role_user_ro;  GRANT ALL ON TABLE \"12bb8fb6_ba3d_4615_9b0b_f4f5cb38656f\"\
    .\"AO_TPC_TUPLES\" TO uny_role_owner;  GRANT UPDATE, DELETE, INSERT, SELECT ON\
    \ TABLE \"12bb8fb6_ba3d_4615_9b0b_f4f5cb38656f\".\"AO_TPC_TUPLES\" TO uny_role_user_app;\
    \  GRANT UPDATE, DELETE, INSERT, SELECT ON TABLE \"12bb8fb6_ba3d_4615_9b0b_f4f5cb38656f\"\
    .\"AO_TPC_TUPLES\" TO uny_role_user_ff;  GRANT SELECT ON TABLE \"12bb8fb6_ba3d_4615_9b0b_f4f5cb38656f\"\
    .\"AO_TPC_TUPLES\" TO uny_role_user_ro;  CREATE UNIQUE INDEX IF NOT EXISTS unique_id_aotpc_tuples\
    \     ON \"12bb8fb6_ba3d_4615_9b0b_f4f5cb38656f\".\"AO_TPC_TUPLES\" USING btree\
    \     (processed_date ASC NULLS LAST, securities_account_id ASC NULLS LAST, location\
    \ ASC NULLS LAST, registration ASC NULLS LAST, instrument_id ASC NULLS LAST, client_legal_entity\
    \ ASC NULLS LAST)     TABLESPACE pg_default;  CREATE TABLE IF NOT EXISTS \"12bb8fb6_ba3d_4615_9b0b_f4f5cb38656f\"\
    .\"AO_TPC_HOLDINGS\" (     sec_account_id text COLLATE pg_catalog.\"default\"\
    ,     location_id text COLLATE pg_catalog.\"default\",     registration text COLLATE\
    \ pg_catalog.\"default\",     sec_id text COLLATE pg_catalog.\"default\",    \
    \ registered numeric,     pending_del_hold_position numeric,     \"pendingDeliveryHoldPositionBreakdown\"\
    \ text COLLATE pg_catalog.\"default\",     \"heldOnSettledInLinkBreakdown\" text\
    \ COLLATE pg_catalog.\"default\" )  TABLESPACE pg_default;  ALTER TABLE IF EXISTS\
    \ \"12bb8fb6_ba3d_4615_9b0b_f4f5cb38656f\".\"AO_TPC_HOLDINGS\"     OWNER to uny_role_owner;\
    \  REVOKE ALL ON TABLE \"12bb8fb6_ba3d_4615_9b0b_f4f5cb38656f\".\"AO_TPC_HOLDINGS\"\
    \ FROM uny_role_user_ro;  GRANT ALL ON TABLE \"12bb8fb6_ba3d_4615_9b0b_f4f5cb38656f\"\
    .\"AO_TPC_HOLDINGS\" TO uny_role_owner;  GRANT UPDATE, DELETE, INSERT, SELECT\
    \ ON TABLE \"12bb8fb6_ba3d_4615_9b0b_f4f5cb38656f\".\"AO_TPC_HOLDINGS\" TO uny_role_user_app;\
    \  GRANT UPDATE, DELETE, INSERT, SELECT ON TABLE \"12bb8fb6_ba3d_4615_9b0b_f4f5cb38656f\"\
    .\"AO_TPC_HOLDINGS\" TO uny_role_user_ff;  GRANT SELECT ON TABLE \"12bb8fb6_ba3d_4615_9b0b_f4f5cb38656f\"\
    .\"AO_TPC_HOLDINGS\" TO uny_role_user_ro;  CREATE UNIQUE INDEX IF NOT EXISTS \"\
    idx_unique_AO_TPC_HOLDINGS\"     ON \"12bb8fb6_ba3d_4615_9b0b_f4f5cb38656f\".\"\
    AO_TPC_HOLDINGS\" USING btree     (sec_account_id COLLATE pg_catalog.\"default\"\
    \ ASC NULLS LAST, location_id COLLATE pg_catalog.\"default\" ASC NULLS LAST, registration\
    \ COLLATE pg_catalog.\"default\" ASC NULLS LAST, sec_id COLLATE pg_catalog.\"\
    default\" ASC NULLS LAST)     TABLESPACE pg_default;  CREATE TABLE IF NOT EXISTS\
    \ \"12bb8fb6_ba3d_4615_9b0b_f4f5cb38656f\".\"AO_TPC_TRADES\" (     settlement_date\
    \ text COLLATE pg_catalog.\"default\",     switching_type text COLLATE pg_catalog.\"\
    default\",     securities_account_id text COLLATE pg_catalog.\"default\",    \
    \ location text COLLATE pg_catalog.\"default\",     registration text COLLATE\
    \ pg_catalog.\"default\",     instrument_id text COLLATE pg_catalog.\"default\"\
    ,     _id text COLLATE pg_catalog.\"default\",     transaction_type text COLLATE\
    \ pg_catalog.\"default\",     split_indicator text COLLATE pg_catalog.\"default\"\
    ,     security_quantity text COLLATE pg_catalog.\"default\",     \"T8STAS\" text\
    \ COLLATE pg_catalog.\"default\",     \"T8PVST\" text COLLATE pg_catalog.\"default\"\
    ,     switching_indicator text COLLATE pg_catalog.\"default\",     depository_indicator\
    \ text COLLATE pg_catalog.\"default\",     unexecute_reason_code text COLLATE\
    \ pg_catalog.\"default\",     scrip_hold_status text COLLATE pg_catalog.\"default\"\
    ,     direction text COLLATE pg_catalog.\"default\" )  TABLESPACE pg_default;\
    \  ALTER TABLE IF EXISTS \"12bb8fb6_ba3d_4615_9b0b_f4f5cb38656f\".\"AO_TPC_TRADES\"\
    \     OWNER to uny_role_owner;  REVOKE ALL ON TABLE \"12bb8fb6_ba3d_4615_9b0b_f4f5cb38656f\"\
    .\"AO_TPC_TRADES\" FROM uny_role_user_ro;  GRANT ALL ON TABLE \"12bb8fb6_ba3d_4615_9b0b_f4f5cb38656f\"\
    .\"AO_TPC_TRADES\" TO uny_role_owner;  GRANT DELETE, SELECT, INSERT, UPDATE ON\
    \ TABLE \"12bb8fb6_ba3d_4615_9b0b_f4f5cb38656f\".\"AO_TPC_TRADES\" TO uny_role_user_app;\
    \  GRANT DELETE, SELECT, INSERT, UPDATE ON TABLE \"12bb8fb6_ba3d_4615_9b0b_f4f5cb38656f\"\
    .\"AO_TPC_TRADES\" TO uny_role_user_ff;  GRANT SELECT ON TABLE \"12bb8fb6_ba3d_4615_9b0b_f4f5cb38656f\"\
    .\"AO_TPC_TRADES\" TO uny_role_user_ro;  CREATE UNIQUE INDEX IF NOT EXISTS \"\
    idx_unique_AO_TPC_TRADES\"     ON \"12bb8fb6_ba3d_4615_9b0b_f4f5cb38656f\".\"\
    AO_TPC_TRADES\" USING btree     (_id COLLATE pg_catalog.\"default\" ASC NULLS\
    \ LAST)     TABLESPACE pg_default;  CREATE UNIQUE INDEX IF NOT EXISTS idx_unique_sec_account_number\
    \     ON \"12bb8fb6_ba3d_4615_9b0b_f4f5cb38656f\".\"AO_TPC_CLIENT_ACCOUNT_MAPPING\"\
    \ USING btree     (sec_account_number ASC NULLS LAST)     TABLESPACE pg_default;"
  scriptEnabled: true
  retention:
    retentionScript: "SET SCHEMA '12bb8fb6_ba3d_4615_9b0b_f4f5cb38656f';\r\n\r\nREFRESH\
      \ MATERIALIZED VIEW CONCURRENTLY \"12bb8fb6_ba3d_4615_9b0b_f4f5cb38656f\".\"\
      AO_TPC_CLIENT_ACCOUNT_MAPPING\";"
    housekeepingSchedule: "0 */15 * * * *"
    retentionEnabled: true
  sourceBatchTopicSuffix: "-batch"
  targetBatchTopicSuffix: "-batch"
  ddlAllowed: true
externalDataSourceJdbcConfigs: {}
