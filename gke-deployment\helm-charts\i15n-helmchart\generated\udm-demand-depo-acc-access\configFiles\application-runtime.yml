---
spring:
  datasource:
    url: "********************************/${DB_NAME}"
    username: "${DB_USER}"
    password: "${DB_PASSWORD}"
  jpa:
    show-sql: false
    generate-ddl: true
    properties:
      hibernate:
        dialect: "org.hibernate.dialect.PostgreSQLDialect"
        hbm2ddl:
          auto: "none"
  application:
    name: "udm-demand-depo-acc-access"
kafkaConfig:
  auto.offset.reset: "latest"
  bootstrap.servers: "kafka-0.kafka-internal.ns-core-prod-apps.svc.cluster.local:9093,kafka-1.kafka-internal.ns-core-prod-apps.svc.cluster.local:9093,kafka-2.kafka-internal.ns-core-prod-apps.svc.cluster.local:9093"
  group.id: "${KAFKA_CONSUMER_GROUP}"
  schema.registry.url: "https://hkl20077608.hc.cloud.hk.hsbc:8082/"
  security.protocol: "SSL"
  ssl.keystore.location: "/hss/apps/certs/unity-microservices.jks"
  ssl.keystore.password: "${KAFKA_KEYSTORE_PASSWORD}"
  ssl.truststore.location: "/hss/apps/certs/unity-microservices.ts"
  ssl.truststore.password: "${KAFKA_TRUSTSTORE_PASSWORD}"
management:
  endpoints:
    web:
      exposure:
        include: "health,prometheus"
  metrics:
    tags:
      application: "${spring.application.name}"
cache:
  provider: "memory"
projectId: "12bb8fb6-ba3d-4615-9b0b-f4f5cb38656f"
accesses:
- persistenceEnabled: true
  sourceTopic: "unity2-PROD-core-transform-udm-demand-depo-acc-out"
  tableName: "udm-demand-depo-acc"
  columns:
  - name: "_trace_id"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "_id"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "created_by"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "created_time_stamp"
    dataType: "timestamp"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "deleted_by"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "deleted_time_stamp"
    dataType: "timestamp"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "updated_by"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "updated_time_stamp"
    dataType: "timestamp"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "_batch_id"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "SITE"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "DFACB"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "DFACS"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "DFACX"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "DFDCB"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "DFDCG"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "DFDCS"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "DFMCB"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "DFMCS"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "DFMCX"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "DFAC2N"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "DFACAI"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "DFACSN"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "DFADRQ"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "DFAPTY"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "DFATMC"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "DFATTC"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "DFBDUP"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "DFBIAB"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "DFBIAS"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "DFBIAX"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "DFBICT"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "DFBIOI"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "DFBLCD"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "DFBLME"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "DFBSCH"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "DFCABS"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "DFCBBH"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "DFCBBK"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "DFCBCI"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "DFCBCL"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "DFCBST"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "DFCCIN"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "DFCGEX"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "DFCGID"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "DFCLBS"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "DFCLVN"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "DFCQDP"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "DFCQID"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "DFCQRC"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "DFCQTP"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "DFCRGR"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "DFCTCD"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "DFCTRS"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "DFCUSR"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "DFCYCD"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "DFDFBT"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "DFDLPU"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "DFDLTN"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "DFDLUP"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "DFDPCD"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "DFDSEX"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "DFDTAO"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "DFDTAP"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "DFDTSS"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "DFDTZB"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "DFDYID"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "DFEBAI"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "DFFDXM"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "DFGABS"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "DFGAT1"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "DFGAT2"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "DFGHCL"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "DFGLBS"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "DFGMAB"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "DFGMCI"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "DFISTR"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "DFLANG"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "DFLCRA"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "DFLLBL"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "DFLLID"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "DFLSRA"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "DFMDFL"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "DFMKP1"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "DFMKP2"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "DFMKP3"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "DFMKS1"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "DFMKS2"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "DFMKS3"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "DFMNSK"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "DFMTCD"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "DFNATY"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "DFNTPD"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "DFPBBL"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "DFPBLN"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "DFPBPN"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "DFPLAC"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "DFRALL"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "DFRCLS"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "DFRCRC"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "DFRCRT"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "DFRDDG"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "DFRDRC"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "DFRDRT"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "DFREQN"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "DFRLBL"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "DFRSTR"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "DFRTCQ"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "DFSAOG"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "DFSCCM"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "DFSTUS"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "DFTAEX"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "DFTAMF"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "DFTATN"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "DFTCLF"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "DFTDCF"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "DFTLUP"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "DFTRIN"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "DFTTHD"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "DFTTPF"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "DFTUPI"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "DFVLBL"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "DFVLOC"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "DFWSCI"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "DFWTCI"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "tranc_id"
    dataType: "text"
    isIndexed: true
    isMultiple: false
    contentSchema: []
  script: "SET SCHEMA '12bb8fb6_ba3d_4615_9b0b_f4f5cb38656f'; CREATE INDEX IF NOT\
    \ EXISTS \"idx_tranc_id\" ON \"udm-demand-depo-acc\" (\"tranc_id\"); CREATE INDEX\
    \ IF NOT EXISTS \"idx_udm-demand-depo-acc_tranc_id\"     ON \"12bb8fb6_ba3d_4615_9b0b_f4f5cb38656f\"\
    .\"udm-demand-depo-acc\" USING btree     (tranc_id COLLATE pg_catalog.\"default\"\
    \ ASC NULLS LAST)     TABLESPACE pg_default; "
  scriptEnabled: true
  retention:
    retentionEnabled: false
  sourceBatchTopicSuffix: "-batch"
  targetBatchTopicSuffix: "-batch"
  ddlAllowed: true
externalDataSourceJdbcConfigs: {}
