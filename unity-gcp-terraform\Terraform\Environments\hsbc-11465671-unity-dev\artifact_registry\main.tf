provider "google" {
  project = var.project_id
  region  = var.gcp_region
}

module "gcp_artifact_registry_repo" {
  source = "../../../Modules/Artifact_Registry"

  project_id             = var.project_id
  location               = var.location
  repository_id          = var.repository_id
  description            = var.description
  kms_key_name           = var.kms_key_name
  format                 = var.format
  
  gcp_region             = var.gcp_region
  art_reg_reader_members = var.art_reg_reader_members
  art_reg_writer_members = var.art_reg_writer_members
  art_reg_admin_members  = var.art_reg_admin_members
}
