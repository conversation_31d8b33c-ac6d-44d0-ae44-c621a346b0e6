def AGENT = "unity-prod-jenkins-agent" 
pipeline {    
    agent { label env.AGENT_LABEL ?: AGENT }
    
    stages {

        stage ('rolling out the management host mig') { 
	        steps {
                this.echo "executing spinningup instance script"
                catchError (buildResult: 'SUCCESS', stageResult: 'SUCCESS') {
                    this.sh 'chmod 755 ./initial_setup/rolling_mig.sh; ./initial_setup/rolling_mig.sh prod'    
                }
          }
        }
    }
   
    post {
        unsuccessful {
            mail bcc: '',
                body: 'ERROR occured in automatic mgmt host rotation occured. Please inspect relevant Jenkins pipeline. Regards DevOps Team',
                cc: '',
                from: 'unity_cloud_devops@noreply',
                replyTo: '',
                subject: "ERROR in automatic mgmt host rotation notification",
                to: 'piotr.mac<PERSON><PERSON><PERSON>@hsbc.com,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>'
        }       
        cleanup {
            script {
                cleanWs()
            }
        }
    }		

}