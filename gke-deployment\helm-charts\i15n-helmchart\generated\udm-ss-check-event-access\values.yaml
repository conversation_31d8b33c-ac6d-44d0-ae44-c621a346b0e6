---
containerPort: 8080
nameOverride: "udm-ss-check-event-access"
replicaCount: 1
namespace: "ns-core-prod-apps"
autoscaling:
  enabled: true
  minReplicas: 1
  maxReplicas: 8
  targetCPUUtilizationValue: "375m"
podAnnotations:
  prometheus.io/scrape: "true"
  prometheus.io/port: "8080"
  prometheus.io/path: "/actuator/prometheus"
  generated-time: "2025-02-28T08:20:08.*********"
  cluster-autoscaler.kubernetes.io/safe-to-evict: "true"
serviceAccount:
  name: "ns-core-prod-sa"
imagePullSecrets:
- name: "docker-secret"
image:
  repository: "gcr.io/hsbc-********-unity-prod"
  name: "unity2-pipeline-data-persistence"
  prefix: "unity/i15n/"
  tag: "2.8.2"
  pullPolicy: "Always"
args:
- "/bin/sh"
- "-c"
- "/hss/apps/config/startup.sh /pipeline-data-persistence.jar -XX:MaxRAMPercentage=75.0\
  \ -XX:MaxMetaspaceSize=128m"
securityContext:
  allowPrivilegeEscalation: false
  capabilities:
    drop:
    - "ALL"
  readOnlyRootFilesystem: false
  runAsNonRoot: true
  runAsUser: *********
  seccompProfile:
    type: "RuntimeDefault"
resources:
  requests:
    cpu: "100m"
    memory: "500Mi"
  limits:
    cpu: "750m"
    memory: "2000Mi"
livenessProbe:
  httpGet:
    path: "/actuator/health/liveness"
    port: 8080
  initialDelaySeconds: 60
  failureThreshold: 10
  periodSeconds: 15
  timeoutSeconds: 5
readinessProbe:
  httpGet:
    path: "/actuator/health/liveness"
    port: 8080
  initialDelaySeconds: 60
  failureThreshold: 10
  periodSeconds: 15
  timeoutSeconds: 5
hostAliases:
- ip: "**************"
  hostnames:
  - "hkl20146687.hc.cloud.hk.hsbc"
- ip: "*************"
  hostnames:
  - "hkl20112359.hc.cloud.hk.hsbc"
service:
  enabled: false
  type: "ClusterIP"
  port: 80
  targetPort: 8080
volumeMounts:
- name: "certs-volume"
  mountPath: "/hss/apps/certs"
- name: "secrets-volume"
  mountPath: "/hss/apps/secrets"
- name: "config-volume"
  mountPath: "/hss/apps/config"
- name: "tmp-volume"
  mountPath: "/tmp"
- name: "unity2-apps-certs-secret-i15n"
  mountPath: "/hss/apps/secrets/unity2-apps-certs-secret-i15n"
- name: "unity2-core-projection-psql-secret"
  mountPath: "/hss/apps/secrets/unity2-core-projection-psql-secret"
volumes:
- name: "tmp-volume"
  emptyDir: {}
- name: "certs-volume"
  configMap:
    name: "unity2-apps-certs-configmap-i15n"
- name: "secrets-volume"
  secret:
    secretName: "unity2-apps-certs-secret-i15n"
- name: "config-volume"
  configMap:
    defaultMode: 493
    name: "udm-ss-check-event-access"
- name: "unity2-apps-certs-secret-i15n"
  secret:
    secretName: "unity2-apps-certs-secret-i15n"
- name: "unity2-core-projection-psql-secret"
  secret:
    secretName: "unity2-core-projection-psql-secret"
customLabels:
  service-category: "app-runtime"
  pipeline0: "udm-ss-check-event"
  service-type: "ACCESS"
configMapLabels: {}
kafkaAutoscalingConfigs: []
cloudSQLProxy:
  enabled: true
  image:
    repository: "gcr.io/cloud-sql-connectors/cloud-sql-proxy"
    tag: "2.15.0"
    pullPolicy: "Always"
  args:
  - "hsbc-********-unity-prod:asia-east2:core-prod-projection-558431"
  - "--port=3307"
  - "--address=127.0.0.1"
  - "--auto-iam-authn"
  - "--private-ip"
  securityContext:
    allowPrivilegeEscalation: false
    capabilities:
      drop:
      - "ALL"
    readOnlyRootFilesystem: false
    runAsNonRoot: true
    runAsUser: *********
    seccompProfile:
      type: "RuntimeDefault"
appDynamic:
  enabled: false
  image:
    repository: "gcr.io/hsbc-********-unity-prod/unity/i15n/appdynamics/java-agent"
    tag: "21.11.3.33314"
    pullPolicy: "Always"
fluentBit:
  enabled: false
  image:
    repository: "gcr.io/hsbc-********-unity-prod/unity/i15n/fluent-bit"
    tag: "v1.8.7"
    pullPolicy: "Always"
target: "GKE"
envType: "PROD"
supportAttributes:
  namespace: "ns-core-prod-apps"
  deployment: "udm-ss-check-event-access"
  consumerGroup: "core-PROD-udm-ss-check-event"
  cacheGroup: "udm-ss-check-event"
  sourceTopics: "unity2-PROD-core-transform-udm-ss-check-event-out"
  targetTopics: "unity2-PROD-core-access-event-udm-ss-check-event-out"
  serviceVersion: "2.8.2"
  minReplicas: "1"
  maxReplicas: "8"
  autoScalingType: "HORIZONTAL_POD_AUTOSCALING"
  lagFactor: "10000"
  kafkaBootstrapServer: "kafka-0.kafka-internal.ns-core-prod-apps.svc.cluster.local:9093,kafka-1.kafka-internal.ns-core-prod-apps.svc.cluster.local:9093,kafka-2.kafka-internal.ns-core-prod-apps.svc.cluster.local:9093"
  pipelineName: "udm-ss-check-event"
  pipelineType: "PIPELINE"
  sourceBatchTopicSuffix: "-batch"
  targetBatchTopicSuffix: "-batch"
  tableName: "udm-ss-check-event"
  isPersistenceEnabled: "true"
  componentType: "ACCESS"
additionalContainers: []
env:
- name: "SPRING_PROFILES_ACTIVE"
  value: "secret,runtime,infra"
- name: "ENV"
  value: "PROD"
- name: "TARGET"
  value: "GKE"
- name: "KAFKA_CONSUMER_GROUP"
  value: "core-PROD-udm-ss-check-event"
- name: "PROJECT"
  value: "core"
- name: "DEFAULT_UNITY2_SCHEMA"
  value: "12bb8fb6_ba3d_4615_9b0b_f4f5cb38656f"
- name: "PIPELINE"
  value: "udm-ss-check-event"
- name: "CLOUDSQL_ENDPOINT"
  value: ""
