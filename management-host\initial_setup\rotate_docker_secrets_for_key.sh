#!/usr/bin/env bash
# Script to rotate workload idnetitiy in GKE after SA keys rotation
# Setup workload identity with keys from ServiceAccountKeys location

function print_usage {
  echo "Usage:$(basename $0)"
  echo ' --project_id             '
  echo ' --path_folder_keys       '
  echo ' --service_account        '
  echo ' --secret_name            '
  echo
  echo "Example:$(basename $0)  --project_id=hsbc-********-unyeu1-dev --path_folder_keys=/home/<USER>/ServiceAccountKeys/ --service_account=runtime-gke --secret_name=docker-secret "
}


# Initiate params
function options() {
    while [ "$1" != "" ];
    do
    _param_=`printf "%s\n" $1 | awk -F= '{print $1}'`
    _value_=`printf "%s\n" $1 | sed 's/^[^=]*=//g'`
    if [[ $_value_ == $_param_ ]]; then
        shift
        _value_=$1
    fi
    case ${_param_} in
    --project_id)
        PROJECT_ID="${_value_}"
        ;;
    --path_folder_keys)
        PATH_FOLDER_KEYS="${_value_}"
        ;;
    --service_account)
        SERVICE_ACCOUNT="${_value_}"
        ;;
    --secret_name)
        SECRET_NAME="${_value_}"
        ;;
    --help)
        print_usage
        exit
        ;;
    esac
    shift;
    done
}

# Function to ensure service account is in full format
function ensure_full_format() {
    local _project_id=${1}
    local _service_account=${2}
    
    # Construct the domain using the project name
    local _project_domain="@${_project_id}.iam.gserviceaccount.com"

    # Check if the service account ends with the correct domain
    if [[ ${_service_account} != *@*.iam.gserviceaccount.com ]]; then
         _service_account="${_service_account}"@"${_project_domain}"
    fi
    echo "${_service_account}"
}

################################################
# MAIN
################################################

options "$@"

SECRET_NAMESPACES=$(kubectl get secret --all-namespaces --field-selector type=kubernetes.io/dockerconfigjson -o=jsonpath='{range .items[*]}{.metadata.namespace} {"\n"}{end}')

GKE_KEY_MAIL=$(ensure_full_format "${PROJECT_ID}" "${SERVICE_ACCOUNT}")

for ns in ${SECRET_NAMESPACES}
do
     echo "Deleting secret for namespace: ${ns}"
     kubectl delete secret -n $ns $SECRET_NAME 
    
     echo "Rotating secret for namespace: ${ns}"
     kubectl create secret docker-registry -n ${ns} $SECRET_NAME  -v3 \
      --docker-server=gcr.io \
      --docker-username=_json_key \
      --docker-password="$(cat ${PATH_FOLDER_KEYS}${SERVICE_ACCOUNT}@${PROJECT_ID}.json)" \
      --docker-email=${GKE_KEY_MAIL}
done




