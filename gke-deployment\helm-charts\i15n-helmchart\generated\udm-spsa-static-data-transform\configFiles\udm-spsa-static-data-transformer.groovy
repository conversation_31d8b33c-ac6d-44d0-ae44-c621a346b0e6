import com.poc.hss.fasttrack.transform.AbstractTransformer
import com.poc.hss.fasttrack.transform.model.TransformerBatchInputContext
import com.poc.hss.fasttrack.transform.model.TransformerRecord
import io.vertx.core.json.JsonObject
import org.slf4j.Logger
import org.slf4j.LoggerFactory

import java.util.stream.Collectors

class SpsaStaticDataTransformer extends AbstractTransformer {
    JsonObject constructRecord(JsonObject source){
        return new JsonObject()
                .put("site", source.getString("SITE")?.trim())
                .put("SDCTCD", source.getString("SDCTCD")?.trim())
                .put("SDGMAB", source.getString("SDGMAB")?.trim())
                .put("SDACB", source.getString("SDACB")?.trim())
                .put("SDACS", source.getString("SDACS")?.trim())
                .put("SDACX", source.getString("SDACX")?.trim())
                .put("SDCPYC", source.getString("SDCPYC")?.trim())
                .put("SDSDLC", source.getString("SDSDLC")?.trim())
                .put("SDIVES", source.getString("SDIVES")?.trim())
                .put("SDATTI", source.getString("SDATTI")?.trim())
    }

    private static final Logger logger = LoggerFactory.getLogger(SpsaStaticDataTransformer.class)

    @Override
    List<TransformerRecord> batchTransform(TransformerBatchInputContext context) {
        List<TransformerRecord> records = context.getRecords()
        return records.stream().map({
            rec ->
                JsonObject source = rec.getData();
                return rec.from(rec).data(constructRecord(source)).build()
        }).collect(Collectors.toList())
    }
}