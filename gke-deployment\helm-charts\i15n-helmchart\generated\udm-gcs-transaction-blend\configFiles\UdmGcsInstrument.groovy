import com.poc.hss.fasttrack.service.LookupService
import com.poc.hss.fasttrack.transform.model.LookupRequest
import com.poc.hss.fasttrack.transform.model.TransformerRecord
import io.vertx.core.json.JsonObject
import org.slf4j.Logger
import org.slf4j.LoggerFactory

import java.util.function.BiConsumer
import java.util.stream.Collectors

class UdmGcsInstrument {

    private static final Logger logger = LoggerFactory.getLogger(UdmGcsInstrument.class);

    static BiConsumer<List<TransformerRecord>, LookupService> queryAndBendInstrumentData = (targetData, lookupService) -> {
        logger.info("Start Blending Instrument data")
        String instrumentIds = targetData.stream()
                .filter(record -> record.getData().getString(UdmGcsTransactionConstant.INSTRUMENT_ID) != null)
                .map(record -> record.getData().getString(UdmGcsTransactionConstant.INSTRUMENT_ID))
                .map(id -> "'" + id + "'")
                .distinct()
                .collect(Collectors.joining(","))
        if (instrumentIds.isEmpty()) {
            logger.info("Skipping instrument lookup request as instrument ids are empty or respective trade records are not exist.")
            return
        }

        String criteria = String.format(UdmGcsTransactionConstant._ID + " in (%s)", instrumentIds)
        logger.debug("instrument lookup request criteria [" + criteria + "]")

        LookupRequest instrumentLookupRequest = UdmGcsTransactionHelper.createLookupRequest(criteria, UdmGcsTransactionConstant.GCS_INSTRUMENT, UdmGcsTransactionConstant.INSTRUMENT_FIELDS)
        Map<String, JsonObject> instrumentMap = lookupService.queryList(instrumentLookupRequest).stream()
                .collect(Collectors.toMap(json -> json.getString(UdmGcsTransactionConstant._ID), json -> json, (a, b) -> a))
        if (null == instrumentMap || instrumentMap.isEmpty()) {
            logger.debug("instrument lookup request failed or no record for Criteria : " + criteria)
            return
        }
        logger.debug("instrument lookup request result for Criteria " + criteria + " is \n" + instrumentMap.toString())
        blendInstrument.accept(targetData, instrumentMap)

    }

    static BiConsumer<List<TransformerRecord>, Map<String, JsonObject>> blendInstrument = (targetData, instrumentMap) -> {
        targetData.forEach(record -> {
            String instrumentId = record.getData().getString(UdmGcsTransactionConstant.INSTRUMENT_ID)
            if (instrumentMap.containsKey(instrumentId)) {
                JsonObject instrument = instrumentMap.get(instrumentId)
                instrumentDataMapping(instrument, record.getData())
            }
        })
    }

    static void instrumentDataMapping(JsonObject instrument, JsonObject output) {
        output.put("instrument_description", instrument.getString("description"))
        output.put("quantity_unit", instrument.getString("qty_unit"))
        output.put("isin", instrument.getString("isin"))
        output.put("sedol", instrument.getString("sedol"))
        output.put("cusip", instrument.getString("cusip"))
        output.put("registrar_id", instrument.getString("registrar_id"))
        output.put("registrar_desc", instrument.getString("registrar_desc"))
		output.put("instrument_type", instrument.getString("type"))
        output.put("instrument_currency", instrument.getString("ccy"))
		output.put("maturity_date", instrument.getString("maturity_date"))
    }
}
