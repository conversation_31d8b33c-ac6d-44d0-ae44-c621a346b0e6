<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HSBC BBD Change Dashboard</title>
    <link rel="stylesheet" href="css/dashboard.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/date-fns@2.29.3/index.min.js"></script>
</head>
<body>
    <div class="dashboard-container">
        <!-- Header -->
        <header class="dashboard-header">
            <div class="header-content">
                <h1>HSBC BBD Change Dashboard</h1>
                <div class="header-stats">
                    <div class="stat-item">
                        <span class="stat-label">Total Changes</span>
                        <span class="stat-value" id="totalChanges">-</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">Last Updated</span>
                        <span class="stat-value" id="lastUpdated">-</span>
                    </div>
                </div>
            </div>
        </header>

        <!-- Charts Section -->
        <section class="charts-section">
            <div class="charts-container">
                <div class="chart-card">
                    <h3>Changes by Scheduled Date</h3>
                    <canvas id="dateChart"></canvas>
                </div>
                <div class="chart-card">
                    <h3>Changes by Status</h3>
                    <canvas id="statusChart"></canvas>
                </div>
            </div>
        </section>

        <!-- Filters Section -->
        <section class="filters-section">
            <div class="filters-container">
                <div class="filter-group">
                    <label for="statusFilter">Status:</label>
                    <select id="statusFilter">
                        <option value="">All Statuses</option>
                    </select>
                </div>
                <div class="filter-group">
                    <label for="groupFilter">Owning Group:</label>
                    <select id="groupFilter">
                        <option value="">All Groups</option>
                    </select>
                </div>
                <div class="filter-group">
                    <label for="searchFilter">Search:</label>
                    <input type="text" id="searchFilter" placeholder="Search changes...">
                </div>
                <button id="clearFilters" class="clear-btn">Clear Filters</button>
            </div>
        </section>

        <!-- Loading Indicator -->
        <div id="loadingIndicator" class="loading-indicator">
            <div class="spinner"></div>
            <p>Loading change data...</p>
        </div>

        <!-- Error Message -->
        <div id="errorMessage" class="error-message" style="display: none;">
            <p>Failed to load change data. Please try again later.</p>
            <button id="retryBtn" class="retry-btn">Retry</button>
        </div>

        <!-- Changes Table -->
        <section class="table-section">
            <div class="table-container">
                <table id="changesTable" class="changes-table">
                    <thead>
                        <tr>
                            <th class="sortable" data-column="assigneeName">Assignee <span class="sort-indicator"></span></th>
                            <th class="sortable" data-column="creatorName">Requestor <span class="sort-indicator"></span></th>
                            <th class="sortable" data-column="snCrNumber">CR Number <span class="sort-indicator"></span></th>
                            <th class="sortable" data-column="gsdTitle">Title <span class="sort-indicator"></span></th>
                            <th class="sortable" data-column="gsdStatus">Status <span class="sort-indicator"></span></th>
                            <th class="sortable" data-column="gsdChangeType">Type <span class="sort-indicator"></span></th>
                            <th class="sortable" data-column="gsdOwningGroup">Owning Group <span class="sort-indicator"></span></th>
                            <th class="sortable" data-column="gsdScheduledStartDate">Scheduled Start <span class="sort-indicator"></span></th>
                            <th class="sortable" data-column="gsdScheduledEndDate">Scheduled End <span class="sort-indicator"></span></th>
                            <th class="sortable" data-column="jiraId">JIRA ID <span class="sort-indicator"></span></th>
                        </tr>
                    </thead>
                    <tbody id="changesTableBody">
                        <!-- Table rows will be populated by JavaScript -->
                    </tbody>
                </table>
            </div>
        </section>
    </div>

    <script src="js/dashboard.js"></script>
</body>
</html>
