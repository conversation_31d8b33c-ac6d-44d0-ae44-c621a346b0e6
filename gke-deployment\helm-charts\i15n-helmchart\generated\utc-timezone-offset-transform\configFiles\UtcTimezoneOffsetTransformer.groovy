import com.poc.hss.fasttrack.transform.AbstractTransformer
import com.poc.hss.fasttrack.transform.model.TransformerBatchInputContext
import com.poc.hss.fasttrack.transform.model.TransformerRecord
import io.vertx.core.json.JsonObject
import org.slf4j.Logger
import org.slf4j.LoggerFactory

import java.util.stream.Collectors

class UtcTimezoneOffsetTransformer extends AbstractTransformer {
    JsonObject constructRecord(JsonObject source){
        new JsonObject()
            .put("_id", String.format("%s-%s-%s", source.getString("SITE","").trim(), source.getString("TOCTCD","").trim(), source.getString("TOGMAB","").trim()))
            .put("SITE", source.getString("SITE","").trim())
            .put("HJURAJ", source.getString("HJURAJ","").trim())
            .put("TOCTCD", source.getString("TOCTCD","").trim())
            .put("TOGMAB", source.getString("TOGMAB","").trim())
            .put("TOTMZA", source.getString("TOTMZA","").trim())
            .put("TOTMZL", source.getString("TOTMZL","").trim())
            .put("TOTZOS", source.getString("TOTZOS","").trim())
            .put("HSTZOST_ID", source.getString("HSTZOST_ID","").trim())
	}

    private static final Logger logger = LoggerFactory.getLogger(UtcTimezoneOffsetTransformer.class)
    @Override
    List<TransformerRecord> batchTransform(TransformerBatchInputContext context) {
        List<TransformerRecord> records = context.getRecords()
        return records.stream().map({
            rec ->
                JsonObject source = rec.getData();
                return rec.from(rec).data(constructRecord(source)).build()
        }).collect(Collectors.toList())
    }
}
