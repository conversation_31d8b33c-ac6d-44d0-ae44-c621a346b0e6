
#!/usr/bin/env bash
set -euo pipefail

echo "<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<< JENKINS >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>"
sudo mkdir -p $JENKINS_PATH/build_tools
sudo mkdir -p $JENKINS_PATH/builds
sudo mkdir -p $JENKINS_PATH/builds/caches
sudo mkdir -p $JENKINS_PATH/builds/remoting
sudo mkdir -p $JENKINS_PATH/builds/support
sudo mkdir -p $JENKINS_PATH/builds/workspace
sudo mkdir -p $JENKINS_PATH/build_tools/git
echo "[MGMT_HOST] created home directory for jenkins and git under build_tools"

if id "jenbld" >/dev/null 2>&1; then
    echo "[MGMT_HOST] jenbld user exists"
else
    sudo useradd -u 865 jenbld
    echo "[MGMT_HOST] jenbld user does not exist, created jenbld user"
fi

#update PATH for jenbld agent user
echo "[MGMT_HOST] change ownership on custom cloud sdk: /opt/google"
sudo chown -R jenbld:jenbld /opt/google
sudo bash -c "if [ ! $(grep PATH=/opt/google/google-cloud-sdk/bin /home/<USER>/.bash_profile) ]; then sed -i 's|PATH=|PATH=/opt/google/google-cloud-sdk/bin:|g' /home/<USER>/.bash_profile; fi"

#sudo usermod -aG root jenbld
sudo chown -R root:root $JENKINS_PATH
sudo chmod -R 777 $JENKINS_PATH
echo "[MGMT_HOST] added jenbld to root group & given permission to jenkins path"

echo "[MGMT_HOST] changing directory to install git"
sudo chmod -R 755 $JENKINS_PATH/build_tools
cd $BUILD_PATH/build_tools
wget --user=$NEXUS_CREDS_USR --password=$NEXUS_CREDS_PWD "https://gbmt-nexus.prd.fx.gbm.cloud.uk.hsbc/repository/gbm-tools-yum/7Server/x86_64/${GIT_VERSION}"
sudo yum install -y ${GIT_VERSION}
cd $JENKINS_PATH/build_tools/git
echo $(pwd)
mkdir -p 2.17.1/bin
sudo chmod 755 -R 2.17.1

#
#sudo tar -xzf $GIT_VERSION
#sudo rm $GIT_VERSION
#sudo chmod 755 -R 2.17.1
echo "[MGMT_HOST] installed git under build tool in $(pwd)"
echo $(ls -ltr)
sudo ln -s /usr/local/bin/git /usr/bin
sudo ln -s /usr/local/bin/git $JENKINS_PATH/build_tools/git/2.17.1/bin/git
sudo ln -s $JENKINS_PATH/build_tools /build_tools
#sudo ln -s /build_tools/git/2.17.1/bin/git /usr/bin
echo "[MGMT_HOST] linked jenkins to /build_tools and git to /usr/bin"

sudo git --version
echo "[MGMT_HOST] git version $(git --version) and which git: $(which git)"

echo "Setup key for jenbld user"
mkdir -p /home/<USER>/.ssh
sudo chmod -R 600 /home/<USER>/.ssh
cp /root/.ssh/id_rsa /home/<USER>/.ssh/id_rsa
cp /root/.ssh/id_rsa.pub /home/<USER>/.ssh/id_rsa.pub
cp /root/.ssh/id_rsa.pub /home/<USER>/.ssh/known_hosts
sudo chown jenbld:jenbld /home/<USER>/.ssh/
sudo chown jenbld:jenbld /home/<USER>/.ssh/id_rsa
sudo chown jenbld:jenbld /home/<USER>/.ssh/id_rsa.pub
sudo chmod 750 /home/<USER>/.ssh/
sudo chmod 600 /home/<USER>/.ssh/id_rsa
sudo chmod 644 /home/<USER>/.ssh/id_rsa.pub
sudo chmod 644 /home/<USER>/.ssh/known_hosts
echo "completed"

##echo "[MGMT_HOST] started cloning git repo for connecting agent to master "
#cd $JENKINS_PATH
#echo "changed path to jenkins_agent"

#git -c "http.extraHeader=Authorization: Bearer $STASH_TOKEN" clone https://stash.hk.hsbc/scm/uoci/hsbc-9087302-jenkins-agent-service-prod.git
GIT_SSH_COMMAND="ssh -o UserKnownHostsFile=/dev/null -o StrictHostKeyChecking=no" git clone ssh://*****************:8203/uoci/hsbc-9087302-jenkins-agent-service-prod.git


git config --global user.email "<EMAIL>" 
git config --global user.name "UNITY_CLOUD_DEVOPS"

sudo chmod -R 775 hsbc-9087302-jenkins-agent-service-prod

echo "permissions updated"
sudo mv hsbc-9087302-jenkins-agent-service-prod agent_service
cd agent_service
ls -ltr
sudo cp -rf . /opt/jenkins_agent/agent_service
cd unity-prod-jenkins-agent
sudo cp -rf . /opt/jenkins_agent/agent_service
chown -R jenbld:jenbld /opt/jenkins_agent/agent_service/
chmod -R 755 /opt/jenkins_agent/agent_service/
cd ../
ls -ltr
echo $(pwd)
echo "[MGMT_HOST] cloned agent repo to opt jenkins_agent"
ls -ltr
echo "[MGMT] jenkins agent setup was completed"

echo "[MGMT_HOST] copy terraformrc file and give it propper permission"
#COPY .terraformrc to home/jenbld/ catalogue
#Change privilages to 755 so user can access them
sudo cp /root/.terraformrc /home/<USER>/.terraformrc
sudo chown jenbld:jenbld /home/<USER>/.terraformrc
sudo chmod 755 /home/<USER>/.terraformrc

sudo chmod -R 777 /home/<USER>/
sudo chmod -R 777 $TERRAFORM_PATH/

chown  jenbld:jenbld /opt/terraform -R
chown  jenbld:jenbld /home/<USER>

echo "[MGMT_HOST] terraformrc copied"
