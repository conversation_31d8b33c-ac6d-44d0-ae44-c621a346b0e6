#/bin/bash
#configuration schemas
#@/home/<USER>/ServiceAccountKeys/jenkins/jenkins-agent.yaml
#jenkins:
#  url: https://alm-jenkins216.hc.cloud.uk.hsbc:8706
#  agent: unity-dev-jenkins-agent
#  user_name: TEST-ADM-OTCUBLD
#  api_token: <MASKED>

function check_metadata_server_connectivity() {
  echo "Checking Metadata Server Connectivity"
  _hc_=$(curl -s -o /dev/null -w "%{http_code}" -H "Metadata-Flavor: Google" "http://metadata.google.internal/computeMetadata/v1/instance/attributes/")
  _retries_=0
  while [[ ${_hc_} -ne 200 && ${_retries_} -lt 120 ]]; do 
    _hc_=$(curl -s -o /dev/null -w "%{http_code}" -H "Metadata-Flavor: Google" "http://metadata.google.internal/computeMetadata/v1/instance/attributes/")
    _retries_=$((_retries++))
    echo "Metadata Server Connectivity: Failed[${_retries_}]"
    sleep 1 
  done 
  if [[  ${_hc_} -eq 200 ]] ; then  
    echo "Metadata Server Connectivity: Connected"
  fi
}

function get_jenkins_agent_name(){
  check_metadata_server_connectivity
  if [[ $(curl -s -H "Metadata-Flavor: Google" "http://metadata.google.internal/computeMetadata/v1/instance/attributes/" | grep jenkins_agent_name) ]]; then 
    _jenkins_agent_name_=$(curl -s -H "Metadata-Flavor: Google" "http://metadata.google.internal/computeMetadata/v1/instance/attributes/jenkins_agent_name")
  fi 
}

function constructor() {
  get_jenkins_agent_name
  echo _jenkins_agent_name_:${_jenkins_agent_name_}
  if [[ -f "/home/<USER>/ServiceAccountKeys/jenkins/${_jenkins_agent_name_}.jenkins-agent.yaml" ]]; then 
    _config_file_=/home/<USER>/ServiceAccountKeys/jenkins/${_jenkins_agent_name_}.jenkins-agent.yaml
  else
    _config_file_=/home/<USER>/ServiceAccountKeys/jenkins/jenkins-agent.yaml
  fi 
  _check_inteval_=60
  _startup_period_=300
}  

function get_config() {
  if [[ -f ${_config_file_} ]]; then     
    yq e '.' ${_config_file_} | egrep -vi "token|password"
    _url_=$(yq e '.jenkins.url' ${_config_file_})
    _agent_=$(yq e '.jenkins.agent' ${_config_file_})
    _user_name_=$(yq e '.jenkins.user_name' ${_config_file_})
    _api_token_=$(yq e '.jenkins.api_token' ${_config_file_})
  fi
}

function get_jenkins_age() {
  _start_time_=$(systemctl show -p ActiveEnterTimestamp jenkinsagent|cut -f2 -d=)
  _start_seconds_=$(date --date="$_start_time_" +%s)
  _now_seconds_=$(date +%s)
  _age_seconds_=$(($_now_seconds_ - _start_seconds_))
}

function restart_agent_status() {
  get_config
  _response_=$(curl -s -u ${_user_name_}:${_api_token_} ${_url_}/computer/${_agent_}/api/json?tree=displayName,offline)
  echo ${_response_} | jq '{displayName,offline}'  
  _agent_status_=$(echo ${_response_} | jq '.offline')   
  if [[ "${_agent_status_}" != "false" ]]; then     
    echo restarting jenkinsagent.service 
    get_jenkins_age
    echo Jenkins started at ${_start_time_} : ${_age_seconds_}s
    if [[ ${_age_seconds_} -gt ${_startup_period_} ]]; then 
      systemctl restart jenkinsagent.service
      systemctl status jenkinsagent.service
    fi
  fi
}

#######################################################################
# MAIN
#######################################################################
constructor
while [ 1 = 1 ]; do 
  restart_agent_status
  sleep ${_check_inteval_}
done  
