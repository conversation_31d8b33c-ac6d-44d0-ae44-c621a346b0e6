---
MM_CUSTODY_CLIENT_API_KEY: "${unity2-apps-certs-secret-i15n.MM_CUSTODY_CLIENT_API_KEY}"
MM_CUSTODY_CLIENT_FEATURE_TRIGGER_API_KEY: "${unity2-apps-certs-secret-i15n.MM_CUSTODY_CLIENT_FEATURE_TRIGGER_API_KEY}"
KAFKA_TRUSTSTORE_PASSWORD: "${unity2-apps-certs-secret-i15n.truststorePassword}"
MM_TRADE_PUSH_CLIENT_API_KEY: "${unity2-apps-certs-secret-i15n.MM_TRADE_PUSH_CLIENT_API_KEY}"
MM_AU_REGISTERED_HOLDER_API_KEY: "${unity2-apps-certs-secret-i15n.MM_AU_REGISTERED_HOLDER_API_KEY}"
MM_CUSTODY_SWIFT_REASON_API_KEY: "${unity2-apps-certs-secret-i15n.MM_CUSTODY_SWIFT_REASON_API_KEY}"
KAFKA_KEYSTORE_PASSWORD: "${unity2-apps-certs-secret-i15n.keystorePassword}"
MM_AU_COMPANY_ID_API_KEY: "${unity2-apps-certs-secret-i15n.MM_AU_COMPANY_ID_API_KEY}"
MQ_PASSWORD: "${unity2-apps-certs-secret-i15n.MQ_PASSWORD}"
