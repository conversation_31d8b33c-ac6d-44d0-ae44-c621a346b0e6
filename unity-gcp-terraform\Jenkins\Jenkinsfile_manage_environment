import hudson.model.ParameterDefinition
import hudson.model.JobProperty
import groovy.json.*
import jenkins.model.*
import java.io.*
import java.io.File
import groovy.io.FileType
def AGENT = 'unity-dev-jenkins-agent'

def initEnvironment() {
  script {
          project_target="GKE"

          if (! env.clusterProject) { 
            //default to hsbc-9087302-unity-dev for backward compatibilities
            echo "Runing backward compatibility mode: fetching configuration for default project hsbc-9087302-unity-dev"
            clusterProject = "hsbc-9087302-unity-dev" 
            authMethod = "IAM-DIRECT"           
          }
            
          static final String C_MINUTES = 'MINUTES'
          static final boolean C_TRUE = true
          static final boolean C_FALSE = false
          static final String C_SUCCESS = 'SUCCESS'
          static final String C_FAILURE = 'FAILURE'
          static final String C_NULL = null
          static final String C_YES = 'Yes'
          static final String C_NO = 'No'
          static final String C_CONFIG_JSON_FILE_PATH = './Jenkins/Configs/hsbc-gcp-config.json'
          static final String C_GROOVY_METHOD_FILE_PATH = "./Jenkins/Methods/JenkinsMethods.groovy"
          static final String c_workspace_path = "${env.WORKSPACE}"
          static final C_THIS = this       
          jenkinsMethodHelper = load("${env.WORKSPACE}/${C_GROOVY_METHOD_FILE_PATH}")
          jenkinsMethodHelper.v_configJSONObject = jenkinsMethodHelper.createFileJSONObject(C_THIS, C_CONFIG_JSON_FILE_PATH, C_TRUE)
          jenkinsMethodHelper.initializeEnvironment(v_envFolder="${clusterProject}")
          
          clusterName = jenkinsMethodHelper.v_clusterName
          clusterProject = jenkinsMethodHelper.v_clusterProject
          clusterRegion = jenkinsMethodHelper.v_clusterRegion
          kubectlProxy = jenkinsMethodHelper.v_kubectlProxy
          serviceAccountKey = jenkinsMethodHelper.v_salocation
          clusterLocation = kubectlProxy.split(":")[0].split("\\.")[-2]

          this.sh """                  
                  cat > ${env.WORKSPACE}/.${project_target}.${clusterProject}.${clusterRegion}.${clusterName}.env <<-'EOT'
                  function authenticate() {			    
                    case ${authMethod} in 
                    IAM-DIRECT)
                       unset HTTPS_PROXY
                       gcloud container clusters get-credentials ${clusterName} --region=${clusterRegion} --project=${clusterProject}	
                  	   export HTTPS_PROXY=${kubectlProxy}
                  	;;
                    KUBECONFIG)
                       echo TODO
                  	 case ${project_target} in 
                  	 KOPS)
                  	   unset HTTPS_PROXY
                       export KUBECONFIG=./jenkins-admin.hk-kops-u2kopd01;		   
                  	   ;;
                  	 GKE)
                  	   export KUBECONFIG=${KUBECONFIG}
                  	   export HTTPS_PROXY=${kubectlProxy}				   
                        ;;			   
                  	 esac
                      ;;				 
                    esac
                 }
                 
                 function gcloud_authenticate() {
                   unset HTTPS_PROXY
                   gcp_project=\$(gcloud config list --format 'value(core.project)')
                   if [[  "\${gcp_project}" != "${clusterProject}" ]]; then 
                     export CLOUDSDK_CONFIG=\$HOME/.cloudsdk/${clusterProject}
                     project_env=\$(echo ${clusterProject} | rev | cut -f1 -d- | rev)        
                     echo proxy:googleapis-\${project_env}.gcp.cloud.hk.hsbc
                     gcloud config set proxy/address googleapis-\${project_env}.gcp.cloud.hk.hsbc
                     gcloud config set proxy/port 3128
                     gcloud config set proxy/type http_no_tunnel
                     gcloud auth activate-service-account --key-file=${serviceAccountKey}
                     gcloud config set project ${clusterProject}                     
                   fi 
                   gcloud config list
                 }

EOT
                  cat ${env.WORKSPACE}/.${project_target}.${clusterProject}.${clusterRegion}.${clusterName}.env
                  """
        }      
} 

pipeline {    
    parameters {
        choice(name: 'operation', choices: ['startup', 'shutdown'], description: 'environment operation')        
    }
    agent { label env.AGENT_LABEL ?: AGENT }
    stages {
      stage("Initialize Environment Variables") {
          options {
              timeout(time: 5, unit: "MINUTES")
          }
          steps {
              script {
                 initEnvironment()
              }
          }
      }        
      stage ('CloudSQL Startup') { 
        when {
          expression { params.operation == 'startup' }
        }
        steps {
          script {
            echo """execute script"""
            if (params.namespace != null) {
              if ( ! namespace.isEmpty() ){
               sh("""
                  source ${env.WORKSPACE}/.${project_target}.${clusterProject}.${clusterRegion}.${clusterName}.env
                  gcloud_authenticate
                  authenticate
                  chmod 755 ./Scripts/bash/scale-cloudsql.sh; 
                  ./Scripts/bash/scale-cloudsql.sh --operation=startup --namespace=${params.namespace} --setup-kubeconfig=0
                  """)              
              } 
            }       
          }
        }        
      }
      stage ('Scale Up Deployment') { 
        when {
          expression { params.operation == 'startup' }
        }
        steps {
          script {
            echo """execute script"""
            if (params.namespace != null) {
              if ( ! namespace.isEmpty() ){
               sh("""
                  source ${env.WORKSPACE}/.${project_target}.${clusterProject}.${clusterRegion}.${clusterName}.env
                  gcloud_authenticate
                  authenticate               
                  chmod 755 ./Scripts/bash/scale-deployment.sh; 
                  ./Scripts/bash/scale-deployment.sh --replicas=1 --namespace=${params.namespace} --setup-kubeconfig=0
                  """)              
              } 
            }       
          }
        }        
      }
      stage ('Scale Down Deployment') { 
        when {
          expression { params.operation == 'shutdown' }
        }
        steps {
          script {
            echo """execute script"""
            if (params.namespace != null) {
              if ( ! namespace.isEmpty() ){
               sh("""
                  source ${env.WORKSPACE}/.${project_target}.${clusterProject}.${clusterRegion}.${clusterName}.env
                  gcloud_authenticate
                  authenticate
                  chmod 755 ./Scripts/bash/scale-deployment.sh; 
                  ./Scripts/bash/scale-deployment.sh --replicas=0 --namespace=${params.namespace} --setup-kubeconfig=0
                  """)              
              } 
            }       
          }
        }        
      }
      stage ('CloudSQL Shutdown') { 
        when {
          expression { params.operation == 'shutdown' }
        }
        steps {
          script {
            echo """execute script"""
            if (params.namespace != null) {
              if ( ! namespace.isEmpty() ){
               sh("""
                  source ${env.WORKSPACE}/.${project_target}.${clusterProject}.${clusterRegion}.${clusterName}.env
                  gcloud_authenticate
                  authenticate               
                  chmod 755 ./Scripts/bash/scale-cloudsql.sh; 
                  ./Scripts/bash/scale-cloudsql.sh --operation=shutdown --namespace=${params.namespace} --setup-kubeconfig=0
                  """)              
              } 
            }       
          }
        }        
      }
      
    }
   
    post {
        unsuccessful {
            mail bcc: '',
                body: "[Jenkins Job]:${env.JOB_NAME}\n[url]:${env.JOB_URL}\n[status]:[failed]\nRegards DevOps Team",
                cc: '',
                from: 'unity_cloud_devops@noreply',
                replyTo: '',
                subject: "[Jenkins][GenericScript][${env.JOB_NAME}][failed] Generic job status notification",
                to: '<EMAIL>,<EMAIL>'
              }
        cleanup {
            script {
                cleanWs()
            }
        }
    }		

}
