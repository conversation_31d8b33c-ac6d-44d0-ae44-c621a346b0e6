#!/usr/bin/env bash
set -euo pipefail

echo "<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<< nexus iq cli >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>"
echo "[MGMT_HOST] starting nexus iq cli  installation"

sudo mkdir -p $NEXUS_IQ_PATH
echo "[MGMT_HOST] created nexus iq directory"

#assigning permissions to terraform user
sudo chown -R root:root $NEXUS_IQ_PATH
sudo chmod -R 777 $NEXUS_IQ_PATH

#downloading nexus iq cli from nexus
cd $BUILD_PATH/build_tools/
echo "[MGMT_HOST] downloading nexus iq cli from nexus"
wget --user=${NEXUS_CREDS_USR} --password=${NEXUS_CREDS_PWD} "https://gbmt-nexus.prd.fx.gbm.cloud.uk.hsbc/repository/maven-release/tools/nexus-iq-cli/${NEXUS_IQ_VERSION}/nexus-iq-cli-${NEXUS_IQ_VERSION}.jar"
sudo mv nexus-iq-cli-${NEXUS_IQ_VERSION}.jar $NEXUS_IQ_PATH
#sudo mv nexus-iq-server-$NEXUS_IQ_VERSION.jar $NEXUS_IQ_PATH
sudo rm -rf nexus-iq-server-$NEXUS_IQ_VERSION.jar
echo "[MGMT_HOST]  moved nexus iq cli to path"
