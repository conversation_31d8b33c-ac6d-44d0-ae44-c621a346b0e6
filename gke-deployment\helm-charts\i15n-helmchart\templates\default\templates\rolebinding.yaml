{{- if .Values.rbac.create -}}
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: {{ include "i15n-helmchart.fullname" . }}
  namespace: {{ .Values.namespace }}
  labels:
    {{- include "i15n-helmchart.labels" . | nindent 4 }}
  {{- with .Values.ingress.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
{{- if (not .Values.rbac.useExistingRole) }}
  name: {{ include "i15n-helmchart.fullname" . }}
{{- else }}
  name: {{ .Values.rbac.useExistingRole }}
{{- end }}
subjects:
- kind: ServiceAccount
  name: {{ include "i15n-helmchart.serviceAccountName" . }}
  namespace: {{ .Values.namespace }}
{{- end -}}
