// HSBC Change Dashboard JavaScript

class ChangeDashboard {
    constructor() {
        this.allChanges = [];
        this.filteredChanges = [];
        this.dateChart = null;
        this.statusChart = null;
        this.groupColors = {};
        this.sortColumn = null;
        this.sortDirection = 'asc';
        this.init();
    }

    async init() {
        this.setupEventListeners();
        await this.loadChangeData();
    }

    setupEventListeners() {
        // Filter event listeners
        document.getElementById('statusFilter').addEventListener('change', () => this.applyFilters());
        document.getElementById('groupFilter').addEventListener('change', () => this.applyFilters());
        document.getElementById('searchFilter').addEventListener('input', () => this.applyFilters());
        document.getElementById('clearFilters').addEventListener('click', () => this.clearFilters());
        document.getElementById('retryBtn').addEventListener('click', () => this.loadChangeData());

        // Sorting event listeners
        document.querySelectorAll('.sortable').forEach(header => {
            header.addEventListener('click', () => {
                const column = header.getAttribute('data-column');
                this.sortTable(column);
            });
        });
    }

    async loadChangeData() {
        try {
            this.showLoading();
            
            const response = await fetch('/api/v1/changes/enriched');
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            
            const result = await response.json();
            
            if (result.status === 'success' && result.data) {
                this.allChanges = result.data;
                this.filteredChanges = [...this.allChanges];
                
                this.updateHeaderStats();
                this.populateFilterOptions();
                this.generateGroupColors();
                this.createCharts();
                this.renderTable();
                this.hideLoading();
            } else {
                throw new Error(result.message || 'Failed to load data');
            }
            
        } catch (error) {
            console.error('Error loading change data:', error);
            this.showError();
        }
    }

    showLoading() {
        document.getElementById('loadingIndicator').style.display = 'block';
        document.getElementById('errorMessage').style.display = 'none';
        document.querySelector('.charts-section').style.display = 'none';
        document.querySelector('.filters-section').style.display = 'none';
        document.querySelector('.table-section').style.display = 'none';
    }

    hideLoading() {
        document.getElementById('loadingIndicator').style.display = 'none';
        document.querySelector('.charts-section').style.display = 'block';
        document.querySelector('.filters-section').style.display = 'block';
        document.querySelector('.table-section').style.display = 'block';
    }

    showError() {
        document.getElementById('loadingIndicator').style.display = 'none';
        document.getElementById('errorMessage').style.display = 'block';
        document.querySelector('.charts-section').style.display = 'none';
        document.querySelector('.filters-section').style.display = 'none';
        document.querySelector('.table-section').style.display = 'none';
    }

    updateHeaderStats() {
        document.getElementById('totalChanges').textContent = this.allChanges.length;
        document.getElementById('lastUpdated').textContent = new Date().toLocaleString();
    }

    populateFilterOptions() {
        // Populate status filter
        const statuses = [...new Set(this.allChanges.map(change => change.gsdStatus))].sort();
        const statusFilter = document.getElementById('statusFilter');
        statusFilter.innerHTML = '<option value="">All Statuses</option>';
        statuses.forEach(status => {
            const option = document.createElement('option');
            option.value = status;
            option.textContent = status;
            statusFilter.appendChild(option);
        });

        // Populate group filter
        const groups = [...new Set(this.allChanges.map(change => change.gsdOwningGroup))].sort();
        const groupFilter = document.getElementById('groupFilter');
        groupFilter.innerHTML = '<option value="">All Groups</option>';
        groups.forEach(group => {
            const option = document.createElement('option');
            option.value = group;
            option.textContent = group;
            groupFilter.appendChild(option);
        });
    }

    generateGroupColors() {
        const groups = [...new Set(this.allChanges.map(change => change.gsdOwningGroup))];
        const colors = [
            '#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0', '#9966FF',
            '#FF9F40', '#FF6384', '#C9CBCF', '#4BC0C0', '#FF6384'
        ];
        
        groups.forEach((group, index) => {
            this.groupColors[group] = colors[index % colors.length];
        });
    }

    createCharts() {
        this.createDateChart();
        this.createStatusChart();
    }

    createDateChart() {
        const ctx = document.getElementById('dateChart').getContext('2d');
        
        // Group changes by date
        const dateGroups = {};
        this.filteredChanges.forEach(change => {
            if (change.gsdScheduledStartDate) {
                const date = new Date(change.gsdScheduledStartDate).toDateString();
                dateGroups[date] = (dateGroups[date] || 0) + 1;
            }
        });

        const labels = Object.keys(dateGroups).sort();
        const data = labels.map(label => dateGroups[label]);

        // Generate distinct colors for each date bar
        const barColors = [
            '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7',
            '#DDA0DD', '#98D8C8', '#F7DC6F', '#BB8FCE', '#85C1E9',
            '#F8C471', '#82E0AA', '#F1948A', '#85C1E9', '#D7BDE2'
        ];
        const backgroundColors = labels.map((_, index) => barColors[index % barColors.length]);
        const borderColors = [
            '#E55555', '#3ABAB0', '#3A9BC1', '#7BB89A', '#E6D497',
            '#C990C9', '#7BC4B4', '#E3C85F', '#A17FBE', '#6FB1D9',
            '#E4B061', '#6FD09A', '#E1847A', '#6FB1D9', '#C7ADD2'
        ];
        const finalBorderColors = labels.map((_, index) => borderColors[index % borderColors.length]);

        if (this.dateChart) {
            this.dateChart.destroy();
        }

        this.dateChart = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: labels,
                datasets: [{
                    label: 'Number of Changes',
                    data: data,
                    backgroundColor: backgroundColors,
                    borderColor: finalBorderColors,
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                onClick: (event, elements) => {
                    if (elements.length > 0) {
                        const index = elements[0].index;
                        const selectedDate = labels[index];
                        this.filterByDate(selectedDate);
                    }
                },
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            stepSize: 1
                        }
                    },
                    x: {
                        ticks: {
                            maxRotation: 45,
                            minRotation: 45
                        }
                    }
                }
            }
        });
    }

    createStatusChart() {
        const ctx = document.getElementById('statusChart').getContext('2d');

        // Group changes by status
        const statusCounts = {};
        this.filteredChanges.forEach(change => {
            const status = change.gsdStatus || 'Unknown';
            statusCounts[status] = (statusCounts[status] || 0) + 1;
        });

        const labels = Object.keys(statusCounts).sort();
        const data = labels.map(label => statusCounts[label]);

        // Define status-specific colors with distinct colors for each actual status
        const statusColors = {
            'Assess': '#ffc107',         // Yellow/Orange
            'Cancelled': '#dc3545',      // Red
            'Implement': '#28a745',      // Green
            'New': '#007bff',            // Blue
            'Review': '#6f42c1',         // Purple
            'Scheduled': '#17a2b8',      // Teal
            // Additional statuses that might appear
            'Completed': '#20c997',      // Mint Green
            'In Progress': '#fd7e14',    // Orange
            'Pending': '#6c757d',        // Gray
            'On Hold': '#e83e8c',        // Pink
            'Approved': '#198754',       // Dark Green
            'Rejected': '#d63384',       // Dark Pink
            'Unknown': '#C9CBCF'         // Light Gray
        };
        const colors = labels.map(label => statusColors[label] || '#C9CBCF');

        if (this.statusChart) {
            this.statusChart.destroy();
        }

        this.statusChart = new Chart(ctx, {
            type: 'pie',
            data: {
                labels: labels,
                datasets: [{
                    data: data,
                    backgroundColor: colors
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                onClick: (event, elements) => {
                    if (elements.length > 0) {
                        const index = elements[0].index;
                        const selectedStatus = labels[index];
                        this.filterByStatus(selectedStatus);
                    }
                },
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });
    }

    renderTable() {
        const tbody = document.getElementById('changesTableBody');
        tbody.innerHTML = '';

        this.filteredChanges.forEach(change => {
            const row = document.createElement('tr');
            
            // Add group-based background color class
            const groupClass = this.getGroupClass(change.gsdOwningGroup);
            row.className = groupClass;

            row.innerHTML = `
                <td>
                    <div class="assignee-cell">
                        <img src="${change.assigneePhotoUrl || 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMjAiIGN5PSIyMCIgcj0iMjAiIGZpbGw9IiNFNUU1RTUiLz4KPGNpcmNsZSBjeD0iMjAiIGN5PSIxNiIgcj0iNiIgZmlsbD0iIzk5OTk5OSIvPgo8cGF0aCBkPSJNMzAgMzJDMzAgMjYuNDc3MSAyNS41MjI5IDIyIDIwIDIyQzE0LjQ3NzEgMjIgMTAgMjYuNDc3MSAxMCAzMiIgZmlsbD0iIzk5OTk5OSIvPgo8L3N2Zz4K'}"
                             alt="${change.assigneeName || 'Unknown'}"
                             class="assignee-photo"
                             onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMjAiIGN5PSIyMCIgcj0iMjAiIGZpbGw9IiNFNUU1RTUiLz4KPGNpcmNsZSBjeD0iMjAiIGN5PSIxNiIgcj0iNiIgZmlsbD0iIzk5OTk5OSIvPgo8cGF0aCBkPSJNMzAgMzJDMzAgMjYuNDc3MSAyNS41MjI5IDIyIDIwIDIyQzE0LjQ3NzEgMjIgMTAgMjYuNDc3MSAxMCAzMiIgZmlsbD0iIzk5OTk5OSIvPgo8L3N2Zz4K'">
                        <div class="assignee-info">
                            <span class="assignee-name">${change.assigneeName || 'Unknown'}</span>
                            <span class="assignee-email">${change.assigneeEmail || ''}</span>
                        </div>
                    </div>
                </td>
                <td>
                    <div class="assignee-cell">
                        <img src="${change.creatorPhotoUrl || 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMjAiIGN5PSIyMCIgcj0iMjAiIGZpbGw9IiNFNUU1RTUiLz4KPGNpcmNsZSBjeD0iMjAiIGN5PSIxNiIgcj0iNiIgZmlsbD0iIzk5OTk5OSIvPgo8cGF0aCBkPSJNMzAgMzJDMzAgMjYuNDc3MSAyNS41MjI5IDIyIDIwIDIyQzE0LjQ3NzEgMjIgMTAgMjYuNDc3MSAxMCAzMiIgZmlsbD0iIzk5OTk5OSIvPgo8L3N2Zz4K'}"
                             alt="${change.creatorName || 'Unknown'}"
                             class="assignee-photo"
                             onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMjAiIGN5PSIyMCIgcj0iMjAiIGZpbGw9IiNFNUU1RTUiLz4KPGNpcmNsZSBjeD0iMjAiIGN5PSIxNiIgcj0iNiIgZmlsbD0iIzk5OTk5OSIvPgo8cGF0aCBkPSJNMzAgMzJDMzAgMjYuNDc3MSAyNS41MjI5IDIyIDIwIDIyQzE0LjQ3NzEgMjIgMTAgMjYuNDc3MSAxMCAzMiIgZmlsbD0iIzk5OTk5OSIvPgo8L3N2Zz4K'">
                        <div class="assignee-info">
                            <span class="assignee-name">${change.creatorName || 'Unknown'}</span>
                            <span class="assignee-email">${change.creatorEmail || ''}</span>
                        </div>
                    </div>
                </td>
                <td>${change.snCrNumber || ''}</td>
                <td title="${change.gsdDescription || ''}">${this.truncateText(change.gsdTitle || '', 50)}</td>
                <td><span class="status-badge status-${(change.gsdStatus || '').toLowerCase().replace(/\s+/g, '-')}">${change.gsdStatus || ''}</span></td>
                <td>${change.gsdChangeType || ''}</td>
                <td>${change.gsdOwningGroup || ''}</td>
                <td>${this.formatDate(change.gsdScheduledStartDate)}</td>
                <td>${this.formatDate(change.gsdScheduledEndDate)}</td>
                <td>${change.jiraId ? `<a href="https://gbmt-gojira.prd.fx.gbm.cloud.uk.hsbc/browse/${change.jiraId}" target="_blank" class="jira-link">${change.jiraId}</a>` : ''}</td>
            `;

            tbody.appendChild(row);
        });
    }

    getGroupClass(group) {
        if (!group) return 'group-default';
        
        const groupLower = group.toLowerCase();
        if (groupLower.includes('hss')) return 'group-gbm-hss';
        if (groupLower.includes('fx')) return 'group-gbm-fx';
        if (groupLower.includes('rates')) return 'group-gbm-rates';
        if (groupLower.includes('credit')) return 'group-gbm-credit';
        
        return 'group-default';
    }

    truncateText(text, maxLength) {
        if (text.length <= maxLength) return text;
        return text.substring(0, maxLength) + '...';
    }

    formatDate(dateString) {
        if (!dateString) return '';
        const date = new Date(dateString);
        return date.toLocaleDateString() + ' ' + date.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});
    }

    applyFilters() {
        const statusFilter = document.getElementById('statusFilter').value;
        const groupFilter = document.getElementById('groupFilter').value;
        const searchFilter = document.getElementById('searchFilter').value.toLowerCase();

        this.filteredChanges = this.allChanges.filter(change => {
            const matchesStatus = !statusFilter || change.gsdStatus === statusFilter;
            const matchesGroup = !groupFilter || change.gsdOwningGroup === groupFilter;
            const matchesSearch = !searchFilter ||
                (change.gsdTitle && change.gsdTitle.toLowerCase().includes(searchFilter)) ||
                (change.snCrNumber && change.snCrNumber.toLowerCase().includes(searchFilter)) ||
                (change.assigneeName && change.assigneeName.toLowerCase().includes(searchFilter)) ||
                (change.creatorName && change.creatorName.toLowerCase().includes(searchFilter)) ||
                (change.jiraId && change.jiraId.toLowerCase().includes(searchFilter));

            return matchesStatus && matchesGroup && matchesSearch;
        });

        this.sortFilteredData();
        this.createCharts();
        this.renderTable();
    }

    filterByDate(selectedDate) {
        this.filteredChanges = this.allChanges.filter(change => {
            if (!change.gsdScheduledStartDate) return false;
            const changeDate = new Date(change.gsdScheduledStartDate).toDateString();
            return changeDate === selectedDate;
        });

        this.renderTable();
        this.createStatusChart(); // Update the status chart to reflect the date filter
    }

    filterByStatus(selectedStatus) {
        document.getElementById('statusFilter').value = selectedStatus;
        this.applyFilters();
    }

    clearFilters() {
        document.getElementById('statusFilter').value = '';
        document.getElementById('groupFilter').value = '';
        document.getElementById('searchFilter').value = '';
        this.sortColumn = null;
        this.sortDirection = 'asc';
        this.updateSortIndicators();
        this.filteredChanges = [...this.allChanges];
        this.createCharts();
        this.renderTable();
    }

    sortTable(column) {
        if (this.sortColumn === column) {
            this.sortDirection = this.sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            this.sortColumn = column;
            this.sortDirection = 'asc';
        }

        this.sortFilteredData();
        this.updateSortIndicators();
        this.renderTable();
    }

    sortFilteredData() {
        if (!this.sortColumn) return;

        this.filteredChanges.sort((a, b) => {
            let aVal = this.getNestedValue(a, this.sortColumn);
            let bVal = this.getNestedValue(b, this.sortColumn);

            // Handle null/undefined values
            if (aVal == null && bVal == null) return 0;
            if (aVal == null) return 1;
            if (bVal == null) return -1;

            // Handle dates
            if (this.sortColumn.includes('Date')) {
                aVal = new Date(aVal);
                bVal = new Date(bVal);
            }

            // Handle strings (case insensitive)
            if (typeof aVal === 'string' && typeof bVal === 'string') {
                aVal = aVal.toLowerCase();
                bVal = bVal.toLowerCase();
            }

            let result = 0;
            if (aVal < bVal) result = -1;
            else if (aVal > bVal) result = 1;

            return this.sortDirection === 'desc' ? -result : result;
        });
    }

    getNestedValue(obj, path) {
        return path.split('.').reduce((current, key) => current && current[key], obj);
    }

    updateSortIndicators() {
        document.querySelectorAll('.sort-indicator').forEach(indicator => {
            indicator.className = 'sort-indicator';
        });

        if (this.sortColumn) {
            const header = document.querySelector(`[data-column="${this.sortColumn}"] .sort-indicator`);
            if (header) {
                header.className = `sort-indicator ${this.sortDirection}`;
            }
        }
    }
}

// Initialize dashboard when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new ChangeDashboard();
});
