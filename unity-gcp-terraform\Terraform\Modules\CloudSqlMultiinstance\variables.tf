variable "project" {
  type = string
}
variable "region" {
  type = string
}

variable "private_network" {
  type = string
}

variable "database_version" {
  type = string
}

variable "sql_proxy_zone" {
  type = string
}

variable "kms_key_sql_link" {
  type = string
}
variable "db_availability_type" {
  type = string
}
variable "point_in_time_recovery" {
  type        = bool
  default     = false
  description = "point in time recovery option to enable on instance"
}

variable "db_flags" {
  type        = list(object({ name : string, value : string }))
  description = "List of flags to set"
  default     = [{ name = "cloudsql.iam_authentication", value = "on" }]
}

variable "deletion_policy" {
  description = "ABANDON or null"
  default     = null
}

variable "sql_objects_list" {
  type = list(object({
    db_instance_name : string,
    #    sql_proxy_zone : string,
    db_tier : string,
    #      database_version : string,
    #    db_machine_type : string,

    #      instance : object({
    #    db_availability_type : string,
    db_databases : list(string),
    database_user_names : list(object({ username : string, type : string })),
    delete_protection : bool,
    retention_log_time : number
    maintenance_window_day : number
    maintenance_window_hour : number
    query_insights_enabled: bool
    master_instance_name: string
    //must be kept in sync with "database_user_names" variable in sql module
    #      })
    })    
  )
}

variable "db_backup_configuration_location" {
  type = string
}
