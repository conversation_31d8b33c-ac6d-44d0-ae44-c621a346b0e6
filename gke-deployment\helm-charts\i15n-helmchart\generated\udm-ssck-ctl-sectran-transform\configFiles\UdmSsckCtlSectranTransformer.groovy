

import com.poc.hss.fasttrack.transform.AbstractTransformer
import com.poc.hss.fasttrack.transform.model.TransformerBatchInputContext
import com.poc.hss.fasttrack.transform.model.TransformerRecord
import io.vertx.core.json.JsonObject
import org.slf4j.Logger
import org.slf4j.LoggerFactory

import java.util.stream.Collectors

class UdmSsckCtlSectranTransformer extends AbstractTransformer {

    private static final Logger logger = LoggerFactory.getLogger(UdmSsckCtlSectranTransformer.class);

    JsonObject constructRecord(JsonObject source) {
        new JsonObject()
                .put("site", source.getString("SITE", "").trim().toUpperCase())
                .put("T8TXFT", source.getString("T8TXFT", "").trim().toUpperCase())
                .put("T8TXFS", source.getString("T8TXFS", "").trim().toUpperCase())
                .put("T8TXFQ", source.getString("T8TXFQ", "").trim().toUpperCase())
                .put("T8TXFX", source.getString("T8TXFX", "").trim().toUpperCase())
                .put("T8TRDT", source.getString("T8TRDT", "").trim().toUpperCase())
                .put("T8OSSC", source.getString("T8OSSC", "").trim().toUpperCase())
                .put("event_source", source.getString("FILE_NAME", "")?.trim())
    }
    static String toUpperCase(String source){
        return source == null ? null : source.toUpperCase();
    }
    static Integer getInteger( String key,JsonObject obj) {
        String result = obj.getString(key);
        if (result != null && !result.trim().equals("")) {
            return new BigDecimal(result).intValueExact();
        } else {
            return 0;
        }
    }

    @Override
    List<TransformerRecord> batchTransform(TransformerBatchInputContext context) {
        List<TransformerRecord> records = context.getRecords()
        logger.info("=====first record, _id:{} data:{}",records.get(0).getId(),records.get(0).getData())
        return records.stream()
                .map(rec -> rec.from(rec).data(constructRecord(rec.getData())).build())
                .collect(Collectors.toList())
    }
}
