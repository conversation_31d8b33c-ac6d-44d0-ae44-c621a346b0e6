---
spring:
  datasource:
    url: "********************************/${DB_NAME}"
    username: "${DB_USER}"
    password: "${DB_PASSWORD}"
  jpa:
    show-sql: false
    generate-ddl: true
    properties:
      hibernate:
        dialect: "org.hibernate.dialect.PostgreSQLDialect"
        hbm2ddl:
          auto: "none"
  application:
    name: "udm-ssck-ctl-sectran-access"
kafkaConfig:
  auto.offset.reset: "latest"
  bootstrap.servers: "kafka-0.kafka-internal.ns-core-prod-apps.svc.cluster.local:9093,kafka-1.kafka-internal.ns-core-prod-apps.svc.cluster.local:9093,kafka-2.kafka-internal.ns-core-prod-apps.svc.cluster.local:9093"
  group.id: "${KAFKA_CONSUMER_GROUP}"
  schema.registry.url: "https://hkl20077608.hc.cloud.hk.hsbc:8082/"
  security.protocol: "SSL"
  ssl.keystore.location: "/hss/apps/certs/unity-microservices.jks"
  ssl.keystore.password: "${KAFKA_KEYSTORE_PASSWORD}"
  ssl.truststore.location: "/hss/apps/certs/unity-microservices.ts"
  ssl.truststore.password: "${KAFKA_TRUSTSTORE_PASSWORD}"
management:
  endpoints:
    web:
      exposure:
        include: "health,prometheus"
  metrics:
    tags:
      application: "${spring.application.name}"
  tracing:
    enabled: true
cache:
  provider: "kafka"
  address: "http://cache"
  topic: "unity2-PROD-core-cache-metric"
businessEventTopic: "unity2-PROD-core-access-event-udm-ssck-ctl-sectran-out"
projectId: "12bb8fb6-ba3d-4615-9b0b-f4f5cb38656f"
accesses:
- persistenceEnabled: true
  sourceTopic: "unity2-PROD-core-transform-udm-ssck-ctl-sectran-out"
  tableName: "udm-ssck-ctl-sectran"
  columns:
  - name: "_trace_id"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "_id"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "created_by"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "created_time_stamp"
    dataType: "timestamp"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "deleted_by"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "deleted_time_stamp"
    dataType: "timestamp"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "updated_by"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "updated_time_stamp"
    dataType: "timestamp"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "_batch_id"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "site"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "T8TXFT"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "T8TXFS"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "T8TXFQ"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "T8TXFX"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "T8OSSC"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "T8TRDT"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "event_source"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  script: ""
  scriptEnabled: false
  retention:
    retentionEnabled: false
  sourceBatchTopicSuffix: "-batch"
  targetBatchTopicSuffix: "-batch"
  ddlAllowed: true
externalDataSourceJdbcConfigs: {}
