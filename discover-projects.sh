#!/bin/bash
set -euo pipefail

# Project Discovery Script for Jenkins Pipeline
# This script discovers Maven projects in the repository

# Function to print usage
usage() {
    echo "Usage: $0 [options]"
    echo ""
    echo "Options:"
    echo "  --format FORMAT    Output format: json|csv|list (default: list)"
    echo "  --include-all      Include 'all' option in output"
    echo "  --validate         <PERSON><PERSON><PERSON> discovered projects"
    echo "  --help, -h         Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0                           # List all projects"
    echo "  $0 --format json             # Output as JSON"
    echo "  $0 --include-all --validate  # Include 'all' and validate"
    exit 1
}

# Default options
FORMAT="list"
INCLUDE_ALL=false
VALIDATE=false

# Parse options
while [[ $# -gt 0 ]]; do
    case $1 in
        --format)
            FORMAT="$2"
            shift 2
            ;;
        --include-all)
            INCLUDE_ALL=true
            shift
            ;;
        --validate)
            VALIDATE=true
            shift
            ;;
        --help|-h)
            usage
            ;;
        *)
            echo "Error: Unknown option $1"
            usage
            ;;
    esac
done

echo "=== Discovering Maven Projects ===" >&2

# Find all pom.xml files (excluding target directories)
POM_FILES=$(find . -name "pom.xml" -not -path "*/target/*" -not -path "*/.git/*" | head -20)

PROJECTS=()

for pom_file in $POM_FILES; do
    # Get directory containing pom.xml
    project_dir=$(dirname "$pom_file")
    
    # Remove leading ./
    project_dir=${project_dir#./}
    
    # Skip root directory and nested projects
    if [ "$project_dir" = "." ] || [[ "$project_dir" == *"/"* ]]; then
        continue
    fi
    
    # Skip hidden directories
    if [[ "$project_dir" == .* ]]; then
        continue
    fi
    
    # Check if it has src directory (indicating it's a source project)
    if [ -d "$project_dir/src" ]; then
        echo "Found project: $project_dir" >&2
        
        if [ "$VALIDATE" = "true" ]; then
            echo "  Validating $project_dir..." >&2
            
            # Check for Java source files
            if find "$project_dir/src" -name "*.java" | head -1 | grep -q .; then
                echo "  ✅ Java sources found" >&2
            else
                echo "  ⚠️  No Java sources found" >&2
            fi
            
            # Check for Spring Boot
            if grep -q "spring-boot" "$project_dir/pom.xml" 2>/dev/null; then
                echo "  ✅ Spring Boot project detected" >&2
            else
                echo "  ℹ️  Not a Spring Boot project" >&2
            fi
            
            # Check for existing Dockerfile
            if [ -f "$project_dir/Dockerfile" ]; then
                echo "  ✅ Dockerfile exists" >&2
            else
                echo "  ⚠️  No Dockerfile found" >&2
            fi
            
            # Check for Helm values
            helm_values="gke-deployment/helm-charts/i15n-helmchart/$project_dir/values.yaml"
            if [ -f "$helm_values" ]; then
                echo "  ✅ Helm values exist" >&2
            else
                echo "  ⚠️  No Helm values found at $helm_values" >&2
            fi
        fi
        
        PROJECTS+=("$project_dir")
    fi
done

# Add 'all' option if requested
if [ "$INCLUDE_ALL" = "true" ]; then
    PROJECTS=("all" "${PROJECTS[@]}")
fi

echo "=== Discovery Complete ===" >&2
echo "Found ${#PROJECTS[@]} projects" >&2

# Output in requested format
case "$FORMAT" in
    "json")
        echo "["
        for i in "${!PROJECTS[@]}"; do
            if [ $i -eq $((${#PROJECTS[@]} - 1)) ]; then
                echo "  \"${PROJECTS[$i]}\""
            else
                echo "  \"${PROJECTS[$i]}\","
            fi
        done
        echo "]"
        ;;
    "csv")
        IFS=','
        echo "${PROJECTS[*]}"
        ;;
    "list"|*)
        for project in "${PROJECTS[@]}"; do
            echo "$project"
        done
        ;;
esac
