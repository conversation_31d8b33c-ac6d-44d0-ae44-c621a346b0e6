#!/bin/bash

constructor() {
  _configfile_=/etc/s3sftp.d/config.yaml
  if [[ -f ${_configfile_} ]]; then
    export AWSSECRETACCESSKEY=$(yq e '.s3.aws_secret_access_key' ${_configfile_})
    export AWSACCESSKEYID=$(yq e '.s3.aws_access_key_id' ${_configfile_})
    export S3_BUCKET=$(yq e '.s3.bucket' ${_configfile_})
    export S3_STORAGE_SERVER_URL=$(yq e '.s3.storage_server_url' ${_configfile_})
    export NETWORK_CONNECTION_LIMIT=$(yq e '.sftp.network_connection_limit' ${_configfile_})
  fi
}

function mount_s3fs() {
  MOUNT_POINT=/var/sftp/${S3_BUCKET}
  if ! df -Ph ${MOUNT_POINT} > /dev/null 2>&1 ; then
    umount -f ${MOUNT_POINT}
    mkdir -p /var/sftp/${S3_BUCKET}
    if [[ ! -z "${S3_BUCKET}" ]]; then 
      if [[ -z "${S3_STORAGE_SERVER_URL}" ]] ; then
        s3fs ${S3_BUCKET} /var/sftp/${S3_BUCKET} -o use_path_request_style -o umask=0007,uid=10001,gid=999 -o allow_other
      else
        s3fs ${S3_BUCKET} /var/sftp/${S3_BUCKET} -o url=${S3_STORAGE_SERVER_URL} -o use_path_request_style -o umask=0007,uid=10001,gid=999 -o allow_other -o singlepart_copy_limit=100 
      fi
      if df -Ph ${MOUNT_POINT} > /dev/null 2>&1 ; then
        echo "$(date): Remount successful." 
      else
        echo "$(date): Remount failed."
      fi
    fi
  fi  
}

# Run the check_mount function every 5 minutes
constructor
while true; do
  mount_s3fs
  sleep 1
done
