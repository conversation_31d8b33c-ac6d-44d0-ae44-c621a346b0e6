#!/usr/bin/env bash
set -euo pipefail
echo "<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<< TERRAGRUNT  >>>>>>>>>>>>>>>>>>>>>>>>>>>"
echo "[MGMT_HOST] starting terragrunt installaton"
echo "[MGMT_HOST] downloading terragrunt from nexus"
wget --user="vagrant" --password="vagrant" "https://efx-nexus.systems.uk.hsbc:8082/nexus/service/local/repositories/Tools/content/tools/gruntwork/terragrunt/0.28.24/terragrunt-0.28.24-linux_amd64.zip"
unzip terragrunt-0.28.24-linux_amd64.zip
mv terragrunt_linux_amd64 terragrunt
mkdir -p /opt/terragrunt/
sudo mv terragrunt /opt/terragrunt/
chmod 777 -R /opt/terragrunt/
ln -s /opt/terragrunt/terragrunt  /usr/bin
echo "[MGMT_HOST]terragrunt installation was completed"