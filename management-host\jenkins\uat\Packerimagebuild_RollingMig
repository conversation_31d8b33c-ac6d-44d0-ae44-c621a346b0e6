pipeline {    
    agent  { 
        label "unity-dev-jenkins-agent"    
    }
    environment {
	    STASH_TOKEN= credentials('github-connect')
	    NEXUS_CREDS=credentials('github-connect')
   
    }
     parameters 
    {
       
        choice(name: 'profile_env', choices: 'dev', description: 'management host var-environment variable')
	      string(name: 'mig_name', defaultValue:'gce-uscentral1dev-mgmtmig-0226',description: 'PLEASE ENTER MIG_NAME ')

    }
    stages {
       stage ('Checkout imagefactory') {
            steps {
		  sh 'whoami'
                 sh 'sudo -i'
                 dir ("/root/test") {
                    git(
               branch: 'master' , credentialsId: 'github-connect', url:'https://alm-github.systems.uk.hsbc/HASE-IM/hsbc-9087302-unity-dev-mgmt-host'
                    )
                   sh 'chmod -R 777 mgmt_host'

                }
            }
        }
        stage('gcloud auth activate') {
	   steps{
		withCredentials([file(credentialsId: 'sa_key', variable: 'gcp_sa_key')]) {
         sh(returnStdout: true, script: "gcloud auth activate-service-account --key-file ${gcp_sa_key}")
         sh 'export GOOGLE_APPLICATION_CREDENTIALS=$gcp_sa_key'
	  }
		   
	   }
	  }	
	 stage ('build  Packer Templates') {
            steps {
                dir("/root/test/mgmt_host/")
                {
                             
			     echo "validating packer script is executing"
		       sh 'echo "git user is ${STASH_TOKEN}"'
           sh 'packer build -timestamp-ui -on-error=cleanup  -var "BUILDSTAGE3=${BUILDSTAGE3}"  -var "STAGE=${STAGE}"  -var "NEXUS_CREDS=${NEXUS_CREDS}" -var "NEXUS_CREDS_USR=${NEXUS_CREDS_USR}" -var "NEXUS_CREDS_PWD=${NEXUS_CREDS_PSW}" -var-file=dev/variables.json dev/image.json'

                            }
                    }
            }
	    
       stage ('rolling out the management host mig') { 
	steps {
	      dir("/root/test/mgmt_host/")
	      {
        echo "executing spinningup instance script"
      sh 'echo "git user is ${GIT_JENK_CRED}"'
    sh 'gcloud compute instance-groups managed  wait-until --stable $mig_name  --zone asia-east2-b'
  sh 'gcloud compute instance-groups managed rolling-action replace  $mig_name --max-unavailable 1 --zone asia-east2-b'
 //sh 'gcloud compute instance-groups managed  wait-until --stable  $mig_name --zone asia-east2-b'
   // sh 'gcloud compute instance-groups managed rolling-action start-update gce-uscentral1-mgmt-mig --version=template=gce-uscentral-mgmttempl,name=gce-uscentral1-mgmt-mig --type="opportunistic" --replacement-method=recreate  --zone=asia-east2-b --max-surge=0'
 }
    }
}

}	
	
}
