load_module '/usr/lib64/nginx/modules/ngx_stream_module.so';
user nginx;
worker_processes 1;
worker_rlimit_nofile 1024000;

error_log  /var/log/nginx/error.log warn;
pid        /var/run/nginx.pid;

events {
    worker_connections  5120;
}

http {
    ssl_session_cache shared:SSL:100m;
    ssl_buffer_size 1m;    
    server_names_hash_bucket_size 512;
    proxy_max_temp_file_size 0;
    send_timeout 30s;

    map $ssl_server_name $proxy_backend {
        #~^(?<appname>.*)\.(?<gcpproject>.*)\.(?<gcpenv>\w+)\.gcp\.cloud\.(?<region>\w+)\.hsbc$ https://$appname-proxy.$gcpproject.$gcpenv.gcp.cloud.$region.hsbc:443;
        ~^(?<appname>.*?)\.(?<domain>.*?)$ https://$appname-proxy.$domain:443; 
    }
  
    map $ssl_server_name $domainname {
        #~^(?<appname>.*)\.(?<gcpproject>.*)\.(?<gcpenv>\w+)\.gcp\.cloud\.(?<region>\w+)\.hsbc$ https://$appname-proxy.$gcpproject.$gcpenv.gcp.cloud.$region.hsbc:443;
        ~^(?<appname>.*?)\.(?<domain>.*?)$ $domain;
    }
  
    map $ssl_server_name $subdomain {
        ~^(?<appname>.*?)\.(?<sub>.*?)\.(?<domain>.*?)$ $sub;
    }
    
    map $ssl_server_name $app {
        ~^(?<appname>.*?)\.(?<domain>.*?)$ $appname;
    }

    log_format custom '$remote_addr [$time_local] [$ssl_client_s_dn] [$app] [$subdomain] [$uri] HTTP $status $bytes_sent "$upstream_addr" "$upstream_bytes_sent" "$upstream_bytes_received" "$upstream_connect_time" "$upstream_response_time" [${request_time}s]';

    server {
        listen 443 ssl default_server;
        resolver                *************** valid=5 ipv6=off;
        server_name             $ssl_server_name;
        ssl_certificate         pki/$subdomain/server.pem;
        ssl_certificate_key     pki/$subdomain/server.key;
        ssl_client_certificate  ca.pem;
        ssl_verify_client       optional;
        ssl_verify_depth        3;
        ssl_protocols           TLSv1.2;
        ssl_ciphers             HIGH:!aNULL:!MD5;

        location / {
          proxy_pass $proxy_backend;
          proxy_http_version 1.1;
          proxy_set_header host $ssl_server_name;
          proxy_set_header Upgrade $http_upgrade;
          proxy_set_header Connection "upgrade";
          set $authorized 0;   
          if ( $domainname  = "beaconglobal-prod.vpc2.hsbc-11465671-unity-prod.prod.gcp.cloud.hk.hsbc" ) { set $authorized 1; }   
          if ( $domainname  = "clientconnectivity-prod.vpc2.hsbc-11465671-unity-prod.prod.gcp.cloud.hk.hsbc" ) { set $authorized 1; }
          if ( $domainname  = "faasia-prod.vpc2.hsbc-11465671-unity-prod.prod.gcp.cloud.hk.hsbc" ) { set $authorized 1; }
          if ( $domainname  = "quartz-prod.vpc2.hsbc-11465671-unity-prod.prod.gcp.cloud.hk.hsbc" ) { set $authorized 1; }
          if ( $domainname  = "core-prod.vpc2.hsbc-11465671-unity-prod.prod.gcp.cloud.hk.hsbc" ) { set $authorized 1; }
          if ( $domainname  = "custodyreporting-prod.vpc2.hsbc-11465671-unity-prod.prod.gcp.cloud.hk.hsbc" ) { set $authorized 1; }
          if ( $domainname  = "irm-prod.vpc2.hsbc-11465671-unity-prod.prod.gcp.cloud.hk.hsbc" ) { set $authorized 1; }
          if ( $domainname  = "ssvmrddcc-prod.vpc2.hsbc-11465671-unity-prod.prod.gcp.cloud.hk.hsbc" ) { set $authorized 1; }
          if ( $domainname  = "ssvods-prod.vpc2.hsbc-11465671-unity-prod.prod.gcp.cloud.hk.hsbc" ) { set $authorized 1; }
          if ( $domainname  = "ssvtreasury-prod.vpc2.hsbc-11465671-unity-prod.prod.gcp.cloud.hk.hsbc" ) { set $authorized 1; }
          if ( $domainname  = "georgeuk-prod.vpc2.hsbc-11465671-unity-prod.prod.gcp.cloud.hk.hsbc" ) { set $authorized 1; }
          if ( $domainname  = "empfpensionhk-prod.vpc2.hsbc-11465671-unity-prod.prod.gcp.cloud.hk.hsbc" ) { set $authorized 1; }
          if ( $domainname  = "ss-prod.vpc2.hsbc-11465671-unity-prod.prod.gcp.cloud.hk.hsbc" ) { set $authorized 1; }
          if ( $domainname  = "ssvas-prod.vpc2.hsbc-11465671-unity-prod.prod.gcp.cloud.hk.hsbc" ) { set $authorized 1; }
          if ( $authorized != 1 ) { return 403; }
          access_log syslog:server=unix:/dev/log,nohostname custom;
          error_log  syslog:server=unix:/dev/log warn;
        }
    }
    
    server {
        listen 443 ssl;
        resolver                *************** valid=5 ipv6=off;
        server_name             $ssl_server_name;
        ssl_certificate         pki/$subdomain/server.pem;
        ssl_certificate_key     pki/$subdomain/server.key;
        ssl_client_certificate  ca.pem;
        ssl_verify_client       optional;
        ssl_verify_depth        3;
        ssl_protocols           TLSv1.2;
        ssl_ciphers             HIGH:!aNULL:!MD5;
 
        server_name *.tpc-prod.vpc2.hsbc-11465671-unity-prod.prod.gcp.cloud.hk.hsbc
                    ;

        location / {
          proxy_pass $proxy_backend;
          proxy_http_version 1.1;
          proxy_set_header host $ssl_server_name;
          proxy_set_header Upgrade $http_upgrade;
          proxy_set_header Connection "upgrade";
          set $authorized 0;
          if ( $app = "grafana") { set $authorized "11"; }
          if ( $app = "support") { set $authorized "11"; }
          if ( $ssl_client_s_dn = "CN=prod1-cfs-tpc.hk.hsbc,OU=GBM,O=Hsbc,L=Guangzhou,ST=Guangdong,C=CN" ) { set $authorized 11; }
          if ( $ssl_client_s_dn = "emailAddress=<EMAIL>,CN=prod1-cfs-tpc.hk.hsbc,OU=GBM,O=Hsbc,L=Guangzhou,ST=Guangdong,C=CN" ) { set $authorized 11; }
          if ( $authorized != 11 ) { return 403; }
          access_log syslog:server=unix:/dev/log,nohostname custom;
          error_log  syslog:server=unix:/dev/log warn;
        }
    }

    server {
        listen 443 ssl;
        resolver                *************** valid=5 ipv6=off;
        server_name             $ssl_server_name;
        ssl_certificate         pki/$subdomain/server.pem;
        ssl_certificate_key     pki/$subdomain/server.key;
        ssl_client_certificate  ca.pem;
        ssl_verify_client       optional;
        ssl_verify_depth        3;
        ssl_protocols           TLSv1.2;
        ssl_ciphers             HIGH:!aNULL:!MD5;
 
        server_name *.dccgfx-prod.vpc2.hsbc-11465671-unity-prod.prod.gcp.cloud.hk.hsbc
                    ;

        location / {
          proxy_pass $proxy_backend;
          proxy_http_version 1.1;
          proxy_set_header host $ssl_server_name;
          proxy_set_header Upgrade $http_upgrade;
          proxy_set_header Connection "upgrade";
          set $authorized 0;
          if ( $app = "grafana") { set $authorized "11"; }
          if ( $app = "support") { set $authorized "11"; }
          if ( $ssl_client_s_dn = "CN=prod.gfx-prod.hsbc-11374370-gfxasia-prod.prod.gcp.cloud.hk.hsbc,OU=GFX,OU=CFS,OU=MSS,O=HSBC,L=GuangZhou,ST=GuangDong,C=CN" ) { set $authorized "11"; } 
          if ( $ssl_client_s_dn = "CN=prod.gfx-prod.hsbc-11374370-gfxasia-prod.prod.gcp.cloud.hk.hsbc,OU=MSS,OU=CFS,OU=GFX,O=HSBC,L=GuangZhou,ST=GuangDong,C=CN" ) { set $authorized "11"; }   
          if ( $authorized != 11 ) { return 403; }
          access_log syslog:server=unix:/dev/log,nohostname custom;
          error_log  syslog:server=unix:/dev/log warn;
        }

        location /api/custom/dcc-static-data {
          proxy_pass $proxy_backend;
          proxy_http_version 1.1;
          proxy_set_header host $ssl_server_name;
          proxy_set_header Upgrade $http_upgrade;
          proxy_set_header Connection "upgrade";
          #allow custom end using outh2 to passthur
          set $authorized 11;
          if ( $authorized != 11 ) { return 403; }
          access_log syslog:server=unix:/dev/log,nohostname custom;
          error_log  syslog:server=unix:/dev/log warn;
        }
    }
  
    server {
        listen 443 ssl;
        resolver                *************** valid=5 ipv6=off;
        server_name             $ssl_server_name;
        ssl_certificate         pki/$subdomain/server.pem;
        ssl_certificate_key     pki/$subdomain/server.key;
        ssl_client_certificate  ca.pem;
        ssl_verify_client       optional;
        ssl_verify_depth        3;
        ssl_protocols           TLSv1.2;
        ssl_ciphers             HIGH:!aNULL:!MD5;
    
        server_name *.ssvods-prod.vpc2.hsbc-11465671-unity-prod.prod.gcp.cloud.hk.hsbc
                    ;

        location / {
          proxy_pass $proxy_backend;
          proxy_http_version 1.1;
          proxy_set_header host $ssl_server_name;
          proxy_set_header Upgrade $http_upgrade;
          proxy_set_header Connection "upgrade";
          set $authorized 0;           
          set $auth_subdomain 0;
          set $auth_dn 0;
          if ( $app = "sa-ods-allgmnt-rst" ) { set $auth_subdomain 1; }
          if ( $app = "ods-allgmnt-api" ) { set $auth_subdomain 1; }
          if ( $ssl_client_s_dn = "CN=prod-cfs-services-consumer,OU=Securities Service,O=The Hongkong and Shanghai Banking Corporation Limited,L=Hong Kong,ST=HKSAR,C=CN" ) { set $auth_dn 1; }
          set $authorized "${auth_subdomain}${auth_dn}";
          if ( $authorized !~ ^(11|00)$ ) { return 403; }
          access_log syslog:server=unix:/dev/log,nohostname custom ;
          error_log  syslog:server=unix:/dev/log warn;
        }        
    }
}
