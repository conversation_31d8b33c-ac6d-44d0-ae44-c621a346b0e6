<?xml version="1.1" encoding="UTF-8" standalone="no"?><flow-definition plugin="workflow-job@1400.v7fd111b_ec82f">
  <actions>
    <org.jenkinsci.plugins.pipeline.modeldefinition.actions.DeclarativeJobAction plugin="pipeline-model-definition@2.2198.v41dd8ef6dd56"/>
    <org.jenkinsci.plugins.pipeline.modeldefinition.actions.DeclarativeJobPropertyTrackerAction plugin="pipeline-model-definition@2.2198.v41dd8ef6dd56">
      <jobProperties/>
      <triggers/>
      <parameters>
        <string>unityReleaseVersion</string>
        <string>chartsList</string>
        <string>changeOrder</string>
        <string>undeployChartsList</string>
      </parameters>
      <options/>
    </org.jenkinsci.plugins.pipeline.modeldefinition.actions.DeclarativeJobPropertyTrackerAction>
  </actions>
  <description>Pipeline that deploys helm chart from https://stash.hk.hsbc/projects/UNITY-I15N-POC/repos/gke-deployment/browse</description>
  <displayName>Pipeline helm charts deployment (auto) __ENV__</displayName>
  <keepDependencies>false</keepDependencies>
  <properties>
    <hudson.plugins.jira.JiraProjectProperty plugin="jira@3.12"/>
    <jenkins.model.BuildDiscarderProperty>
      <strategy class="hudson.tasks.LogRotator">
        <daysToKeep>14</daysToKeep>
        <numToKeep>20</numToKeep>
        <artifactDaysToKeep>2</artifactDaysToKeep>
        <artifactNumToKeep>-1</artifactNumToKeep>
      </strategy>
    </jenkins.model.BuildDiscarderProperty>
    <org.jenkinsci.plugins.workflow.job.properties.DisableConcurrentBuildsJobProperty>
      <abortPrevious>false</abortPrevious>
    </org.jenkinsci.plugins.workflow.job.properties.DisableConcurrentBuildsJobProperty>
    <com.sonyericsson.rebuild.RebuildSettings plugin="rebuild@330.v645b_7df10e2a_">
      <autoRebuild>false</autoRebuild>
      <rebuildDisabled>false</rebuildDisabled>
    </com.sonyericsson.rebuild.RebuildSettings>
    <hudson.model.ParametersDefinitionProperty>
      <parameterDefinitions>
        <hudson.model.TextParameterDefinition>
          <name>chartsList</name>
          <description>Multi-line parameter (csv), default empty to deploy all services</description>
          <trim>false</trim>
        </hudson.model.TextParameterDefinition>
        <hudson.model.TextParameterDefinition>
          <name>undeployChartsList</name>
          <description>Multi-line parameter (csv), default empty not to undeploy services</description>
          <trim>false</trim>
        </hudson.model.TextParameterDefinition>
        <hudson.model.StringParameterDefinition>
          <name>changeOrder</name>
          <description>SNOW change order number</description>
          <trim>false</trim>
        </hudson.model.StringParameterDefinition>
        <hudson.model.StringParameterDefinition>
          <name>unityReleaseVersion</name>
          <description>unity_release_version is used for LTTD calculation: documentation: https://gbmt-confluence.prd.fx.gbm.cloud.uk.hsbc/x/p0vnSQ</description>
          <trim>false</trim>
        </hudson.model.StringParameterDefinition>
        <hudson.model.StringParameterDefinition>
          <name>branch_specifier</name>
          <description>defaulton branch: &lt;br&gt;
branch: ${GIT_BRANCH_PREFIX}-PROD&lt;br&gt;
tag: tags/_TAG_NAME_&lt;br&gt;
commit: _COMMIT_ID_&lt;br&gt;
&lt;br&gt;
Example:&lt;br&gt;
Using Tag: tags/UAT-v202406051636-grafana-beta&lt;br&gt;
Using Commit: eb0121b94dfc67d5e9d2ecdcdc42dfa339a22ac0&lt;br&gt;</description>
          <defaultValue>*/${GIT_BRANCH_PREFIX}-__ENV____ENV_SUFFIX__</defaultValue>
          <trim>false</trim>
        </hudson.model.StringParameterDefinition>
        <hudson.model.StringParameterDefinition>
          <name>refspec</name>
          <description>default refspec on branch: &lt;br&gt;
branch: +refs/heads/${GIT_BRANCH_PREFIX}-PROD:remotes/origin/${GIT_BRANCH_PREFIX}-PROD &lt;br&gt;
tag: +refs/tags/_TAG_NAME_:remotes/tags/_TAG_NAME_ &lt;br&gt;
commit: _COMMIT_ID_:refs/remotes/origin/local &lt;br&gt;
&lt;br&gt;
Example:&lt;br&gt;
Using Tag: +refs/tags/UAT-v202406051636-grafana-beta:remotes/tags/UAT-v202406051636-grafana-beta&lt;br&gt;
Using Commit: eb0121b94dfc67d5e9d2ecdcdc42dfa339a22ac0:refs/remotes/origin/local&lt;br&gt;</description>
          <defaultValue>+refs/heads/${GIT_BRANCH_PREFIX}-__ENV____ENV_SUFFIX__:remotes/origin/${GIT_BRANCH_PREFIX}-__ENV____ENV_SUFFIX__</defaultValue>
          <trim>false</trim>
        </hudson.model.StringParameterDefinition>
        <hudson.model.StringParameterDefinition>
          <name>clusterProject</name>
          <description>(GCP Configuration)</description>
          <defaultValue>__CLUSTER_PROJECT__</defaultValue>
          <trim>false</trim>
        </hudson.model.StringParameterDefinition>
        <hudson.model.StringParameterDefinition>
          <name>clusterRegion</name>
          <description>(GCP Configuration)</description>
          <defaultValue>__CLUSTER_REGION__</defaultValue>
          <trim>false</trim>
        </hudson.model.StringParameterDefinition>
        <hudson.model.StringParameterDefinition>
          <name>clusterName</name>
          <defaultValue>__CLUSTER_NAME__</defaultValue>
          <trim>false</trim>
        </hudson.model.StringParameterDefinition>
        <hudson.model.StringParameterDefinition>
          <name>kubectlProxy</name>
          <defaultValue>__KUBECTL_PROXY__</defaultValue>
          <trim>false</trim>
        </hudson.model.StringParameterDefinition>
        <hudson.model.ChoiceParameterDefinition>
          <name>authMethod</name>
          <choices class="java.util.Arrays$ArrayList">
            <a class="string-array">
              <string>IAM-DIRECT</string>
              <string>IAM-SERVICE-ACCOUNT-KEY-JENKINS-CREDENTIAL</string>
              <string>KUBECONFIG-PRECONFIGURED</string>
              <string>KUBECONFIG-JENKINS-CREDENTIAL</string>
            </a>
          </choices>
        </hudson.model.ChoiceParameterDefinition>
        <hudson.model.StringParameterDefinition>
          <name>serviceAccount</name>
          <defaultValue>gce-stage3-image-builder</defaultValue>
          <trim>false</trim>
        </hudson.model.StringParameterDefinition>
        <hudson.model.StringParameterDefinition>
          <name>jenkinsCredential</name>
          <description>Name of Jenkins Credential</description>
          <trim>false</trim>
        </hudson.model.StringParameterDefinition>
      </parameterDefinitions>
    </hudson.model.ParametersDefinitionProperty>
    
    <org.jenkinsci.plugins.workflow.job.properties.PipelineTriggersJobProperty>
      <triggers/>
    </org.jenkinsci.plugins.workflow.job.properties.PipelineTriggersJobProperty>
  </properties>
  <definition class="org.jenkinsci.plugins.workflow.cps.CpsScmFlowDefinition" plugin="workflow-cps@3903.v48a_8836749e9">
    <scm class="hudson.plugins.git.GitSCM" plugin="git@5.2.2">
      <configVersion>2</configVersion>
      <userRemoteConfigs>
        <hudson.plugins.git.UserRemoteConfig>
          <refspec>${refspec}</refspec>
          <url>https://stash.hk.hsbc/scm/unity-i15n-poc/gke-deployment.git</url>
          <credentialsId>NEXUS3_CONNECT</credentialsId>
        </hudson.plugins.git.UserRemoteConfig>
      </userRemoteConfigs>
      <branches>
        <hudson.plugins.git.BranchSpec>
          <name>${branch_specifier}</name>
        </hudson.plugins.git.BranchSpec>
      </branches>
      <doGenerateSubmoduleConfigurations>false</doGenerateSubmoduleConfigurations>
      <gitTool>Default</gitTool>
      <submoduleCfg class="empty-list"/>
      <extensions>
        <hudson.plugins.git.extensions.impl.CloneOption>
          <shallow>true</shallow>
          <noTags>true</noTags>
          <reference/>
          <depth>1</depth>
          <honorRefspec>true</honorRefspec>
        </hudson.plugins.git.extensions.impl.CloneOption>
        <hudson.plugins.git.extensions.impl.WipeWorkspace/>
      </extensions>
    </scm>
    <scriptPath>jenkins/Jenkinsfile_custom</scriptPath>
    <lightweight>false</lightweight>
  </definition>
  <triggers/>
  <disabled>false</disabled>
</flow-definition>