---
spring:
  datasource:
    url: "********************************/${DB_NAME}"
    username: "${DB_USER}"
    password: "${DB_PASSWORD}"
  jpa:
    show-sql: false
    generate-ddl: true
    properties:
      hibernate:
        dialect: "org.hibernate.dialect.PostgreSQLDialect"
        hbm2ddl:
          auto: "none"
  application:
    name: "udm-sec-tran-jrnl-access"
kafkaConfig:
  auto.offset.reset: "latest"
  bootstrap.servers: "kafka-0.kafka-internal.ns-core-prod-apps.svc.cluster.local:9093,kafka-1.kafka-internal.ns-core-prod-apps.svc.cluster.local:9093,kafka-2.kafka-internal.ns-core-prod-apps.svc.cluster.local:9093"
  group.id: "${KAFKA_CONSUMER_GROUP}"
  schema.registry.url: "https://hkl20077608.hc.cloud.hk.hsbc:8082/"
  security.protocol: "SSL"
  ssl.keystore.location: "/hss/apps/certs/unity-microservices.jks"
  ssl.keystore.password: "${KAFKA_KEYSTORE_PASSWORD}"
  ssl.truststore.location: "/hss/apps/certs/unity-microservices.ts"
  ssl.truststore.password: "${KAFKA_TRUSTSTORE_PASSWORD}"
management:
  endpoints:
    web:
      exposure:
        include: "health,prometheus"
  metrics:
    tags:
      application: "${spring.application.name}"
cache:
  provider: "memory"
businessEventTopic: "unity2-PROD-core-access-event-udm-sec-tran-jrnl-out"
projectId: "12bb8fb6-ba3d-4615-9b0b-f4f5cb38656f"
accesses:
- persistenceEnabled: true
  sourceTopic: "unity2-PROD-core-transform-udm-sec-tran-jrnl-out"
  tableName: "udm-sec-tran-jrnl"
  columns:
  - name: "_trace_id"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "_id"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "created_by"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "created_time_stamp"
    dataType: "timestamp"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "deleted_by"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "deleted_time_stamp"
    dataType: "timestamp"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "updated_by"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "updated_time_stamp"
    dataType: "timestamp"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "_batch_id"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "JHCREF"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "JHXREF"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "JHTCAC"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "JHDLUP"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "JHTLUP"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "JHTXFT"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "JHTXFS"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "JHTXFQ"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "JHTXFX"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "JHSFTP"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "JHPSTS"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "transaction_id"
    dataType: "text"
    isIndexed: true
    isMultiple: false
    contentSchema: []
  - name: "site"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "partition_key"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  script: "SET SCHEMA '12bb8fb6_ba3d_4615_9b0b_f4f5cb38656f'; CREATE INDEX IF NOT\
    \ EXISTS \"idx_udm-sec-tran-jrnl_transaction_id\" ON \"udm-sec-tran-jrnl\"  (\"\
    transaction_id\");"
  scriptEnabled: true
  retention:
    retentionEnabled: false
  sourceBatchTopicSuffix: "-batch"
  targetBatchTopicSuffix: "-batch"
  ddlAllowed: true
externalDataSourceJdbcConfigs: {}
