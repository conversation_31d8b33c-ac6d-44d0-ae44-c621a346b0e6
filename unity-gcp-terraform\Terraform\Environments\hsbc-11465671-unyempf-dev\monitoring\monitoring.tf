##### ------------------------------- Running Steps ------------------------------------------####
# This module contains multiple modules depending on the team that is run for. For Global metrics provided for each team go to the folder
# terraform/dev/monitoring and run below command: 
# terraform apply/plan --var-file=./varfiles/global/monitoring.tfvars --var-file=../ibm.tfvars 


# For Team specific module there are required 2 additional steps: 
#  1. terraform init 
#  2. terraform workspaces new <name of the team> for example terraform workspaces new hina_dev -> names of the teams are the same as the var files provided for them under varfiles/<team>/team_name.tfvars
#  3. terraform init <again> this step will create state file for given team and not default 
#  4. Terraform plan/Apply/destroy --var-file=./varfiles/<team>/<team_env>.tfvars --var-file=../ibm.tfvars

##### ------------------------------- /Running Steps -----------------------------------------####
##### ------------------------------- Notification / Resource Groups -----------------------------------------####


resource "google_monitoring_notification_channel" "basic" {
  display_name = var.channel_recipients["display_name"]
  type         = var.channel_recipients["type"]
  labels  = {
    username = var.channel_recipients["username"],
    url      = var.channel_recipients["url"]
  }
  sensitive_labels {
    password = var.channel_recipients["password"]
  }
}

##### ------------------------------- GCP Logging Metrics -------------------------------------------------------####

resource "google_logging_metric" "logging_metric" {
  for_each = var.logs
  name     = each.value.name
  filter   = var.team_name == "" ? each.value.filter : format("%s%s\"", each.value.filter, var.team_name)
  metric_descriptor {
    metric_kind  = each.value.metric_kind
    value_type   = each.value.value_type
    unit         = each.value.unit
    display_name = each.value.display_name
    dynamic "labels" {
      for_each = each.value.labels
      content {
        key = labels.value["key"]
        description = labels.value["description"] 
        value_type = labels.value["value_type"]
      }
    }
  }  
  label_extractors = each.value.label_extractors
}


module "monitoring" {
  source = "../../../Modules/Monitoring"

  notification_channel = google_monitoring_notification_channel.basic.name
  metrics             = var.metrics
  project             = var.project
  api_key             = var.api_key
  team_name           = var.team_name
  
  depends_on = [
    google_monitoring_notification_channel.basic,
    google_logging_metric.logging_metric
  ]

}