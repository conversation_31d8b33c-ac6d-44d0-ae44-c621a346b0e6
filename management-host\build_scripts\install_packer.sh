#!/usr/bin/env bash
set -euo pipefail
echo "<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<< PACKER  >>>>>>>>>>>>>>>>>>>>>>>>>>>"
echo "[MGMT_HOST] starting packer installaton"
sudo unlink /sbin/packer
echo "[MGMT_HOST] unlinked packer from sbin"
cd $BUILD_PATH/build_tools/
echo "[MGMT_HOST] downloading packer from nexus"
wget --user="vagrant" --password="vagrant" "https://efx-nexus.systems.uk.hsbc:8082/nexus/service/local/repositories/Tools/content/tools/hashicorp/packer/hashicorp/packer_1.6.6/1.6.6/packer_1.6.6-1.6.6.zip"
sudo unzip $PACKER_VERSION
sudo mv packer $PACKER_PATH
sudo rm -rf $PACKER_VERSION
chmod 755 $PACKER_PATH/packer
echo "[MGMT_HOST]packer installation was completed"
