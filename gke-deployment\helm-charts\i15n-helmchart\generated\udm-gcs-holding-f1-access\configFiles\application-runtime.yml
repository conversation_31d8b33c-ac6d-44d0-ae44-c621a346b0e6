---
spring:
  datasource:
    url: "********************************/${DB_NAME}"
    username: "${DB_USER}"
    password: "${DB_PASSWORD}"
  jpa:
    show-sql: false
    generate-ddl: true
    properties:
      hibernate:
        dialect: "org.hibernate.dialect.PostgreSQLDialect"
        hbm2ddl:
          auto: "none"
  application:
    name: "udm-gcs-holding-f1-access"
kafkaConfig:
  auto.offset.reset: "latest"
  bootstrap.servers: "kafka-0.kafka-internal.ns-core-prod-apps.svc.cluster.local:9093,kafka-1.kafka-internal.ns-core-prod-apps.svc.cluster.local:9093,kafka-2.kafka-internal.ns-core-prod-apps.svc.cluster.local:9093"
  group.id: "${KAFKA_CONSUMER_GROUP}"
  schema.registry.url: "https://hkl20077608.hc.cloud.hk.hsbc:8082/"
  security.protocol: "SSL"
  ssl.keystore.location: "/hss/apps/certs/unity-microservices.jks"
  ssl.keystore.password: "${KAFKA_KEYSTORE_PASSWORD}"
  ssl.truststore.location: "/hss/apps/certs/unity-microservices.ts"
  ssl.truststore.password: "${KAFKA_TRUSTSTORE_PASSWORD}"
management:
  endpoints:
    web:
      exposure:
        include: "health,prometheus"
  metrics:
    tags:
      application: "${spring.application.name}"
  tracing:
    enabled: true
cache:
  provider: "kafka"
  address: "http://cache"
  topic: "unity2-PROD-core-cache-metric"
businessEventTopic: "unity2-PROD-core-access-event-GCS_HOLDING-out"
projectId: "12bb8fb6-ba3d-4615-9b0b-f4f5cb38656f"
accesses:
- persistenceEnabled: true
  sourceTopic: "unity2-PROD-core-transform-udm-gcs-holding-f1-out"
  tableName: "GCS_HOLDING"
  columns:
  - name: "_trace_id"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "_id"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "created_by"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "created_time_stamp"
    dataType: "timestamp"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "deleted_by"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "deleted_time_stamp"
    dataType: "timestamp"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "updated_by"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "updated_time_stamp"
    dataType: "timestamp"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "_batch_id"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "site"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "sec_id"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "sec_acc_id"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "registration"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "registration_description"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "security_account_number"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "system_country_code"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "fx_rate"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "business_date"
    dataType: "date"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "location_id"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "status"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "security_price_currency"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "security_price"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "settled_balance_security_currency"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "traded_balance_security_currency"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "traded_balance_report_currency"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "settled_balance_report_currency"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "settled_balance_quantity"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "settled_market_value_in_security_currency"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "settled_market_value_in_report_currency"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "traded_balance_quantity"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "traded_market_value_in_security_currency"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "traded_market_value_in_report_currency"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "registered"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "pending_corp_action_receipt"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "pending_corp_action_delivery"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "pending_del_hold_position"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "pending_receipt"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "restricted"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "on_loan"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "out_for_registration"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "pending_on_loan_del_hold_position"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "blocked_balance"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "blocked_corporate_action_balance"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "pending_borrowed_delivery_balance"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "pending_borrowed_receipt_balance"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "pending_on_loan_receipt_balance"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "in_transshipment_balance"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "associated_sub_account"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "instrument_id"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "country_loc_code"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "seq_no"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  script: "SET SCHEMA '12bb8fb6_ba3d_4615_9b0b_f4f5cb38656f'; CREATE INDEX IF NOT\
    \ EXISTS \"idx.udm_gcs_holding_id\" ON \"GCS_HOLDING\" USING btree (_id); CREATE\
    \ INDEX IF NOT EXISTS \"idx_udm_gcs_holding.site\" ON \"GCS_HOLDING\" USING btree\
    \ (site); CREATE INDEX IF NOT EXISTS \"idx_udm_gcs_holding.sec_acc_id\" ON \"\
    GCS_HOLDING\" USING btree (sec_acc_id); CREATE INDEX IF NOT EXISTS \"idx_udm_gcs_holding.location_id\"\
    \ ON \"GCS_HOLDING\" USING btree (location_id);"
  scriptEnabled: true
  retention:
    retentionEnabled: false
  sourceBatchTopicSuffix: "-batch"
  targetBatchTopicSuffix: "-batch"
  ddlAllowed: true
externalDataSourceJdbcConfigs: {}
