#!/usr/bin/bash
set -euo pipefail

echo "[mgm_host] Started stage3 build"

#bash $BUILD_PATH/ansible/install_ansible.sh
bash $BUILD_PATH/ansible/setup_ansible_repo.sh

export PATH="/usr/lib64/python3.6/site-packages:/usr/lib/python3.6/site-packages:/usr/local/lib/python3.6/site-packages:$PATH"
export PATH="/usr/local/bin:$PATH"
PYTHONPATH="$HOME/Scripts/:$PATH" 
whoami
ansible --version
echo $PATH
ansible-playbook $BUILD_PATH/build_tools/ansible/setup/lock_down.yml
#bash $BUILD_PATH/ansible/uninstall_ansible.sh
cat /etc/sysctl.conf
#sysctl -w net.ipv4.ip_forward=0
#echo 0 > /proc/sys/net/ipv4/ip_forward
echo "oc/sys/net/ipv4/ip_for"
echo 0 > /proc/sys/net/ipv4/ip_forward
cat /proc/sys/net/ipv4/ip_forward

echo "[mgm_host] Completed stage3 build"