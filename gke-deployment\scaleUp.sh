#!/bin/bash
if [ $# == 0 ]; then
    echo "usage: $0 [appName] [namespace]"
    exit 1
fi

app_name=$1
namespace=$2
echo -------------------------------------------------------------------------------------------------------------------------
result=`kubectl scale deploy ${app_name} -n ${namespace}  --replicas=1 2>&1`;
if [[ $? == 0 || $result == *"not found"  ]] ; then
  echo "Normal:" $result
else
  echo -------------------------------------------------------------------------------------------------------------------------
  exit 1
fi
echo -------------------------------------------------------------------------------------------------------------------------