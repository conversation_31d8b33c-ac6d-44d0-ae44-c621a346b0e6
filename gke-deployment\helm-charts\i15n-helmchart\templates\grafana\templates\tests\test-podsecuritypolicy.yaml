{{- if and .Values.testFramework.enabled .Values.rbac.pspEnabled }}
apiVersion: policy/v1beta1
kind: PodSecurityPolicy
metadata:
  name: {{ template "grafana.fullname" . }}-test
  labels:
    {{- include "grafana.labels" . | nindent 4 }}
spec:
  allowPrivilegeEscalation: true
  privileged: false
  hostNetwork: false
  hostIPC: false
  hostPID: false
  fsGroup:
    rule: RunAsAny
  seLinux:
    rule: RunAsAny
  supplementalGroups:
    rule: RunAsAny
  runAsUser:
    rule: RunAsAny
  volumes:
  - configMap
  - downwardAPI
  - emptyDir
  - projected
  - csi
  - secret
{{- end }}
