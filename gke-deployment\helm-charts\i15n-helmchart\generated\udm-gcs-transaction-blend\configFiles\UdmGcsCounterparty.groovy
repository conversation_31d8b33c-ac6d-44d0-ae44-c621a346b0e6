import com.poc.hss.fasttrack.service.LookupService
import com.poc.hss.fasttrack.transform.model.LookupRequest
import com.poc.hss.fasttrack.transform.model.TransformerRecord
import io.vertx.core.json.JsonObject
import org.slf4j.Logger
import org.slf4j.LoggerFactory

import java.util.function.BiConsumer
import java.util.stream.Collectors

class UdmGcsCounterparty {
    private static final Logger logger = LoggerFactory.getLogger(UdmGcsCounterparty.class)

    static BiConsumer<List<TransformerRecord>, LookupService> queryAndBlendCounterPartyData = (targetData, lookupService) -> {
        logger.info("Start Blending Counterparty data")
        String counterPartyBrokerIds = getCounterpartyId(targetData, UdmGcsTransactionConstant.COUNTERPARTY_BROKER_TYPE, UdmGcsTransactionConstant.COUNTERPARTY_BROKER_NAME)
        String counterPartyBuyerSellerIds = getCounterpartyId(targetData, UdmGcsTransactionConstant.COUNTERPARTY_BUYER_SELLER_TYPE, UdmGcsTransactionConstant.COUNTERPARTY_BUYER_SELLER_NAME)
        String counterPartyIntermediaryIds = getCounterpartyId(targetData, UdmGcsTransactionConstant.COUNTERPARTY_INTERMEDIARY_TYPE, UdmGcsTransactionConstant.COUNTERPARTY_INTERMEDIARY_NAME)
        if (counterPartyBrokerIds.isEmpty() && counterPartyBuyerSellerIds.isEmpty() && counterPartyIntermediaryIds.isEmpty()) {
            logger.info("Skipping counterParty lookup request as Broker ids, BuyerSeller ids, Intermediary ids are empty.")
            return
        }
        lookupAndBlendCounterparty(targetData, counterPartyBrokerIds, lookupService, UdmGcsTransactionConstant.COUNTERPARTY_BROKER_SWIFT_CODE, UdmGcsTransactionConstant.COUNTERPARTY_BROKER_TYPE, UdmGcsTransactionConstant.COUNTERPARTY_BROKER_NAME)
        lookupAndBlendCounterparty(targetData, counterPartyBuyerSellerIds, lookupService, UdmGcsTransactionConstant.COUNTERPARTY_BUYER_SELLER_SWIFT_CODE, UdmGcsTransactionConstant.COUNTERPARTY_BUYER_SELLER_TYPE, UdmGcsTransactionConstant.COUNTERPARTY_BUYER_SELLER_NAME)
        lookupAndBlendCounterparty(targetData, counterPartyIntermediaryIds, lookupService, UdmGcsTransactionConstant.COUNTERPARTY_INTERMEDIARY_SWIFT_CODE, UdmGcsTransactionConstant.COUNTERPARTY_INTERMEDIARY_TYPE, UdmGcsTransactionConstant.COUNTERPARTY_INTERMEDIARY_NAME)

    }

    static String getCounterpartyId(List<TransformerRecord> targetData, String counterpartyType, String counterpartyName) {
        String counterpartyIds = targetData.stream()
                .filter(record -> record.getData().getString(counterpartyType) != "")
                .map(record -> record.getData().getString(counterpartyName) + "-" + record.getData().getString(counterpartyType))
                .map(id -> "'" + id + "'")
                .distinct()
                .collect(Collectors.joining(","))

        if (counterpartyIds.isEmpty()) {
            logger.debug("Skipping counterParty lookup request as counterparty id for {}-{} is empty type {} is 'B'.", counterpartyName, counterpartyType, counterpartyType)
            return ""
        }
        return counterpartyIds
    }


    static lookupAndBlendCounterparty(List<TransformerRecord> targetData, String counterpartyIds, LookupService lookupService, String swift_code_key, String counterpartyType, String counterpartyName) {
        if (!counterpartyIds.isEmpty()) {
            String criteria = String.format(UdmGcsTransactionConstant._ID + " in (%s)", counterpartyIds)
            logger.debug("counterPartyLookupRequest criteria [" + criteria + "]")
            LookupRequest counterPartyLookupRequest = UdmGcsTransactionHelper.createLookupRequest(criteria, UdmGcsTransactionConstant.GCS_COUNTERPARTY, UdmGcsTransactionConstant.COUNTERPARTY_FIELDS)
            Map<String, JsonObject> counterPartyMap = lookupService.queryList(counterPartyLookupRequest).stream()
                    .collect(Collectors.toMap(json -> json.getString(UdmGcsTransactionConstant._ID), json -> json, (a, b) -> a))

            if (null == counterPartyMap || counterPartyMap.isEmpty()) {
                logger.debug("counterParty lookup request failed or no record for Criteria : [" + criteria + "]")
                return
            }
            logger.debug("Counterparty result for Criteria [" + criteria + "] is \n" + counterPartyMap.toString())
            blendCounterParty(targetData, counterPartyMap, swift_code_key, counterpartyType, counterpartyName)
        }
    }

    static blendCounterParty(List<TransformerRecord> targetData, Map<String, JsonObject> counterPartyMap, String swift_code_key, String counterpartyType, String counterpartyName) {
        targetData.forEach(record -> {
            String counterpartyId = String.join("-", record.getData().getString(counterpartyName), record.getData().getString(counterpartyType))
            if (counterPartyMap.containsKey(counterpartyId)) {
                JsonObject counterparty = counterPartyMap.get(counterpartyId)
                counterpartyDataMapping(counterparty, record.getData(), swift_code_key)
            }
        })
    }

    static void counterpartyDataMapping(JsonObject counterparty, JsonObject output, String swift_code_key) {
        output.put(swift_code_key, counterparty.getString("swift_bic").trim())
    }
}
