import com.poc.hss.fasttrack.transform.AbstractTransformer
import com.poc.hss.fasttrack.transform.model.TransformerBatchInputContext
import com.poc.hss.fasttrack.transform.model.TransformerRecord
import io.vertx.core.json.JsonObject
import org.apache.commons.lang3.StringUtils

import java.sql.SQLOutput
import java.util.stream.Collectors

class UdmGcsHoldingTransformer extends AbstractTransformer {

    @Override
    List<TransformerRecord> batchTransform(TransformerBatchInputContext context) {
        List<TransformerRecord> output = context.getRecords().stream()
                .map({ rec ->
                    JsonObject source = rec.getData()
                    final String site = StringUtils.trim(UdmGcsHoldingHelper.getSiteBySourceSystem(source.getString(UdmGcsHoldingConstant.SOURCE_SYSTEM_NAME)))
                    final String isin = StringUtils.trim(source.getString(UdmGcsHoldingConstant.ISIN))
                    final String security_account_number = StringUtils.trim(source.getString(UdmGcsHoldingConstant.SECURITY_ACCOUNT_NUMBER))
                    final String secAcctId = String.format("%s-%s-%s", StringUtils.upperCase(site), UdmGcsHoldingHelper.siteToAdminEntity(site),security_account_number)
                    final String locationCode = StringUtils.trim(source.getString(UdmGcsHoldingConstant.LOCATION_CODE))
                    final String registrationCode = StringUtils.trim(source.getString(UdmGcsHoldingConstant.REGISTRATION_CODE))
                    final String registrationDescription = StringUtils.trim(source.getString(UdmGcsHoldingConstant.REGISTRATION_DESCRIPTION))
                    final String fx_rate = getQuantity(source,UdmGcsHoldingConstant.FX_RATE)
                    final String id = String.format("%s-%s-%s-%s", secAcctId, isin, locationCode, registrationCode)
                    final Map<String, Double> quantityMap = getQuantityMap(source, UdmGcsHoldingConstant.getBalanceConstants())
                    // UNITNIDN-634
                    final String status = quantityMap.values().stream().allMatch(v -> v == 0.0)
                            ? UdmGcsHoldingConstant.STATUS_INACTIVE
                            : UdmGcsHoldingConstant.STATUS_ACTIVE

                    return TransformerRecord.from(rec)
                            .id(id)
                            .data(new JsonObject()
                                    .put("_id", id)
                                    .put("site", site)
                                    .put("sec_id", isin)
                                    .put("instrument_id", isin)
                                    .put("sec_acc_id", secAcctId)
                                    .put("registration", registrationCode)
                                    .put("registration_description", registrationDescription)
                                    .put("security_account_number", security_account_number)
                                    .put("system_country_code", StringUtils.trim(source.getString(UdmGcsHoldingConstant.SYSTEM_COUNTRY_CODE)))// try running otherwise get it from source
                                    .put("fx_rate", fx_rate)
                                    .put("business_date",StringUtils.trim(source.getString(UdmGcsHoldingConstant.BUSINESS_DATE)))
                                    .put("location_id", getTopLevelLocation(locationCode))
                                    .put("country_loc_code", getTopLevelLocation(locationCode))
                                    .put("status", status)
                                    .put("security_price_currency", tryParseCurrency(source.getString(UdmGcsHoldingConstant.SECURITY_PRICE_CURRENCY)))
                                    .put("security_price", new BigDecimal(source.getString(UdmGcsHoldingConstant.SECURITY_PRICE,"0.0")).setScale(9).toPlainString())
                                    .put("settled_balance_security_currency", tryParseCurrency(source.getString(UdmGcsHoldingConstant.SETTLED_BALANCE_SECURITY_CURRENCY)))
                                    .put("traded_balance_security_currency", tryParseCurrency(source.getString(UdmGcsHoldingConstant.TRADED_BALANCE_SECURITY_CURRENCY)))
                                    .put("traded_balance_report_currency", tryParseCurrency(source.getString(UdmGcsHoldingConstant.TRADED_BALANCE_REPORT_CURRENCY)))
                                    .put("settled_balance_report_currency", tryParseCurrency(source.getString(UdmGcsHoldingConstant.SETTLED_BALANCE_REPORT_CURRENCY)))
                                    .put("settled_balance_quantity", getQuantity(source, UdmGcsHoldingConstant.SETTLED_BALANCE_QUANTITY))
                                    .put("settled_market_value_in_security_currency", getQuantity(source, UdmGcsHoldingConstant.SETTLED_MARKET_VALUE_IN_SECURITY_CURRENCY))
                                    .put("settled_market_value_in_report_currency", getQuantity(source, UdmGcsHoldingConstant.SETTLED_MARKET_VALUE_IN_REPORT_CURRENCY))
                                    .put("traded_balance_quantity", getQuantity(source, UdmGcsHoldingConstant.TRADED_BALANCE_QUANTITY))
                                    .put("traded_market_value_in_security_currency", getQuantity(source, UdmGcsHoldingConstant.TRADED_MARKET_VALUE_IN_SECURITY_CURRENCY))
                                    .put("traded_market_value_in_report_currency", getQuantity(source, UdmGcsHoldingConstant.TRADED_MARKET_VALUE_IN_REPORT_CURRENCY))
                                    .put("registered", quantityMap.get(UdmGcsHoldingConstant.AVAILABLE_BALANCE))
                                    .put("pending_corp_action_receipt", quantityMap.get(UdmGcsHoldingConstant.PENDING_CORPORATE_ACTION_RECEIPT_BALANCE))
                                    .put("pending_corp_action_delivery", quantityMap.get(UdmGcsHoldingConstant.PENDING_CORPORATE_ACTION_DELIVERY_BALANCE))
                                    .put("pending_del_hold_position", quantityMap.get(UdmGcsHoldingConstant.PENDING_DELIVERY_BALANCE))
                                    .put("pending_receipt", quantityMap.get(UdmGcsHoldingConstant.PENDING_RECEIPT_BALANCE))
                                    .put("restricted", quantityMap.get(UdmGcsHoldingConstant.RESTRICTED_BALANCE))
                                    .put("on_loan", quantityMap.get(UdmGcsHoldingConstant.ON_LOAN_BALANCE))
                                    .put("out_for_registration", quantityMap.get(UdmGcsHoldingConstant.OUT_OF_REGISTRATION_BALANCE))
                                    .put("pending_on_loan_del_hold_position", quantityMap.get(UdmGcsHoldingConstant.PENDING_ON_LOAN_DELIVERY_BALANCE))
                                    .put("blocked_balance", quantityMap.get(UdmGcsHoldingConstant.BLOCKED_BALANCE))
                                    .put("blocked_corporate_action_balance", quantityMap.get(UdmGcsHoldingConstant.BLOCKED_CORPORATE_ACTION_BALANCE))
                                    .put("pending_borrowed_delivery_balance", quantityMap.get(UdmGcsHoldingConstant.PENDING_BORROWED_DELIVERY_BALANCE))
                                    .put("pending_borrowed_receipt_balance", quantityMap.get(UdmGcsHoldingConstant.PENDING_BORROWED_RECEIPT_BALANCE))
                                    .put("pending_on_loan_receipt_balance", quantityMap.get(UdmGcsHoldingConstant.PENDING_ON_LOAN_RECEIPT_BALANCE))
                                    .put("in_transshipment_balance", quantityMap.get(UdmGcsHoldingConstant.IN_TRANSSHIPMENT_BALANCE))
                                    .put("associated_sub_account", source.getString(UdmGcsHoldingConstant.ASSOCIATED_SUB_ACCOUNT,"").trim()))
                                    .build()
                })
                .collect(Collectors.toList())
        return output
    }

    private static String getQuantity(JsonObject source, String key) {
        return String.valueOf(new BigDecimal(StringUtils.trim(source.getString(key, "0.0"))))
    }

    private static Map<String, Double> getQuantityMap(JsonObject source, List<String> keys) {
        Map<String, Double> quantityMap = new HashMap<>();
        for (String key : keys) {
            quantityMap.put(key, getQuantity(source, key));
        }
        return quantityMap;
    }

    private static String getTopLevelLocation (final String location)
    {
        //
        // In GCS, a 3-letter location code (e.g. ABN) will have "sub-record" with 4-letter location code
        // (e.g. ABNC, ABNP, ABNS).  The main record and sub-record will share the same place of housekeeping,
        // and country code.
        //
        // Therefore, during dcc-holding-api aggregation, these records will be aggregated together anyway
        //
        // We always project the first 3 letter here because it's more convenient to make GCS holdings
        // always pointing directly to the parent location record.  This will save dcc-holding-api from
        // making an GCS ad-hoc call for parent lookup
        //
        final String topLevelLocation = (location == null || location.length() <= 3) ? location : location.substring(0, 3);
        return topLevelLocation;
    }

    private static String tryParseCurrency ( final String currencyStr ) {
        return StringUtils.isEmpty((StringUtils.trim(currencyStr)))
                ? "NOT_APPLICABLE"
                : UdmGcsHoldingConstant.CURRENCY_MAP.containsKey(currencyStr)
                ? currencyStr
                : "UNDEFINED"
    }
}