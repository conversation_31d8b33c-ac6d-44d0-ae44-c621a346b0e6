#!/bin/bash
helm ls &> /dev/null
if [ $? -ne 0 ]; then
    echo Failed to execute helm maybe due to proxy, please execute
    echo source ./1-setProxy.sh
    exit 1
fi
echo Show existing helm release
namespace=$(cat helm-charts/i15n-helmchart/generated/.namespace)
installed_charts=`helm ls --namespace ${namespace}|grep "i15n-helmchart"|awk '{print $1}'`
echo "$installed_charts" | while read installedChart ; do
   if [[ "$installedChart" == "NAME" || "$installedChart" == "" ]]; then
     continue
   fi
   echo "installedChart: $installedChart"
   found=0
   while read -r chart || [ -n "$chart" ]; do
       if [[ "$chart" == "$installedChart" ]]; then
           found=1
       fi
   done < helm-charts/i15n-helmchart/generated/.fullChartList
   while read -r chart || [ -n "$chart" ]; do
       if [[ "$chart" == "$installedChart" ]]; then
           found=1
       fi
   done < helm-charts/i15n-helmchart/generated/.coreChartList
   if [[ $found == 0 ]]; then
       echo "installed chart $installedChart should be uninstalled"
       helm uninstall $installedChart --namespace ${namespace}
   fi
done