#!/bin/bash
set -euo pipefail

# Deployment script for change-dashboard using gke-deployment
echo "=== Deploying change-dashboard to GKE ==="

# Configuration
APP_NAME="change-dashboard"
NAMESPACE="ns-i15n-dev"
MAX_HISTORY="5"
POST_ACTION="start"
ADDITIONAL_OPTIONS=""

# Check if gke-deployment directory exists
if [ ! -d "gke-deployment" ]; then
    echo "Error: gke-deployment directory not found"
    echo "Please ensure you're running this script from the repository root"
    exit 1
fi

# Change to gke-deployment directory
cd gke-deployment

# Check if Helm values exist
VALUES_FILE="helm-charts/i15n-helmchart/${APP_NAME}/values.yaml"
if [ ! -f "${VALUES_FILE}" ]; then
    echo "Error: Helm values file not found: ${VALUES_FILE}"
    echo "Please ensure the Helm values are created first"
    exit 1
fi

echo "=== Pre-deployment Checks ==="
echo "App Name: ${APP_NAME}"
echo "Namespace: ${NAMESPACE}"
echo "Values File: ${VALUES_FILE}"

# Verify kubectl connectivity
echo "Checking kubectl connectivity..."
kubectl cluster-info --request-timeout=10s > /dev/null || {
    echo "Error: Cannot connect to Kubernetes cluster"
    echo "Please ensure kubectl is configured and you have access to the cluster"
    exit 1
}

# Verify namespace exists
echo "Checking namespace ${NAMESPACE}..."
kubectl get namespace "${NAMESPACE}" > /dev/null 2>&1 || {
    echo "Warning: Namespace ${NAMESPACE} does not exist"
    echo "Creating namespace..."
    kubectl create namespace "${NAMESPACE}"
}

echo "=== Deploying with Helm ==="
# Use the existing deploy.sh script from gke-deployment
chmod +x ./deploy.sh
./deploy.sh "${APP_NAME}" "${NAMESPACE}" "${MAX_HISTORY}" "${POST_ACTION}" "${ADDITIONAL_OPTIONS}"

if [ $? -eq 0 ]; then
    echo "=== Deployment completed successfully ==="
    echo "Application: ${APP_NAME}"
    echo "Namespace: ${NAMESPACE}"
    
    echo "=== Checking deployment status ==="
    kubectl get deployment "${APP_NAME}" -n "${NAMESPACE}" || true
    kubectl get pods -l app.kubernetes.io/name="${APP_NAME}" -n "${NAMESPACE}" || true
    kubectl get service "${APP_NAME}" -n "${NAMESPACE}" || true
    
    echo "=== Getting application URL ==="
    if kubectl get ingress "${APP_NAME}" -n "${NAMESPACE}" > /dev/null 2>&1; then
        echo "Ingress URL:"
        kubectl get ingress "${APP_NAME}" -n "${NAMESPACE}" -o jsonpath='{.spec.rules[0].host}' || true
        echo ""
    fi
else
    echo "=== Deployment failed ==="
    exit 1
fi

cd ..
