art_reg_admin_members = [
  "serviceAccount:<EMAIL>",
  "group:<EMAIL>"
]
art_reg_reader_members = [
  "group:<EMAIL>",
  "serviceAccount:<EMAIL>"
]
art_reg_writer_members = [
  "serviceAccount:<EMAIL>",
  "group:<EMAIL>"
]

description  = "Artifact Registory eu.gcr.io"
gcp_region   = "asia-east2"
kms_key_name = "projects/hsbc-6320774-kms-dev/locations/europe/keyRings/ar/cryptoKeys/arSharedKey"
location     = "europe"
project_id   = "hsbc-********-unity-dev"
repository_id = "eu.gcr.io"
format = "DOCKER"
