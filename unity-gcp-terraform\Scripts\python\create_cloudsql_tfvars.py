import sys
import os
from jinja2 import Environment, FileSystemLoader

if __name__ == "__main__":
    project_name = sys.argv[1]
    project_env = sys.argv[2]
    cloudsql_zone = sys.argv[3]
    db_index = sys.argv[4]    
    gcp_project = sys.argv[5]
    db_backuplocation = sys.argv[6]
    cloudsql_region = '-'.join(cloudsql_zone.split("-")[:2])

    if cloudsql_region == "asia-east2":
      db_uid = "52e12a"
    elif cloudsql_region == "asia-south1" or cloudsql_region == "asia-south2":
      db_uid = "62e12a"
    elif cloudsql_region == "europe-west1" or cloudsql_region == "europe-west2" or cloudsql_region == "europe-west3":
      db_uid = "72e12a"
    
    template_path = 'cloudsql_tfvars.j2'
    cloudsql_dir = f"./Terraform/Environments/{gcp_project}/cloud_sql/varfiles/{project_name}"
    cloudsql_tfvar_dir = f"{cloudsql_dir}/{project_name}_{project_env}{db_index}.tfvars"
    env = Environment(loader = FileSystemLoader('./Terraform/Templates'), trim_blocks=True, lstrip_blocks=True)

    #if project_env == "prod":
    #    template_path = 'cloudsql_tfvars_prod.j2'
    #    cloudsql_dir = f"./Terraform/Environments/Prod/cloud_sql/varfiles/{project_name}/"
    #    cloudsql_tfvar_dir = f"{cloudsql_dir}/{project_name}_{project_env}.tfvars"
#
    template = env.get_template(template_path)
    os.makedirs(os.path.dirname(cloudsql_dir+'/'), exist_ok=True)

    if not os.path.exists(cloudsql_tfvar_dir):
      file=open(cloudsql_tfvar_dir, "w")
      file.write(template.render(project_name=project_name, project_env=project_env, cloudsql_zone=cloudsql_zone, db_index=db_index, db_uid=db_uid, gcp_project=gcp_project, db_backuplocation=db_backuplocation))
      file.close()
    else:
      print(f"skipping: output file already exists: {cloudsql_tfvar_dir}") 

    #template terraform base file
    output_dir = f"./Terraform/Environments/{gcp_project}/cloud_sql"
    template_dir = f"./Terraform/Templates/cloudsql"

    env = Environment(loader = FileSystemLoader(template_dir), trim_blocks=True, lstrip_blocks=True)
    for filename in os.listdir(template_dir):
      if filename.endswith('.j2'):
        template = env.get_template(filename)
        outfile = os.path.join(output_dir, filename.replace('.j2',''))
        if os.path.exists(outfile):
           print(f"skipping: output file already exists: {outfile}") 
           continue 
           
        with open(outfile,'w') as output:
            output.write(template.render(gcp_project=gcp_project))    
