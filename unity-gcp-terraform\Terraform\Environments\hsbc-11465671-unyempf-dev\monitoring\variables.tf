
variable "project" {
  type = string
}

variable "region" {
  type = string
}

variable "provider_zone" {
  type = string
}

variable "kms_id" {
  type        = string
  description = "project id to use encryption keys from"
}

variable "environment_type" {
  type = string
}
variable "bq_priv_group_email" {
  type        = string
  description = "Email of bq priv group in prod"
}

variable "unit" {
  type        = string
  description = "The unit in which the metric value is reported"
}

variable "key" {
  type        = string
  description = "The label key"
  default     = ""
}

variable "label_value_type" {
  type        = string
  description = "The type of data that can be assigned to the label"
  default     = ""
}

variable "label_description" {
  type        = string
  description = "The description of the label"
  default     = ""
}

variable "logs" {
  type = map(object({
    name         = string
    filter       = string
    metric_kind  = string
    value_type   = string
    unit         = string
    display_name = string
    labels       = list(map(string))
    label_extractors = map(string)
  }))
}

#for each loop
variable "metrics" {
  type = map(object({
    name                 = string
    object_name          = string
    alert_severity       = string
    combiner             = string
    filter               = string
    duration             = string
    comparison           = string
    cross_series_reducer = string
    alignment_period     = string
    per_series_aligner   = string
    threshold            = number
    trigger_count        = number
    group_by_fields      = list(string)
    api_key              = string
    enabled              = bool
  }))
}

variable "kms_project" {
  description = "project kms for deployment"
  type        = string
}

variable "channel_recipients" {
  type = map(string)
}

variable "api_key" {
  type = string
}

variable "team_name" {
  type = string
}