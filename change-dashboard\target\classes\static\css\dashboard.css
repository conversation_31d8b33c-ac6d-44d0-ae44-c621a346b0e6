/* HSBC Change Dashboard Styles */
:root {
    --hsbc-red: #db0011;
    --hsbc-dark-red: #b8000e;
    --hsbc-light-gray: #f5f5f5;
    --hsbc-dark-gray: #333333;
    --hsbc-medium-gray: #666666;
    --hsbc-border-gray: #ddd;
    --success-green: #28a745;
    --warning-orange: #fd7e14;
    --info-blue: #17a2b8;
    --shadow: 0 2px 4px rgba(0,0,0,0.1);
    --shadow-hover: 0 4px 8px rgba(0,0,0,0.15);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: var(--hsbc-light-gray);
    color: var(--hsbc-dark-gray);
    line-height: 1.6;
}

.dashboard-container {
    max-width: 95%;
    margin: 0 auto;
    padding: 20px;
    min-width: 1200px;
}

/* Header Styles */
.dashboard-header {
    background: linear-gradient(135deg, var(--hsbc-red), var(--hsbc-dark-red));
    color: white;
    padding: 30px;
    border-radius: 10px;
    margin-bottom: 30px;
    box-shadow: var(--shadow);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 20px;
}

.header-content h1 {
    font-size: 2.5rem;
    font-weight: 300;
    margin: 0;
}

.header-stats {
    display: flex;
    gap: 30px;
}

.stat-item {
    text-align: center;
}

.stat-label {
    display: block;
    font-size: 0.9rem;
    opacity: 0.9;
    margin-bottom: 5px;
}

.stat-value {
    display: block;
    font-size: 1.8rem;
    font-weight: bold;
}

/* Charts Section */
.charts-section {
    margin-bottom: 30px;
}

.charts-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
}

.chart-card {
    background: white;
    padding: 25px;
    border-radius: 10px;
    box-shadow: var(--shadow);
    transition: box-shadow 0.3s ease;
}

.chart-card:hover {
    box-shadow: var(--shadow-hover);
}

.chart-card h3 {
    margin-bottom: 20px;
    color: var(--hsbc-dark-gray);
    font-size: 1.3rem;
    text-align: center;
}

.chart-card canvas {
    max-height: 300px;
}

/* Filters Section */
.filters-section {
    margin-bottom: 30px;
}

.filters-container {
    background: white;
    padding: 20px;
    border-radius: 10px;
    box-shadow: var(--shadow);
    display: flex;
    gap: 20px;
    align-items: end;
    flex-wrap: wrap;
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.filter-group label {
    font-weight: 600;
    color: var(--hsbc-dark-gray);
    font-size: 0.9rem;
}

.filter-group select,
.filter-group input {
    padding: 8px 12px;
    border: 2px solid var(--hsbc-border-gray);
    border-radius: 5px;
    font-size: 0.9rem;
    min-width: 150px;
    transition: border-color 0.3s ease;
}

.filter-group select:focus,
.filter-group input:focus {
    outline: none;
    border-color: var(--hsbc-red);
}

.clear-btn {
    background: var(--hsbc-red);
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 5px;
    cursor: pointer;
    font-size: 0.9rem;
    transition: background-color 0.3s ease;
}

.clear-btn:hover {
    background: var(--hsbc-dark-red);
}

/* Loading and Error States */
.loading-indicator {
    text-align: center;
    padding: 50px;
    background: white;
    border-radius: 10px;
    box-shadow: var(--shadow);
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid var(--hsbc-border-gray);
    border-top: 4px solid var(--hsbc-red);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.error-message {
    text-align: center;
    padding: 30px;
    background: #f8d7da;
    color: #721c24;
    border-radius: 10px;
    border: 1px solid #f5c6cb;
}

.retry-btn {
    background: var(--hsbc-red);
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 5px;
    cursor: pointer;
    margin-top: 15px;
    transition: background-color 0.3s ease;
}

.retry-btn:hover {
    background: var(--hsbc-dark-red);
}

/* Table Section */
.table-section {
    background: white;
    border-radius: 10px;
    box-shadow: var(--shadow);
    overflow: hidden;
}

.table-container {
    overflow-x: auto;
}

.changes-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.9rem;
    table-layout: fixed;
}

/* Column width optimization for desktop */
.changes-table th:nth-child(1), /* Assignee */
.changes-table td:nth-child(1) {
    width: 12%;
    min-width: 150px;
}

.changes-table th:nth-child(2), /* Creator */
.changes-table td:nth-child(2) {
    width: 12%;
    min-width: 150px;
}

.changes-table th:nth-child(3), /* CR Number */
.changes-table td:nth-child(3) {
    width: 8%;
    min-width: 100px;
}

.changes-table th:nth-child(4), /* Title */
.changes-table td:nth-child(4) {
    width: 20%;
    min-width: 200px;
}

.changes-table th:nth-child(5), /* Status */
.changes-table td:nth-child(5) {
    width: 8%;
    min-width: 100px;
}

.changes-table th:nth-child(6), /* Type */
.changes-table td:nth-child(6) {
    width: 10%;
    min-width: 120px;
}

.changes-table th:nth-child(7), /* Owning Group */
.changes-table td:nth-child(7) {
    width: 10%;
    min-width: 120px;
}

.changes-table th:nth-child(8), /* Scheduled Start */
.changes-table td:nth-child(8) {
    width: 10%;
    min-width: 120px;
}

.changes-table th:nth-child(9), /* Scheduled End */
.changes-table td:nth-child(9) {
    width: 10%;
    min-width: 120px;
}

.changes-table th:nth-child(10), /* JIRA ID */
.changes-table td:nth-child(10) {
    width: 8%;
    min-width: 100px;
}

.changes-table th {
    background: var(--hsbc-red);
    color: white;
    padding: 15px 12px;
    text-align: left;
    font-weight: 600;
    position: sticky;
    top: 0;
    z-index: 10;
}

.changes-table th.sortable {
    cursor: pointer;
    user-select: none;
    position: relative;
    transition: background-color 0.2s ease;
}

.changes-table th.sortable:hover {
    background: var(--hsbc-dark-red);
}

.sort-indicator {
    margin-left: 5px;
    font-size: 0.8rem;
    opacity: 0.7;
}

.sort-indicator:after {
    content: '↕';
}

.sort-indicator.asc:after {
    content: '↑';
    opacity: 1;
}

.sort-indicator.desc:after {
    content: '↓';
    opacity: 1;
}

.changes-table td {
    padding: 12px;
    border-bottom: 1px solid var(--hsbc-border-gray);
    vertical-align: middle;
}

.changes-table tbody tr {
    transition: background-color 0.2s ease;
}

.changes-table tbody tr:hover {
    background-color: #f8f9fa;
}

/* Assignee Cell */
.assignee-cell {
    display: flex;
    align-items: center;
    gap: 10px;
}

.assignee-photo {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    object-fit: cover;
    border: 2px solid var(--hsbc-border-gray);
}

.assignee-info {
    display: flex;
    flex-direction: column;
}

.assignee-name {
    font-weight: 600;
    color: var(--hsbc-dark-gray);
}

.assignee-email {
    font-size: 0.8rem;
    color: var(--hsbc-medium-gray);
}

/* Status Badges */
.status-badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
}

.status-implement {
    background: #d4edda;
    color: #155724;
}

.status-scheduled {
    background: #d1ecf1;
    color: #0c5460;
}

.status-completed {
    background: #d4edda;
    color: #155724;
}

.status-cancelled {
    background: #f8d7da;
    color: #721c24;
}

.status-assess {
    background: #fff3cd;
    color: #856404;
}

.status-new {
    background: #cce5ff;
    color: #004085;
}

.status-review {
    background: #e2d9f3;
    color: #432874;
}

/* JIRA Link */
.jira-link {
    color: var(--hsbc-red);
    text-decoration: none;
    font-weight: 600;
    padding: 4px 8px;
    border-radius: 4px;
    transition: all 0.2s ease;
    display: inline-block;
}

.jira-link:hover {
    text-decoration: underline;
    background-color: rgba(219, 0, 17, 0.1);
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.jira-link:active {
    transform: translateY(0);
}

/* Group-based row colors */
.group-gbm-hss { background-color: #fff3cd; }
.group-gbm-fx { background-color: #d1ecf1; }
.group-gbm-rates { background-color: #d4edda; }
.group-gbm-credit { background-color: #f8d7da; }
.group-default { background-color: #e2e3e5; }

/* Desktop Optimization */
@media (min-width: 1920px) {
    .dashboard-container {
        max-width: 98%;
        padding: 30px;
    }

    .changes-table {
        font-size: 1rem;
    }

    .assignee-photo {
        width: 45px;
        height: 45px;
    }
}

/* Large Desktop */
@media (min-width: 1440px) and (max-width: 1919px) {
    .dashboard-container {
        max-width: 96%;
        padding: 25px;
    }
}

/* Tablet and smaller desktop */
@media (max-width: 1199px) {
    .dashboard-container {
        max-width: 100%;
        min-width: auto;
        padding: 15px;
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .header-content {
        flex-direction: column;
        text-align: center;
    }

    .charts-container {
        grid-template-columns: 1fr;
    }

    .filters-container {
        flex-direction: column;
        align-items: stretch;
    }

    .filter-group select,
    .filter-group input {
        min-width: auto;
    }

    .changes-table {
        font-size: 0.8rem;
    }

    .assignee-photo {
        width: 30px;
        height: 30px;
    }
}

@media (max-width: 480px) {
    .dashboard-container {
        padding: 10px;
    }
    
    .header-content h1 {
        font-size: 2rem;
    }
    
    .chart-card,
    .filters-container {
        padding: 15px;
    }
}
