package com.hsbc.changedashboard.service;

import com.hsbc.changedashboard.config.ApiConfig;
import com.hsbc.changedashboard.model.JiraIssue;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Service
public class JiraApiService {

    private static final Logger logger = LoggerFactory.getLogger(JiraApiService.class);
    private static final Pattern JIRA_PATTERN = Pattern.compile("\\[JIRA\\]:-([A-Za-z0-9]+-\\d+)", Pattern.CASE_INSENSITIVE);
    private static final Pattern JIRA_URL_PATTERN = Pattern.compile("https://gbmt-gojira\\.prd\\.fx\\.gbm\\.cloud\\.uk\\.hsbc/browse/([A-Za-z0-9]+-\\d+)", Pattern.CASE_INSENSITIVE);

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private ApiConfig apiConfig;

    /**
     * Extract JIRA ID from the description field
     * Supports two patterns:
     * 1. [JIRA]:-JIRA-ID
     * 2. https://gbmt-gojira.prd.fx.gbm.cloud.uk.hsbc/browse/JIRA-ID
     */
    public String extractJiraId(String description) {
        if (description == null || description.trim().isEmpty()) {
            return null;
        }

        // Try the [JIRA]:- pattern first
        Matcher matcher = JIRA_PATTERN.matcher(description);
        if (matcher.find()) {
            String jiraId = matcher.group(1);
            logger.debug("Extracted JIRA ID: {} from [JIRA]:- pattern", jiraId);
            return jiraId;
        }

        // Try the full URL pattern
        matcher = JIRA_URL_PATTERN.matcher(description);
        if (matcher.find()) {
            String jiraId = matcher.group(1);
            logger.debug("Extracted JIRA ID: {} from URL pattern", jiraId);
            return jiraId;
        }

        logger.debug("No JIRA ID found in description");
        return null;
    }

    /**
     * Get JIRA issue details by JIRA ID
     */
    public JiraIssue getJiraIssue(String jiraId) {
        if (jiraId == null || jiraId.trim().isEmpty()) {
            logger.warn("JIRA ID is null or empty, skipping JIRA API call");
            return null;
        }

        try {
            String url = UriComponentsBuilder.fromHttpUrl(apiConfig.getJira().getBaseUrl())
                    .path("/rest/api/latest/issue/{jiraId}")
                    .queryParam("fields", "key,summary,creator")
                    .buildAndExpand(jiraId)
                    .toUriString();

            HttpHeaders headers = new HttpHeaders();
            headers.set("Authorization", apiConfig.getJira().getAuthorization());
            headers.set("Cookie", apiConfig.getJira().getCookie());

            HttpEntity<String> entity = new HttpEntity<>(headers);

            logger.debug("Making request to JIRA API for issue: {}", jiraId);

            ResponseEntity<JiraIssue> response = restTemplate.exchange(
                    url, HttpMethod.GET, entity, JiraIssue.class);

            logger.debug("Successfully fetched JIRA issue: {}", jiraId);
            return response.getBody();

        } catch (Exception e) {
            logger.error("Error fetching JIRA issue: {}", jiraId, e);
            // Return null instead of throwing exception to allow processing to continue
            return null;
        }
    }
}
