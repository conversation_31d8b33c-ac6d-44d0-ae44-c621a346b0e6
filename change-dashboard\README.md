# Change Dashboard Microservice

A Spring Boot microservice that aggregates and enriches change data from multiple APIs (ICE, JIRA, and Poddy) to provide a comprehensive view of change records.

## Overview

This microservice follows a 9-step process to collect and enrich change data:

1. **ICE API Call**: Fetches changes scheduled for the current week (Monday to Monday)
2. **Data Capture**: Parses the ICE API response into structured objects
3. **JIRA ID Extraction**: Extracts JIRA IDs from change descriptions using regex pattern `\[JIRA\]:-([A-Z]+-\d+)`
4. **JIRA API Call**: Fetches JIRA issue details for each extracted JIRA ID
5. **JIRA Data Enrichment**: Adds JIRA key and summary to change records
6. **Staff ID Extraction**: Gets assignee staff IDs from change records
7. **Poddy API Call**: Fetches person information for each staff ID
8. **Person Data Enrichment**: Adds assignee name, email, and photo URL to change records
9. **Endpoint Exposure**: Provides REST endpoint to access enriched data

## API Endpoints

### Get Enriched Changes
```
GET /api/v1/changes/enriched
```

Returns enriched change data with JIRA and person information.

**Response Format:**
```json
{
  "status": "success",
  "count": 2,
  "timestamp": 1703123456789,
  "data": [
    {
      "snCrNumber": "CHG5179056",
      "gsdTitle": "NZM GHSS:NZM GHSS release SWIFT processing job after Public holiday",
      "gsdDescription": "...",
      "gsdStatus": "Implement",
      "gsdAssigneeUserStaffID": "43167442",
      "gsdScheduledStartDate": "2025-06-02T10:00:00Z",
      "gsdScheduledEndDate": "2025-06-02T22:00:00Z",
      "jiraKey": "DCCRTB-10807",
      "jiraSummary": "NZM GHSS release SWIFT processing job after Public holiday",
      "assigneeName": "Vishal Avad",
      "assigneeEmail": "<EMAIL>",
      "assigneePhotoUrl": "https://apprunner.hk.hsbc/ldap-photo-cache/photos/43963533"
    }
  ]
}
```

### Health Check
```
GET /api/v1/health
```

Returns service health status.

## Configuration

The application uses the following configuration in `application.yml`:

- **ICE API**: Base URL, authorization token, app IDs, and field specifications
- **JIRA API**: Base URL, authorization token, and session cookies
- **Poddy API**: Base URL (no authentication required)

## Date Logic

The service automatically calculates:
- **Current Week Monday**: Monday of the current week at 00:00:00 UTC
- **Next Week Monday**: Monday of the next week at 00:00:00 UTC

These dates are used to filter changes scheduled for the current week.

## Error Handling

- Individual API failures don't stop the entire process
- Failed JIRA or Poddy API calls result in null enrichment data
- Comprehensive logging for debugging
- Graceful degradation when external services are unavailable

## Running the Application

### Prerequisites
- Java 17 or higher
- Maven 3.6 or higher

### Build and Run
```bash
# Build the application
mvn clean compile

# Run tests
mvn test

# Start the application
mvn spring-boot:run
```

The application will start on port 8080 by default.

### Testing the Endpoint
```bash
# Test the health endpoint
curl http://localhost:8080/api/v1/health

# Test the enriched changes endpoint
curl http://localhost:8080/api/v1/changes/enriched
```

## Architecture

### Components

1. **Controllers**: REST endpoints (`ChangeController`)
2. **Services**: Business logic and API integration
   - `ChangeDataService`: Main orchestration service
   - `IceApiService`: ICE API integration
   - `JiraApiService`: JIRA API integration
   - `PoddyApiService`: Poddy API integration
   - `DateUtilService`: Date calculation utilities
3. **Models**: Data transfer objects for API responses
4. **Configuration**: API configuration and REST template setup

### Key Features

- **Asynchronous Processing**: Uses CompletableFuture for parallel API calls
- **Robust Error Handling**: Continues processing even if individual enrichments fail
- **Configurable**: All API endpoints and credentials externalized
- **Comprehensive Logging**: Detailed logging for monitoring and debugging
- **RESTful Design**: Standard REST API patterns

## Testing

The project includes unit tests for:
- Date utility functions
- JIRA ID extraction logic
- Application context loading

Run tests with:
```bash
mvn test
```

## Monitoring

The application includes:
- Health check endpoint for monitoring
- Comprehensive logging with different levels
- Request/response timing information
- Error tracking and reporting
