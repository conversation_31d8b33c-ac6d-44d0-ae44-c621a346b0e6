{"firewall_rules": [{"action": "ALLOW", "description": "Grants access to the Ingress Service", "direction": "EGRESS", "destination_ranges": ["***********/32"], "rules": [{"ports": ["443", "3128"], "protocol": "TCP"}], "priority": 101, "target_id": "gke-egress", "remote_id": "gke-ingress", "target_network_name": "hsbc-6320774-vpchost-asia-dev-cinternal-vpc1", "target_service_account": "<EMAIL>", "ticket_ref": "6448d9c70a"}, {"action": "ALLOW", "description": "Grants access to the HSBC GKE Managed Service management cluster", "direction": "EGRESS", "destination_ranges": ["***********/32"], "rules": [{"ports": ["443"], "protocol": "TCP"}], "priority": 101, "target_id": "hgke-cust", "remote_id": "hgke-mgmt", "target_network_name": "hsbc-6320774-vpchost-asia-dev-cinternal-vpc1", "target_service_account": "<EMAIL>", "ticket_ref": "c69a788072"}, {"action": "ALLOW", "description": "Grants access to the Egress Service on premise", "direction": "EGRESS", "destination_ranges": ["10.0.0.0/8", "*********/2"], "rules": [{"ports": ["0-65535"], "protocol": "TCP"}, {"ports": ["0-65535"], "protocol": "UDP"}], "target_id": "gke-egress", "remote_id": "gke-ingress", "target_network_name": "hsbc-6320774-vpchost-asia-dev-cinternal-vpc1", "target_service_account": "<EMAIL>", "ticket_ref": "csreportd-001"}], "firewall_rules_version": 2}