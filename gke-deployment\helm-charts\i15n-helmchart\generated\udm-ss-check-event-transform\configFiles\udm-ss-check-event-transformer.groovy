import com.poc.hss.fasttrack.transform.AbstractTransformer
import com.poc.hss.fasttrack.transform.model.TransformerBatchInputContext
import com.poc.hss.fasttrack.transform.model.TransformerRecord
import io.vertx.core.json.JsonObject
import org.slf4j.Logger
import org.slf4j.LoggerFactory

import java.util.stream.Collectors

class StockSufficientCheckEventlTransformer extends AbstractTransformer {
    JsonObject constructRecord(JsonObject source){
        try{
            return new JsonObject()
                    .put("site", source.getString("SITE")?.trim())
                    .put("SETXFT", source.getString("SETXFT")?.trim())
                    .put("SETXFS", source.getString("SETXFS")?.trim())
                    .put("SETXFQ", source.getString("SETXFQ")?.trim())
                    .put("SETXFX", source.getString("SETXFX")?.trim())
                    .put("SETRDT", source.getString("SETRDT")?.trim())
                    .put("SESSCE", source.getString("SESSCE")?.trim())
                    .put("transaction_id", String.format("%s-%s-%06d-%02d-%01d-%08d",source.getString("SITE")?.trim().toUpperCase(),source.getString("SETXFT")?.trim(),
                            getInteger(source.getString("SETXFS")?.trim()),getInteger(source.getString("SETXFQ")?.trim()),
                            getInteger(source.getString("SETXFX")?.trim()),getInteger(source.getString("SETRDT")?.trim())))
                    .put("partition_key", String.format("%s-%s-%06d-%02d-%01d",source.getString("SITE")?.trim().toUpperCase(),source.getString("SETXFT")?.trim(),
                            getInteger(source.getString("SETXFS")?.trim()),getInteger(source.getString("SETXFQ")?.trim()),
                            getInteger(source.getString("SETXFX")?.trim())))
                    .put("event_source", source.getString("FILE_NAME", "")?.trim())
        }catch(Exception e){
            logger.error("constructRecord failed! data:{} error:{}",source, ExceptionUtils.getStackTrace(e))
            throw e
        }
    }

    static Integer getInteger(String key) {
        if (key != null && !key.trim().equals("")) {
            return new BigDecimal(key).intValueExact();
        } else {
            return 0;
        }
    }

    private static final Logger logger = LoggerFactory.getLogger(StockSufficientCheckEventlTransformer.class)

    @Override
    List<TransformerRecord> batchTransform(TransformerBatchInputContext context) {
        List<TransformerRecord> records = context.getRecords()
        return records.stream().map({
            rec ->
                JsonObject source = rec.getData()
                JsonObject transformedSource = constructRecord(source)
                if(transformedSource == null){
                    return null
                }
                return rec.from(rec).data(transformedSource).kafkaPartitionKey(transformedSource.getString("partition_key")).build()
        }).filter(Objects::nonNull).collect(Collectors.toList())
    }
}
