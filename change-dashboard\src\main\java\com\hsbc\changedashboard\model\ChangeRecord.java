package com.hsbc.changedashboard.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.time.ZonedDateTime;
import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
public class ChangeRecord {
    
    @JsonProperty("snCrNumber")
    private String snCrNumber;
    
    @JsonProperty("gsdTitle")
    private String gsdTitle;
    
    @JsonProperty("gsdDescription")
    private String gsdDescription;
    
    @JsonProperty("gsdStatus")
    private String gsdStatus;
    
    @JsonProperty("gsdChangeType")
    private String gsdChangeType;
    
    @JsonProperty("gsdEnduringChange")
    private Boolean gsdEnduringChange;
    
    @JsonProperty("gsdClosedByStaffID")
    private String gsdClosedByStaffID;
    
    @JsonProperty("gsdClosedDate")
    private ZonedDateTime gsdClosedDate;
    
    @JsonProperty("gsdClosureCode")
    private String gsdClosureCode;
    
    @JsonProperty("gsdAppIDs")
    private List<Long> gsdAppIDs;
    
    @JsonProperty("gsdOwningGroup")
    private String gsdOwningGroup;
    
    @JsonProperty("gsdChangeOwnerStaffId")
    private String gsdChangeOwnerStaffId;
    
    @JsonProperty("gsdAssigneeUserStaffID")
    private String gsdAssigneeUserStaffID;
    
    @JsonProperty("gsdImplementingGroup")
    private String gsdImplementingGroup;
    
    @JsonProperty("gsdScheduledStartDate")
    private ZonedDateTime gsdScheduledStartDate;
    
    @JsonProperty("gsdScheduledEndDate")
    private ZonedDateTime gsdScheduledEndDate;
    
    @JsonProperty("gsdBusinessClosed")
    private Boolean gsdBusinessClosed;
    
    @JsonProperty("gsdCategory")
    private String gsdCategory;
    
    @JsonProperty("gsdRequiresPar")
    private Boolean gsdRequiresPar;
    
    @JsonProperty("gsdDeploymentToolNames")
    private List<String> gsdDeploymentToolNames;
    
    @JsonProperty("gsdIsProduction")
    private Boolean gsdIsProduction;
    
    @JsonProperty("postDeploymentVerificationEvidenceUrl")
    private String postDeploymentVerificationEvidenceUrl;
    
    @JsonProperty("actualDeploymentStartDate")
    private ZonedDateTime actualDeploymentStartDate;
    
    @JsonProperty("actualDeploymentEndDate")
    private ZonedDateTime actualDeploymentEndDate;
    
    @JsonProperty("gsdTechnicalImpact")
    private String gsdTechnicalImpact;
    
    @JsonProperty("gsdBusinessImpact")
    private String gsdBusinessImpact;
    
    @JsonProperty("gsdDataFrom")
    private String gsdDataFrom;

    // Constructors
    public ChangeRecord() {}

    // Getters and Setters
    public String getSnCrNumber() { return snCrNumber; }
    public void setSnCrNumber(String snCrNumber) { this.snCrNumber = snCrNumber; }

    public String getGsdTitle() { return gsdTitle; }
    public void setGsdTitle(String gsdTitle) { this.gsdTitle = gsdTitle; }

    public String getGsdDescription() { return gsdDescription; }
    public void setGsdDescription(String gsdDescription) { this.gsdDescription = gsdDescription; }

    public String getGsdStatus() { return gsdStatus; }
    public void setGsdStatus(String gsdStatus) { this.gsdStatus = gsdStatus; }

    public String getGsdChangeType() { return gsdChangeType; }
    public void setGsdChangeType(String gsdChangeType) { this.gsdChangeType = gsdChangeType; }

    public Boolean getGsdEnduringChange() { return gsdEnduringChange; }
    public void setGsdEnduringChange(Boolean gsdEnduringChange) { this.gsdEnduringChange = gsdEnduringChange; }

    public String getGsdClosedByStaffID() { return gsdClosedByStaffID; }
    public void setGsdClosedByStaffID(String gsdClosedByStaffID) { this.gsdClosedByStaffID = gsdClosedByStaffID; }

    public ZonedDateTime getGsdClosedDate() { return gsdClosedDate; }
    public void setGsdClosedDate(ZonedDateTime gsdClosedDate) { this.gsdClosedDate = gsdClosedDate; }

    public String getGsdClosureCode() { return gsdClosureCode; }
    public void setGsdClosureCode(String gsdClosureCode) { this.gsdClosureCode = gsdClosureCode; }

    public List<Long> getGsdAppIDs() { return gsdAppIDs; }
    public void setGsdAppIDs(List<Long> gsdAppIDs) { this.gsdAppIDs = gsdAppIDs; }

    public String getGsdOwningGroup() { return gsdOwningGroup; }
    public void setGsdOwningGroup(String gsdOwningGroup) { this.gsdOwningGroup = gsdOwningGroup; }

    public String getGsdChangeOwnerStaffId() { return gsdChangeOwnerStaffId; }
    public void setGsdChangeOwnerStaffId(String gsdChangeOwnerStaffId) { this.gsdChangeOwnerStaffId = gsdChangeOwnerStaffId; }

    public String getGsdAssigneeUserStaffID() { return gsdAssigneeUserStaffID; }
    public void setGsdAssigneeUserStaffID(String gsdAssigneeUserStaffID) { this.gsdAssigneeUserStaffID = gsdAssigneeUserStaffID; }

    public String getGsdImplementingGroup() { return gsdImplementingGroup; }
    public void setGsdImplementingGroup(String gsdImplementingGroup) { this.gsdImplementingGroup = gsdImplementingGroup; }

    public ZonedDateTime getGsdScheduledStartDate() { return gsdScheduledStartDate; }
    public void setGsdScheduledStartDate(ZonedDateTime gsdScheduledStartDate) { this.gsdScheduledStartDate = gsdScheduledStartDate; }

    public ZonedDateTime getGsdScheduledEndDate() { return gsdScheduledEndDate; }
    public void setGsdScheduledEndDate(ZonedDateTime gsdScheduledEndDate) { this.gsdScheduledEndDate = gsdScheduledEndDate; }

    public Boolean getGsdBusinessClosed() { return gsdBusinessClosed; }
    public void setGsdBusinessClosed(Boolean gsdBusinessClosed) { this.gsdBusinessClosed = gsdBusinessClosed; }

    public String getGsdCategory() { return gsdCategory; }
    public void setGsdCategory(String gsdCategory) { this.gsdCategory = gsdCategory; }

    public Boolean getGsdRequiresPar() { return gsdRequiresPar; }
    public void setGsdRequiresPar(Boolean gsdRequiresPar) { this.gsdRequiresPar = gsdRequiresPar; }

    public List<String> getGsdDeploymentToolNames() { return gsdDeploymentToolNames; }
    public void setGsdDeploymentToolNames(List<String> gsdDeploymentToolNames) { this.gsdDeploymentToolNames = gsdDeploymentToolNames; }

    public Boolean getGsdIsProduction() { return gsdIsProduction; }
    public void setGsdIsProduction(Boolean gsdIsProduction) { this.gsdIsProduction = gsdIsProduction; }

    public String getPostDeploymentVerificationEvidenceUrl() { return postDeploymentVerificationEvidenceUrl; }
    public void setPostDeploymentVerificationEvidenceUrl(String postDeploymentVerificationEvidenceUrl) { this.postDeploymentVerificationEvidenceUrl = postDeploymentVerificationEvidenceUrl; }

    public ZonedDateTime getActualDeploymentStartDate() { return actualDeploymentStartDate; }
    public void setActualDeploymentStartDate(ZonedDateTime actualDeploymentStartDate) { this.actualDeploymentStartDate = actualDeploymentStartDate; }

    public ZonedDateTime getActualDeploymentEndDate() { return actualDeploymentEndDate; }
    public void setActualDeploymentEndDate(ZonedDateTime actualDeploymentEndDate) { this.actualDeploymentEndDate = actualDeploymentEndDate; }

    public String getGsdTechnicalImpact() { return gsdTechnicalImpact; }
    public void setGsdTechnicalImpact(String gsdTechnicalImpact) { this.gsdTechnicalImpact = gsdTechnicalImpact; }

    public String getGsdBusinessImpact() { return gsdBusinessImpact; }
    public void setGsdBusinessImpact(String gsdBusinessImpact) { this.gsdBusinessImpact = gsdBusinessImpact; }

    public String getGsdDataFrom() { return gsdDataFrom; }
    public void setGsdDataFrom(String gsdDataFrom) { this.gsdDataFrom = gsdDataFrom; }
}
