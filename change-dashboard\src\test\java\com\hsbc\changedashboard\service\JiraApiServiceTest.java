package com.hsbc.changedashboard.service;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest
class JiraApiServiceTest {

    @Autowired
    private JiraApiService jiraApiService;

    @Test
    void testExtractJiraId() {
        // Test with valid JIRA ID
        String description1 = "To release the NZM GHSS SWIFTIN handler jobs held on 31May.\r\n\r\n \r\n\r\nIT questionie:\r\n # Business impact: There is no GHSS logic change. Release the SWIFTIN job which was held on 31May to resume SWIFT processing\r\n # Technical impact: There is no IT component logic being change.  Releasing the SWIFTIN job will not impact the system, messages would be processed when job resumed. \r\n # Is Implementation plan available? Yes.\r\n # Is PVT available? IT health check would be conducted right after the release. User PVT will be on 3Jun morning.\r\n # Is Backout plan available? Yes.\r\n\r\n \n[JIRA]:-DCCRTB-10807\n[Application]:-NZM GHSS-GO";
        String jiraId1 = jiraApiService.extractJiraId(description1);
        assertEquals("DCCRTB-10807", jiraId1);

        // Test with another valid JIRA ID
        String description2 = "[MESH]\r\n\r\nTrade Data in ODS/Mesh with new loosely coupled approach, which allows easy rollout of new markets\r\n---------------------------------------------------------------------------\r\n\r\nMake new trades data available to outside teams and products by switching trades view to point to the new trades table.\r\n\r\nAt the moment new trades data is available under all_markets_trade_view_v2 view.\r\n\r\nReplace all_markets_trade_view with all_markets_trade_view_v2.\n[JIRA]:-BBDRPT-11315\n[Application]:-Non-prod-UNITY2.0-MESH-CUSTODY-ANSIBLE-NOTPAM";
        String jiraId2 = jiraApiService.extractJiraId(description2);
        assertEquals("BBDRPT-11315", jiraId2);

        // Test with lowercase JIRA ID (CHG5196873 example)
        String description3 = "1) Add 'TDR' Types to Giltlink list in GCCS978\r\n\r\nAn issue has been raised in BOXI where they have failed to reconcile DBV Crest positions. It was identified that this was down to ATXP 'TDR' types not being sent to Precis in the daily feed if the ATSP message had come from Crest via Giltlink.\r\n\r\nThere is code in GCS that determines which ATXP types need an ATXQ to be returned, if GCS does not recognise the reference and 'TDR' type is not one of them.\r\n\r\nA change is being made in the GCS Crest module GCCS978 to add 'TDR' to the list that generates a ATXQ message, should it have been received from Giltlink.\r\n\r\n[JIRA]:-gcsuk-1104\r\n[Application]:-Online";
        String jiraId3 = jiraApiService.extractJiraId(description3);
        assertEquals("gcsuk-1104", jiraId3);

        // Test with HSSAI4SD JIRA ID (CHG5184777 example)
        String description4 = "Created this ticket for Bi-weekly auto Clouseau scanning on AI4 production hosts (App & Web & Microservice & DB) via G3-Ansible.\r\n\r\nScanning hosts:\r\n\r\nhkl20044744\r\nhkl20044745\r\nhkl20044746\r\nhkl20044747\r\nhkl25032303\r\nhkl25032304\r\nhkl25032305\r\nhkl25032306\r\nhkl25150454\r\nhkl25150455\r\nhkl25150456\r\nhkl25150460\r\nhkl25150487\r\nhkl25150515\r\nhkl25150517\r\nhkl25150508\r\nhkl25054242\r\nhkl25054243\r\nhkl25054244\r\nhkl25054245\r\n[JIRA]:-HSSAI4SD-3023\r\n[Application]:-ClouseauScan";
        String jiraId4 = jiraApiService.extractJiraId(description4);
        assertEquals("HSSAI4SD-3023", jiraId4);

        // Test with no JIRA ID
        String description5 = "Some description without JIRA ID";
        String jiraId5 = jiraApiService.extractJiraId(description5);
        assertNull(jiraId5);

        // Test with null description
        String jiraId6 = jiraApiService.extractJiraId(null);
        assertNull(jiraId6);

        // Test with empty description
        String jiraId7 = jiraApiService.extractJiraId("");
        assertNull(jiraId7);
    }

    @Test
    void testJiraUrlGeneration() {
        // Test URL generation for different JIRA IDs
        String baseUrl = "https://gbmt-gojira.prd.fx.gbm.cloud.uk.hsbc/browse/";

        // Test uppercase JIRA ID
        String jiraId1 = "TREASURY-462";
        String expectedUrl1 = baseUrl + jiraId1;
        assertEquals("https://gbmt-gojira.prd.fx.gbm.cloud.uk.hsbc/browse/TREASURY-462", expectedUrl1);

        // Test lowercase JIRA ID
        String jiraId2 = "gcsuk-1104";
        String expectedUrl2 = baseUrl + jiraId2;
        assertEquals("https://gbmt-gojira.prd.fx.gbm.cloud.uk.hsbc/browse/gcsuk-1104", expectedUrl2);

        // Test mixed case JIRA ID
        String jiraId3 = "DccRtb-10807";
        String expectedUrl3 = baseUrl + jiraId3;
        assertEquals("https://gbmt-gojira.prd.fx.gbm.cloud.uk.hsbc/browse/DccRtb-10807", expectedUrl3);

        // Test alphanumeric JIRA ID
        String jiraId4 = "HSSAI4SD-3023";
        String expectedUrl4 = baseUrl + jiraId4;
        assertEquals("https://gbmt-gojira.prd.fx.gbm.cloud.uk.hsbc/browse/HSSAI4SD-3023", expectedUrl4);
    }

    @Test
    void testExtractJiraId_AdditionalPatterns() {
        // Test various JIRA ID patterns that might exist

        // Test with numbers in project key
        String description1 = "Some description\n[JIRA]:-PROJECT123-456\n[Application]:-Test";
        String result1 = jiraApiService.extractJiraId(description1);
        assertEquals("PROJECT123-456", result1);

        // Test with mixed alphanumeric
        String description2 = "Some description\n[JIRA]:-ABC123DEF-789\n[Application]:-Test";
        String result2 = jiraApiService.extractJiraId(description2);
        assertEquals("ABC123DEF-789", result2);

        // Test with single letter project
        String description3 = "Some description\n[JIRA]:-A-123\n[Application]:-Test";
        String result3 = jiraApiService.extractJiraId(description3);
        assertEquals("A-123", result3);
    }

    @Test
    void testExtractJiraId_UrlPatterns() {
        // Test JIRA URL pattern extraction

        // Test with TANGRAM-694 example
        String description1 = "Some description with full URL\nhttps://gbmt-gojira.prd.fx.gbm.cloud.uk.hsbc/browse/TANGRAM-694\nMore text";
        String result1 = jiraApiService.extractJiraId(description1);
        assertEquals("TANGRAM-694", result1);

        // Test with mixed case URL
        String description2 = "Description\nhttps://gbmt-gojira.prd.fx.gbm.cloud.uk.hsbc/browse/ProjectABC-123\nEnd";
        String result2 = jiraApiService.extractJiraId(description2);
        assertEquals("ProjectABC-123", result2);

        // Test with alphanumeric project in URL
        String description3 = "Text\nhttps://gbmt-gojira.prd.fx.gbm.cloud.uk.hsbc/browse/HSS123AI4SD-456\nMore";
        String result3 = jiraApiService.extractJiraId(description3);
        assertEquals("HSS123AI4SD-456", result3);

        // Test that [JIRA]:- pattern takes precedence over URL pattern
        String description4 = "Description\n[JIRA]:-PRIORITY-123\nhttps://gbmt-gojira.prd.fx.gbm.cloud.uk.hsbc/browse/SECONDARY-456\nEnd";
        String result4 = jiraApiService.extractJiraId(description4);
        assertEquals("PRIORITY-123", result4); // Should return the [JIRA]:- pattern first

        // Test with URL pattern only (no [JIRA]:- pattern)
        String description5 = "Only URL pattern here:\nhttps://gbmt-gojira.prd.fx.gbm.cloud.uk.hsbc/browse/URLONLY-789";
        String result5 = jiraApiService.extractJiraId(description5);
        assertEquals("URLONLY-789", result5);
    }
}
