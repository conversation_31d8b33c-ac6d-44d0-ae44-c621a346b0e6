#!/bin/bash
if [[ -n "$CLOUDSQL_ENDPOINT"  ]] ; then
  while ! < /dev/tcp/$CLOUDSQL_ENDPOINT ; do echo "Waiting" && sleep 1; done
fi

if [[ -n "$EXTERNAL_CLOUD_ENDPOINT" ]]; then
  IFS=','
  read -a array <<< "$EXTERNAL_CLOUD_ENDPOINT"
  for i in "${array[@]}"
  do
     while ! < /dev/tcp/$i ; do echo "Waiting" && sleep 1; done
  done
fi
#springboot 3.2 is bundled with java 21 upgrade
java_version=$(java -version 2>&1 | awk -F[\"_] '/version/ {print $2}')
java_major_version=$(echo "$java_version" | awk -F. '{print $1}')
if [[ "$java_major_version" -ge 21 ]]; then
  _launch_class_=org.springframework.boot.loader.launch.JarLauncher
else  
  _launch_class_=org.springframework.boot.loader.JarLauncher  
fi
java -Dserver.port=8080 -Dspring.config.additional-location=file:/hss/apps/config/ \
      -XX:+PrintFlagsFinal -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=/tmp/dumps/dump.hprof \
      ${@:2} \
      -cp /hss/apps/config:$1 \
      ${_launch_class_}