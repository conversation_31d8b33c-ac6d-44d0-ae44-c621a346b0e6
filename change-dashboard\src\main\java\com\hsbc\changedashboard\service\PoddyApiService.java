package com.hsbc.changedashboard.service;

import com.hsbc.changedashboard.config.ApiConfig;
import com.hsbc.changedashboard.model.PersonInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

@Service
public class PoddyApiService {

    private static final Logger logger = LoggerFactory.getLogger(PoddyApiService.class);

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private ApiConfig apiConfig;

    /**
     * Get person information by staff ID
     */
    public PersonInfo getPersonInfo(String staffId) {
        if (staffId == null || staffId.trim().isEmpty()) {
            logger.warn("Staff ID is null or empty, skipping Poddy API call");
            return null;
        }

        // Validate staff ID format (should be 8 characters)
        if (staffId.length() != 8) {
            logger.warn("Invalid staff ID format: {}, expected 8 characters", staffId);
            return null;
        }

        try {
            String url = UriComponentsBuilder.fromHttpUrl(apiConfig.getPoddy().getBaseUrl())
                    .path("/poddy/people/{staffId}")
                    .buildAndExpand(staffId)
                    .toUriString();

            HttpHeaders headers = new HttpHeaders();
            // No authorization required for Poddy API based on the example

            HttpEntity<String> entity = new HttpEntity<>(headers);

            logger.debug("Making request to Poddy API for staff ID: {}", staffId);

            ResponseEntity<PersonInfo> response = restTemplate.exchange(
                    url, HttpMethod.GET, entity, PersonInfo.class);

            logger.debug("Successfully fetched person info for staff ID: {}", staffId);
            return response.getBody();

        } catch (Exception e) {
            logger.error("Error fetching person info for staff ID: {}", staffId, e);
            // Return null instead of throwing exception to allow processing to continue
            return null;
        }
    }
}
