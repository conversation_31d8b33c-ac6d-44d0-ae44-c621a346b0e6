#!/usr/bin/env bash
set -euo pipefail

echo "[mgm_host] installing auditd"

sudo yum install -y audit

echo "[mgm] installed auditd"

cat > /etc/audit/rules.d/z-custom.rules <<EOF
-b 65536
-w /var/lib/docker/ -p wa
-w /etc/docker/ -p wa
-w /lib/systemd/system/docker.service -p wa
-w /lib/systemd/system/docker.socket -p wa
-w /etc/default/docker -p wa
-w /etc/docker/daemon.json -p wa
-w /usr/bin/docker-containerd -p wa
-w /usr/bin/docker-runc -p wa
-w /usr/bin/containerd -p wa
-w /var/lib/containerd/ -p wa
-w /usr/lib/systemd/system/docker.socket -p wa
-w /usr/lib/systemd/system/docker.service -p wa
-a never,exclude -F AUID=unset -F exe=/usr/bin/dockerd
EOF

#-w /var/lib/docker/ -p wa
#-w /etc/docker/ -p wa
#-w /lib/systemd/system/docker.service -p wa
#-w /lib/systemd/system/docker.socket -p wa
#-w /etc/default/docker -p wa
#-w /etc/docker/daemon.json -p wa
#-w /usr/bin/docker-containerd -p wa
#-w /usr/bin/docker-runc -p wa
#-w /usr/bin/containerd -p wa
#-w /var/lib/containerd/ -p wa
#-w /usr/lib/systemd/system/docker.socket -p wa
#-w /usr/lib/systemd/system/docker.service -p wa


#echo "disabling the ip forward"
#cat > /etc/sysctl.conf  <<EOF
#net.ipv4.ip_forward = 0
#EOF

#cat /etc/sysctl.conf
#echo "completed disabling forwarding"

echo "[mgm_host] audit rules setting completed"
echo "doing cat for audit.rule/z-custom.rules"

cat /etc/audit/rules.d/z-custom.rules
echo "completed audit rules"
