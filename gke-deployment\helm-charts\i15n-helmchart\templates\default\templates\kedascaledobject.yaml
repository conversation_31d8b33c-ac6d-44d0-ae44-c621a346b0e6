{{- if .Values.supportAttributes.autoScalingType }}
{{- if eq .Values.supportAttributes.autoScalingType "TOPIC_LAG_AUTOSCALING" }}
{{- if .Capabilities.APIVersions.Has "keda.sh/v1alpha1" -}}
{{- $sourceTopics := split "," .Values.supportAttributes.sourceTopics }}
apiVersion: keda.sh/v1alpha1
kind: ScaledObject
metadata:
  name: {{ include "i15n-helmchart.fullname" . }}
  namespace: {{ .Values.namespace }}
  annotations:
    autoscaling.keda.sh/paused: "{{.Values.keda.paused}}"
  labels:
    {{- include "i15n-helmchart.labels" . | nindent 4 }}
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: {{ include "i15n-helmchart.fullname" . }}
  pollingInterval: 15
  minReplicaCount: {{ $.Values.supportAttributes.minReplicas }}
  maxReplicaCount: {{ $.Values.supportAttributes.maxReplicas }}
  triggers:
{{- if eq $.Values.supportAttributes.componentType "CONSUMER_ADAPTOR" }}
    - type: kafka
      metadata:
        bootstrapServers: "{{ $.Values.supportAttributes.kafkaBootstrapServer }}"
        consumerGroup: "{{ $.Values.supportAttributes.consumerGroup }}"
        {{- if eq (len $sourceTopics)  1 }}
        topic: "{{ $.Values.supportAttributes.sourceTopics }}{{ $.Values.supportAttributes.sourceBatchTopicSuffix }}"
        {{- end }}
        lagThreshold: "{{ $.Values.supportAttributes.lagFactor }}"
        tls: enable
        offsetResetPolicy: earliest
        activationLagThreshold: "0"
        cooldownPeriod: "240"
        limitToPartitionsWithLag: "true"
      authenticationRef:
        name: keda-trigger-auth-kafka-credential
{{- end }}
    - type: kafka
      metadata:
        bootstrapServers: "{{ $.Values.supportAttributes.kafkaBootstrapServer }}"
        consumerGroup: "{{ $.Values.supportAttributes.consumerGroup }}"
        {{- if eq (len $sourceTopics)  1 }}
        topic: "{{ $.Values.supportAttributes.sourceTopics }}"
        {{- end }}
        lagThreshold: "{{ $.Values.supportAttributes.lagFactor }}"
        tls: enable
        offsetResetPolicy: earliest
        activationLagThreshold: "0"
        cooldownPeriod: "240"
        limitToPartitionsWithLag: "true"
      authenticationRef:
        name: keda-trigger-auth-kafka-credential
{{- end }}
{{- end }}
{{- end }}