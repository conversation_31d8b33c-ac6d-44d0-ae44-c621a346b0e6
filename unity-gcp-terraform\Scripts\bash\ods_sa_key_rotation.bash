#!/bin/bash

function options() {
while [[ "$#" -gt 0 ]]; 
do
  case $1 in 
  --config=*)
    config="${1#*=}"
    ;; 
  --config)
    config="${2}"
    shift
    ;;      
  --help)
    print_usage
    exit
    ;;
  esac
  shift;
done
}

function setup_kubeconfig(){
  unset HTTPS_PROXY
  project_id=`gcloud config list --format 'value(core.project)'`
  gsutil cp gs://${project_id}-app-runtime-dependencies/secrets/env/kop/kubeconfig/${namespace}.${clustername} ./${namespace}.${clustername}.kubeconfig  
  export KUBECONFIG=./${namespace}.${clustername}.kubeconfig  
}

function update_secrets(){
  patch_file=$(mktemp)
  sa=runtime-gke
  gsutil cp gs://${project_id}-key-management/${sa}/${sa}.json ./${sa}.json
  cat > $patch_file <<-EOT
data:
  runtime-gke: $(base64 --wrap=0 ./${sa}.json)
EOT
  kubectl patch secrets --patch-file $patch_file -n $namespace unity2-apps-certs-secret-i15n --dry-run=client -o yaml | kubectl apply -f - 
  rm -f ${sa}.json $patch_file
}

function restart_deployments(){
  if [[ "$restart_type" == "label" ]]; then 
      kubectl rollout restart deployment -l ${conditions} -n ${namespace}
  else
    for d in $(echo $conditions | sed 's|,| |g'); do 
      kubectl rollout restart deployment/$d -n $namespace
    done
  fi
}

##########################################
# MAIN 
##########################################
options "$@"
for c in $(echo $config | tr '|' ' '); do 
  clustername=$(echo $c|cut -f1 -d,) 
  namespace=$(echo $c|cut -f2 -d,) 
  additional_parameters=$(echo $c|cut -f3- -d,)
  restart_type=$(echo ${additional_parameters}|cut -f1 -d=)
  conditions=$(echo ${additional_parameters}|cut -f2- -d=)
  setup_kubeconfig
  update_secrets
  restart_deployments
  rm -f ${KUBECONFIG}
done
