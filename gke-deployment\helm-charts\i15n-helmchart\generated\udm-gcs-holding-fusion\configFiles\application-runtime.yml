---
spring:
  datasource:
    url: "********************************/${DB_NAME}"
    username: "${DB_USER}"
    password: "${DB_PASSWORD}"
  jpa:
    show-sql: false
    generate-ddl: true
    properties:
      hibernate:
        dialect: "org.hibernate.dialect.PostgreSQLDialect"
        hbm2ddl:
          auto: "none"
  application:
    name: "udm-gcs-holding-fusion"
kafkaConfig:
  auto.offset.reset: "latest"
  bootstrap.servers: "kafka-0.kafka-internal.ns-core-prod-apps.svc.cluster.local:9093,kafka-1.kafka-internal.ns-core-prod-apps.svc.cluster.local:9093,kafka-2.kafka-internal.ns-core-prod-apps.svc.cluster.local:9093"
  group.id: "${KAFKA_CONSUMER_GROUP}"
  schema.registry.url: "https://hkl20077608.hc.cloud.hk.hsbc:8082/"
  security.protocol: "SSL"
  ssl.keystore.location: "/hss/apps/certs/unity-microservices.jks"
  ssl.keystore.password: "${KAFKA_KEYSTORE_PASSWORD}"
  ssl.truststore.location: "/hss/apps/certs/unity-microservices.ts"
  ssl.truststore.password: "${KAFKA_TRUSTSTORE_PASSWORD}"
management:
  endpoints:
    web:
      exposure:
        include: "health,prometheus"
  metrics:
    tags:
      application: "${spring.application.name}"
  tracing:
    enabled: true
cache:
  provider: "kafka"
  address: "http://cache"
  topic: "unity2-PROD-core-cache-metric"
projectId: "12bb8fb6-ba3d-4615-9b0b-f4f5cb38656f"
accesses:
- persistenceEnabled: true
  sourceTopic: "unity2-PROD-core-blend-udm-gcs-holding-out"
  tableName: "UDM_GCS_HOLDING"
  columns:
  - name: "_trace_id"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "_id"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "created_by"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "created_time_stamp"
    dataType: "timestamp"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "deleted_by"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "deleted_time_stamp"
    dataType: "timestamp"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "updated_by"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "updated_time_stamp"
    dataType: "timestamp"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "_batch_id"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "Site"
    dataType: "text"
    isIndexed: true
    isMultiple: false
    contentSchema: []
  - name: "SafekeepingAccountIdentification"
    dataType: "text"
    isIndexed: true
    isMultiple: false
    contentSchema: []
  - name: "SafekeepingAccountName"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "AssociatedAccountIdentification"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "AssociatedSubAccountIdentification"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "AccountBaseCurrency"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "DepositoryCode"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "DepositoryDescription"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "DepositoryCountryTerritoryCode"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "LocalCustodianCode"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "LocalCustodianDescription"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "LocalCustodianCountryTerritoryCode"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "RegistrationCode"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "RegistrationDescription"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "FxRateToAccountBaseCurrency"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "BusinessDate"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "FinancialInstrumentIdentificationPrimaryIdentifier"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "FinancialInstrumentIdentificationIsin"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "FinancialInstrumentIdentificationSedol"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "FinancialInstrumentIdentificationCusip"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "FinancialInstrumentIdentificationLocalSecurityCode"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "FinancialInstrumentIdentificationDescription"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "FinancialInstrumentIdentificationProductType"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "FinancialInstrumentIndicativePriceCurrency"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "FinancialInstrumentIndicativePrice"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "RegistrarIdentification"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "RegistrarDescription"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "AvailableBalanceQuantity"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "OnLoanBalanceQuantity"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "SettledBalanceQuantity"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "SettledBalanceMarketValue"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "AccountBaseCurrencySettledBalanceMarketValue"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "TradedBalanceQuantity"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "TradedBalanceMarketValue"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "AccountBaseCurrencyTradedBalanceMarketValue"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  - name: "FinancialInstrumentMaturityDate"
    dataType: "text"
    isIndexed: false
    isMultiple: false
    contentSchema: []
  script: "SET SCHEMA '12bb8fb6_ba3d_4615_9b0b_f4f5cb38656f';  CREATE INDEX IF NOT\
    \ EXISTS \"idx_udm_gcs_holding.Safekeeping_account_identification\" ON \"UDM_GCS_HOLDING\"\
    \ USING btree (\"SafekeepingAccountIdentification\"); CREATE INDEX IF NOT EXISTS\
    \ \"idx_udm_gcs_holding.Site\" ON \"UDM_GCS_HOLDING\" USING btree (\"Site\");"
  scriptEnabled: true
  sourceBatchTopicSuffix: "-batch"
  targetBatchTopicSuffix: "-batch"
  ddlAllowed: true
externalDataSourceJdbcConfigs: {}
