#!/bin/bash
set +e
set -o pipefail

function print_usage {
  echo "Usage:$(basename $0)"
  echo ' --project_id             '
  echo ' --path_folder_keys       '
  echo ' --service_account_admin  '
  echo ' --service_accounts       '
  echo ' --access-token           '
  echo
  echo "Example:$(basename $0)  --project_id=hsbc-********-unyeu1-dev --service_account_admin=gce-stage3-image-builder --service_accounts=<EMAIL>,runtime-gke"
}


# Initiate params
function options() {
    while [ "$1" != "" ];
    do
    _param_=`printf "%s\n" $1 | awk -F= '{print $1}'`
    _value_=`printf "%s\n" $1 | sed 's/^[^=]*=//g'`
    if [[ $_value_ == $_param_ ]]; then
        shift
        _value_=$1
    fi
    case ${_param_} in
    --project_id)
        PROJECT_ID="${_value_}"
        ;;
    --path_folder_keys)
        PATH_FOLDER_KEYS="${_value_}"
        ;;
    --service_account_admin)
        SERVICE_ACCOUNT_ADMIN="${_value_}"
        ;;
    --service_accounts)
        IFS=',' read -r -a SERVICE_ACCOUNTS <<< "${_value_}"  # Split comma-separated service accounts into an array
        ;;
    --access-token)
        ACCESS_TOKEN="${_value_}"
        ;;
    --help)
        print_usage
        exit
        ;;
    esac
    shift;
    done
}


# Function to log messages with timestamps
log_message() {
    echo "$(date +'%Y-%m-%d %H:%M:%S') - $1"
}

# Function to check if a service account exists
check_service_account_exists() {
    local service_account=$1

    gcloud iam service-accounts describe "$service_account" --project "$PROJECT_ID" > /dev/null 2>&1;
    if [ $? -eq 0 ]; then
        return 0  # Service account exists
    else
        log_message "Service account $service_account does not exist. Skipping rotation."
        return 1  # Service account does not exist
    fi
}



#Function to add unique elemnt 
add_unique_element() {
    local -n arr="$1"
    local elem="$2"
    
    for item in "${arr[@]}"; do
        if [[ "${item,,}" == "${elem,,}" ]]; then
            return
        fi
    done 
    arr+=("${elem}")
}


# Function to rotate keys for a specific service account
rotate_key() {
    local service_account=$1
    local service_name=${service_account%@*}  # Strip domain part of the email
    local service_account_admin=$2

    log_message "Starting key rotation for $service_account"

    # Check if the service account exists before proceeding
    check_service_account_exists "$service_account"
    if [  $? -ne 0 ]; then
        return   # Skip rotation if service account doesn't exist
    fi

    # Generate a new key
    local new_key_file="/tmp/${service_name}_key.json"
    gcloud iam service-accounts keys create "$new_key_file"  --iam-account="$service_account" --project="$PROJECT_ID" 
    if [ $? -ne 0 ]; then
        log_message "Error creating key for $service_account"
        return 1
    fi

    # Validate the new key for the key-admin service account if necessary
    if [[ "${service_account,,}" == "${service_account_admin,,}" ]]; then
        log_message "Validating new key for key-admin service account"

        # Technical wating for key ( time sync )
        gcloud config list
        sleep 10

        # Temporarily authenticate with the new key
        gcloud auth activate-service-account "$service_account" --key-file="$new_key_file"  
        if [ $? -ne 0 ]; then
            log_message "Error auth admin account with new key"
            return 1
        fi
        
        # Check if the new key works
        gcloud iam service-accounts keys list --iam-account="${service_account}" --project="$PROJECT_ID"
        if [ $? -ne 0 ]; then
            log_message "Failed to validate new key for key-admin; aborting rotation for this account."
            rm "$new_key_file"
            return 1  
        fi
        log_message "New key validated for key-admin service account ${service_account}"
    fi

    # Copy key to path folder of master project (mgm)
    mv "${new_key_file}" "${PATH_FOLDER_KEYS}${service_name}@${PROJECT_ID}.json"

    # Clean up old keys if exceeding the 1
    local key_ids_to_delete=($(gcloud iam service-accounts keys list --iam-account="$service_account" --project="$PROJECT_ID" --managed-by=user --format="value(name)" --sort-by=validAfterTime | head -n -1))
    
    log_message "More than 1 keys found. Backing up and deleting oldest keys..."

    # Sort keys by creation time and delete the oldest after backing up
    for oldest_key_id in "${key_ids_to_delete[@]}"; do

        # Delete the oldest key
        gcloud iam service-accounts keys delete "$oldest_key_id" \
            --iam-account="$service_account" \
            --project="$PROJECT_ID" -q

         if [ $? -ne 0 ]; then
            log_message "Error delate key for $service_account: $oldest_key_id"
            return 1
        fi

        log_message "Deleted oldest key for $service_account: $oldest_key_id"
    done
}

# Function to ensure service account is in full format
ensure_full_format() {
    local SERVICE_ACCOUNT=$1
    
    # Construct the domain using the project name
    local PROJECT_DOMAIN="@${PROJECT_ID}.iam.gserviceaccount.com"

    # Check if the service account ends with the correct domain
    if [[ $SERVICE_ACCOUNT != *@*.iam.gserviceaccount.com ]]; then
        SERVICE_ACCOUNT="${SERVICE_ACCOUNT}${PROJECT_DOMAIN}"
    fi
    echo "$SERVICE_ACCOUNT"
}

###############################################
# main 
###############################################

options "$@"

SERVICE_ACCOUNT_ADMIN=$(ensure_full_format "${SERVICE_ACCOUNT_ADMIN}")

for idx in "${!SERVICE_ACCOUNTS[@]}"; do
    SERVICE_ACCOUNTS[$idx]=$(ensure_full_format "${SERVICE_ACCOUNTS[$idx]}")
done

log_message "Add SERVICE_ACCOUNT_ADMIN service account to list of rotated keys service account."
add_unique_element  SERVICE_ACCOUNTS "${SERVICE_ACCOUNT_ADMIN}"

log_message "Starting key rotation for all specified service accounts"
for service_account in "${SERVICE_ACCOUNTS[@]}"; do
    log_message "Rotating key for  ${service_account}"
    rotate_key "$service_account" "${SERVICE_ACCOUNT_ADMIN}" || log_message "Key rotation failed for $service_account"
    if [ $? -ne 0 ]; then
        log_message "Error rotating key $service_account"
        exit 1
    fi
done
log_message "Key rotaition completed for all service accounts."
