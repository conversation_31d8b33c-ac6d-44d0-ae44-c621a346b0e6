package com.hsbc.changedashboard.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
public class IceApiResponse {
    
    @JsonProperty("offset")
    private Integer offset;
    
    @JsonProperty("limit")
    private Integer limit;
    
    @JsonProperty("changes")
    private List<ChangeRecord> changes;

    // Constructors
    public IceApiResponse() {}

    // Getters and Setters
    public Integer getOffset() { return offset; }
    public void setOffset(Integer offset) { this.offset = offset; }

    public Integer getLimit() { return limit; }
    public void setLimit(Integer limit) { this.limit = limit; }

    public List<ChangeRecord> getChanges() { return changes; }
    public void setChanges(List<ChangeRecord> changes) { this.changes = changes; }
}
