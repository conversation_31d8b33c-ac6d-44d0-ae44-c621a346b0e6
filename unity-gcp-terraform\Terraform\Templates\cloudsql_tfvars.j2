json_data_path = ""
sql_proxy_zone = "{{cloudsql_zone}}"
db_version = "POSTGRES_14"
{% if db_index == "-0" %}db_backup_configuration_location = "{{db_backuplocation}}"{% endif %}

multi_instance_sql_data = [
  {
    db_instance_name : "{{ project_name }}-{{ project_env }}-projection-{{db_uid}}{{ db_index }}",
    db_tier : "db-custom-2-8192",
    {% if db_index == "-0" or db_index == "" %}
    master_instance_name: null,
    db_databases : ["pipeline_data"],
    database_user_names : [
      { "username" : "gce-stage3-image-builder@{{gcp_project}}.iam", "type" : "CLOUD_IAM_SERVICE_ACCOUNT" },
      { "username" : "runtime-gke@{{gcp_project}}.iam", "type" : "CLOUD_IAM_SERVICE_ACCOUNT" }
    ],
    {% else %}
    master_instance_name: "{{ project_name }}-{{ project_env }}-projection-{{db_uid}}-0"
    db_databases : []
    database_user_names : []
    {%endif%}
    delete_protection : false,
    retention_log_time : 1
    maintenance_window_day : 6 
    # UTC Time
    maintenance_window_hour : 2
    query_insights_enabled: true
  }
]