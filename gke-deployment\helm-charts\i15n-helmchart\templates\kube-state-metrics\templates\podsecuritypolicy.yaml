{{- if .Values.podSecurityPolicy.enabled }}
apiVersion: policy/v1beta1
kind: PodSecurityPolicy
metadata:
  name: {{ template "kube-state-metrics.fullname" . }}
  labels:
    {{- include "kube-state-metrics.labels" . | indent 4 }}
{{- if .Values.podSecurityPolicy.annotations }}
  annotations:
{{ toYaml .Values.podSecurityPolicy.annotations | indent 4 }}
{{- end }}
spec:
  privileged: false
  volumes:
    - 'secret'
{{- if .Values.podSecurityPolicy.additionalVolumes }}
{{ toYaml .Values.podSecurityPolicy.additionalVolumes | indent 4 }}
{{- end }}
  hostNetwork: false
  hostIPC: false
  hostPID: false
  runAsUser:
    rule: 'MustRunAsNonRoot'
  seLinux:
    rule: 'RunAsAny'
  supplementalGroups:
    rule: 'MustRunAs'
    ranges:
      # Forbid adding the root group.
      - min: 1
        max: 65535
  fsGroup:
    rule: 'MustRunAs'
    ranges:
      # Forbid adding the root group.
      - min: 1
        max: 65535
  readOnlyRootFilesystem: false
{{- end }}
