{"variables": {"NEXUS_CREDS_PSW": "{{ env `NEXUS_CREDS_PSW` }}", "NEXUS_CREDS_USR": "{{ env `NEXUS_CREDS_USR` }}", "github_creds": "{{ env `GITHUB_CREDS` }}", "buildstage3": "{{user `BUILDSTAGE3` }}", "stage": "{{user `STAGE` }}", "image": "{{user `IMAGE` }}", "project_id": "", "image_encryption_key": "", "region": "", "zone": "", "machine_type": "n2-standard-4", "image_description": "", "image_name": "", "image_family": "", "image_faimly_suffix": "", "disk_size": "200", "subnetwork": "projects/hsbc-6320774-vpchost-asia-prod/regions/asia-east2/subnetworks/cinternal-vpc1-asia-east2", "source_image_project_id": "", "source_image_family": "", "service_account_email": "", "tags": "", "labels": "", "environment": "dev", "build_script": "", "ssh_username": "builder"}, "sensitive-variables": ["NEXUS_CREDS_PSW"], "builders": [{"type": "googlecompute", "project_id": "{{user `project_id`}}", "source_image_project_id": "{{user `source_image_project_id`}}", "source_image_family": "{{user `source_image_family`}}", "ssh_username": "{{user `ssh_username`}}", "use_os_login": true, "region": "{{user `region`}}", "zone": "{{user `zone`}}", "machine_type": "{{user `machine_type`}}", "image_description": "{{user `image_description`}}", "image_name": "{{user `image_name`}}-{{user `STAGE`}}-{{isotime \"**************\"}}", "image_family": "{{user `image_family`}}-{{user `STAGE`}}-{{user `image_faimly_suffix`}}", "disk_size": "{{user `disk_size`}}", "subnetwork": "{{user `subnetwork`}}", "omit_external_ip": "true", "service_account_email": "{{user `service_account_email`}}", "use_internal_ip": "true", "image_encryption_key": {"kmsKeyName": "{{ user `image_encryption_key` }}"}, "metadata": {"enable-oslogin": "TRUE", "enable-oslogin2fa": "FALSE"}, "image_labels": {"owner": "{{ user `image_label_owner` }}", "version": "v0"}, "tags": "{{ user `tags` }}", "scopes": "{{ user `scopes` }}"}], "provisioners": [{"type": "shell", "execute_command": "echo 'packer' | sudo -S sh -c '{{ .Vars }} {{ .Path }}'", "inline": ["mkdir -p {{ user `build_path` }}", "mkdir -p {{ user `build_path` }}/build_scripts", "mkdir -p {{ user `build_path` }}/build_tools", "mkdir -p {{ user `build_path` }}/ansible", "chmod 777 -R {{ user `build_path` }}"]}, {"type": "file", "source": "./build_scripts/", "destination": "{{ user `build_path` }}/build_scripts/"}, {"type": "file", "source": "./build_tools/", "destination": "{{ user `build_path` }}/build_tools/"}, {"type": "file", "source": "./ansible/", "destination": "{{ user `build_path` }}/ansible/"}, {"type": "shell", "execute_command": "echo 'packer' | sudo -S sh -c '{{ .Vars }} {{ .Path }}'", "expect_disconnect": true, "start_retry_timeout": "1m", "environment_vars": ["STASH_TOKEN={{ user `STASH_TOKEN` }}", "GITHUB_TOKEN={{ user `GITHUB_TOKEN` }}", "ENVIRONMENT={{ user `environment` }}", "NEXUS_CREDS_USR={{ user `NEXUS_CREDS_USR` }}", "NEXUS_CREDS_PWD={{ user `NEXUS_CREDS_PWD` }}", "BUILD_PATH={{ user `build_path` }}", "JAVA_VERSION={{ user `java_version` }}", "GIT_VERSION={{ user `git_version` }}", "PACKER_VERSION={{ user `packer_version` }}", "PACKER_PATH={{ user `packer_path` }}", "GROOVY_VERSION={{ user `groovy_version` }}", "GROOVY_PATH={{ user `groovy_path` }}", "TERRAFORM_VERSION={{ user `terraform_version` }}", "TERRAFORM_PATH={{ user `terraform_path` }}", "MAVEN_BINARY={{ user `maven_binary` }}", "MAVEN_VERSION={{ user `maven_version` }}", "MAVEN_PATH={{ user `maven_path` }}", "JQ_BINARY={{ user `jq_binary` }}", "JQ_VERSION={{ user `jq_version` }}", "JQ_PATH={{ user `jq_path` }}", "NEXUS_IQ_PATH={{user `nexus_iq_path` }}", "NEXUS_IQ_VERSION={{user `nexus_iq_version` }}", "JENKINS_PATH={{ user `jenkins_path` }}", "GOROOT={{user `go_root`}}", "GOPATH={{user `go_path`}}", "GO_VERSION={{user `go_version`}}", "BUCKET_BINARIES_PATH={{user `bucket_binaries_path`}}", "YUM_DATAPLATFORMS_VERSION={{ user `yum_dataplatforms_version` }}"], "inline": ["cd {{ user `build_path` }}", "sudo sed -i 's/\r//' ./build_scripts/*.sh", "bash ./build_scripts/resize_logical_volumes.sh", "bash ./build_scripts/install_partition.sh", "bash ./build_scripts/prepare_account_prod.sh", "bash ./build_scripts/gitconfig.sh", "bash ./build_scripts/mgmt_host_fluentd.sh", "bash ./build_scripts/install_java.sh", "bash ./build_scripts/install_python.sh", "bash ./build_scripts/install_google_cloud_sdk.sh", "bash ./build_scripts/install_jq.sh", "bash ./build_scripts/install_terraform.sh", "bash ./build_scripts/install_packer.sh", "bash ./build_scripts/install_groovy.sh", "bash ./build_scripts/install_nano.sh", "bash ./build_scripts/install_nexus_iq_cli.sh", "bash ./build_scripts/install_jenkins_prod.sh", "bash ./build_scripts/install_maven.sh", "bash ./build_scripts/install_helm.sh", "bash ./build_scripts/install_psql.sh", "bash ./build_scripts/install_terragrunt.sh", "bash ./build_scripts/install_yq.sh"]}, {"type": "shell", "expect_disconnect": true, "start_retry_timeout": "90s", "inline": ["sudo groupadd docker", "sudo usermod -aG docker root", "sudo usermod -aG docker jenbld", "sudo usermod -aG docker ${USER}", "echo '[MGMT_HOST] created docker group and added root & current user to                                                                                                                      it, rebooting to take effect'", "sudo reboot", "echo '[MGMT_HOST] rebooted'"]}, {"type": "shell", "execute_command": "echo 'packer' | sudo -S sh -c '{{ .Vars }} {{ .Path }}'", "environment_vars": ["NEXUS_CREDS={{ user `nexus_creds` }}", "STASH_TOKEN={{ user `STASH_TOKEN` }}", "GITHUB_TOKEN={{ user `GITHUB_TOKEN` }}", "NEXUS_CREDS_USR={{ user `NEXUS_CREDS_USR` }}", "NEXUS_CREDS_PWD={{ user `NEXUS_CREDS_PWD` }}", "BUILD_PATH={{ user `build_path` }}", "DOCKER_VERSION={{ user `docker_version` }}", "DOCKER_PATH={{ user `docker_path` }}"], "inline": ["cd {{ user `build_path` }}", "sudo sed -i 's/\r//' ./build_scripts/*.sh", "echo '[MGMT_HOST] rebooted'", "bash ./build_scripts/install_docker.sh"]}, {"type": "shell", "execute_command": "echo 'packer' | sudo -S sh -c '{{ .Vars }} {{ .Path }}'", "environment_vars": ["BUILD_PATH={{ user `build_path` }}", "BUILDSTAGE3={{user `BUILDSTAGE3`}}"], "inline": ["cd {{ user `build_path` }}", "bash ./build_scripts/install_audit.sh", "bash ./build_scripts/enable_service.sh", "bash ./build_scripts/create_daemon.sh", "[[ $(uname -r | grep el7 | wc -l) -gt 0 ]] && systemctl stop rpcbind.socket", "[[ $(uname -r | grep el7 | wc -l) -gt 0 ]] && systemctl stop rpcbind", "[[ $(uname -r | grep el7 | wc -l) -gt 0 ]] && systemctl disable rpcbind", "[[ $(uname -r | grep el7 | wc -l) -gt 0 ]] && rpcinfo -p || true", "echo '[MGM] RPC Portmapper service has been shutdown'"]}, {"type": "shell", "execute_command": "echo 'packer' | sudo -S sh -c '{{ .Vars }} {{ .Path }}'", "inline": ["mkdir -p /opt/script/", "mkdir -p /etc/systemd/system/", "chmod 777 -R /opt/script/", "chmod 777 -R /etc/systemd/system/"]}, {"type": "file", "source": "./system_init/key_management.service", "destination": "/etc/systemd/system/key_management.service"}, {"type": "file", "source": "./system_init/key_management.sh", "destination": "/opt/script/key_management.sh"}, {"type": "file", "source": "./system_init/jenkinsagent_healthcheck.service", "destination": "/etc/systemd/system/jenkinsagent_healthcheck.service"}, {"type": "file", "source": "./system_init/jenkinsagent_healthcheck.bash", "destination": "/opt/script/jenkinsagent_healthcheck.bash"}, {"type": "shell", "execute_command": "echo 'packer' | sudo -S sh -c '{{ .Vars }} {{ .Path }}'", "environment_vars": ["BUILDSTAGE3={{user `BUILDSTAGE3`}}"], "inline": ["chmod 644 /etc/systemd/system/key_management.service", "chmod 755 /opt/script/key_management.sh", "sudo systemctl enable key_management.service", "chmod 644 /etc/systemd/system/jenkinsagent_healthcheck.service", "chmod 755 /opt/script/jenkinsagent_healthcheck.bash", "sudo systemctl enable jenkinsagent_healthcheck.service"]}, {"type": "shell", "inline": ["echo '[MGMT_HOST] stage2 image build completed'"]}, {"type": "shell", "execute_command": "echo 'packer' | sudo -S sh -c '{{ .Vars }} {{ .Path }}'", "inline": ["mkdir -p {{ user `build_path` }}/ansible", "chmod 777 -R {{ user `build_path` }}"]}, {"type": "file", "source": "./ansible/", "destination": "{{ user `build_path` }}/ansible/"}, {"type": "shell", "execute_command": "echo 'packer' | sudo -S sh -c '{{ .Vars }} {{ .Path }}'", "environment_vars": ["BUILD_PATH={{ user `build_path` }}", "ENVIRONMENT={{ user `ENVIRONMENT` }}", "PROJECT_ID={{user `project_id`}}", "IMAGE={{user `IMAGE`}}", "NEXUS_CREDS_USR={{ user `NEXUS_CREDS_USR` }}", "NEXUS_CREDS_PWD={{ user `NEXUS_CREDS_PWD` }}", "GIT_VERSION={{ user `git_version` }}", "BUILDSTAGE3={{user `BUILDSTAGE3`}}", "STASH_TOKEN={{ user `STASH_TOKEN` }}", "GITHUB_TOKEN={{ user `GITHUB_TOKEN` }}"], "inline": ["cd {{ user `build_path` }}", "echo ${BUILDSTAGE3}", "${BUILDSTAGE3} && bash ./ansible/start_stage3_build.sh", "sudo rm -rf ./ansible"]}, {"type": "shell", "execute_command": "echo 'packer' | sudo -S sh -c '{{ .Vars }} {{ .Path }}'", "environment_vars": ["BUILDSTAGE3={{user `BUILDSTAGE3`}}"], "inline": ["${BUILDSTAGE3} && echo '[mgm] stage3 image build completed'", "echo 'completed!'"]}]}