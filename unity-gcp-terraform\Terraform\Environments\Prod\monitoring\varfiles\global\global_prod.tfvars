key               = "gcp"
unit              = "1"
label_value_type  = "STRING"
label_description = "metric for gcp instance"
api_key           = "8ee79039-d34b-47d6-8ed9-2faa86087181"
team_name         = ""

channel_recipients = {
    display_name  = "Global_Prod_Monitoring_recipients" 
    type = "webhook_basicauth"
    username = "BC000010001"
    url = "https://hap-api.hsbc.co.uk/api/sendAlert?@aboutSource=GCP"
    password = "DEWQ42@fgwer23"
}

logs                = {
  custom_api_err = {
    name                = "Global_Custom_API_internal_error"
    filter              = "jsonPayload.message: \"UNY2ERR500\""
    metric_kind         = "DELTA"
    value_type          = "INT64"
    unit                = "1"
    display_name        = "Custom API internal error - Unexcepted error was raised during the invocation of a custom API"
    labels              = []
    label_extractors    = {}
  },
  pipeline_app_err = {
    name                = "Global_Pipeline_application_fatal_error"
    filter              = "resource.type:\"k8s_container\" AND jsonPayload.message: \"UNY2ERR999\" AND NOT jsonPayload.stack_trace: \"Connection reset; nested exception is java.net.SocketException: Connection reset\" AND NOT jsonPayload.stack_trace: \"I/O error on POST request\" AND NOT jsonPayload.stack_trace: \"EntityManagerFactory is Closed\""
    metric_kind         = "DELTA"
    value_type          = "INT64"
    unit                = "1"
    display_name        = "Unexpected error has caused the Unity 2.0 ETL pipelines to fail - container would be restarted"
    labels              = []
    label_extractors    = {}
  },
  kafka_connection_err = {
    name                = "Global_Kafka_connection_fatal_error"
    filter              = "resource.type:\"k8s_container\" AND jsonPayload.stack_trace: \"Failed to construct kafka consumer\""
    metric_kind         = "DELTA"
    value_type          = "INT64"
    unit                = "1"
    display_name        = "Unexpected Kafka connections error"
    labels              = []
    label_extractors    = {}
  },
  kafka_cancel_inflight_coordinator_err = {
    name                = "Global_Kafka_cancel_inflight_coordinator_error"
    filter              = "resource.type:\"k8s_container\" AND jsonPayload.message: \"Cancelled in-flight FIND_COORDINATOR request\""
    metric_kind         = "DELTA"
    value_type          = "INT64"
    unit                = "1"
    display_name        = "Cancelled in-flight FIND_COORDINATOR request"
    labels              = []
    label_extractors    = {}
  }
}

metrics = {
  MNGM_host_cpu = {
    name                = "CPU Utilization - Google Cloud GCE management host - Cpu Utilization"
    object_name          = "mngmcpu"
    alert_severity       = "WARNING"
    combiner            = "OR"
    filter              = "metric.type=\"compute.googleapis.com/instance/cpu/utilization\" resource.type=\"gce_instance\" metadata.system_labels.instance_group=has_substring(\"mgmt-host\")"
    duration            = "120s"
    comparison          = "COMPARISON_GT"
    cross_series_reducer= ""
    alignment_period    = "120s"
    per_series_aligner  = "ALIGN_MEAN"
    threshold           = 0.8
    trigger_count       = 2
    group_by_fields     =  ["resource.label.database_id"]
    api_key             = ""
    enabled             = true
  },
  Proxy_in_cpu = {
    name                = "CPU Utilization - Google Cloud GCE Proxy Mig - Cpu Utilization"
    object_name          = "proxyincpu"
    alert_severity       = "WARNING"
    combiner            = "OR"
    filter              = "metric.type=\"compute.googleapis.com/instance/cpu/utilization\" resource.type=\"gce_instance\" metadata.system_labels.instance_group=has_substring(\"gke-proxy\")"
    duration            = "300s"
    comparison          = "COMPARISON_GT"
    cross_series_reducer= ""
    alignment_period    = "60s"
    per_series_aligner  = "ALIGN_MEAN"
    threshold           = 0.8
    trigger_count       = 6
    group_by_fields     =  ["resource.label.database_id"]
    api_key             = ""
    enabled             = true
  },
  Proxy_Kubectl_Cpu = {
    name                = "CPU Utilization - Google Cloud GCE Kubectl Mig - Cpu Utilization"
    object_name          = "kubctlcpu"
    alert_severity       = "WARNING"    
    combiner            = "OR"
    filter              = "metric.type=\"compute.googleapis.com/instance/cpu/utilization\" resource.type=\"gce_instance\" metadata.system_labels.instance_group=has_substring(\"gke-kubectl\")"
    duration            = "120s"
    comparison          = "COMPARISON_GT"
    cross_series_reducer= ""
    alignment_period    = "120s"
    per_series_aligner  = "ALIGN_MEAN"
    threshold           = 1
    trigger_count       = 2
    group_by_fields     =  ["resource.label.database_id"]
    api_key             = ""
    enabled             = true
  },
  MGNM_memory = {
    name                = "Memory Utilization - Google Cloud GCE Management Host Mig - Memory Utilization"
    object_name          = "mngmmem"
    alert_severity       = "WARNING"    
    combiner            = "OR"
    filter              = "metric.type=\"agent.googleapis.com/memory/percent_used\" AND resource.type=\"gce_instance\" metadata.system_labels.instance_group=has_substring(\"mgmt-host\") metric.label.state!=\"free\""
    duration            = "120s"
    comparison          = "COMPARISON_GT"
    cross_series_reducer= "REDUCE_MEAN"
    alignment_period    = "120s"
    per_series_aligner  = "ALIGN_MAX"
    threshold           = 91
    trigger_count       = 1
    group_by_fields     =  ["metric.label.state"]
    api_key             = ""
    enabled             = true
  },
  Proxyin_memory = {
    name                = "Memory Utilization - Google Cloud GCE Proxy Mig - Memory Utilization"
    object_name          = "proxyinmem"
    alert_severity       = "WARNING"     
    combiner            = "OR"
    filter              = "metric.type=\"agent.googleapis.com/memory/percent_used\" AND resource.type=\"gce_instance\" metadata.system_labels.instance_group=has_substring(\"gke-proxy\") metric.label.state!=\"free\""
    duration            = "120s"
    comparison          = "COMPARISON_GT"
    cross_series_reducer= "REDUCE_MEAN"
    alignment_period    = "120s"
    per_series_aligner  = "ALIGN_MAX"
    threshold           = 80
    trigger_count       = 1
    group_by_fields     =  ["metric.label.state"]
    api_key             = ""
    enabled             = true
  },
  Proxykubectl_memory = {
    name                = "Memory Utilization - Google Cloud GCE Proxy kubectl Mig - Memory Utilization"
    object_name          = "kubectlmem"
    alert_severity       = "WARNING"     
    combiner            = "OR"
    filter              = "metric.type=\"agent.googleapis.com/memory/percent_used\" AND resource.type=\"gce_instance\" metadata.system_labels.instance_group=has_substring(\"gke-kubectl\") metric.label.state!=\"free\""
    duration            = "120s"
    comparison          = "COMPARISON_GT"
    cross_series_reducer= "REDUCE_MEAN"
    alignment_period    = "120s"
    per_series_aligner  = "ALIGN_MAX"
    threshold           = 80
    trigger_count       = 1
    group_by_fields     =  ["metric.label.state"]
    api_key             = ""
    enabled             = true
  },
  MGNM_disk_ut = {
    name                = "Disk Utilization - Google Cloud GCE Management Host Mig - Disk Utilization"
    object_name          = "mngmdisc"
    alert_severity       = "WARNING" 
    combiner            = "OR"
    filter              = "metric.type=\"agent.googleapis.com/disk/percent_used\" AND resource.type=\"gce_instance\" metadata.system_labels.instance_group=has_substring(\"mgmt-host\") metric.label.state!=\"free\""
    duration            = "120s"
    comparison          = "COMPARISON_GT"
    cross_series_reducer= "REDUCE_MEAN"
    alignment_period    = "120s"
    per_series_aligner  = "ALIGN_MEAN"
    threshold           = 80
    trigger_count       = 1
    group_by_fields     =  ["metric.label.state","resource.label.database_id"]
    api_key             = ""
    enabled             = true
  },
  Proxyin_disk_ut = {
    name                = "Disk Utilization - Google Cloud GCE Proxy Mig - Disk Utilization"
    object_name          = "proxyindisc"
    alert_severity       = "WARNING"     
    combiner            = "OR"
    filter              = "metric.type=\"agent.googleapis.com/disk/percent_used\" AND resource.type=\"gce_instance\" metadata.system_labels.instance_group=has_substring(\"gke-proxy\") metric.label.state!=\"free\""
    duration            = "120s"
    comparison          = "COMPARISON_GT"
    cross_series_reducer= "REDUCE_MEAN"
    alignment_period    = "120s"
    per_series_aligner  = "ALIGN_MEAN"
    threshold           = 80
    trigger_count       = 1
    group_by_fields     =  ["metric.label.state","resource.label.database_id"]
    api_key             = ""
    enabled             = true
  },
  Proxykubectl_disk_ut = {
    name                = "Disk Utilization - Google Cloud GCE Proxy kubectl Mig - Disk Utilization"
    object_name          = "kubectldisc"
    alert_severity       = "WARNING"     
    combiner            = "OR"
    filter              = "metric.type=\"agent.googleapis.com/disk/percent_used\" AND resource.type=\"gce_instance\" metadata.system_labels.instance_group=has_substring(\"gke-kubectl\") metric.label.state!=\"free\""
    duration            = "120s"
    comparison          = "COMPARISON_GT"
    cross_series_reducer= "REDUCE_MEAN"
    alignment_period    = "120s"
    per_series_aligner  = "ALIGN_MEAN"
    threshold           = 80
    trigger_count       = 1
    group_by_fields     =  ["metric.label.state","resource.label.database_id"]
    api_key             = ""
    enabled             = true
  },
  MGNM_disk_performance = {
    name                = "Disk performance - Google Cloud GCE Management Host Mig - Disk performance"
    object_name          = "mngmdperf"
    alert_severity       = "WARNING"     
    combiner            = "OR"
    filter              = "metric.type=\"agent.googleapis.com/disk/weighted_io_time\" AND resource.type=\"gce_instance\" metadata.system_labels.instance_group=has_substring(\"mgmt-host\")"
    duration            = "60s"
    comparison          = "COMPARISON_GT"
    cross_series_reducer= "REDUCE_MEAN"
    alignment_period    = "300s"
    per_series_aligner  = "ALIGN_RATE"
    threshold           = 1000
    trigger_count       = 2
    group_by_fields     =  ["resource.label.database_id"]
    api_key             = ""
    enabled             = true
  },
  Proxykubectl_disk_performance = {
    name                = "Disk performance - Google Cloud GCE Proxy kubectl Mig - Disk performance"
    object_name          = "kubectldperf"
    alert_severity       = "WARNING"
    combiner            = "OR"
    filter              = "metric.type=\"agent.googleapis.com/disk/weighted_io_time\" AND resource.type=\"gce_instance\" metadata.system_labels.instance_group=has_substring(\"gke-kubectl\")"
    duration            = "240s"
    comparison          = "COMPARISON_GT"
    cross_series_reducer= "REDUCE_MEAN"
    alignment_period    = "300s"
    per_series_aligner  = "ALIGN_RATE"
    threshold           = 300
    trigger_count       = 1
    group_by_fields     =  ["resource.label.database_id"]
    api_key             = ""
    enabled             = true
  },
  Sql_Auto_failover_Requests = {
    name                = "Auto_failover_Requests - Google Cloud CloudSQl PostGreSQL - Auto_failover_Requests"
    object_name          = "sqlautofail"
    alert_severity       = "WARNING"     
    combiner            = "OR"
    filter              = "metric.type=\"cloudsql.googleapis.com/database/auto_failover_request_count\" AND resource.type=\"cloudsql_database\""
    duration            = "300s"
    comparison          = "COMPARISON_GT"
    cross_series_reducer= ""
    alignment_period    = "300s"
    per_series_aligner  = "ALIGN_MAX"
    threshold           = 0.8
    trigger_count       = 1
    group_by_fields     =  ["resource.label.database_id"]
    api_key             = ""
    enabled             = true
  },
  Gke_cluster_errors = {
    name                = "GKE Cluster Errors - Google GKE Container - GKE Cluster Errors"
    object_name          = "gkeerrors"
    alert_severity       = "WARNING"     
    combiner            = "OR"
    filter              = "metric.type=\"logging.googleapis.com/log_entry_count\" AND resource.type=\"k8s_cluster\" AND  metric.label.severity=\"ERROR\""
    duration            = "300s"
    comparison          = "COMPARISON_GT"
    cross_series_reducer= "REDUCE_SUM"
    alignment_period    = "300s"
    per_series_aligner  = "ALIGN_RATE"
    threshold           = 3
    trigger_count       = 1
    group_by_fields     = ["resource.label.database_id"]
    api_key             = ""
    enabled             = true
  },
  Gke_pods_panic = {
    name                = "GKE pod panic mode - Google GKE Pods - GKE Pods pod panic mode"
    object_name          = "gkepanic"
    alert_severity       = "WARNING"     
    object_name          = "gkepanic"
    alert_severity       = "WARNING"
    combiner            = "OR"
    filter              = "metric.type=\"kubernetes.io/anthos/autoscaler_panic_mode\" AND resource.type=\"k8s_pod\""
    duration            = "120s"
    comparison          = "COMPARISON_GT"
    cross_series_reducer= ""
    alignment_period    = "120s"
    per_series_aligner  = "ALIGN_MEAN"
    threshold           = 1
    trigger_count       = 1
    group_by_fields     = ["resource.label.pod_name"]
    api_key             = ""
    enabled             = true
  },
  Sql_Connections = {
    name                = "REMOVE - SQL Connections - Google Cloud CloudSQl PostGreSQL - Connections"
    object_name          = "sqlcon"
    alert_severity       = "CRITICAL"     
    combiner            = "OR"
    filter              = "metric.type=\"cloudsql.googleapis.com/database/network/connections\" AND resource.type=\"cloudsql_database\" "
    duration            = "120s"
    comparison          = "COMPARISON_GT"
    cross_series_reducer= ""
    alignment_period    = "120s"
    per_series_aligner  = "ALIGN_MAX"
    threshold           = 20
    trigger_count       = 1
    group_by_fields     =  ["resource.label.database_id"]
    api_key             = ""
    enabled             = false },
  Sql_cpu_utilization = {
    name                = "REMOVE - SQL CPU Utilization - Google Cloud CloudSQl PostGreSQL - SQL CPU Utilization"
    object_name          = "sqlcpu"
    alert_severity       = "CRITICAL"     
    combiner            = "OR"
    filter              = "metric.type=\"cloudsql.googleapis.com/database/cpu/utilization\" AND resource.type=\"cloudsql_database\" "
    duration            = "180s"
    comparison          = "COMPARISON_GT"
    cross_series_reducer= ""
    alignment_period    = "60s"
    per_series_aligner  = "ALIGN_MEAN"
    threshold           = 0.95
    trigger_count       = 2
    group_by_fields     =  ["resource.label.database_id"]
    api_key             = ""
    enabled             = false },
  Sql_disk_io_reads = {
    name                = "REMOVE - SQL Disk I/O Reads > 17500 /s - Google Cloud CloudSQl PostGreSQL - SQL Disk I/O Reads (70% of IOPS LIMIT)"
    object_name          = "sqldicread"
    alert_severity       = "CRITICAL"     
    combiner            = "OR"
    filter              = "metric.type=\"cloudsql.googleapis.com/database/disk/read_ops_count\" AND resource.type=\"cloudsql_database\" "
    duration            = "120s"
    comparison          = "COMPARISON_GT"
    cross_series_reducer= ""
    alignment_period    = "120s"
    per_series_aligner  = "ALIGN_RATE"
    threshold           = 17500
    trigger_count       = 1
    group_by_fields     =  ["resource.label.database_id"]
    api_key             = ""
    enabled             = false },
  Sql_disk_io_writes = {
    name                = "REMOVE - SQL Disk I/O Writes > 17500 /s - Google Cloud CloudSQl PostGreSQL - SQL Disk I/O Writes (70% of IOPS LIMIT)"
    object_name          = "sqldiscwrite"
    alert_severity       = "CRITICAL"     
    combiner            = "OR"
    filter              = "metric.type=\"cloudsql.googleapis.com/database/disk/write_ops_count\" AND resource.type=\"cloudsql_database\" "
    duration            = "300s"
    comparison          = "COMPARISON_GT"
    cross_series_reducer= ""
    alignment_period    = "300s"
    per_series_aligner  = "ALIGN_RATE"
    threshold           = 17500
    trigger_count       = 2
    group_by_fields     =  ["resource.label.database_id"]
    api_key             = ""
    enabled             = false },
  Sql_disk_utilization = {
    name                = "REMOVE - SQL Disk Utilization  - Google Cloud CloudSQl PostGreSQL - SQL Disk Utilization"
    object_name          = "sqldiscut"
    alert_severity       = "CRITICAL"     
    combiner            = "OR"
    filter              = "metric.type=\"cloudsql.googleapis.com/database/disk/utilization\" AND resource.type=\"cloudsql_database\" "
    duration            = "120s"
    comparison          = "COMPARISON_GT"
    cross_series_reducer= ""
    alignment_period    = "120s"
    per_series_aligner  = "ALIGN_PERCENT_CHANGE"
    threshold           = 15
    trigger_count       = 1
    group_by_fields     =  ["resource.label.database_id"]
    api_key             = ""
    enabled             = false },
  Sql_memory_utilization = {
    name                = "REMOVE - SQL Memory Utilization  - Google Cloud CloudSQl PostGreSQL - SQL Memory Utilization"
    object_name          = "sqlmem"
    alert_severity       = "CRITICAL"     
    combiner            = "OR"
    filter              = "metric.type=\"cloudsql.googleapis.com/database/memory/utilization\" AND resource.type=\"cloudsql_database\" "
    duration            = "300s"
    comparison          = "COMPARISON_GT"
    cross_series_reducer= ""
    alignment_period    = "300s"
    per_series_aligner  = "ALIGN_PERCENT_CHANGE"
    threshold           = 15
    trigger_count       = 1
    group_by_fields     =  ["resource.label.database_id"]
    api_key             = ""
    enabled             = false },
  Sql_send_byte_rate = {
    name                = "REMOVE - SQL Byte Rate > 80MBytes/s - Google Cloud CloudSQl PostGreSQL - SQL Byte Rate"
    object_name          = "sqlbytesrate"
    alert_severity       = "CRITICAL"     
    combiner            = "OR"
    filter              = "metric.type=\"cloudsql.googleapis.com/database/network/sent_bytes_count\" AND resource.type=\"cloudsql_database\" "
    duration            = "120s"
    comparison          = "COMPARISON_GT"
    cross_series_reducer= ""
    alignment_period    = "120s"
    per_series_aligner  = "ALIGN_RATE"
    threshold           = 80000000
    trigger_count       = 1
    group_by_fields     = ["resource.label.database_id"]
    api_key             = ""
    enabled             = false },
  Gke_containers_cpu = {
    name                = "REMOVE - GKE Container CPU - Google GKE Container - GKE Container CPU utilization"
    object_name          = "gkecpu"
    alert_severity       = "CRITICAL"     
    combiner            = "OR"
    filter              = "metric.type=\"kubernetes.io/container/cpu/limit_utilization\" AND resource.type=\"k8s_container\""
    duration            = "300s"
    comparison          = "COMPARISON_GT"
    cross_series_reducer= ""
    alignment_period    = "300s"
    per_series_aligner  = "ALIGN_MEAN"
    threshold           = 0.8
    trigger_count       = 1
    group_by_fields     = ["resource.label.container_name"]
    api_key             = ""
    enabled             = false },  
  Gke_containers_memory = {
    name                = "REMOVE - GKE Container Memory - Google GKE Container - GKE Container memory limit utilization"
    object_name          = "gkemem"
    alert_severity       = "CRITICAL"     
    combiner            = "OR"
    filter              = "metric.type=\"kubernetes.io/container/memory/limit_utilization\" AND resource.type=\"k8s_container\""
    duration            = "300s"
    comparison          = "COMPARISON_GT"
    cross_series_reducer= ""
    alignment_period    = "300s"
    per_series_aligner  = "ALIGN_MEAN"
    threshold           = 0.9
    trigger_count       = 1
    group_by_fields     = ["resource.label.container_name"]
    api_key             = ""
    enabled             = false },
  Gke_containers_restarts = {
    name                = "REMOVE - GKE Container Restarts - Google GKE Container - GKE Container Restarts"
    object_name          = "gkerestarts"
    alert_severity       = "CRITICAL"     
    combiner            = "OR"
    filter              = "metric.type=\"kubernetes.io/container/restart_count\" AND resource.type=\"k8s_container\""
    duration            = "120s"
    comparison          = "COMPARISON_GT"
    cross_series_reducer= "REDUCE_SUM"
    alignment_period    = "120s"
    per_series_aligner  = "ALIGN_DELTA"
    threshold           = 1
    trigger_count       = 2
    group_by_fields     = ["resource.label.container_name"]
    api_key             = ""
     enabled             = false },
 Custom_API_internal_error = {
    name                = "REMOVE - Custom API internal error - Unexcepted error was raised during the invocation of a custom API"
    object_name          = "custapierr"
    alert_severity       = "CRITICAL"
    combiner            = "OR"
    filter              = "metric.type=\"logging.googleapis.com/user/core_Prod_Custom_API_internal_error\" AND resource.type=\"k8s_container\""
    duration            = "60s"
    comparison          = "COMPARISON_GT"
    cross_series_reducer= "REDUCE_COUNT"
    alignment_period    = "60s"
    per_series_aligner  = "ALIGN_DELTA"
    threshold           = 0
    trigger_count       = 1
    group_by_fields     = ["metric.labels.log","resource.label.namespace_name"]
    api_key             = ""
    enabled             = false },
   Pipeline_api_internal_error = {
    name                = "REMOVE - Pipeline application fatal error - Unexpected error has caused the Unity 2.0 ETL pipelines to fail"
    object_name          = "pipapiinterr"
    alert_severity       = "CRITICAL"
    combiner            = "OR"
    filter              = "metric.type=\"logging.googleapis.com/user/core_Prod_Pipeline_application_fatal_error\" AND resource.type=\"k8s_container\""
    duration            = "60s"
    comparison          = "COMPARISON_GT"
    cross_series_reducer= "REDUCE_COUNT"
    alignment_period    = "60s"
    per_series_aligner  = "ALIGN_DELTA"
    threshold           = 0
    trigger_count       = 1
    group_by_fields     = ["metric.labels.log","resource.label.namespace_name"]
    api_key             = ""
    enabled             = false },
   Sql_server_is_down = {
    name                = "REMOVE - CloudSQL is Down - Unexpected error caused Cloud SQL to be down"
    object_name          = "sql-server-is-down"
    alert_severity       = "CRITICAL"
    combiner            = "OR"
    filter              = "metric.type=\"cloudsql.googleapis.com/database/up\" resource.type=\"cloudsql_database\" "
    duration            = "0s"
    comparison          = "COMPARISON_LT"
    cross_series_reducer= ""
    alignment_period    = "60s"
    per_series_aligner  = "ALIGN_MEAN"
    threshold           = 1
    trigger_count       = 1
    group_by_fields     = []
    api_key             = ""
    enabled             = false },
  Sql_server_available_for_failover = {
    name                = "REMOVE - CloudSQL is Down - Database Server failover unavaliable"
    object_name          = "sql-failover-is-down"
    alert_severity       = "CRITICAL"
    combiner            = "OR"
    filter              = "metric.type=\"cloudsql.googleapis.com/database/up\" resource.type=\"cloudsql_database\" "
    duration            = "180s"
    comparison          = "COMPARISON_LT"
    cross_series_reducer= ""
    alignment_period    = "60s"
    per_series_aligner  = "ALIGN_MEAN"
    threshold           = 1
    trigger_count       = 1
    group_by_fields     = []
    api_key             = ""
    enabled             = false },
  Sql_server_uptime = {
    name                = "REMOVE - CloudSQL is Down - Database Server down for more than 5 min"
    object_name          = "sql-uptime"
    alert_severity       = "CRITICAL"
    combiner            = "OR"
    filter              = "metric.type=\"cloudsql.googleapis.com/database/uptime\" resource.type=\"cloudsql_database\" "
    duration            = "300s"
    comparison          = "COMPARISON_LT"
    cross_series_reducer= ""
    alignment_period    = "60s"
    per_series_aligner  = "ALIGN_MEAN"
    threshold           = 1
    trigger_count       = 1
    group_by_fields     = []
    api_key             = ""
    enabled             = false },
  Sql_server_state = {
    name                = "REMOVE - CloudSQL failed - Database Server is in state failed for 3 minutes"
    object_name          = "sql-state"
    alert_severity       = "CRITICAL"
    combiner            = "OR"
    filter              = "metric.type=\"cloudsql.googleapis.com/database/instance_state\" resource.type=\"cloudsql_database\" AND metric.label.state=\"FAILED\" "
    duration            = "180s"
    comparison          = "COMPARISON_GT"
    cross_series_reducer= ""
    alignment_period    = "60s"
    per_series_aligner  = "ALIGN_COUNT_TRUE"
    threshold           = 0
    trigger_count       = 1
    group_by_fields     = []
    api_key             = ""
    enabled             = false
  }
}
