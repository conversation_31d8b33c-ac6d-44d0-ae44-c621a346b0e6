#!/usr/bin/env bash
set -euo pipefail

echo "[MGMT_HOST] set env vars for build"

METADATA_URL=http://metadata.google.internal/computeMetadata/v1
PROJ_PATH=/project/project-id
INSTANCE_NAME=/instance/name
NETWORK_TAGS=/instance/tags
NETWORK_PATH_PARAM=/instance/network-interfaces/0/network

export PROJECT_ID=$(curl --fail -s -H 'Metadata-Flavor: Google' "${METADATA_URL}${PROJ_PATH}")
export INSTANCE_NAME=$(curl --fail -s -H 'Metadata-Flavor: Google' "${METADATA_URL}${INSTANCE_NAME}")

echo "[MGMT_HOST] ENV: PROJECT_ID=$PROJECT_ID INSTANCE_NAME=$INSTANCE_NAME"