#!/bin/bash
set +e
set -o pipefail

function print_usage {
  echo "Usage:$(basename $0)"
  echo ' --bucket_project         '
  echo ' --bucket_name            '
  echo ' --bucket_cmek_project    '
  echo ' --bucket_region          '
  echo ' --project_id             '
  echo
  echo "Example:$(basename $0)  --bucket_name=hsbc-9087302-unity-dev-key-management --bucket_cmek_project=hsbc-6320774-kms-dev --bucket_region=asia-south1 "
}


# Initiate params
function options() {
    while [ "$1" != "" ];
    do
    _param_=`printf "%s\n" $1 | awk -F= '{print $1}'`
    _value_=`printf "%s\n" $1 | sed 's/^[^=]*=//g'`
    if [[ $_value_ == $_param_ ]]; then
        shift
        _value_=$1
    fi
    case ${_param_} in
    --master_project)
        MASTER_PROJECT="${_value_}"
        BUCKET_NAME=${MASTER_PROJECT}-key-management
        ;;
    --cmek_project)
        CMEK_PROJECT="${_value_}"
        ;;
    --region)
        REGION="${_value_}"
        ;;
    --key_project_id)
        KEY_PROJECT_ID="${_value_}"
        ;;
    --path_folder_keys)
        PATH_FOLDER_KEYS="${_value_}"
        ;;
    --help)
        print_usage
        exit
        ;;
    esac
    shift;
    done
}

# Function to log messages with timestamps
log_message() {
    echo "$(date +'%Y-%m-%d %H:%M:%S') - $1"
}

# Function to create a bucket with a KMS encryption key
function create_bucket_with_kms() {
    local _backet_project=$1
    local _bucket_name=$2
    local _bucket_cmek_project=$3
    local _bucket_region=$4



    local _kms_key="projects/${_bucket_cmek_project}/locations/${_bucket_region}/keyRings/cloudStorage/cryptoKeys/cloudStorage"

    # Check if the bucket already exists
    
    gsutil ls -p "${_backet_project}" "gs://${_bucket_name}" > /dev/null 2>&1
    if [ $? -eq 0 ]; then
        log_message "Bucket gs://${_bucket_name} already exists. No need to create."
    else
        log_message "Creating bucket gs://${bucket_name} with encryption key ${bucket_kms_key}..."
        gsutil mb -p ${_backet_project} -l ${_bucket_region} -b on -k ${_bucket_kms_key} gs://${_bucket_name}
        
    fi
}

function store_kys() {
    local _project_id=$1
    local _bucket_name=$2
    local _path_folder_keys=$3
    
    log_message "Upload keys to ${_bucket_name}"

    service_accounts_json=($(ls "${_path_folder_keys}"*@"${_project_id}".json | xargs -n 1 basename))

    for service_account_json in "${service_accounts_json[@]}"; do

        local service_name=${service_account_json%@*}  # Strip name of project_id

        sa_key_file="${_path_folder_keys}${service_account_json}"

        gcs_key_path="gs://${_bucket_name}/${service_name}/${service_account_json}"

        #Copy newly generated key file to bucket
        gsutil cp ${sa_key_file} ${gcs_key_path}
        if [ $? -ne 0 ]; then
            log_message "Error copy new key to $gcs_key_path"
            return 1
        fi
        log_message "Uploaded new key to $gcs_key_path"
    done
}

###############################################
# main 
###############################################

options "$@"

create_bucket_with_kms "${MASTER_PROJECT}" "${BUCKET_NAME}" "${CMEK_PROJECT}" "${REGION}"
 if [ $? -ne 0 ]; then
    log_message "Error creating bucket ${BUCKET_NAME}"
    return 1
fi
store_kys "${KEY_PROJECT_ID}" "${BUCKET_NAME}" "${PATH_FOLDER_KEYS}"

