pipeline {    
    agent  { 
        label "unity-dev-jenkins-agent"    
    }
    environment {
	    STASH_TOKEN= credentials('github-connect')
	    NEXUS_CREDS=credentials('github-connect')
   
    }
    stages {
    stage ('Checkout imagefactory') {
            steps {
		  sh 'whoami'
                 sh 'sudo -i'
                 sh 'git version'
                 dir ("/root/test") {
                    git(
             credentialsId: 'github-connect', url:'https://alm-github.systems.uk.hsbc/HASE-IM/hsbc-9087302-unity-dev-mgmt-host'
                    )
                   sh 'chmod -R 777 mgmt_host'

                }
            }
        }
    stage  ('store logs in tmp') {
         steps {
		  withCredentials([usernamePassword(credentialsId: 'cardsusernamepwd', passwordVariable: 'password', usernameVariable: 'username')]) {
	      sh 'set +x'
       sh 'wget --auth-no-challenge --user $username --password $password https://alm-jenkins218.systems.uk.hsbc:8706/job/OSS/job/DMS_Filenet/job/management_host/job/Packerimagebuild_stage2_RollingMig/lastBuild/consoleText -O /tmp/consoleoutput'
       sh 'echo "https://alm-jenkins218.systems.uk.hsbc:8706/job/OSS/job/DMS_Filenet/job/management_host/job/Packerimagebuild_stage2_RollingMig/consoletext" >> /tmp/consoleoutput'
       sh 'cat /tmp/consoleoutput'


                }
            }
        }
        
             
    }
}
