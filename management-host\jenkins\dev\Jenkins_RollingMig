def AGENT = 'unity-dev-jenkins-agent'
pipeline {    
    agent { label env.AGENT_LABEL ?: AGENT }
    stages {

        stage ('rolling out the management host mig') { 
	        steps {
                this.echo "executing spinningup instance script"
                catchError (buildResult: 'SUCCESS', stageResult: 'SUCCESS') {
                    this.sh 'chmod 755 ./initial_setup/rolling_mig.sh; ./initial_setup/rolling_mig.sh dev'    
                }
          }
        }
    }
   
    post {
        unsuccessful {
            mail bcc: '',
                body: "Attention @here ${env.JOB_NAME} #${env.BUILD_NUMBER} has failed.",
                cc: '',
                from: 'unity_cloud_devops@noreply',
                replyTo: '',
                subject: "Failed Pipeline: ${currentBuild.fullDisplayName}",
                to: '<EMAIL>,<EMAIL>'
        }
        success {
            mail bcc: '',
                body: "Succesfully roatated: ${env.JOB_NAME} #${env.BUILD_NUMBER}",
                cc: '',
                from: 'unity_cloud_devops@noreply',
                replyTo: '',
                subject: "The pipeline ${currentBuild.fullDisplayName} completed successfully.",
                to: '<EMAIL>,<EMAIL>'
        }    
        cleanup {
            script {
                cleanWs()
            }
        }
    }		

}