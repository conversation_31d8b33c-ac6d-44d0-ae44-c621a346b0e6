function EnsureDirectoryExist {
    Param(
        [Parameter(Mandatory=$true, HelpMessage="Target directory")]
        $TargetDir
    )
 
    If(!(Test-Path $TargetDir))
    {
        New-Item -ItemType Directory -Force -Path $TargetDir
    }
}
 
function GetHashicorpArtifactsFromNexus {
    Param(
        [Parameter(Mandatory=$true, HelpMessage="Target directory")]
        $TargetDir,
 
        [Parameter(Mandatory=$true, HelpMessage="Hashicorp artifact name, such as terraform, packer etc")]
        $ArtifactName,
 
        [Parameter(Mandatory=$true, HelpMessage="Hashicorp artifact version")]
        $ArtifactVersion,
 
        [Parameter(Mandatory=$true, HelpMessage="Hashicorp artifact OS version, such as linux_amd64, windows_amd64 etc")]
        $ArtifactOSVersion,
 
        [Parameter(Mandatory=$true, HelpMessage="Hashicorp artifact package extension, such as zip etc")]
        $PackageExtension
    )
 
    $FileName = -join($ArtifactName, "_", $ArtifactVersion, "_", $ArtifactOSVersion, ".", $PackageExtension)
    $FullFileName = "$TargetDir\$FileName"
 
    $URI = "https://gbmt-nexus.prd.fx.gbm.cloud.uk.hsbc/repository/hashicorp-releases//$ArtifactName/$ArtifactVersion/$FileName"
 
    Write-Output "Will download $URI to $FullFileName"
    Invoke-WebRequest -Uri $URI -OutFile $FullFileName
}

# Allign those paths with your system
$PackerDir = "C:\repo\hsbc-9087302-unity-dev-mgmt-host\build_tools\tf_providers"

 
EnsureDirectoryExist $PackerDir
 
GetHashicorpArtifactsFromNexus $PackerDir 'terraform-provider-local'         '1.4.0'     'linux_amd64' 'zip'
GetHashicorpArtifactsFromNexus $PackerDir 'terraform-provider-null'          '2.1.2'     'linux_amd64' 'zip'
GetHashicorpArtifactsFromNexus $PackerDir 'terraform-provider-google'        '3.7.0'     'linux_amd64' 'zip'
GetHashicorpArtifactsFromNexus $PackerDir 'terraform-provider-google-beta'   '3.7.0'     'linux_amd64' 'zip'
GetHashicorpArtifactsFromNexus $PackerDir 'terraform-provider-google'        '3.43.0'    'linux_amd64' 'zip'
GetHashicorpArtifactsFromNexus $PackerDir 'terraform-provider-google-beta'   '3.36.0'    'linux_amd64' 'zip'
GetHashicorpArtifactsFromNexus $PackerDir 'terraform-provider-google'        '4.8.0'    'linux_amd64' 'zip'
GetHashicorpArtifactsFromNexus $PackerDir 'terraform-provider-google-beta'   '4.8.0'    'linux_amd64' 'zip'
GetHashicorpArtifactsFromNexus $PackerDir 'terraform-provider-random'        '2.3.0'     'linux_amd64' 'zip'
GetHashicorpArtifactsFromNexus $PackerDir 'terraform-provider-template'      '2.1.2'     'linux_amd64' 'zip'
GetHashicorpArtifactsFromNexus $PackerDir 'terraform-provider-tls'           '2.1.1'     'linux_amd64' 'zip'
