load_module '/usr/lib64/nginx/modules/ngx_stream_module.so';
user nginx;
worker_processes 1;
worker_rlimit_nofile 1024000;

error_log  /var/log/nginx/error.log warn;
pid        /var/run/nginx.pid;

events {
    worker_connections  5120;
}

stream {
    map_hash_bucket_size 512;
    log_format proxy '$remote_addr [$time_local] $protocol $status $bytes_sent $bytes_received $session_time "$upstream_addr" "$upstream_bytes_sent" "$upstream_bytes_received" "$upstream_connect_time"';

    # This map allows traffic to be directed to other sources
    # based on the SNI header.  Example below allows access to
    map $ssl_preread_server_name $backend {
        # traffic direction: from on-prem DRN into gke
        # The below support routing of traffic from <appname>.<gcpproject>.<gcpenv>.gcp.cloud.<region>.hsbc
        # to <appname>-proxy.<gcpproject>.<gcpenv>.gcp.cloud.<region>.hsbc:443
        # for example
        # gkeapp1.hsbc-7766782-gbmhackasia-dev.dev.gcp.cloud.hk.hsbc to gkeapp1-proxy.hsbc-7766782-gbmhackasia-dev.dev.gcp.cloud.hk.hsbc
        #~^(?<appname>.*)\.(?<gcpproject>.*)\.(?<gcpenv>\w+)\.gcp\.cloud\.(?<region>\w+)\.hsbc$ $appname-proxy.$gcpproject.$gcpenv.gcp.cloud.$region.hsbc:443;
        ~^(?<appname>.*?)\.(?<domain>.*?)$ $appname-proxy.$domain:443;
    }

    server {
        resolver *************** valid=5 ipv6=off;
        # The below switches between either the static restricted
        # server group, or enters the $unrestricted map and using
        # SNI.
        listen 443;
        proxy_pass $backend;
        ssl_preread on;
        access_log syslog:server=unix:/dev/log,nohostname proxy;
        error_log  syslog:server=unix:/dev/log warn;
    }
}
http {
    ssl_session_cache shared:SSL:100m;
    ssl_buffer_size 1m;
    server_names_hash_bucket_size 512;
    proxy_max_temp_file_size 0;
    send_timeout 30s;

    map $ssl_server_name $proxy_backend {
        #~^(?<appname>.*)\.(?<gcpproject>.*)\.(?<gcpenv>\w+)\.gcp\.cloud\.(?<region>\w+)\.hsbc$ https://$appname-proxy.$gcpproject.$gcpenv.gcp.cloud.$region.hsbc:443;
        ~^(?<appname>.*?)\.(?<domain>.*?)$ https://$appname-proxy.$domain:443;
    }

    map $ssl_server_name $domainname {
        #~^(?<appname>.*)\.(?<gcpproject>.*)\.(?<gcpenv>\w+)\.gcp\.cloud\.(?<region>\w+)\.hsbc$ https://$appname-proxy.$gcpproject.$gcpenv.gcp.cloud.$region.hsbc:443;
        ~^(?<appname>.*?)\.(?<domain>.*?)$ $domain;
    }

    map $ssl_server_name $subdomain {
        ~^(?<appname>.*?)\.(?<sub>.*?)\.(?<domain>.*?)$ $sub;
    }
    map $ssl_server_name $app {
        ~^(?<appname>.*?)\.(?<domain>.*?)$ $appname;
    }
    
   log_format custom '$remote_addr [$time_local] [$ssl_client_s_dn] [$app] [$subdomain] [$uri] HTTP $status $bytes_sent "$upstream_addr" "$upstream_bytes_sent" "$upstream_bytes_received" "$upstream_connect_time" "$upstream_response_time" [${request_time}s]';
   server {
        listen 8443 ssl default_server;
        resolver                *************** valid=5 ipv6=off;
        server_name             $ssl_server_name;
        ssl_certificate         pki/$subdomain/server.pem;
        ssl_certificate_key     pki/$subdomain/server.key;
        ssl_client_certificate  ca.pem;
        ssl_verify_client       optional;
        ssl_verify_depth        3;
        ssl_protocols           TLSv1.2;
        ssl_ciphers             HIGH:!aNULL:!MD5;
        location / {
          proxy_pass $proxy_backend;
          proxy_http_version 1.1;
          proxy_set_header host $ssl_server_name;
          proxy_set_header Upgrade $http_upgrade;
          proxy_set_header Connection "upgrade";
          set $authorized 0;
          #default behaviour - blocks everything
          if ( $app = "grafana") { set $authorized "11"; }
          if ( $authorized != 11 ) { return 403; }
          access_log syslog:server=unix:/dev/log,nohostname custom;
          error_log  syslog:server=unix:/dev/log warn;
        }
    }

    server {
        listen 8443 ssl;
        resolver                *************** valid=5 ipv6=off;
        server_name             $ssl_server_name;
        ssl_certificate         pki/$subdomain/server.pem;
        ssl_certificate_key     pki/$subdomain/server.key;
        ssl_client_certificate  ca.pem;
        ssl_verify_client       optional;
        ssl_verify_depth        3;
        ssl_protocols           TLSv1.2;
        ssl_ciphers             HIGH:!aNULL:!MD5;

        server_name *.faasia-dev.vpc3.hsbc-9087302-unity-dev.dev.gcp.cloud.hk.hsbc 
                    *.faasia-sit.vpc3.hsbc-9087302-unity-dev.dev.gcp.cloud.hk.hsbc 
                    *.faasia-uat.vpc3.hsbc-9087302-unity-dev.dev.gcp.cloud.hk.hsbc 
                    *.faasia-preprod.vpc3.hsbc-9087302-unity-dev.dev.gcp.cloud.hk.hsbc 
                    *.quartz-dev.vpc3.hsbc-9087302-unity-dev.dev.gcp.cloud.hk.hsbc 
                    *.quartz-sit.vpc3.hsbc-9087302-unity-dev.dev.gcp.cloud.hk.hsbc 
                    *.quartz-uat.vpc3.hsbc-9087302-unity-dev.dev.gcp.cloud.hk.hsbc 
                    *.quartz-preprod.vpc3.hsbc-9087302-unity-dev.dev.gcp.cloud.hk.hsbc 
                    ;
        location / {
          proxy_pass $proxy_backend;
          proxy_http_version 1.1;
          proxy_set_header host $ssl_server_name;
          proxy_set_header Upgrade $http_upgrade;
          proxy_set_header Connection "upgrade";          
          #custom passthur
          access_log syslog:server=unix:/dev/log,nohostname custom;
          error_log  syslog:server=unix:/dev/log warn;
        }
    }

    server {
        listen 8443 ssl;
        resolver                *************** valid=5 ipv6=off;
        server_name             $ssl_server_name;
        ssl_certificate         pki/$subdomain/server.pem;
        ssl_certificate_key     pki/$subdomain/server.key;
        ssl_client_certificate  ca.pem;
        ssl_verify_client       optional;
        ssl_verify_depth        3;
        ssl_protocols           TLSv1.2;
        ssl_ciphers             HIGH:!aNULL:!MD5;
 
        server_name *.dccgfx-dev.vpc3.hsbc-9087302-unity-dev.dev.gcp.cloud.hk.hsbc 
                    *.dccgfx-sit.vpc3.hsbc-9087302-unity-dev.dev.gcp.cloud.hk.hsbc 
                    *.dccgfx-uat.vpc3.hsbc-9087302-unity-dev.dev.gcp.cloud.hk.hsbc 
                    *.dccgfx-preprod.vpc3.hsbc-9087302-unity-dev.dev.gcp.cloud.hk.hsbc 
                    ;

        location / {
          proxy_pass $proxy_backend;
          proxy_http_version 1.1;
          proxy_set_header host $ssl_server_name;
          proxy_set_header Upgrade $http_upgrade;
          proxy_set_header Connection "upgrade";
          set $authorized 0;
          if ( $app = "grafana") { set $authorized "11"; }
          if ( $ssl_client_s_dn = "CN=uat.gfx-dev.hsbc-11374370-gfxasia-dev.dev.gcp.cloud.hk.hsbc,OU=GFX,OU=CFS,OU=MSS,O=HSBC,L=GuangZhou,ST=GuangDong,C=CN" ) { set $authorized "11"; } 
          if ( $authorized != 11 ) { return 403; }
          access_log syslog:server=unix:/dev/log,nohostname custom;
          error_log  syslog:server=unix:/dev/log warn;
        }

        location /api/custom/dcc-static-data {
          proxy_pass $proxy_backend;
          proxy_http_version 1.1;
          proxy_set_header host $ssl_server_name;
          proxy_set_header Upgrade $http_upgrade;
          proxy_set_header Connection "upgrade";
          #allow custom end using outh2 to passthur
          set $authorized 11;
          if ( $authorized != 11 ) { return 403; }
          access_log syslog:server=unix:/dev/log,nohostname custom;
          error_log  syslog:server=unix:/dev/log warn;
        }
    }

    server {
        listen 8443 ssl;
        resolver                *************** valid=5 ipv6=off;
        server_name             $ssl_server_name;
        ssl_certificate         pki/$subdomain/server.pem;
        ssl_certificate_key     pki/$subdomain/server.key;
        ssl_client_certificate  ca.pem;
        ssl_verify_client       optional;
        ssl_verify_depth        3;
        ssl_protocols           TLSv1.2;
        ssl_ciphers             HIGH:!aNULL:!MD5;
        server_name *.tpc-dev.vpc3.hsbc-9087302-unity-dev.dev.gcp.cloud.hk.hsbc 
                    *.tpc-sit.vpc3.hsbc-9087302-unity-dev.dev.gcp.cloud.hk.hsbc 
                    *.tpc-uat.vpc3.hsbc-9087302-unity-dev.dev.gcp.cloud.hk.hsbc 
                    *.tpc-preprod.vpc3.hsbc-9087302-unity-dev.dev.gcp.cloud.hk.hsbc 
                    ;

        location / {
          proxy_pass $proxy_backend;
          proxy_http_version 1.1;
          proxy_set_header host $ssl_server_name;
          proxy_set_header Upgrade $http_upgrade;
          proxy_set_header Connection "upgrade";
          set $authorized 0;
          if ( $app = "grafana") { set $authorized "11"; }
          if ( $ssl_client_s_dn = "CN=mss-cfs-uat.hc.cloud.hk.hsbc,OU=GBM,O=Hsbc,L=Guangzhou,ST=Guangdong,C=CN" ) { set $authorized 11; }
          if ( $ssl_client_s_dn = "emailAddress=<EMAIL>,CN=mss-cfs-uat.hc.cloud.hk.hsbc,OU=GBM,O=Hsbc,L=Guangzhou,ST=Guangdong,C=CN" ) { set $authorized 11; }
          if ( $authorized != 11 ) { return 403; }
          access_log syslog:server=unix:/dev/log,nohostname custom ;
          error_log  syslog:server=unix:/dev/log warn;
        }
    }

    server {
        listen 8443 ssl;
        resolver                *************** valid=5 ipv6=off;
        server_name             $ssl_server_name;
        ssl_certificate         pki/$subdomain/server.pem;
        ssl_certificate_key     pki/$subdomain/server.key;
        ssl_client_certificate  ca.pem;
        ssl_verify_client       optional;
        ssl_verify_depth        3;
        ssl_protocols           TLSv1.2;
        ssl_ciphers             HIGH:!aNULL:!MD5;
        server_name *.ssvods-dev.vpc3.hsbc-9087302-unity-dev.dev.gcp.cloud.hk.hsbc 
                    *.ssvods-sit.vpc3.hsbc-9087302-unity-dev.dev.gcp.cloud.hk.hsbc 
                    *.ssvods-uat.vpc3.hsbc-9087302-unity-dev.dev.gcp.cloud.hk.hsbc 
                    *.ssvods-preprod.vpc3.hsbc-9087302-unity-dev.dev.gcp.cloud.hk.hsbc 
                    ;

        location / {
          proxy_pass $proxy_backend;
          proxy_http_version 1.1;
          proxy_set_header host $ssl_server_name;
          proxy_set_header Upgrade $http_upgrade;
          proxy_set_header Connection "upgrade";
          set $authorized 0;           
          set $auth_subdomain 0;
          set $auth_dn 0;
          if ( $app = "sa-ods-allgmnt-rst" ) { set $auth_subdomain 1; }
          if ( $app = "ods-allgmnt-api" ) { set $auth_subdomain 1; }
          if ( $ssl_client_s_dn = "CN=uat-cfs-services-consumer,OU=Securities Service,O=The Hong Kong Shanghai Banking Corporation Limited,L=Hong Kong,ST=HKSAR,C=CN" ) { set $auth_dn 1; }
          set $authorized "${auth_subdomain}${auth_dn}";
          if ( $authorized !~ ^(11|00)$ ) { return 403; }
          access_log syslog:server=unix:/dev/log,nohostname custom ;
          error_log  syslog:server=unix:/dev/log warn;
        }        
    }
}

