{{- if and (eq  .Values.customRbac.create true) (eq .Values.customRbac.useClusterRole false) -}}
{{- range (join "," $.Values.namespaces) | split "," }}
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  labels:
    {{- include "kube-state-metrics.labels" $ | indent 4 }}
  name: {{ template "kube-state-metrics.fullname" $ }}
  namespace: {{ . }}
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
{{- if (not $.Values.customRbac.useExistingRole) }}
  name: {{ template "kube-state-metrics.fullname" $ }}
{{- else }}
  name: {{ $.Values.customRbac.useExistingRole }}
{{- end }}
subjects:
- kind: ServiceAccount
  name: {{ template "kube-state-metrics.serviceAccountName" $ }}
  namespace: {{ template "kube-state-metrics.namespace" $ }}
{{- end -}}
{{- end -}}