---
spring:
  datasource:
    url: "********************************/${DB_NAME}"
    username: "${DB_USER}"
    password: "${DB_PASSWORD}"
  application:
    name: "udm-loc-sec-rd-tran-transform"
kafkaConfig:
  auto.offset.reset: "latest"
  bootstrap.servers: "kafka-0.kafka-internal.ns-core-prod-apps.svc.cluster.local:9093,kafka-1.kafka-internal.ns-core-prod-apps.svc.cluster.local:9093,kafka-2.kafka-internal.ns-core-prod-apps.svc.cluster.local:9093"
  group.id: "${KAFKA_CONSUMER_GROUP}"
  schema.registry.url: "https://hkl20077608.hc.cloud.hk.hsbc:8082/"
  security.protocol: "SSL"
  ssl.keystore.location: "/hss/apps/certs/unity-microservices.jks"
  ssl.keystore.password: "${KAFKA_KEYSTORE_PASSWORD}"
  ssl.truststore.location: "/hss/apps/certs/unity-microservices.ts"
  ssl.truststore.password: "${KAFKA_TRUSTSTORE_PASSWORD}"
  max.poll.records: "5000"
management:
  endpoints:
    web:
      exposure:
        include: "health,prometheus"
  metrics:
    tags:
      application: "${spring.application.name}"
  tracing:
    enabled: true
cache:
  provider: "kafka"
  address: "http://cache"
  topic: "unity2-PROD-core-cache-metric"
transformations:
- sourceBatchTopicSuffix: "-batch"
  sourceTopics:
  - "unity2-PROD-core-source-adaptor-udm-loc-sec-rd-tran-v1-2-0-out"
  transformers:
  - "UdmLocSecRdtranTransformer.groovy"
  targetTopic: "unity2-PROD-core-transform-udm-loc-sec-rd-tran-out"
  forwardDeletion: true
  targetBatchTopicSuffix: "-batch"
defaultDataPersistMode: "OVERWRITE"
pipelineName: "udm-loc-sec-rd-tran"
sourceGroup: "udm-loc-sec-rd-tran"
