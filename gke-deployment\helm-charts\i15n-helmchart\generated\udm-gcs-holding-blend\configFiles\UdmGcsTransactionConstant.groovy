class UdmGcsTransactionConstant {

    static final String GCS_ACCOUNT = "GCS_ACCOUNT"
    static final String GCS_TRADE = "GCS_TRADE"
    static final String GCS_COUNTERPARTY = "GCS_COUNTERPARTY"
    static final String GCS_LOCATION = "GCS_LOCATION"
    static final String GCS_INSTRUMENT = "GCS_INSTRUMENT"
    static final String GCS_INSTRUMENT_PRICE = "GCS_INSTRUMENT_PRICE"
    static final String GCS_CLIENT = "GCS_CLIENT"

    static final String _ID = "_id"
    static final String ACCOUNT_ID = "sec_acc_id"
    static final String PLACE_OF_SAFEKEEPING = "place_of_safekeeping"
    static final String LOCATION_ID = "country_loc_code"
    static final String INSTRUMENT_ID = "instrument_id"
    static final String LEGAL_ENTITY_ID = "legal_entity_id"
    static final String LEGAL_ENTITY_SUB_FUND_INDICATOR = "legal_entity_sub_fund_indicator"
    static final String COUNTERPARTY_BROKER_NAME = "broker_id_name"
    static final String COUNTERPARTY_BUYER_SELLER_NAME = "buyer_seller_id_name"
    static final String COUNTERPARTY_INTERMEDIARY_NAME = "intermediary_party_id_name"
    static final String COUNTERPARTY_BROKER_TYPE = "broker_id_type"
    static final String COUNTERPARTY_BUYER_SELLER_TYPE = "buyer_seller_id_type"
    static final String COUNTERPARTY_INTERMEDIARY_TYPE = "intermediary_party_id_type"
    static final String COUNTERPARTY_BROKER_SWIFT_CODE = "counterparty_broker_swift_code"
    static final String COUNTERPARTY_BUYER_SELLER_SWIFT_CODE = "counterparty_buyer_seller_swift_code"
    static final String COUNTERPARTY_INTERMEDIARY_SWIFT_CODE = "counterparty_intermediary_swift_code"
    static final String DELIVER_SECURITY_MOVEMENT_TYPE = "DELI";
    static final String RECEIVE_SECURITY_MOVEMENT_TYPE = "RECE";

    public static final List<String> COUNTERPARTY_FIELDS = Collections.unmodifiableList(
            new ArrayList<String>() {
                {
                    add("_id")
                    add("swift_bic")
                }
            })

    public static final List<String> ACCOUNT_FIELDS = Collections.unmodifiableList(
            new ArrayList<String>() {
                {
                    add("_id")
                    add("acc_region")
                    add("acc_short_name")
                    add("acc_currency_code")
                    add("acc_currency_role")
                    add("acc_currency_name")
                    add("hbfr_acc_indicator")
                    add("legal_entity_sub_fund_indicator")
                    add("legal_entity_id")
                }
            })

    public static final List<String> LOCATION_FIELDS = Collections.unmodifiableList(
            new ArrayList<String>() {
                {
                    add("_id")
                    add("place_of_safekeeping_country_code")
                    add("place_of_safekeeping_name")
                }
            })

    public static final List<String> INSTRUMENT_FIELDS = Collections.unmodifiableList(
            new ArrayList<String>() {
                {
                    add("_id")
                    add("description")
                    add("type")
                    add("ccy")
                    add("qty_unit")
                    add("isin")
                    add("sedol")
                    add("cusip")
                    add("registrar_id")
                    add("registrar_desc")
                    add("maturity_date")
                }
            })
    public static final List<String> INSTRUMENT_PRICE_FIELDS = Collections.unmodifiableList(
            new ArrayList<String>() {
                {
                    add("_id")
                    add("market_price")
                    add("currency")
                }
            })
    public static final List<String>CLIENT_FIELDS  = Collections.unmodifiableList(
            new ArrayList<String>() {
                {
                    add("_id")
                    add("client_service_location_code")
                }
            })
}