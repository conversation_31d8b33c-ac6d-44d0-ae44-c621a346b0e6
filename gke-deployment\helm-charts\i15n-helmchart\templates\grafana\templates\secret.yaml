{{- if and (or (and (not .Values.admin.existingSecret) (not .Values.env.GF_SECURITY_ADMIN_PASSWORD)) (and .Values.ldap.enabled (not .Values.ldap.existingSecret))) (not .Values.env.GF_SECURITY_DISABLE_INITIAL_ADMIN_CREATION) }}
apiVersion: v1
kind: Secret
metadata:
  name: {{ template "grafana.fullname" . }}
  namespace: {{ template "grafana.namespace" . }}
  labels:
    {{- include "grafana.labels" . | nindent 4 }}
{{- with .Values.annotations }}
  annotations:
{{ toYaml . | indent 4 }}
{{- end }}
type: Opaque
data:
  {{- if and (not .Values.admin.existingSecret) (not .Values.env.GF_SECURITY_ADMIN_PASSWORD) }}
  admin-user: {{ .Values.adminUser | b64enc | quote }}
  {{- if .Values.adminPassword }}
  admin-password: {{ .Values.adminPassword | b64enc | quote }}
  {{- else }}
  admin-password: {{ template "grafana.password" . }}
  {{- end }}
  {{- end }}
  {{- if not .Values.ldap.existingSecret }}
  ldap-toml: {{ tpl .Values.ldap.config $ | b64enc | quote }}
  {{- end }}
{{- end }}
