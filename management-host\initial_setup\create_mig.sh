#!/usr/bin/env bash
set -euo pipefail
#please pass the iput vale either dev or prod like sh ./initial_setup/create_gcebucket dev
profile_env=$1

echo "[MGMT_HOST] checking for default conf"
if [ ! -f "./initial_setup/var-${profile_env}.conf" ]; then
 echo "Parameter file var-${profile_env}.conf not found in initial_setup."
 exit 1
fi
source ./initial_setup/var-${profile_env}.conf

echo " ${MIG_NAME} ${REGION} ${INSTANCE_TEMPLATE_NAME} ${SIZE} ${PROJECT_ID}"
gcloud compute instance-groups managed create ${MIG_NAME} --region ${REGION} --template ${INSTANCE_TEMPLATE_NAME}  --size ${SIZE}  --project ${PROJECT_ID}
