{"machine_type": "n2-standard-4", "source_image_project_id": "hsbc-6320774-distcompute-prod", "source_image_family": "gce-rhel7-ver2-stage2", "image_description": "Management Host: can be used as Jenkins agent or Developer VM", "name_prefix": "gce", "image_name": "mgmt-host-release", "image_family": "gce-image", "image_faimly_suffix": "rhel7-mgmt-host-unity", "image_encryption_key": "projects/hsbc-6320774-kms-dev/locations/asia-east2/keyRings/computeEngine/cryptoKeys/HSMcomputeEngine", "disk_size": "200", "tags": "cmbsp02-128-13-212-71-32-a-e-all,dev-default-out-rule,fwtag-all-dev-in-alm-jenkins-1-production,fwtag-all-dev-in-alm-jenkins-2-production,fwtag-all-dev-out-alm-github,fwtag-all-dev-out-alm-nexus-2-production,fwtag-all-dev-out-alm-nexus-3-production,fwtag-all-dev-out-alm-nexus-3-uat,fwtag-all-dev-out-efx-nexus-2-production,fwtag-all-dev-out-efx-nexus-3-production,fwtag-gke-nodepool,https,st-********-leapxuat-mongo,st-********-leapxuat-uatmongo,t-6320774-dev-in-all-jenkinsslave-ssh,t-all-dev-all-in-ilb-healthcheck,t-all-dev-in-drn-jumpservers,t-dev-all-out-alm-nexus-3-docker-uat,t-dev-all-out-drn-proxy,t-hsbc-cmbsp-sb-all-in-vpc1,tt-********-leapxuat-128-13-210-72-a-e-jenkins,tt-********-leapxuat-128-13-210-72-a-i-jenkins,tt-********-leapxuat-128-161-212-105-a-e-jenkins,tt-********-leapxuat-128-161-212-105-a-i-jenkins,tt-********-leapxuat-gke-a-e-nodepool,tt-********-leapxuat-gke-a-i-nodepool,tt-********-leapxuat-10-91-0-0-x-18-a-e-uatmongo,tt-********-leapxuat-source-tag-a-i-uatmongo,tt-6320774-vpchost-asia-cinternal-drn-a-i-ssh,tt-9820327-cmbsp02-128-13-212-71-32-a-i-all,tt-9820327-cmbsp02-128-161-210-105-32-a-e-all,tt-9820327-cmbsp02-128-161-210-105-32-a-i-all,tt-9820327-cmbsp02-130-178-53-232-a-e-all,tt-9820327-cmbsp02-130-178-53-232-a-i-all,tt-9820327-cmbsp05-10-91-0-0-18-a-e-all,tt-9820327-cmbsp05-10-91-0-0-18-a-i-all,tt-9820327-cmbsp05-10-91-64-0-18-a-e-all,tt-9820327-cmbsp05-10-91-64-0-18-a-i-all,tt-9820327-cmbsp05-128-13-212-71-32-a-e-all,tt-9820327-cmbsp05-128-13-212-71-32-a-i-all,tt-9820327-cmbsp05-128-161-210-105-32-a-e-all,tt-9820327-cmbsp05-128-161-210-105-32-a-i-all,tt-9820327-cmbsp05-130-178-53-232-32-a-i-all,tt-9820327-cmbsp05-130-178-55-232-32-a-i-all,tt-9820327-cmbsp05-130-178-57-232-32-a-i-all,tt-9820327-cmbsp12-check-a-i-all,tt-9820327-cmbsp12-gce-a-e-all,tt-9820327-cmbsp12-gce-a-i-all,tt-9820327-cmbsp12-jenkins-a-e-all,tt-9820327-cmbsp12-jenkins-a-i-all,tt-9820327-cmbsp12-jumphost21-a-i-all,tt-9820327-cmbsp12-jumphost22-a-i-all,tt-9820327-cmbsp13-10-98-0-0-18-a-e-all,tt-9820327-cmbsp13-10-98-0-0-18-a-i-all,tt-9820327-cmbsp13-10-98-64-0-18-a-e-all,tt-9820327-cmbsp13-10-98-64-0-18-a-i-all,tt-********-leapxuat-10-98-0-0-17-a-i-uatcidmzpx,tt-********-leapxuat-10-98-0-0-17-a-e-uatcidmzpx", "project_id": "hsbc-9087302-unity-dev", "region": "asia-east2", "zone": "asia-east2-a", "service_account_email": "<EMAIL>", "scopes": "https://www.googleapis.com/auth/cloud-platform", "subnetwork": "projects/hsbc-6320774-vpchost-asia-dev/regions/asia-east2/subnetworks/cinternal-vpc1-asia-east2", "STASH_TOKEN": "fee74e5f6e7601eaa27e0275093060236e33d15c", "build_path": "/tmp/build", "java_version": "java-1.8.0-openjdk", "git_version": "git-2.17.1.tar.gz", "docker_version": "docker-18.09.6.tgz", "docker_path": "/usr/bin", "packer_version": "packer_1.6.6-1.6.6.zip", "packer_path": "/usr/bin", "groovy_path": "/opt/jenkins_agent/build_tools/groovy", "groovy_version": "groovy-binary-3.0.6.zip", "maven_binary": "apache-maven-3.6.2-bin.tar.gz", "maven_version": "apache-maven-3.6.2", "maven_path": "/opt/maven", "terraform_version": "0.13.7", "terraform_path": "/opt/terraform", "jq_binary": "jq-1.5.gz", "jq_path": "/opt/jq", "jq_version": "1.5", "nexus_iq_path": "/opt/nexus_iq", "nexus_iq_version": "1.87.0-02", "jenkins_path": "/opt/jenkins_agent", "go_root": "/opt/.go", "go_path": "/opt/go", "go_version": "1.14.6", "bucket_binaries_path": "gs://hsbc-9087302-unity-dev-mgmt-host-pipeline/mgmt_host_binaries", "yum_dataplatforms_version": "yum-dataplatforms-1.0.1-4.x86_64.rpm", "STAGE": "stage2", "BUILDSTAGE3": "false"}