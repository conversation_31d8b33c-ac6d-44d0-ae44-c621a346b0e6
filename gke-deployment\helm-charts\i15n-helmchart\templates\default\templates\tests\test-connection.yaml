apiVersion: v1
kind: Pod
metadata:
  name: "{{ include "i15n-helmchart.fullname" . }}-test-connection"
  labels:
    {{- include "i15n-helmchart.labels" . | nindent 4 }}
  annotations:
    "helm.sh/hook": test-success
spec:
  containers:
    - name: wget
      image: busybox
      command: ['wget']
      args: ['{{ include "i15n-helmchart.fullname" . }}:{{ .Values.service.port }}']
  restartPolicy: Never
