#!/usr/bin/env bash
set -euo pipefail

echo "net.ipv4.ip_forward=0" >>  /etc/sysctl.conf
echo "net.ipv4.conf.all.accept_redirects=0" >>  /etc/sysctl.conf
echo "net.ipv4.conf.default.accept_redirects=0" >>  /etc/sysctl.conf
echo "net.ipv4.conf.all.secure_redirects=0" >>  /etc/sysctl.conf
echo "net.ipv4.conf.default.secure_redirects=0" >>  /etc/sysctl.conf
sysctl -w net.ipv4.conf.all.accept_redirects=0
sysctl -w net.ipv4.conf.default.accept_redirects=0
sysctl -w net.ipv4.conf.all.secure_redirects=0
sysctl -w net.ipv4.conf.default.secure_redirects=0
sysctl -w net.ipv4.route.flush=1
echo "wenjiansys"
cat /etc/sysctl.conf

grep "net\.ipv4\.conf\.default\.accept_redirects" /etc/sysctl.conf /etc/sysctl.d/*