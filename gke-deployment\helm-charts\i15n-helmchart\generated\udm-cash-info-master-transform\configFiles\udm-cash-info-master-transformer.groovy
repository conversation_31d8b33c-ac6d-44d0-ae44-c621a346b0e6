import com.poc.hss.fasttrack.transform.AbstractTransformer
import com.poc.hss.fasttrack.transform.model.TransformerBatchInputContext
import com.poc.hss.fasttrack.transform.model.TransformerRecord
import io.vertx.core.json.JsonObject
import org.slf4j.Logger
import org.slf4j.LoggerFactory

import java.util.stream.Collectors

class CashAccountInfoMasterTransformer extends AbstractTransformer {
    JsonObject constructRecord(JsonObject source){
        return new JsonObject()
                .put("site", source.getString("SITE")?.trim())
                .put("LWC0040OAT", source.getString("LWC0040OAT")?.trim())
                .put("LWC0040OAC", source.getString("LWC0040OAC")?.trim())
                .put("LWC0040OAF", source.getString("LWC0040OAF")?.trim())
                .put("LWACSN", source.getString("LWACSN")?.trim())
    }

    private static final Logger logger = LoggerFactory.getLogger(CashAccountInfoMasterTransformer.class)

    @Override
    List<TransformerRecord> batchTransform(TransformerBatchInputContext context) {
        List<TransformerRecord> records = context.getRecords()
        return records.stream().map({
            rec ->
                JsonObject source = rec.getData();
                return rec.from(rec).data(constructRecord(source)).build()
        }).collect(Collectors.toList())
    }
}