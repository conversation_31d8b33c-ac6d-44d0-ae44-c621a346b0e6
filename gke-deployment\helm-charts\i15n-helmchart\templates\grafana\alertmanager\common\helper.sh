#!/bin/sh
#api reference: https://grafana.com/docs/grafana/latest/developers/http_api/
#alert provisioning: https://grafana.com/docs/grafana/latest/developers/http_api/alerting_provisioning/

constructor() {
  GRAFANA_HOST="http://127.0.0.1:3000"
  if ! [ -z "${GF_SECURITY_ADMIN_USER__FILE}" ]; then
    export REQ_USERNAME=`cat $GF_SECURITY_ADMIN_USER__FILE`
  fi
  if ! [ -z "${GF_SECURITY_ADMIN_PASSWORD__FILE}" ]; then
    export REQ_PASSWORD=`cat $GF_SECURITY_ADMIN_PASSWORD__FILE`
  fi
  export GRAFANA_VERSION=$(curl -s ${GRAFANA_HOST}/api/health | jq -r '.version' | cut -f1 -d.)
  echo GRAFANA_VERSION:${GRAFANA_VERSION}
}

alert_rule_handler() {
  if [ "${GRAFANA_VERSION}" = "9" ]; then 
    if [ $(curl -s -u ${REQ_USERNAME}:${REQ_PASSWORD} -XGET -H 'Content-Type: application/json' ${GRAFANA_HOST}/api/v1/provisioning/contact-points | jq '.[]|select(.uid =="xmatter-webhook")'  | wc -l) -gt 0 || $(curl -s -u ${REQ_USERNAME}:${REQ_PASSWORD} -XGET -H 'Content-Type: application/json' ${GRAFANA_HOST}/api/v1/provisioning/contact-points | jq '.[]|select(.uid =="xmatter-symphony")'  | wc -l) -gt 0 ]; then
      _tmp_file_=/tmp/$(basename $0).out
      ls $FOLDER/*.alert-rule.json 2> /dev/null | while read f; do
        uid=$(cat $f | jq -r '.uid')
        r=$(curl -s -u ${REQ_USERNAME}:${REQ_PASSWORD} -XPUT -H 'Content-Type: application/json' -w "%{http_code}\n" ${GRAFANA_HOST}/api/v1/provisioning/alert-rules/${uid} -d @$f -o ${_tmp_file_} )
        if [ $r -ne 200 ] ; then
          curl -s -u ${REQ_USERNAME}:${REQ_PASSWORD} -XPOST -H 'Content-Type: application/json' ${GRAFANA_HOST}/api/v1/provisioning/alert-rules -d @$f
        else
          cat $_tmp_file_
        fi
      done
    fi
  elif [ "${GRAFANA_VERSION}" = "10" ]; then     
    curl -s -u ${REQ_USERNAME}:${REQ_PASSWORD} -XPOST ${GRAFANA_HOST}/api/admin/provisioning/alerting/reload
  fi
}

contact_point_handler() {
  _tmp_file_=/tmp/$(basename $0).out
  ls $FOLDER/*.contact-point.json 2> /dev/null | while read f; do
    uid=$(cat $f | jq -r '.uid')
    r=$(curl -s -u ${REQ_USERNAME}:${REQ_PASSWORD} -XPUT -H 'Content-Type: application/json' -w "%{http_code}\n" ${GRAFANA_HOST}/api/v1/provisioning/contact-points/${uid} -d @$f -o ${_tmp_file_} )
    if [ $r -ne 202 ] ; then
      curl -s -u ${REQ_USERNAME}:${REQ_PASSWORD} -XPOST -H 'Content-Type: application/json' ${GRAFANA_HOST}/api/v1/provisioning/contact-points -d @$f
    else
      cat $_tmp_file_
    fi
  done
}

notification_policy_handler() {
  if [ $(curl -s -u ${REQ_USERNAME}:${REQ_PASSWORD} -XGET -H 'Content-Type: application/json' ${GRAFANA_HOST}/api/v1/provisioning/contact-points | jq '.[]|select(.uid =="xmatter-webhook")'  | wc -l) -gt 0 ]; then
    _tmp_file_=/tmp/$(basename $0).out
    ls $FOLDER/*.notification-policy.json 2> /dev/null  | while read f; do
      r=$(curl -s -u ${REQ_USERNAME}:${REQ_PASSWORD} -XPUT -H 'Content-Type: application/json' -w "%{http_code}\n" ${GRAFANA_HOST}/api/v1/provisioning/policies -d @$f -o ${_tmp_file_} )
      cat $_tmp_file_
    done
  fi
}

#########################################################
# main
#########################################################
constructor
contact_point_handler
notification_policy_handler
alert_rule_handler