#!/usr/bin/env bash
set -euo pipefail

echo "<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<< AppD machine agent install >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>"
echo "[MGMT_HOST] starting appd machine agent installation"

mkdir -p $APPD_MACHINEAGENT_PATH/machineagent

wget --user=${NEXUS_CREDS_USR} --password=${NEXUS_CREDS_PWD}  https://hkl20090861.hc.cloud.hk.hsbc/devops/$APPD_MACHINEAGENT_VERSION -O $APPD_MACHINEAGENT_PATH/machineagent/machineagent.zip

unzip $APPD_MACHINEAGENT_PATH/machineagent/machineagent.zip -d $APPD_MACHINEAGENT_PATH/machineagent/

sudo useradd appdmon
sudo groupadd appdmongrp
sudo usermod -a -G appdmongrp appdmon

chown appdmon:appdmongrp -R $APPD_MACHINEAGENT_PATH
chown appdmon:appdmongrp -R $APPD_MACHINEAGENT_PATH/
chown appdmon:appdmongrp -R $APPD_MACHINEAGENT_PATH/machineagent
chmod 777 $APPD_MACHINEAGENT_PATH/machineagent/*
chmod 777 $APPD_MACHINEAGENT_PATH/machineagent/bin/machine-agent

cp $BUILD_PATH/appd/appdynamics-machine-agent.service  /etc/systemd/system/appdynamics-machine-agent.service

systemctl enable appdynamics-machine-agent