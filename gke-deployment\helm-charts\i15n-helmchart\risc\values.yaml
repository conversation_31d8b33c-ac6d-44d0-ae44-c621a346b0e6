---
containerPort: 8080
nameOverride: "risc"
replicaCount: 1
namespace: "ns-i15n-dev"

autoscaling:
  enabled: true
  minReplicas: 1
  maxReplicas: 2
  targetCPUUtilizationValue: "400m"

podAnnotations:
  prometheus.io/scrape: "true"
  prometheus.io/port: "8080"
  prometheus.io/path: "/actuator/prometheus"
  cluster-autoscaler.kubernetes.io/safe-to-evict: "true"

serviceAccount:
  name: "ns-i15n-dev-sa"

imagePullSecrets:
- name: "docker-secret"

image:
  repository: "gcr.io/hsbc-9087302-unity-dev"
  name: "risc"
  prefix: "unity/i15n/"
  tag: "0.0.1-SNAPSHOT"
  pullPolicy: "Always"

service:
  enabled: true
  type: ClusterIP
  port: 80
  targetPort: 8080

ingress:
  enabled: true
  annotations:
    kubernetes.io/ingress.class: nginx
    nginx.ingress.kubernetes.io/rewrite-target: /
    external-dns.alpha.kubernetes.io/ingress-hostname-source: "annotation-only"
    external-dns.alpha.kubernetes.io/hostname: "risc.hsbc-9087302-unity-dev.dev.gcp.cloud.hk.hsbc"
  hosts:
    - host: risc.hsbc-9087302-unity-dev.dev.gcp.cloud.hk.hsbc
      paths:
        - path: /
          pathType: Prefix

resources:
  limits:
    cpu: 500m
    memory: 512Mi
  requests:
    cpu: 250m
    memory: 256Mi

env:
  - name: SPRING_PROFILES_ACTIVE
    value: "dev"
  - name: JAVA_OPTS
    value: "-Xmx384m -Xms256m"

# Health checks (will need to add actuator dependency)
livenessProbe:
  httpGet:
    path: /actuator/health/liveness
    port: 8080
  initialDelaySeconds: 60
  periodSeconds: 30
  timeoutSeconds: 5
  failureThreshold: 3

readinessProbe:
  httpGet:
    path: /actuator/health/readiness
    port: 8080
  initialDelaySeconds: 30
  periodSeconds: 10
  timeoutSeconds: 5
  failureThreshold: 3

# Security context
securityContext:
  runAsNonRoot: true
  runAsUser: 1000
  allowPrivilegeEscalation: false
  capabilities:
    drop:
      - ALL

# Volume mounts for configuration
volumeMounts:
  - name: tmp-volume
    mountPath: /tmp

volumes:
  - name: tmp-volume
    emptyDir: {}

# Node selector for specific node pools if needed
nodeSelector: {}

# Tolerations for node taints
tolerations: []

# Pod affinity/anti-affinity rules
affinity: {}

# Termination grace period
terminationGracePeriodSeconds: 30
