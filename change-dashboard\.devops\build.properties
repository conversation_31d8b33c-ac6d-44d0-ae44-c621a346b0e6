# Build Configuration for change-dashboard
mvn_build_enable=true
docker_build_enable=true
jdk_version=17
mvn_skip_tests=false
mvn_maven_test_skip=false

# Docker Configuration
docker_registry_nexus_path=unity/i15n/
docker_image_name_prefix=change-dashboard-
java_base_image=nexus3.systems.uk.hsbc:18096/com/hsbc/group/itid/es/mw/java/mw-azuljava-v17:17.36

# Security Scanning
container_scan_enable=true
sast_scan_enable=true

# Maven Options
mvn_opts=-Xmx2048m -Djavax.net.ssl.trustStore=/etc/pki/java/cacerts
