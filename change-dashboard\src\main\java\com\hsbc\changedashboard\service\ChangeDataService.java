package com.hsbc.changedashboard.service;

import com.hsbc.changedashboard.model.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

@Service
public class ChangeDataService {

    private static final Logger logger = LoggerFactory.getLogger(ChangeDataService.class);

    @Autowired
    private IceApiService iceApiService;

    @Autowired
    private JiraApiService jiraApiService;

    @Autowired
    private PoddyApiService poddyApiService;

    private final ExecutorService executorService = Executors.newFixedThreadPool(10);

    /**
     * Main method to get enriched change data following all the steps
     */
    public List<EnrichedChangeRecord> getEnrichedChangeData() {
        logger.info("Starting enriched change data collection process");

        try {
            // Step 1: Get changes from ICE API
            logger.info("Step 1: Fetching changes from ICE API");
            IceApiResponse iceResponse = iceApiService.getChanges();
            
            if (iceResponse == null || iceResponse.getChanges() == null || iceResponse.getChanges().isEmpty()) {
                logger.warn("No changes found from ICE API");
                return new ArrayList<>();
            }

            List<ChangeRecord> changes = iceResponse.getChanges();
            logger.info("Found {} changes from ICE API", changes.size());

            // Step 2-8: Process each change record and enrich with JIRA and person data
            List<CompletableFuture<EnrichedChangeRecord>> futures = new ArrayList<>();

            for (ChangeRecord change : changes) {
                CompletableFuture<EnrichedChangeRecord> future = CompletableFuture.supplyAsync(() -> {
                    return enrichChangeRecord(change);
                }, executorService);
                futures.add(future);
            }

            // Wait for all enrichment tasks to complete
            List<EnrichedChangeRecord> enrichedRecords = new ArrayList<>();
            for (CompletableFuture<EnrichedChangeRecord> future : futures) {
                try {
                    EnrichedChangeRecord enrichedRecord = future.get();
                    if (enrichedRecord != null) {
                        enrichedRecords.add(enrichedRecord);
                    }
                } catch (Exception e) {
                    logger.error("Error processing change record", e);
                }
            }

            logger.info("Successfully enriched {} change records", enrichedRecords.size());
            return enrichedRecords;

        } catch (Exception e) {
            logger.error("Error in enriched change data collection process", e);
            throw new RuntimeException("Failed to get enriched change data", e);
        }
    }

    /**
     * Enrich a single change record with JIRA and person information
     */
    private EnrichedChangeRecord enrichChangeRecord(ChangeRecord change) {
        try {
            logger.debug("Enriching change record: {}", change.getSnCrNumber());

            // Create enriched record from original change record
            EnrichedChangeRecord enriched = new EnrichedChangeRecord(change);

            // Step 3: Extract JIRA ID from description
            String jiraId = jiraApiService.extractJiraId(change.getGsdDescription());

            if (jiraId != null) {
                // Always store the extracted JIRA ID
                enriched.setJiraId(jiraId);

                // Step 4-5: Get JIRA issue details
                logger.debug("Fetching JIRA details for: {}", jiraId);
                JiraIssue jiraIssue = jiraApiService.getJiraIssue(jiraId);

                if (jiraIssue != null) {
                    enriched.setJiraKey(jiraIssue.getKey());
                    if (jiraIssue.getFields() != null) {
                        enriched.setJiraSummary(jiraIssue.getFields().getSummary());

                        // Extract creator staff ID from JIRA creator field
                        if (jiraIssue.getFields().getCreator() != null) {
                            String creatorStaffId = jiraIssue.getFields().getCreator().getName();
                            enriched.setJiraCreatorStaffId(creatorStaffId);
                            logger.debug("Extracted creator staff ID: {} from JIRA issue: {}", creatorStaffId, jiraId);
                        }
                    }
                    logger.debug("Successfully enriched with JIRA data for: {}", jiraId);
                } else {
                    logger.warn("Failed to fetch JIRA issue for: {}", jiraId);
                }
            } else {
                logger.debug("No JIRA ID found in description for change: {}", change.getSnCrNumber());
            }

            // Step 6-8: Get person information for assignee
            String assigneeStaffId = change.getGsdAssigneeUserStaffID();
            if (assigneeStaffId != null && !assigneeStaffId.trim().isEmpty()) {
                logger.debug("Fetching person info for assignee staff ID: {}", assigneeStaffId);
                PersonInfo assigneeInfo = poddyApiService.getPersonInfo(assigneeStaffId);

                if (assigneeInfo != null) {
                    enriched.setAssigneeName(assigneeInfo.getName());
                    enriched.setAssigneeEmail(assigneeInfo.getEmail());
                    enriched.setAssigneePhotoUrl(assigneeInfo.getSquarePhotoUrl());
                    logger.debug("Successfully enriched with assignee person data for staff ID: {}", assigneeStaffId);
                } else {
                    logger.warn("Failed to fetch assignee person info for staff ID: {}", assigneeStaffId);
                }
            } else {
                logger.debug("No assignee staff ID found for change: {}", change.getSnCrNumber());
            }

            // Step 9: Get person information for creator (from JIRA)
            String creatorStaffId = enriched.getJiraCreatorStaffId();
            if (creatorStaffId != null && !creatorStaffId.trim().isEmpty()) {
                logger.debug("Fetching person info for creator staff ID: {}", creatorStaffId);
                PersonInfo creatorInfo = poddyApiService.getPersonInfo(creatorStaffId);

                if (creatorInfo != null) {
                    enriched.setCreatorName(creatorInfo.getName());
                    enriched.setCreatorEmail(creatorInfo.getEmail());
                    enriched.setCreatorPhotoUrl(creatorInfo.getSquarePhotoUrl());
                    logger.debug("Successfully enriched with creator person data for staff ID: {}", creatorStaffId);
                } else {
                    logger.warn("Failed to fetch creator person info for staff ID: {}", creatorStaffId);
                }
            } else {
                logger.debug("No creator staff ID found for change: {}", change.getSnCrNumber());
            }

            return enriched;

        } catch (Exception e) {
            logger.error("Error enriching change record: {}", change.getSnCrNumber(), e);
            // Return the basic enriched record even if enrichment fails
            return new EnrichedChangeRecord(change);
        }
    }
}
