def AGENT = 'unity-dev-jenkins-agent'
def charts = []
def undeployCharts = []
def deploymentEnv = ''
def rolloutStatusCallbackUrl = ''
def rolloutStatusId = ''
def namespaces = []
def filesToBeCleanup = []
def testHistoryCallbackUrl = ''
def testComponentList = []
def testReportUrl = BUILD_URL + "Html_20Report/"
def v_gcpKeyBucketAppDep = "hsbc-9087302-unity-dev-key-management"
def updateDeploymentBody = [:]
def v_impersonateServiceAccount = ""
def v_authMethod = ""
def deployControlCharts = []
def deployControlUndeployCharts = []
def releaseCharts = []
def releaseUndeployCharts = []
def allowedUndeployCharts = []
def clusterType = "GKE"
def jenkinsCredential  = ""
def v_kubehome = ""

pipeline {
    agent { label env.AGENT_LABEL ?: AGENT }
    parameters {
        text(name: "chartsList", description: "Multi-line parameter (csv), default empty to deploy all services")
        text(name: "undeployChartsList", description: "Multi-line parameter (csv), default empty not to undeploy services")
        string(name: "changeOrder", description: "SNOW change order number")
        string(name: "unityReleaseVersion", description: "unity_release_version is used for LTTD calculation: documentation: https://gbmt-confluence.prd.fx.gbm.cloud.uk.hsbc/x/p0vnSQ")
        booleanParam(name: "rollback", defaultValue: false, description: "rollback")       
    }
    environment {
        ICE_AUTH_TOKEN = credentials('ICE_AUTH_TOKEN')
    }
    stages {
        stage("Initial Environment") {
            steps {
                script {  
                       if (params.jenkinsCredential) {
                         jenkinsCredential = params.jenkinsCredential
                       } else if (env.jenkinsCredential) {
                         jenkinsCredential = env.jenkinsCredential
                       }
                       if (params.clusterType) {
                         clusterType = params.clusterType
                       } else if (env.clusterType) {
                         clusterType = env.clusterType
                       }
                       if (kubectlProxy != "" && !kubectlProxy.contains(":")) {
                           kubectlProxy += ":3128"
                       }                       
                       echo "kubectlProxy is set to: ${kubectlProxy}"                                            
                    }
                }
            
        }   
        stage('Connect GCP') {
            steps {
                script {
                    v_authMethod = env.authMethod ?: "IAM-DIRECT"
                    env.v_project = this.sh(
                            script: """gcloud config list --format 'value(core.project)'""",
                            returnStdout: true
                    ).trim()
                    if (params.clusterProject) {
                        clusterProjectOption = "--project ${clusterProject}"
                    } else {
                        clusterProjectOption = ""
                    }
                    if (params.clusterRegion) {
                       v_clusterRegion = "${clusterRegion}"
                    } else {
                       v_clusterRegion = 'asia-east2'
                    }
                    if (params.serviceAccount) {
                       v_serviceAccount = "${serviceAccount}"
                    } else {
                       v_serviceAccount = ""
                    }
                    if (params.clusterName) {
                       env.v_clusterName = "${clusterName}"
                    } else {
                       if (v_authMethod == 'IAM-DIRECT') {
                         env.v_clusterName = this.sh(
                                 script: """gcloud container clusters list --filter="NAME:gke-t2-vpc3" ${clusterProjectOption} --region=${v_clusterRegion} --format 'value(NAME)'""",
                                 returnStdout: true
                         ).trim()
                       }
                    }
                    if (params.kubectlProxy) {
                       env.v_kubeCtlLib = "${kubectlProxy}"
                    } else {
                       if (v_authMethod == 'IAM-DIRECT') {
                         env.v_kubeCtlLib = this.sh(
                                 script: """gcloud compute addresses list --filter="NAME:gke-kubectl-vpc3" --format 'value(ADDRESS)'""",
                                 returnStdout: true
                         ).trim()
                       }
                    }
                    clusterEnv = "gcp"
                }
            }
        }
        stage("Connect to KOPS") {
            // What Role to use
            when {
                expression {
                    clusterType == "KOPS"
                }
            }
            steps {
                script {
                    env.v_project = this.sh(
                            script: """gcloud config list --format 'value(core.project)'""",
                            returnStdout: true
                    ).trim()
                    v_kubehome = "$HOME/kubeconfig/kop"
                    this.sh """mkdir -p ${v_kubehome};
                               gsutil -m cp -r gs://${v_project}-app-runtime-dependencies/secrets/env/kop/kubeconfig/* ${v_kubehome}/;"""
                    clusterEnv = "kop"
                }
            }
        }
        stage("Connect to IKP") {
            // What Role to use
            when {
                expression {
                    clusterType == "IKP"
                }
            }
            steps {
                script {
                    env.v_project = this.sh(
                            script: """gcloud config list --format 'value(core.project)'""",
                            returnStdout: true
                    ).trim()
                    v_kubehome = "$HOME/kubeconfig/ikp"
                    this.sh """mkdir -p ${v_kubehome};
                               gsutil -m cp -r gs://${v_project}-app-runtime-dependencies/secrets/env/ikp/kubeconfig/* ${v_kubehome}/;"""
                    clusterEnv = "ikp"
                }
            }
        }        
        stage("Connect to ALI") {
            // What Role to use
            when {
                expression {
                    clusterType == "ALI"
                }
            }
            steps {
                script {
                    withCredentials([file(credentialsId: jenkinsCredential, variable: 'KUBECONFIG_FILE')]) {
                       this.sh(
                           script: """cp ${KUBECONFIG_FILE} ${env.WORKSPACE}/${jenkinsCredential};""",
                           returnStdout: true
                       )
                       clusterEnv = "ali"
                    }
                }
            }
        }                                   
        stage("create environment variable file") {
            steps {
                script {
                        this.sh """
                                cat > /tmp/.${clusterType}.${clusterProject}.${clusterRegion}.${clusterName}.env <<-'EOT'

                                function authenticate() {			    
                                  case ${authMethod} in 
                                  IAM-DIRECT)
                                     unset HTTPS_PROXY
                                     gcloud auth activate-service-account --key-file $HOME/ServiceAccountKeys/${v_serviceAccount}.json;
                                     gcloud container clusters get-credentials ${clusterName} --region=${clusterRegion} --project=${clusterProject}	
                                	 export HTTPS_PROXY=${kubectlProxy}
                                	 ;;
                                  KUBECONFIG-JENKINS-CREDENTIAL)		
                                     export NO_PROXY=${kubectlProxy}
                                     export HTTPS_PROXY=${kubectlProxy}
                                     export KUBECONFIG=${env.WORKSPACE}/${jenkinsCredential}
                                     ;;
                                  KUBECONFIG)
                                     case ${clusterType} in 
                                     KOPS)
                                       unset HTTPS_PROXY
                                       export KUBECONFIG=${v_kubehome}/\${_namespace_}.\${_cluster_};	   
                                	   ;;
                                     IKP)
                                       unset HTTPS_PROXY
                                       export KUBECONFIG=${v_kubehome}/\${_namespace_}.\${_cluster_};	   
                                	   ;;                                       
                                	 GKE)
                                	   export KUBECONFIG=${KUBECONFIG}
                                	   export HTTPS_PROXY=${kubectlProxy}				   
                                       ;;			   
                                	 esac
                                     ;;				 
                                  esac
                                }
EOT
                                cat /tmp/.${clusterType}.${clusterProject}.${clusterRegion}.${clusterName}.env
                                """
                    }
                }
            
        }                
        stage('Set Properties') {
            steps {
                script {
                    echo "ON BRANCH: ${env.GIT_BRANCH} "

                    dir('helm-charts/i15n-helmchart/generated/') {
                        if (fileExists('.env')) {
                            def envString = readFile('.env')
                            deploymentEnv = envString.split(',')[0]
                            deploymentEnvType = envString.split(',')[1]
                        }

                        if (fileExists('.version')) {
                            version = readFile('.version')
                            currentBuild.displayName = version
                        }

                        if (fileExists('.rolloutStatusCallbackUrl')) {
                            rolloutStatusCallbackUrl = readFile('.rolloutStatusCallbackUrl')
                        }
                    }
                    
                    echo "checking for deployment control"
                    cloneDeploymentControl(params.changeOrder)

                    if ("${chartsList}".isEmpty()) {

                        echo "chartsList is empty, calling checkoutDeploymentControl"

                        if (fileExists('gke-deployment-control')) {
                          dir('gke-deployment-control') {
                          if (fileExists('.chartList')) {
                              def chartsList = readFile('.chartList')
                              chartsList.readLines().each {
                                  String[] split = it.split(',');
                                  for (chart in split) {
                                    if (chart.trim()) {
                                      deployControlCharts.add(chart)
                                    }
                                  }
                              }
                           }
                         }
                        }

                        dir('helm-charts/i15n-helmchart/generated/') {
                            if (fileExists('.chartList')) {
                                def chartsList = readFile('.chartList')
                                chartsList.readLines().each {
                                    String[] split = it.split(',');
                                    for (chart in split) {
                                        releaseCharts.add(chart)
                                    }
                                }
                            }
                        }

                        if (deployControlCharts.isEmpty()) {
                            charts = releaseCharts
                        } else {
                            charts = deployControlCharts.findAll { it in releaseCharts }
                        }

                    } else if ("${chartsList}".equalsIgnoreCase("ALL")) {
                        dir('helm-charts/i15n-helmchart/generated/') {
                            foundFiles = findFiles()
                        }
                        for (f in foundFiles) {
                            if (f.directory) {
                                charts.add(f.name)
                            }
                        }
                    } else {
                        "${chartsList}".readLines().each {
                            String[] chartsList;
                            chartsList = it.split(',');
                            for (chart in chartsList) {
                                charts.add(chart)
                            }
                        }
                    }

                    if ("${undeployChartsList}".isEmpty()) {

                        echo "undeployChartsList is empty, calling checkoutDeploymentControl"

                        if (fileExists('gke-deployment-control')) {
                          dir('gke-deployment-control') {
                          if (fileExists('.undeployChartList')) {
                              def chartsList = readFile('.undeployChartList')
                              chartsList.readLines().each {
                                  String[] split = it.split(',');
                                  for (chart in split) {
                                    if (chart.trim()) {
                                      deployControlUndeployCharts.add(chart)
                                    }
                                  }
                              }
                           }
                         }
                        }

                        dir('helm-charts/i15n-helmchart/generated/') {
                            if (fileExists('.undeployChartList')) {
                                def undeployChartsList = readFile('.undeployChartList')
                                undeployChartsList.readLines().each {
                                    String[] split = it.split(',');
                                    for (chart in split) {
                                        releaseUndeployCharts.add(chart)
                                    }
                                }
                            }
                        }
                        
                        if (deployControlUndeployCharts.isEmpty()) {
                            undeployCharts = releaseUndeployCharts
                        } else {
                            allowedUndeployCharts = releaseCharts + releaseUndeployCharts
                            undeployCharts = deployControlUndeployCharts.findAll { it in allowedUndeployCharts }
                        }

                    } else if ("${undeployChartsList}".equalsIgnoreCase("ALL")) {
                        dir('helm-charts/i15n-helmchart/generated/') {
                            foundFiles = findFiles()
                        }
                        for (f in foundFiles) {
                            if (f.directory) {
                                undeployCharts.add(f.name)
                            }
                        }
                    } else {
                        "${undeployChartsList}".readLines().each {
                            String[] undeployChartsList;
                            undeployChartsList = it.split(',');
                            for (chart in undeployChartsList) {
                                undeployCharts.add(chart)
                            }
                        }
                    }

                    dir('helm-charts/i15n-helmchart/generated/') {
                        if (fileExists('.namespace')) {
                            namespaces.add(readFile('.namespace'))
                        }
                    }

                    dir('tests/generated/') {
                        if (fileExists('.testHistoryCallbackUrl')) {
                            testHistoryCallbackUrl = readFile('.testHistoryCallbackUrl')
                        }
                        if (fileExists('.testComponentList')) {
                            def componentList = readFile('.testComponentList')
                            componentList.readLines().each {
                                testComponentList.add(it)
                            }
                        }
                    }

                    clusters = "${clusterName}".split(",")
                    charts = sortCharts(charts)
                    echo "releaseCharts: ${releaseCharts}."
                    echo "releaseUndeployCharts: ${releaseUndeployCharts}."
                    echo "deployControlCharts: ${deployControlCharts}."
                    echo "deployControlUndeployCharts: ${deployControlUndeployCharts}."
                    echo "charts: ${charts}."
                    echo "undeployCharts: ${undeployCharts}."
                    echo "namespaces: ${namespaces}."
                    echo "rolloutStatusCallbackUrl ${rolloutStatusCallbackUrl}"
                    echo "testHistoryCallbackUrl ${testHistoryCallbackUrl}"
                    def jobUserId
                    wrap([$class: 'BuildUser']) {
                        jobUserId = "${BUILD_USER_ID}"
                    }
                    echo "jobUserId ${jobUserId}"
                    jobUserId = jobUserId == "scmChange" ? "SYSTEM" : jobUserId
                    rolloutStatusId = startRollout(deploymentEnv, rolloutStatusCallbackUrl, charts, jobUserId)
                }
            }
        }
        stage('Parameter Validation') {
            when {
                expression { deploymentEnvType == 'PROD' }
            }
            steps {
                script {
                    if (params.changeOrder.trim() == '') {
                        error("Change Order field is empty.  Aborting Job.")
                    }
                }
            }
        }
        stage('Rollback helm charts') {
            when {
                expression { params.changeOrder && params.rollback }
            }            
            steps {
                script {
                    def parallelBatches = charts.collate(10);
                    echo "Sync backup folder - Backup for each chart will be taken only once per change order"
                    this.sh """set -x;
                          mkdir -p ${env.WORKSPACE}/${params.changeOrder}
                          if [[ \$(gsutil ls gs://${v_project}-app-runtime-dependencies/helm-backup/${params.changeOrder} 2> /dev/null) ]]; then
                            gsutil rsync -r gs://${v_project}-app-runtime-dependencies/helm-backup/${params.changeOrder} ${env.WORKSPACE}/${params.changeOrder}
                          fi
                          tree ${env.WORKSPACE}/${params.changeOrder}
                          """
                    for (parallelBatch in parallelBatches) {
                        def tasks = [:]
                        for (tmpChart in parallelBatch) {
                            def chart = tmpChart
                            tasks["${chart}"] = {
                                for (namespace in namespaces) {
                                  for (cluster in clusters) {
                                    def helm_backup_dir = params.changeOrder + "/" + clusterType + "-" + clusterProject + "-" + clusterRegion + "-" + cluster 
                                    echo "Executing rollback script for: ${chart} in ${namespace}"
                                    retry(3) {                                        
                                         this.sh """set +x;
                                            source /tmp/.${clusterType}.${clusterProject}.${clusterRegion}.${clusterName}.env
                                            export _namespace_=${namespace}
                                            export _cluster_=${cluster}
                                            authenticate              
                                            if [[ -d "${env.WORKSPACE}/${helm_backup_dir}/${chart}" ]]; then 
                                              echo running rollback validation for chart: ${chart}
                                              kubectl apply -f ${env.WORKSPACE}/${helm_backup_dir}/${chart} --dry-run=client
                                              if [[ \$? -eq 0 ]]; then 
                                                echo dropping chart ${chart} for rollback
                                                helm delete ${chart} -n ${namespace}
                                                echo rolling back chart ${chart}
                                                kubectl apply -f ${env.WORKSPACE}/${helm_backup_dir}/${chart}
                                              fi
                                            else
                                              echo "No backup found at ${env.WORKSPACE}/${helm_backup_dir}/${chart}. Rollback Skipped"
                                            fi
                                            """                                                                                                                     
                                    }
                                }
                              }
                            }
                        }
                        parallel tasks
                    }
                }
            }
        }                   
        stage('Backup helm charts') {
            when {
                expression { params.changeOrder && ! params.rollback }
            }            
            steps {
                script {
                    def parallelBatches = charts.collate(10);
                    echo "Sync backup folder - Backup for each chart will be taken only once per change order"
                    this.sh """set -x;
                          mkdir -p ${env.WORKSPACE}/${params.changeOrder}
                          if [[ \$(gsutil ls gs://${v_project}-app-runtime-dependencies/helm-backup/${params.changeOrder} 2> /dev/null) ]]; then
                            gsutil rsync -r gs://${v_project}-app-runtime-dependencies/helm-backup/${params.changeOrder} ${env.WORKSPACE}/${params.changeOrder}
                          fi
                          tree ${env.WORKSPACE}/${params.changeOrder}
                          """
                    for (parallelBatch in parallelBatches) {
                        def tasks = [:]
                        for (tmpChart in parallelBatch) {
                            def chart = tmpChart
                            tasks["${chart}"] = {
                                for (namespace in namespaces) {
                                  for (cluster in clusters) {
                                    def helm_backup_dir = params.changeOrder + "/" + clusterType + "-" + clusterProject + "-" + clusterRegion + "-" + cluster 
                                    echo "Executing backup script for: ${chart} in ${namespace}"
                                    retry(3) {                                        
                                         this.sh """set +x;
                                            source /tmp/.${clusterType}.${clusterProject}.${clusterRegion}.${clusterName}.env
                                            export _namespace_=${namespace}
                                            export _cluster_=${cluster}
                                            authenticate
                                            chmod +x ./helm-backup.sh;  
                                            #create folder for first backup
                                            mkdir -p ${env.WORKSPACE}/${helm_backup_dir}
                                            #logic ensure only first process will acquire token and create helm history
                                            if ( set -o noclobber; echo "_token_" > "${env.WORKSPACE}/${helm_backup_dir}/token" ) 2> /dev/null; then                                                     
                                              if [[ ! -f "${env.WORKSPACE}/${helm_backup_dir}/helm-charts.lst" ]]; then 
                                                 helm list -n ${namespace} --max 0 > ${env.WORKSPACE}/${helm_backup_dir}/helm-charts.lst
                                                 cat  ${env.WORKSPACE}/${helm_backup_dir}/helm-charts.lst
                                              fi
                                            fi                              
                                            if [[ ! -d "${env.WORKSPACE}/${helm_backup_dir}/${chart}" ]]; then 
                                              ./helm-backup.sh --chart=${chart} --namespace=${namespace} --output-dir=${env.WORKSPACE}/${helm_backup_dir}
                                            else
                                              echo "Previous backup found at ${env.WORKSPACE}/${helm_backup_dir}/${chart}. Backup Skipped"
                                            fi
                                            """                                                                                                                     
                                    }
                                }
                              }
                            }
                        }
                        parallel tasks
                    }
                    echo "Sync backup folder - Backup for each chart will be taken only once per change order"
                    this.sh """set -x;
                          tree ${env.WORKSPACE}/${params.changeOrder}
                          for f in \$(ls ${env.WORKSPACE}/${params.changeOrder}); do 
                            rm -f ${env.WORKSPACE}/${params.changeOrder}/\$f/token                         
                            gsutil rsync -r ${env.WORKSPACE}/${params.changeOrder}/\$f gs://${v_project}-app-runtime-dependencies/helm-backup/${params.changeOrder}/\$f
                          done 
                          """
                }
            }
        }    
        stage('List Pre-release helm charts details') {
            when {
                expression { params.changeOrder && ! params.rollback }
            }               
            steps {
                script {
                    if (charts && charts.size() > 0) {
                        def chart = charts[0]
                        for (namespace in namespaces) {
                          for (cluster in clusters) {
                            echo "List Pre-Release Helm Chart Revision for ${cluster}.${namespace}"
                            def helm_backup_dir = params.changeOrder + "/" + clusterType + "-" + clusterProject + "-" + clusterRegion + "-" + cluster                                                      
                            this.sh """set +x;
                                 cat ${env.WORKSPACE}/${helm_backup_dir}/helm-charts.lst
                                 """                                                            
                          }
                        }
                    }
                }
            }
        }                            
        stage('Undeploy helm charts') {
            when {
                expression { ! params.rollback }
            }              
            steps {
                script {
                    for (chart in undeployCharts) {
                        for (namespace in namespaces) {
                          for (cluster in clusters) {
                            echo "Executing undeployment script for: ${chart} in ${namespace}"
                            retry(3) {                                
                                this.sh """set +x;
                                     source /tmp/.${clusterType}.${clusterProject}.${clusterRegion}.${clusterName}.env
                                     export _namespace_=${namespace}
                                     export _cluster_=${cluster}
                                     authenticate
                                     chmod +x ./undeploy.sh;
                                     ./undeploy.sh ${chart} ${namespace}"""                                
                            }
                          }
                        }
                    }
                }
            }
        }
        stage('Deploy helm charts') {
            when {
                expression { ! params.rollback }
            }               
            steps {
                script {
                    def parallelBatches = charts.collate(10);
                    for (parallelBatch in parallelBatches) {
                        def tasks = [:]
                        for (tmpChart in parallelBatch) {
                            def chart = tmpChart
                            tasks["${chart}"] = {
                                for (namespace in namespaces) {
                                  for (cluster in clusters) {
                                    additionalOptions = " "
                                    postAction = "start"                                    
                                    echo "Downloading custom datasource configuration from gcp bucket"
                                    if (chart == "grafana") {
                                        this.sh """set -x;
                                          set +e;
                                          tree ./helm-charts/i15n-helmchart/generated/grafana
                                          if [[ ! -f "./helm-charts/i15n-helmchart/generated/grafana/datasource/plaintext/datasource.yaml" ]] ; then
                                             mkdir -p ./helm-charts/i15n-helmchart/generated/grafana/datasource/plaintext
                                             if [[ \$(gsutil ls gs://${v_project}-app-runtime-dependencies/secrets/env/${clusterEnv}/grafana/${namespace}/datasource/plaintext/datasource.yaml 2> /dev/null) ]]; then
                                               gsutil cp gs://${v_project}-app-runtime-dependencies/secrets/env/${clusterEnv}/grafana/${namespace}/datasource/plaintext/datasource.yaml ./helm-charts/i15n-helmchart/generated/grafana/datasource/plaintext/
                                               rm -f ./helm-charts/i15n-helmchart/templates/grafana/datasource/plaintext/datasource.yaml
                                             elif [[ \$(gsutil ls gs://${v_project}-app-runtime-dependencies/secrets/env/${clusterEnv}/grafana/default/datasource/plaintext/datasource.yaml 2> /dev/null) ]]; then
                                               gsutil cp gs://${v_project}-app-runtime-dependencies/secrets/env/${clusterEnv}/grafana/default/datasource/plaintext/datasource.yaml ./helm-charts/i15n-helmchart/generated/grafana/datasource/plaintext/
                                               rm -f ./helm-charts/i15n-helmchart/templates/grafana/datasource/plaintext/datasource.yaml
                                             fi
                                          else
                                            rm -f ./helm-charts/i15n-helmchart/templates/grafana/datasource/plaintext/datasource.yaml
                                          fi
                                          tree ./helm-charts/i15n-helmchart/generated/grafana"""
                                    }
                                    if (params.activeClusters != null) {
                                      if (!activeClusters.contains("${cluster}")){
                                        additionalOptions = "--set replicaCount=0"
                                        postAction = "stop"
                                      }
                                    }
                                    maxHistory = ""                                    
                                    echo "Executing deployment script for: ${chart} in ${namespace}"
                                    retry(3) {                                        
                                         this.sh """set +x;
                                            source /tmp/.${clusterType}.${clusterProject}.${clusterRegion}.${clusterName}.env
                                            export _namespace_=${namespace}
                                            export _cluster_=${cluster}
                                            authenticate
                                            chmod +x ./deploy.sh;
                                            ./deploy.sh ${chart} ${namespace} '${maxHistory}' '${postAction}' '${additionalOptions}'"""                                                                                                                   
                                    }
                                    updateDeploymentRollout(rolloutStatusId, rolloutStatusCallbackUrl, chart, 'In_Progress', 'Rollout in progress', updateDeploymentBody)
                                }
                              }
                            }
                        }
                        parallel tasks
                    }
                }
            }
        }
        stage('Wait until rollout complete') {    
            options {
                timeout(time: 15, unit: "MINUTES")
            }
            steps {
                script {
                    def parallelBatches = charts.collate(10);
                    for (parallelBatch in parallelBatches) {
                        def tasks = [:]
                        for (tmpChart in parallelBatch) {
                            def chart = tmpChart
                            tasks["${chart}"] = {
                                try {
                                    for (namespace in namespaces) {
                                      for (cluster in clusters) {
                                        echo "Checking rollout status for: ${chart} in ${namespace}"
                                        retry(3) {
                                           this.sh """set +x;
                                             source /tmp/.${clusterType}.${clusterProject}.${clusterRegion}.${clusterName}.env
                                             export _namespace_=${namespace}
                                             export _cluster_=${cluster}
                                             authenticate
                                             kubectl rollout status deployment ${chart} -n ${namespace} """
                                        }
                                        updateDeploymentRollout(rolloutStatusId, rolloutStatusCallbackUrl, chart, 'Success', 'Rollout Successfully', updateDeploymentBody)
                                     }
                                   }
                                } catch (org.jenkinsci.plugins.workflow.steps.FlowInterruptedException e) {
                                    updateDeploymentRollout(rolloutStatusId, rolloutStatusCallbackUrl, chart, 'Fail', 'Rollout timeout', updateDeploymentBody)
                                    unstable(message: "Timeout reached, please check the status in GKE directly")
                                } catch (Throwable e) {
                                    updateDeploymentRollout(rolloutStatusId, rolloutStatusCallbackUrl, chart, 'Fail', e.getCause(), updateDeploymentBody)
                                    unstable(message: "Caught unexpected Exception: ${e.toString()}")
                                }                              
                            }
                        }
                        parallel tasks
                    }
                }
            }
        }
        stage('List Post-release helm charts details') {
            when {
                expression { params.changeOrder }
            }               
            steps {
                script {
                    if (charts && charts.size() > 0) {
                        def chart = charts[0]
                        for (namespace in namespaces) {
                          for (cluster in clusters) {
                            echo "List Post-Release Helm Chart Revision for ${cluster}.${namespace}"
                            def helm_backup_dir = params.changeOrder + "/" + clusterType + "-" + clusterProject + "-" + clusterRegion + "-" + cluster                                                      
                            this.sh """set +x;
                               source /tmp/.${clusterType}.${clusterProject}.${clusterRegion}.${clusterName}.env
                               export _namespace_=${namespace}
                               export _cluster_=${cluster}
                               authenticate
                               helm list -n ${namespace} --max 0 > ${env.WORKSPACE}/${helm_backup_dir}/helm-charts.post.lst  
                               cat ${env.WORKSPACE}/${helm_backup_dir}/helm-charts.post.lst                              
                               """
                            echo "Displaying changed items for ${cluster}.${namespace}"   
                            this.sh """
                                diff -u ${env.WORKSPACE}/${helm_backup_dir}/helm-charts.lst ${env.WORKSPACE}/${helm_backup_dir}/helm-charts.post.lst | grep -E "^\\+|^-" || true                           
                               """                                                                                      
                          }
                        }
                    }
                }
            }
        }         
        stage('Update ICE - Artifacts and LTTD') {
            
            when {
                expression { deploymentEnvType == 'PROD' && ! params.rollback }
            }
            steps {
                script {              
                    // run generateSourceDiffURL.sh
                    this.sh """chmod +x ./generateSourceDiffURL.sh;
                    ./generateSourceDiffURL.sh ${unityReleaseVersion}"""
                    
                    statusCode = sh returnStatus: true, script:
                                """status_code=\$(curl --write-out "%{http_code}\\n" --silent --output /dev/null \
                                -X 'PATCH' -H 'Authorization: Basic ${ICE_AUTH_TOKEN}' 'https://ice.it.global.hsbc/ice/api/v1/changes/${changeOrder}' -H 'accept: */*' -H 'Content-Type: application/json' -d @artifact.ice.json.updated)
                                if [ \$status_code -ne 204 ]
                                then
                                    if [ \$status_code -eq 400 ]
                                    then
                                        echo 'A request to update a field source from GSD was made, or the request body JSON describing the patch was invalid'
                                    elif [ \$status_code -eq 404 ]
                                    then
                                        echo 'The CR does not exist'
                                    fi
                                    exit \$status_code
                                fi"""
                    
                    if ( statusCode != 0 ) {
                        unstable(message: "ICE update failed: please refer to previous log for details" )
                    }                  
                }                
            }
        }
        stage('Scale Up Test Components') {
            when {
                expression { ! params.rollback }
            }               
            options {
                timeout(time: 30, unit: "MINUTES")
            }
            steps {
                script {
                    if (testComponentList) {
                        def parallelBatches = testComponentList.findAll{!charts.contains(it)}.collate(10);
                        for (parallelBatch in parallelBatches) {
                            def tasks = [:]
                            for (tmpComponent in parallelBatch) {
                                def component = tmpComponent
                                tasks["${component}"] = {
                                    for (namespace in namespaces) {
                                      for (cluster in clusters) {
                                        try {
                                            /*
                                            echo "Checking rollout status for: ${component} in ${namespace}"
                                            retry(3) {
                                               this.sh """set +x;
                                               source /tmp/.${clusterType}.${clusterProject}.${clusterRegion}.${clusterName}.env
                                               export _namespace_=${namespace}
                                               export _cluster_=${cluster}
                                               authenticate
                                               chmod +x ./scaleUp.sh;
                                               ./scaleUp.sh ${component} ${namespace}"""                                                                                                  
                                            }
                                            */
                                            echo "Check component rollout status: ${component} in ${namespace}"
                                            retry(3) {
                                               this.sh """
                                                   source /tmp/.${clusterType}.${clusterProject}.${clusterRegion}.${clusterName}.env
                                                   export _namespace_=${namespace}
                                                   export _cluster_=${cluster}
                                                   authenticate
                                                   kubectl rollout status deployment ${component} -n ${namespace}
                                               """                                            
                                            }
                                        } catch (Throwable e) {
                                            echo "Failed to scale up component: ${component}"
                                        }
                                      }
                                    }
                                }
                            }
                            parallel tasks
                        }
                    }
                }
            }
        }
        stage('Execute Tests') {
            when {
                expression { ! params.rollback }
            }               
            options {
                timeout(time: 30, unit: "MINUTES")
            }
            steps {
                script {
                    if (testHistoryCallbackUrl) {
                        startTestExecution(testHistoryCallbackUrl)

                        def curTest = ''
                        try {
                            setupTestFrameworkRuntime()
                            supplementTestConfigFiles(filesToBeCleanup, v_gcpKeyBucketAppDep)

                            dir('tests/') {
                                for (namespace in namespaces) {
                                  for (cluster in clusters) {
                                      def lookupPod = this.sh(
                                              script: """set +x;
                                               source /tmp/.${clusterType}.${clusterProject}.${clusterRegion}.${clusterName}.env
                                               export _namespace_=${namespace}
                                               export _cluster_=${cluster}
                                               authenticate
                                               kubectl get pods -n ${namespace} -o json | jq '.items[] | select(.metadata.name|test("data-lookup"))| .metadata.name' | head -1 | tr -d '"';
                                           """,
                                              returnStdout: true
                                      ).trim()      
                                      def saPod = this.sh(
                                              script: """set +x;
                                               source /tmp/.${clusterType}.${clusterProject}.${clusterRegion}.${clusterName}.env
                                               export _namespace_=${namespace}
                                               export _cluster_=${cluster}
                                               authenticate
                                               kubectl get pods -n ${namespace} -o json | jq '.items[] | select(.metadata.name|test("-psa-"))| .metadata.name' | head -1 | tr -d '"';
                                           """,
                                              returnStdout: true
                                      ).trim()                                        
                                      echo "Executing tests in namespace: ${namespace}, supplement config by data lookup pod: ${lookupPod} and sa pod: ${saPod}"
                                      supplementTestConfig(namespace, lookupPod, saPod, filesToBeCleanup)
                                      executeTest(namespace, lookupPod)                                                                         
                                  }
                                }
                            }

                            finishTestExecution(testHistoryCallbackUrl, "COMPLETED", testReportUrl)
                        }  catch (org.jenkinsci.plugins.workflow.steps.FlowInterruptedException e) {
                            finishTestExecution(testHistoryCallbackUrl, "TIMED_OUT", testReportUrl)
                            unstable(message: "Timeout reached, please check the test execution log")
                        } catch (Throwable e) {
                            finishTestExecution(testHistoryCallbackUrl, "ERROR", testReportUrl)
                            unstable(message: "Caught unexpected Exception: ${e.toString()}")
                        }
                    } else {
                        echo "No test required, skipping..."
                    }
                }
            }
        }
    }
    post {
        success {
            script {
                finishRollout(rolloutStatusId, rolloutStatusCallbackUrl, 'Success', 'Rollout successfully', updateDeploymentBody)
            }
        }
        unsuccessful {
            finishRollout(rolloutStatusId, rolloutStatusCallbackUrl, 'Fail', 'Rollout failed, please check Jenkins job url for more details.', updateDeploymentBody)
        }
        always {
            script {
                if (testHistoryCallbackUrl) {
                    echo "Publish test report"
                    dir('tests/test-framework-runtime/') {
                        this.sh "mvn -Dmaven.test.skip=true site"
                        publishHTML target: [
                                allowMissing: false,
                                alwaysLinkToLastBuild: false,
                                keepAll: true,
                                reportDir: 'target/site',
                                reportFiles: 'surefire-report.html',
                                reportName: 'Test Report'
                        ]
                    }

                    dir('tests/') {
                        cleanupTestFiles(filesToBeCleanup)
                    }
                }
            }
        }
    }
}

def cloneDeploymentControl(changeOrder) {
    def repoUrl = 'https://gbmt-bitbucket.prd.fx.gbm.cloud.uk.hsbc/projects/UNITY-I15N-POC/repos/gke-deployment-control'
    def branchExists = this.sh(
        script: """curl -s -o /dev/null -w "%{http_code}" ${repoUrl}/raw/.chartList?at=refs%2Fheads%2F${changeOrder}""",
        returnStdout: true
    ).trim()

    if (branchExists == '200') {
        this.sh "git clone --depth 1 --branch ${changeOrder} https://gbmt-bitbucket.prd.fx.gbm.cloud.uk.hsbc/scm/unity-i15n-poc/gke-deployment-control.git"        
    } else {
        echo "Branch ${changeOrder} does not exist in ${repoUrl}"
    }
}

def startRollout(env, rolloutStatusCallbackUrl, charts, jobUserId) {
    def deploymentBody = "["
    charts.eachWithIndex { item, index ->
        deploymentBody = deploymentBody + """
        {
            "name": "${item}",
            "status": "Not_Started",
            "statusDescription": "Deployment not started"
        }
        """
        if (index != (charts.size() - 1)) {
            deploymentBody = deploymentBody + ','
        }
    }

    deploymentBody = deploymentBody + ']'


    def requestBodyPayload = """
        {
            "jobId":"${BUILD_ID}",
            "jobUrl": "${BUILD_URL}",
            "overallProgress": 0,
            "overallStatus": "In_Progress",
            "overallStatusDescription": "Rollout in progress",
            "envName": "${env}",
            "deployments" : ${deploymentBody},
            "createdBy": "${jobUserId}"
        }
    """
    println('requestPayload: ' + requestBodyPayload)
    try {
        def response
        retry(5) {
            response = httpRequest httpMode: 'PUT', contentType: 'APPLICATION_JSON', requestBody: requestBodyPayload, ignoreSslErrors: true, url: rolloutStatusCallbackUrl, timeout: 5
            println('Status: ' + response.status)
            println('Content: ' + response.content)
            def responseObj = readJSON text: response.content
            return responseObj['id']
        }
    } catch (Exception e) {
        echo "Caught unexpected Exception when updating rollout status: ${e.toString()}"
        return ''
    }
}

def finishRollout(rolloutStatusId, rolloutStatusCallbackUrl, status, statusDescription, updateDeploymentBody) {
    def updateDeploymentBodyList = updateDeploymentBody.collect { it -> it.value }
    def requestBodyPayload = """
        {
            "id": "${rolloutStatusId}",
            "overallStatus" : "${status}",
            "overallStatusDescription": "${statusDescription}",
            "deployments" : ${updateDeploymentBodyList}
        }
    """
    println('requestPayload: ' + requestBodyPayload)
    try {
        def response
        retry(5) {
            response = httpRequest httpMode: 'PATCH', contentType: 'APPLICATION_JSON', requestBody: requestBodyPayload, ignoreSslErrors: true, url: rolloutStatusCallbackUrl, timeout: 5
            println('Status: ' + response.status)
            println('Content: ' + response.content)
        }
    } catch (Exception e) {
        echo "Caught unexpected Exception when updating rollout status: ${e.toString()}"
        return ''
    }
}


def updateDeploymentRollout(rolloutStatusId, rolloutStatusCallbackUrl, deployment, status, statusDescription, updateDeploymentBody) {
    updateDeploymentBody["${deployment}"] = """
    {
            "name": "${deployment}",
            "status": "${status}",
            "statusDescription": "${statusDescription}"
    }
    """
    def updateDeploymentBodyList = updateDeploymentBody.collect { it -> it.value }
    def requestBodyPayload = """
        {
            "id": "${rolloutStatusId}",
            "deployments" : ${updateDeploymentBodyList}
        }
    """
    println('requestPayload: ' + requestBodyPayload)
    try {
        def response
        retry(5) {
            response = httpRequest httpMode: 'PATCH', contentType: 'APPLICATION_JSON', requestBody: requestBodyPayload, ignoreSslErrors: true, url: rolloutStatusCallbackUrl, timeout: 5
            println('Status: ' + response.status)
            println('Content: ' + response.content)
        }
    } catch (Exception e) {
        echo "Caught unexpected Exception when updating rollout status: ${e.toString()}"
        return ''
    }
}

def setupTestFrameworkRuntime() {
    dir('tests/') {

        if (fileExists('test-framework-runtime')) {
            this.sh '''rm -rf test-framework-runtime'''
        }

        this.sh """
            mkdir -p test-framework-runtime/src/test/resources;
            cp generated/logback-test.xml test-framework-runtime/src/test/resources/;
            cp generated/pom.xml test-framework-runtime/;
            cp -r generated/test-cases test-framework-runtime/src/test/resources/;
        """
    }
}

def supplementTestConfigFiles(filesToBeCleanup, v_gcpKeyBucketAppDep) {
    echo "Supplementing GCP credential file"
    this.sh """
        mkdir -p /tmp/bigQuery/;
        gsutil -m cp -r gs://${v_gcpKeyBucketAppDep}/gce-stage3-image-builder/gce-stage3-image-builder.json /tmp/bigQuery/;
    """
    filesToBeCleanup.add('/tmp/bigQuery/gce-stage3-image-builder.json')
}

def supplementTestConfig(namespace, lookupPod, saPod, filesToBeCleanup) {
    try {
        this.sh """set +x;
                gcloud auth activate-service-account --key-file $HOME/ServiceAccountKeys/${v_serviceAccount}.json;
                gcloud container clusters get-credentials ${env.v_clusterName} --region=${v_clusterRegion} ${clusterProjectOption} ${v_impersonateServiceAccount};
                export HTTPS_PROXY=${env.v_kubeCtlLib};
                chmod +x ../supplementTestConfig.sh;
                ../supplementTestConfig.sh ${namespace} ${lookupPod} ${saPod};"""

        dir("generated/") {
            if (fileExists('.kafkaConfigFiles')) {
                def fileList = readFile('.kafkaConfigFiles')
                fileList.readLines().each { file ->
                    def (source, destination) = file.split(',')
                    echo "Supplementing Kafka file: ${source} from pod [${saPod}] to: ${destination} in local"
                    this.sh """mkdir -p /tmp/kafka/;
                    cd /tmp/kafka/;
                    set +x;
                    gcloud auth activate-service-account --key-file $HOME/ServiceAccountKeys/${v_serviceAccount}.json;
                    gcloud container clusters get-credentials ${env.v_clusterName} --region=${v_clusterRegion} ${clusterProjectOption} ${v_impersonateServiceAccount};
                    export HTTPS_PROXY=${env.v_kubeCtlLib};
                    kubectl exec ${saPod} -n ${namespace} -- tar cfh - ${source} | tar xfh -"""
                    filesToBeCleanup.add(destination)
                }
            }
            if (fileExists('.mqConfigFiles')) {
                def fileList = readFile('.mqConfigFiles')
                fileList.readLines().each { file ->
                    def (source, destination) = file.split(',')
                    echo "Supplementing MQ file: ${source} from pod [${saPod}] to: ${destination} in local"
                    this.sh """mkdir -p /tmp/mq/;
                    cd /tmp/mq/;
                    set +x;
                    gcloud auth activate-service-account --key-file $HOME/ServiceAccountKeys/${v_serviceAccount}.json;
                    gcloud container clusters get-credentials ${env.v_clusterName} --region=${v_clusterRegion} ${clusterProjectOption} ${v_impersonateServiceAccount};
                    export HTTPS_PROXY=${env.v_kubeCtlLib};
                    kubectl exec ${saPod} -n ${namespace} -- tar cfh - ${source} | tar xfh -"""
                    filesToBeCleanup.add(destination)
                }
            }
            if (fileExists('.sftpConfigFiles')) {
                def fileList = readFile('.sftpConfigFiles')
                fileList.readLines().each { file ->
                    def (source, destination) = file.split(',')
                    echo "Supplementing SFTP file: ${source} from pod [${saPod}] to: ${destination} in local"
                    this.sh """mkdir -p /tmp/sftp/;
                    cd /tmp/sftp/;
                    set +x;
                    gcloud auth activate-service-account --key-file $HOME/ServiceAccountKeys/${v_serviceAccount}.json;
                    gcloud container clusters get-credentials ${env.v_clusterName} --region=${v_clusterRegion} ${clusterProjectOption} ${v_impersonateServiceAccount};
                    export HTTPS_PROXY=${env.v_kubeCtlLib};
                    kubectl exec ${saPod} -n ${namespace} -- tar cfh - ${source} | tar xfh -"""
                    filesToBeCleanup.add(destination)
                }
                def knownHostsFile = "/tmp/sftp/known_hosts"
                this.sh """find ./ -name test.json -exec grep -w 'host' {} \\;| uniq > .hosts"""
                def hostList = readFile('.hosts')
                hostList.readLines().each {
                    String[] split = it.split(': "');
                    def host = split[1].split('"')[0]
                    this.sh """ssh-keyscan -t rsa ${host} >> ${knownHostsFile};"""
                }
                this.sh """rm .hosts"""

                filesToBeCleanup.add(knownHostsFile)
            }
        }

    } catch (Exception e) {
        echo "Caught unexpected Exception when supplementing test config in test: ${namespace} ${lookupPod} ${saPod}"
        echo "${e.toString()}"
        return ''
    }
}

def executeTest(namespace, pod) {
    try {
        this.sh """set +x;
               gcloud auth activate-service-account --key-file $HOME/ServiceAccountKeys/${v_serviceAccount}.json;
               gcloud container clusters get-credentials ${env.v_clusterName} --region=${v_clusterRegion} ${clusterProjectOption} ${v_impersonateServiceAccount};
               export HTTPS_PROXY=${env.v_kubeCtlLib};
               kubectl port-forward ${pod} -n ${namespace} 3307:3307 &"""

        this.sh """chmod +x ../executeTest.sh;
                cat /home/<USER>/.m2/settings.xml| sed  's|https://efx-nexus.systems.uk.hsbc:8082/nexus/content/groups/public/|https://gbmt-nexus.prd.fx.gbm.cloud.uk.hsbc/repository/maven-public|g'  > /home/<USER>/.m2/settings-gbmt.xml;
                mkdir -p test-framework-runtime/.mvn;
                echo "-s /home/<USER>/.m2/settings-gbmt.xml" > test-framework-runtime/.mvn/maven.config;
                export JAVA_HOME=/opt/java/zulu17;
                export MAVEN_OPTS="-Djavax.net.ssl.trustStore=/etc/pki/java/cacerts"
                ../executeTest.sh;"""
    } catch (Exception e) {
        echo "Caught unexpected Exception when executing test: ${namespace} ${pod}"
        echo "${e.toString()}"
        return ''
    }
}


def cleanupTestFiles(filesToBeCleanup) {
    this.sh """rm -rf test-framework-runtime"""
    filesToBeCleanup.each { item ->
        try {
            echo "Removing ${item}"
            this.sh "rm ${item}"
        } catch (Exception e) {
            echo "Caught unexpected Exception when cleaning up test files"
            echo "${e.toString()}"
            return ''
        }
    }
}

def startTestExecution(testHistoryCallbackUrl) {
    def requestBodyPayload = """
        {
            "status" : "IN_PROGRESS"
        }
    """
    println('Start test execution, update test history requestPayload: ' + requestBodyPayload)
    try {
        def response = httpRequest httpMode: 'PUT', contentType: 'APPLICATION_JSON', requestBody: requestBodyPayload, ignoreSslErrors: true, url: testHistoryCallbackUrl, timeout: 60
        println('Status: ' + response.status)
        println('Content: ' + response.content)
    } catch (Exception e) {
        echo "Caught unexpected Exception when updating test history: ${e.toString()}"
        return ''
    }
}

def finishTestExecution(testHistoryCallbackUrl, status, result) {
    def requestBodyPayload = """
        {
            "status": "${status}",
            "result": "${result}"
        }
    """
    println('Test execution finished, update test history requestPayload: ' + requestBodyPayload)
    try {
        def response = httpRequest httpMode: 'PUT', contentType: 'APPLICATION_JSON', requestBody: requestBodyPayload, ignoreSslErrors: true, url: testHistoryCallbackUrl, timeout: 5
        println('Status: ' + response.status)
        println('Content: ' + response.content)
    } catch (Exception e) {
        echo "Caught unexpected Exception when updating test history: ${e.toString()}"
        return ''
    }
}

@NonCPS
def sortCharts(List<String> charts) {
    // prioritize data lookup and cache
    return charts.toSorted{a, b -> sortChartComparator(a, b)}
}

@NonCPS
def sortChartComparator(String a , String b){
    if ((a.equals("data-lookup") || a.equals("cache")) && (b.equals("data-lookup") || b.equals("cache"))) {
        return 0
    }

    if (a.equals("data-lookup") || a.equals("cache")) {
        return -1
    }
    if (b.equals("data-lookup") || b.equals("cache")) {
        return 1
    }
    return a.compareTo(b)
}
