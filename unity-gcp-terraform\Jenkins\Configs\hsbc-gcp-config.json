{"operations": ["Create GCP enviroment", "Demise GCP enviroment"], "kubeconfig_path": "/home/<USER>/.kube/config", "dockerconf_path": "/home/<USER>/.docker", "gcp": {"Dev": {"credential_id": "gcp_connect", "team": "ibm-cinternal-vpc3", "credential_foldername": "Unity", "project_id": "hsbc-9087302-unity-dev", "network": "VPC3", "environment": "<PERSON>", "clusterName": "gke-t2-vpc3-377436", "clusterRegion": "asia-east2", "clusterProject": "hsbc-9087302-unity-dev", "kubectlProxy": "gke-kubectl-vpc3.hsbc-9087302-unity-dev.dev.gcp.cloud.hk.hsbc:3128", "envSuffix": "", "region": "asia-east2", "gcpBuildServiceAccount": "<EMAIL>", "storage_bucket": "gs://hsbc-9087302-unity-dev-key-management", "storage_bucket_app_dependencies": "gs://hsbc-9087302-unity-dev-app-runtime-dependencies", "image_builder_sa_file": "/home/<USER>/ServiceAccountKeys/gce-stage3-image-builder.json", "module_list": ["Cloud SQL", "Cluster with Proxies", "K8 config", "Dns", "Tanium scanning", "Monitoring", "Logging Sink"], "modules": {"Monitoring": {"folder_name": "monitoring", "tfvars": "varfiles/global/global_dev"}, "Cloud SQL": {"folder_name": "cloud_sql", "tfvars": "sql"}, "Cluster/Proxies": {"folder_name": "cluster", "tfvars": "cluster"}, "Dns": {"folder_name": "dns", "tfvars": "dns"}, "Namespace configurations": {"folder_name": "k8", "tfvars": "cluster"}, "Logging Sink": {"folder_name": "logging_sink", "tfvars": "logging_sink"}, "Tanium scanning": {"folder_name": "tanium_scanning", "tfvars": "tanium-dev"}}, "teams": {"ibm-cinternal-vpc3": {"team_name": "IBM", "default_vars": "ibm"}}}, "Sit": {"credential_id": "gcp_connect", "team": "ibm-cinternal-vpc3", "credential_foldername": "Unity", "project_id": "hsbc-9087302-unity-dev", "network": "VPC3", "environment": "<PERSON>", "clusterName": "gke-t2-vpc3-377436", "clusterRegion": "asia-east2", "clusterProject": "hsbc-9087302-unity-dev", "kubectlProxy": "gke-kubectl-vpc3.hsbc-9087302-unity-dev.dev.gcp.cloud.hk.hsbc:3128", "envSuffix": "", "region": "asia-east2", "gcpBuildServiceAccount": "<EMAIL>", "storage_bucket": "gs://hsbc-9087302-unity-dev-key-management", "storage_bucket_app_dependencies": "gs://hsbc-9087302-unity-dev-app-runtime-dependencies", "image_builder_sa_file": "/home/<USER>/ServiceAccountKeys/gce-stage3-image-builder.json", "module_list": ["Cloud SQL", "Cluster with Proxies", "K8 config", "Dns", "Tanium scanning", "Monitoring", "Logging Sink"], "modules": {"Monitoring": {"folder_name": "monitoring", "tfvars_folder": "varfiles/global/global_dev.tfvars"}, "Cloud SQL": {"folder_name": "cloud_sql", "tfvars": "sql"}, "Cluster/Proxies": {"folder_name": "cluster", "tfvars": "cluster"}, "Dns": {"folder_name": "dns", "tfvars": "dns"}, "Namespace configurations": {"folder_name": "k8", "tfvars": "cluster"}}, "teams": {"ibm-cinternal-vpc3": {"team_name": "IBM", "default_vars": "ibm"}}}, "Uat": {"credential_id": "gcp_connect", "team": "ibm-cinternal-vpc3", "credential_foldername": "Unity", "project_id": "hsbc-9087302-unity-dev", "network": "VPC3", "environment": "<PERSON>", "clusterName": "gke-t2-vpc3-377436", "clusterRegion": "asia-east2", "clusterProject": "hsbc-9087302-unity-dev", "kubectlProxy": "gke-kubectl-vpc3.hsbc-9087302-unity-dev.dev.gcp.cloud.hk.hsbc:3128", "envSuffix": "", "region": "asia-east2", "gcpBuildServiceAccount": "<EMAIL>", "storage_bucket": "gs://hsbc-9087302-unity-dev-key-management", "storage_bucket_app_dependencies": "gs://hsbc-9087302-unity-dev-app-runtime-dependencies", "image_builder_sa_file": "/home/<USER>/ServiceAccountKeys/gce-stage3-image-builder.json", "module_list": ["Cloud SQL", "Cluster with Proxies", "K8 config", "Dns", "Tanium scanning", "Monitoring", "Logging Sink"], "modules": {"Monitoring": {"folder_name": "monitoring", "tfvars_folder": "varfiles/global/global_dev.tfvars"}, "Cloud SQL": {"folder_name": "cloud_sql", "tfvars": "sql"}, "Cluster/Proxies": {"folder_name": "cluster", "tfvars": "cluster"}, "Dns": {"folder_name": "dns", "tfvars": "dns"}, "Namespace configurations": {"folder_name": "k8", "tfvars": "cluster"}}, "teams": {"ibm-cinternal-vpc3": {"team_name": "IBM", "default_vars": "ibm"}}}, "Preprod": {"credential_id": "gcp_connect", "team": "ibm-cinternal-vpc3", "credential_foldername": "Unity", "project_id": "hsbc-9087302-unity-dev", "network": "VPC3", "environment": "<PERSON>", "clusterName": "gke-t2-vpc3-377436", "clusterRegion": "asia-east2", "clusterProject": "hsbc-9087302-unity-dev", "kubectlProxy": "gke-kubectl-vpc3.hsbc-9087302-unity-dev.dev.gcp.cloud.hk.hsbc:3128", "envSuffix": "", "region": "asia-east2", "gcpBuildServiceAccount": "<EMAIL>", "storage_bucket": "gs://hsbc-9087302-unity-dev-key-management", "storage_bucket_app_dependencies": "gs://hsbc-9087302-unity-dev-app-runtime-dependencies", "image_builder_sa_file": "/home/<USER>/ServiceAccountKeys/gce-stage3-image-builder.json", "module_list": ["Cloud SQL", "Cluster with Proxies", "K8 config", "Dns", "Tanium scanning", "Monitoring", "Logging Sink"], "modules": {"Monitoring": {"folder_name": "monitoring", "tfvars_folder": "varfiles/global/global_dev.tfvars"}, "Cloud SQL": {"folder_name": "cloud_sql", "tfvars": "sql"}, "Cluster/Proxies": {"folder_name": "cluster", "tfvars": "cluster"}, "Dns": {"folder_name": "dns", "tfvars": "dns"}, "Namespace configurations": {"folder_name": "k8", "tfvars": "cluster"}}, "teams": {"ibm-cinternal-vpc3": {"team_name": "IBM", "default_vars": "ibm"}}}, "Dev2": {"credential_id": "gcp_connect", "credential_foldername": "Unity", "team": "ibm-cinternal-vpc2", "project_id": "hsbc-9087302-unity-dev", "network": "VPC2", "environment": "Dev2", "clusterName": "gke-t2-vpc2-377436", "clusterRegion": "asia-east2", "clusterProject": "hsbc-9087302-unity-dev", "kubectlProxy": "gke-kubectl-vpc2.hsbc-9087302-unity-dev.dev.gcp.cloud.hk.hsbc:3128", "envSuffix": "", "region": "asia-east2", "gcpBuildServiceAccount": "<EMAIL>", "storage_bucket": "gs://hsbc-9087302-unity-dev-key-management", "storage_bucket_app_dependencies": "gs://hsbc-9087302-unity-dev-app-runtime-dependencies", "image_builder_sa_file": "/home/<USER>/ServiceAccountKeys/gce-stage3-image-builder.json", "module_list": ["Cloud SQL", "Cluster with Proxies", "K8 config", "Dns", "Tanium scanning", "Monitoring", "Logging Sink"], "modules": {"Monitoring": {"folder_name": "monitoring", "tfvars": "varfiles/global/global_dev"}, "Cloud SQL": {"folder_name": "cloud_sql", "tfvars": "sql"}, "Cluster/Proxies": {"folder_name": "cluster", "tfvars": "cluster"}, "Dns": {"folder_name": "dns", "tfvars": "dns"}, "Namespace configurations": {"folder_name": "k8", "tfvars": "cluster"}, "Logging Sink": {"folder_name": "logging_sink", "tfvars": "logging_sink"}}, "teams": {"ibm-cinternal-vpc2": {"team_name": "ibm", "default_vars": "ibm"}}}, "hsbc-********-unity-prod": {"credential_id": "gcp_connect", "credential_foldername": "Unity", "team": "ibm-cinternal-vpc2", "project_id": "hsbc-********-unity-prod", "network": "VPC2", "environment": "Prod", "clusterName": "gke-t2-vpc2-c818f9", "clusterRegion": "asia-east2", "clusterProject": "hsbc-********-unity-prod", "kubectlProxy": "kubectl.vpc2.hsbc-********-unity-prod.prod.gcp.cloud.hk.hsbc:3128", "envSuffix": "", "region": "asia-east2", "gcpBuildServiceAccount": "<EMAIL>", "storage_bucket": "gs://hsbc-********-unity-prod-key-management", "storage_bucket_app_dependencies": "gs://hsbc-********-unity-prod-app-runtime-dependencies", "image_builder_sa_file": "/home/<USER>/ServiceAccountKeys/gce-stage3-image-builder.json", "module_list": ["Cloud SQL", "Cluster with Proxies", "K8 config", "Dns", "Tanium scanning", "Monitoring", "Logging Sink"], "modules": {"Cloud SQL": {"folder_name": "cloud_sql", "tfvars": "sql"}, "Monitoring": {"folder_name": "monitoring", "tfvars": "varfiles/global/global_prod"}}, "teams": {"ibm-cinternal-vpc2": {"team_name": "ibm", "default_vars": "ibm"}}}, "Prod": {"credential_id": "gcp_connect", "credential_foldername": "Unity", "team": "ibm-cinternal-vpc2", "project_id": "hsbc-********-unity-prod", "network": "VPC2", "environment": "Prod", "clusterName": "gke-t2-vpc2-c818f9", "clusterRegion": "asia-east2", "clusterProject": "hsbc-********-unity-prod", "kubectlProxy": "kubectl.vpc2.hsbc-********-unity-prod.prod.gcp.cloud.hk.hsbc:3128", "envSuffix": "", "region": "asia-east2", "gcpBuildServiceAccount": "<EMAIL>", "storage_bucket": "gs://hsbc-********-unity-prod-key-management", "storage_bucket_app_dependencies": "gs://hsbc-********-unity-prod-app-runtime-dependencies", "image_builder_sa_file": "/home/<USER>/ServiceAccountKeys/gce-stage3-image-builder.json", "module_list": ["Cloud SQL", "Cluster with Proxies", "K8 config", "Dns", "Tanium scanning", "Monitoring", "Logging Sink"], "modules": {"Cloud SQL": {"folder_name": "cloud_sql", "tfvars": "sql"}, "Cluster/Proxies": {"folder_name": "cluster", "tfvars": "cluster"}, "Dns": {"folder_name": "dns", "tfvars": "dns"}, "Monitoring": {"folder_name": "monitoring", "tfvars": "varfiles/global/global_prod"}, "Namespace configurations": {"folder_name": "k8", "tfvars": "cluster"}, "Logging Sink": {"folder_name": "logging_sink", "tfvars": "logging_sink"}}, "teams": {"ibm-cinternal-vpc2": {"team_name": "ibm", "default_vars": "ibm"}}}, "hsbc-9087302-unity-dev": {"credential_id": "gcp_connect", "team": "ibm-cinternal-vpc3", "credential_foldername": "Unity", "project_id": "hsbc-9087302-unity-dev", "network": "VPC3", "environment": "<PERSON>", "clusterName": "gke-t2-vpc3-377436", "clusterRegion": "asia-east2", "clusterProject": "hsbc-9087302-unity-dev", "kubectlProxy": "gke-kubectl-vpc3.hsbc-9087302-unity-dev.dev.gcp.cloud.hk.hsbc:3128", "envSuffix": "", "region": "asia-east2", "gcpBuildServiceAccount": "<EMAIL>", "storage_bucket": "gs://hsbc-9087302-unity-dev-key-management", "storage_bucket_app_dependencies": "gs://hsbc-9087302-unity-dev-app-runtime-dependencies", "image_builder_sa_file": "/home/<USER>/ServiceAccountKeys/gce-stage3-image-builder.json", "module_list": ["Cloud SQL", "Cluster with Proxies", "K8 config", "Dns", "Tanium scanning", "Monitoring", "Logging Sink"], "modules": {"Monitoring": {"folder_name": "monitoring", "tfvars": "varfiles/global/global_dev"}, "Cloud SQL": {"folder_name": "cloud_sql", "tfvars": "sql"}, "Cluster/Proxies": {"folder_name": "cluster", "tfvars": "cluster"}, "Dns": {"folder_name": "dns", "tfvars": "dns"}, "Namespace configurations": {"folder_name": "k8", "tfvars": "cluster"}, "Logging Sink": {"folder_name": "logging_sink", "tfvars": "logging_sink"}, "Tanium scanning": {"folder_name": "tanium_scanning", "tfvars": "tanium-dev"}}, "teams": {"ibm-cinternal-vpc3": {"team_name": "IBM", "default_vars": "ibm"}}}, "hsbc-********-unyquartz-dev": {"credential_id": "gcp_connect", "team": "devcls", "credential_foldername": "Unity", "project_id": "hsbc-********-unyquartz-dev", "network": "hsbc-default-network", "environment": "hsbc-********-unyquartz-dev", "clusterName": "devcls", "clusterRegion": "asia-east2", "clusterProject": "hsbc-********-unyquartz-dev", "kubectlProxy": "ingress.devcls.hsbc-********-unyquartz-dev.dev.gcp.cloud.hk.hsbc:3128", "envSuffix": "-CORE", "region": "asia-east2", "gcpBuildServiceAccount": "<EMAIL>", "storage_bucket": "gs://hsbc-9087302-unity-dev-key-management", "storage_bucket_app_dependencies": "gs://hsbc-9087302-unity-dev-app-runtime-dependencies", "image_builder_sa_file": "/home/<USER>/ServiceAccountKeys/<EMAIL>", "module_list": ["Cloud SQL", "Monitoring"], "modules": {"Cloud SQL": {"folder_name": "cloud_sql"}, "Monitoring": {"folder_name": "monitoring"}}, "teams": {"devcls": {"team_name": "quartz", "default_vars": "project-asia-east2"}}}, "hsbc-********-unity-dev": {"credential_id": "gcp_connect", "team": "devcls", "credential_foldername": "Unity", "project_id": "hsbc-********-unity-dev", "network": "hsbc-default-network", "environment": "hsbc-********-unity-dev", "clusterName": "devcls", "clusterRegion": "asia-east2", "clusterProject": "hsbc-********-unity-dev", "kubectlProxy": "ingress.devcls.hsbc-********-unity-dev.dev.gcp.cloud.hk.hsbc:3128", "envSuffix": "-CORE", "region": "asia-east2", "gcpBuildServiceAccount": "<EMAIL>", "storage_bucket": "gs://hsbc-9087302-unity-dev-key-management", "storage_bucket_app_dependencies": "gs://hsbc-9087302-unity-dev-app-runtime-dependencies", "image_builder_sa_file": "/home/<USER>/ServiceAccountKeys/<EMAIL>", "module_list": ["Cloud SQL", "Monitoring"], "modules": {"Cloud SQL": {"folder_name": "cloud_sql"}, "Monitoring": {"folder_name": "monitoring"}}, "teams": {"devcls": {"team_name": "core", "default_vars": "project-asia-east2"}}}, "hsbc-********-unyin1-dev": {"credential_id": "gcp_connect", "team": "devcls", "credential_foldername": "Unity", "project_id": "hsbc-********-unyin1-dev", "network": "hsbc-default-network", "environment": "hsbc-********-unyin1-dev", "clusterName": "devcls", "clusterRegion": "asia-south1", "clusterProject": "hsbc-********-unyin1-dev", "kubectlProxy": "ingress.devcls.hsbc-********-unyin1-dev.dev.gcp.cloud.in.hsbc:3128", "envSuffix": "-IN1", "region": "asia-south1", "gcpBuildServiceAccount": "<EMAIL>", "storage_bucket": "gs://hsbc-9087302-unity-dev-key-management", "storage_bucket_app_dependencies": "gs://hsbc-9087302-unity-dev-app-runtime-dependencies", "image_builder_sa_file": "/home/<USER>/ServiceAccountKeys/<EMAIL>", "module_list": ["Cloud SQL", "Monitoring"], "modules": {"Cloud SQL": {"folder_name": "cloud_sql"}, "Monitoring": {"folder_name": "monitoring"}}, "teams": {"devcls": {"team_name": "core", "default_vars": "project-asia-south1"}}}, "hsbc-********-unyin2-dev": {"credential_id": "gcp_connect", "team": "devcls", "credential_foldername": "Unity", "project_id": "hsbc-********-unyin2-dev", "network": "hsbc-default-network", "environment": "hsbc-********-unyin2-dev", "clusterName": "devcls", "clusterRegion": "asia-south2", "clusterProject": "hsbc-********-unyin2-dev", "kubectlProxy": "ingress.devcls.hsbc-********-unyin2-dev.dev.gcp.cloud.in.hsbc:3128", "envSuffix": "-IN2", "region": "asia-south2", "gcpBuildServiceAccount": "<EMAIL>", "storage_bucket": "gs://hsbc-9087302-unity-dev-key-management", "storage_bucket_app_dependencies": "gs://hsbc-9087302-unity-dev-app-runtime-dependencies", "image_builder_sa_file": "/home/<USER>/ServiceAccountKeys/<EMAIL>", "module_list": ["Cloud SQL", "Monitoring"], "modules": {"Cloud SQL": {"folder_name": "cloud_sql"}, "Monitoring": {"folder_name": "monitoring"}}, "teams": {"devcls": {"team_name": "core", "default_vars": "project-asia-south1"}}}, "hsbc-********-unyin1-prod": {"credential_id": "gcp_connect", "team": "prodcls", "credential_foldername": "Unity", "project_id": "hsbc-********-unyin1-prod", "network": "hsbc-default-network", "environment": "hsbc-********-unyin1-prod", "clusterName": "prodcls", "clusterRegion": "asia-south1", "clusterProject": "hsbc-********-unyin1-prod", "kubectlProxy": "ingress.prodcls.hsbc-********-unyin1-prod.prod.gcp.cloud.in.hsbc:3128", "envSuffix": "-IN1", "region": "asia-south1", "gcpBuildServiceAccount": "<EMAIL>", "gcpServiceAccount2mgm": "<EMAIL>,<EMAIL>,<EMAIL>", "cmek_project": "hsbc-6320774-kms-dev", "storage_bucket": "gs://hsbc-********-unity-prod-key-management", "storage_bucket_app_dependencies": "gs://hsbc-********-unity-prod-app-runtime-dependencies", "image_builder_sa_file": "/home/<USER>/ServiceAccountKeys/<EMAIL>", "module_list": ["Cloud SQL", "Monitoring"], "modules": {"Cloud SQL": {"folder_name": "cloud_sql"}, "Monitoring": {"folder_name": "monitoring"}}, "teams": {"prodcls": {"team_name": "core", "default_vars": "project-asia-south1"}}}, "hsbc-********-unyin2-prod": {"credential_id": "gcp_connect", "team": "prodcls", "credential_foldername": "Unity", "project_id": "hsbc-********-unyin2-prod", "network": "hsbc-default-network", "environment": "hsbc-********-unyin2-prod", "clusterName": "prodcls", "clusterRegion": "asia-south2", "clusterProject": "hsbc-********-unyin2-prod", "kubectlProxy": "ingress.prodcls.hsbc-********-unyin2-prod.prod.gcp.cloud.in.hsbc:3128", "envSuffix": "-IN2", "region": "asia-south2", "gcpBuildServiceAccount": "<EMAIL>", "gcpServiceAccount2mgm": "<EMAIL>,<EMAIL>,<EMAIL>", "cmek_project": "hsbc-6320774-kms-dev", "storage_bucket": "gs://hsbc-********-unity-prod-key-management", "storage_bucket_app_dependencies": "gs://hsbc-********-unity-prod-app-runtime-dependencies", "image_builder_sa_file": "/home/<USER>/ServiceAccountKeys/<EMAIL>", "module_list": ["Cloud SQL", "Monitoring"], "modules": {"Cloud SQL": {"folder_name": "cloud_sql"}, "Monitoring": {"folder_name": "monitoring"}}, "teams": {"prodcls": {"team_name": "core", "default_vars": "project-asia-south1"}}}, "hsbc-********-unyeu1-dev": {"credential_id": "gcp_connect", "team": "devcls", "credential_foldername": "Unity", "project_id": "hsbc-********-unyeu1-dev", "network": "hsbc-default-network", "environment": "hsbc-********-unyeu1-dev", "clusterName": "devcls", "clusterRegion": "europe-west1", "clusterProject": "hsbc-********-unyeu1-dev", "kubectlProxy": "ingress.devcls.hsbc-********-unyeu1-dev.dev.gcp.cloud.uk.hsbc:3128", "envSuffix": "-EU1", "region": "europe-west1", "gcpBuildServiceAccount": "<EMAIL>", "storage_bucket": "gs://hsbc-9087302-unity-dev-key-management", "storage_bucket_app_dependencies": "gs://hsbc-9087302-unity-dev-app-runtime-dependencies", "image_builder_sa_file": "/home/<USER>/ServiceAccountKeys/<EMAIL>", "module_list": ["Cloud SQL", "Monitoring"], "modules": {"Cloud SQL": {"folder_name": "cloud_sql"}, "Monitoring": {"folder_name": "monitoring"}}, "teams": {"devcls": {"team_name": "core", "default_vars": "project-europe-west1"}}}, "hsbc-********-unyeu2-dev": {"credential_id": "gcp_connect", "team": "gke-t2-vpc1-125da3", "credential_foldername": "Unity", "project_id": "hsbc-********-unyeu2-dev", "network": "hsbc-********-unyeu2-dev-cinternal-vpc1", "environment": "hsbc-********-unyeu2-dev", "clusterName": "gke-t2-vpc1-125da3", "clusterRegion": "europe-west3", "clusterProject": "hsbc-********-unyeu2-dev", "kubectlProxy": "ingress.gke-t2-vpc1-125da3.hsbc-********-unyeu2-dev.dev.gcp.cloud.uk.hsbc:3128", "envSuffix": "-EU2", "region": "europe-west2", "gcpBuildServiceAccount": "<EMAIL>", "storage_bucket": "gs://hsbc-9087302-unity-dev-key-management", "storage_bucket_app_dependencies": "gs://hsbc-9087302-unity-dev-app-runtime-dependencies", "image_builder_sa_file": "/home/<USER>/ServiceAccountKeys/<EMAIL>", "module_list": ["Cloud SQL", "Monitoring"], "modules": {"Cloud SQL": {"folder_name": "cloud_sql"}, "Tanium scanning": {"folder_name": "tanium_scanning", "tfvars": "tanium-dev"}}, "teams": {"gke-t2-vpc1-125da3": {"team_name": "core", "default_vars": "project-europe-west2"}}}, "hsbc-********-unybitlm-dev": {"credential_id": "gcp_connect", "team": "devcls", "credential_foldername": "Unity", "project_id": "hsbc-********-unybitlm-dev", "network": "hsbc-default-network", "environment": "hsbc-********-unybitlm-dev", "clusterName": "devcls", "clusterRegion": "asia-east2", "clusterProject": "hsbc-********-unybitlm-dev", "kubectlProxy": "ingress.devcls.hsbc-********-unybitlm-dev.dev.gcp.cloud.hk.hsbc:3128", "envSuffix": "-BITLM", "region": "asia-east2", "gcpBuildServiceAccount": "<EMAIL>", "storage_bucket": "gs://hsbc-9087302-unity-dev-key-management", "storage_bucket_app_dependencies": "gs://hsbc-9087302-unity-dev-app-runtime-dependencies", "image_builder_sa_file": "/home/<USER>/ServiceAccountKeys/<EMAIL>", "module_list": ["Cloud SQL", "Monitoring"], "modules": {"Cloud SQL": {"folder_name": "cloud_sql"}, "Monitoring": {"folder_name": "monitoring"}}, "teams": {"devcls": {"team_name": "bitlm", "default_vars": "project-asia-east2"}}}, "hsbc-********-unyfaasia-dev": {"credential_id": "gcp_connect", "team": "devcls", "credential_foldername": "Unity", "project_id": "hsbc-********-unyfaasia-dev", "network": "hsbc-default-network", "environment": "hsbc-********-unyfaasia-dev", "clusterName": "devcls", "clusterRegion": "asia-east2", "clusterProject": "hsbc-********-unyfaasia-dev", "kubectlProxy": "ingress.devcls.hsbc-********-unyfaasia-dev.dev.gcp.cloud.hk.hsbc:3128", "envSuffix": "-FAASIA", "region": "asia-east2", "gcpBuildServiceAccount": "<EMAIL>", "storage_bucket": "gs://hsbc-9087302-unity-dev-key-management", "storage_bucket_app_dependencies": "gs://hsbc-9087302-unity-dev-app-runtime-dependencies", "image_builder_sa_file": "/home/<USER>/ServiceAccountKeys/<EMAIL>", "module_list": ["Cloud SQL", "Monitoring"], "modules": {"Cloud SQL": {"folder_name": "cloud_sql"}, "Monitoring": {"folder_name": "monitoring"}}, "teams": {"devcls": {"team_name": "unyfaasia", "default_vars": "project-asia-east2"}}}, "hsbc-********-unycfs-dev": {"credential_id": "gcp_connect", "team": "devcls", "credential_foldername": "Unity", "project_id": "hsbc-********-unycfs-dev", "network": "hsbc-default-network", "environment": "hsbc-********-unycfs-dev", "clusterName": "devcls", "clusterRegion": "asia-east2", "clusterProject": "hsbc-********-unycfs-dev", "kubectlProxy": "ingress.devcls.hsbc-********-unycfs-dev.dev.gcp.cloud.hk.hsbc:3128", "envSuffix": "-CFS", "region": "asia-east2", "gcpBuildServiceAccount": "<EMAIL>", "storage_bucket": "gs://hsbc-9087302-unity-dev-key-management", "storage_bucket_app_dependencies": "gs://hsbc-9087302-unity-dev-app-runtime-dependencies", "image_builder_sa_file": "/home/<USER>/ServiceAccountKeys/<EMAIL>", "module_list": ["Cloud SQL", "Monitoring"], "modules": {"Cloud SQL": {"folder_name": "cloud_sql"}, "Monitoring": {"folder_name": "monitoring"}}, "teams": {"devcls": {"team_name": "unycfs", "default_vars": "project-asia-east2"}}}, "hsbc-********-unyempf-dev": {"credential_id": "gcp_connect", "team": "devcls", "credential_foldername": "Unity", "project_id": "hsbc-********-unyempf-dev", "network": "hsbc-default-network", "environment": "hsbc-********-unyempf-dev", "clusterName": "devcls", "clusterRegion": "asia-east2", "clusterProject": "hsbc-********-unyempf-dev", "kubectlProxy": "ingress.devcls.hsbc-********-unyempf-dev.dev.gcp.cloud.hk.hsbc:3128", "envSuffix": "-EMPF", "region": "asia-east2", "gcpBuildServiceAccount": "<EMAIL>", "storage_bucket": "gs://hsbc-9087302-unity-dev-key-management", "storage_bucket_app_dependencies": "gs://hsbc-9087302-unity-dev-app-runtime-dependencies", "image_builder_sa_file": "/home/<USER>/ServiceAccountKeys/<EMAIL>", "module_list": ["Cloud SQL", "Monitoring"], "modules": {"Cloud SQL": {"folder_name": "cloud_sql"}, "Monitoring": {"folder_name": "monitoring"}}, "teams": {"devcls": {"team_name": "unycfs", "default_vars": "project-asia-east2"}}}, "hsbc-********-unycsreport-dev": {"credential_id": "gcp_connect", "team": "devcls", "credential_foldername": "Unity", "project_id": "hsbc-********-unycsreport-dev", "network": "hsbc-default-network", "environment": "hsbc-********-unycsreport-dev", "clusterName": "devcls", "clusterRegion": "asia-east2", "clusterProject": "hsbc-********-unycsreport-dev", "kubectlProxy": "ingress.devcls.hsbc-********-unycsreport-dev.dev.gcp.cloud.hk.hsbc:3128", "envSuffix": "-CSREPORT", "region": "asia-east2", "gcpBuildServiceAccount": "<EMAIL>", "storage_bucket": "gs://hsbc-9087302-unity-dev-key-management", "storage_bucket_app_dependencies": "gs://hsbc-9087302-unity-dev-app-runtime-dependencies", "image_builder_sa_file": "/home/<USER>/ServiceAccountKeys/<EMAIL>", "module_list": ["Cloud SQL", "Monitoring"], "modules": {"Cloud SQL": {"folder_name": "cloud_sql"}, "Monitoring": {"folder_name": "monitoring"}}, "teams": {"devcls": {"team_name": "unycsreport", "default_vars": "project-asia-east2"}}}, "hsbc-********-unyirm-dev": {"credential_id": "gcp_connect", "team": "devcls", "credential_foldername": "Unity", "project_id": "hsbc-********-unyirm-dev", "network": "hsbc-default-network", "environment": "hsbc-********-unyirm-dev", "clusterName": "devcls", "clusterRegion": "asia-east2", "clusterProject": "hsbc-********-unyirm-dev", "kubectlProxy": "ingress.devcls.hsbc-********-unyirm-dev.dev.gcp.cloud.hk.hsbc:3128", "envSuffix": "-IRM", "region": "asia-east2", "gcpBuildServiceAccount": "<EMAIL>", "storage_bucket": "gs://hsbc-9087302-unity-dev-key-management", "storage_bucket_app_dependencies": "gs://hsbc-9087302-unity-dev-app-runtime-dependencies", "image_builder_sa_file": "/home/<USER>/ServiceAccountKeys/<EMAIL>", "module_list": ["Cloud SQL", "Monitoring"], "modules": {"Cloud SQL": {"folder_name": "cloud_sql"}, "Monitoring": {"folder_name": "monitoring"}}, "teams": {"devcls": {"team_name": "unyirm", "default_vars": "project-asia-east2"}}}, "hsbc-********-unybitlm-prod": {"credential_id": "gcp_connect", "team": "prodcls", "credential_foldername": "Unity", "project_id": "hsbc-********-unybitlm-prod", "network": "hsbc-default-network", "environment": "hsbc-********-unybitlm-prod", "clusterName": "prodcls", "clusterRegion": "asia-east2", "clusterProject": "hsbc-********-unybitlm-prod", "kubectlProxy": "ingress.prodcls.hsbc-********-unybitlm-prod.prod.gcp.cloud.hk.hsbc:3128", "envSuffix": "-BITLM", "region": "asia-east2", "gcpBuildServiceAccount": "<EMAIL>", "storage_bucket": "gs://hsbc-********-unity-prod-key-management", "storage_bucket_app_dependencies": "gs://hsbc-********-unity-prod-app-runtime-dependencies", "image_builder_sa_file": "/home/<USER>/ServiceAccountKeys/<EMAIL>", "module_list": ["Cloud SQL", "Monitoring"], "modules": {"Cloud SQL": {"folder_name": "cloud_sql"}, "Monitoring": {"folder_name": "monitoring"}}, "teams": {"prodcls": {"team_name": "bitlm", "default_vars": "project-asia-east2"}}}, "hsbc-********-unyfaasia-prod": {"credential_id": "gcp_connect", "team": "prodcls", "credential_foldername": "Unity", "project_id": "hsbc-********-unyfaasia-prod", "network": "hsbc-default-network", "environment": "hsbc-********-unyfaasia-prod", "clusterName": "prodcls", "clusterRegion": "asia-east2", "clusterProject": "hsbc-********-unyfaasia-prod", "kubectlProxy": "ingress.prodcls.hsbc-********-unyfaasia-prod.prod.gcp.cloud.hk.hsbc:3128", "envSuffix": "-FAASIA", "region": "asia-east2", "gcpBuildServiceAccount": "<EMAIL>", "storage_bucket": "gs://hsbc-********-unity-prod-key-management", "storage_bucket_app_dependencies": "gs://hsbc-********-unity-prod-app-runtime-dependencies", "image_builder_sa_file": "/home/<USER>/ServiceAccountKeys/<EMAIL>", "module_list": ["Cloud SQL", "Monitoring"], "modules": {"Cloud SQL": {"folder_name": "cloud_sql"}, "Monitoring": {"folder_name": "monitoring"}}, "teams": {"prodcls": {"team_name": "unyfaasia", "default_vars": "project-asia-east2"}}}, "hsbc-********-unycfs-prod": {"credential_id": "gcp_connect", "team": "prodcls", "credential_foldername": "Unity", "project_id": "hsbc-********-unycfs-prod", "network": "hsbc-default-network", "environment": "hsbc-********-unycfs-prod", "clusterName": "prodcls", "clusterRegion": "asia-east2", "clusterProject": "hsbc-********-unycfs-prod", "kubectlProxy": "ingress.prodcls.hsbc-********-unycfs-prod.prod.gcp.cloud.hk.hsbc:3128", "envSuffix": "-CFS", "region": "asia-east2", "gcpBuildServiceAccount": "<EMAIL>", "storage_bucket": "gs://hsbc-********-unity-prod-key-management", "storage_bucket_app_dependencies": "gs://hsbc-********-unity-prod-app-runtime-dependencies", "image_builder_sa_file": "/home/<USER>/ServiceAccountKeys/<EMAIL>", "module_list": ["Cloud SQL", "Monitoring"], "modules": {"Cloud SQL": {"folder_name": "cloud_sql"}, "Monitoring": {"folder_name": "monitoring"}}, "teams": {"prodcls": {"team_name": "unycfs", "default_vars": "project-asia-east2"}}}, "hsbc-********-unyempf-prod": {"credential_id": "gcp_connect", "team": "prodcls", "credential_foldername": "Unity", "project_id": "hsbc-********-unyempf-prod", "network": "hsbc-default-network", "environment": "hsbc-********-unyempf-prod", "clusterName": "prodcls", "clusterRegion": "asia-east2", "clusterProject": "hsbc-********-unyempf-prod", "kubectlProxy": "ingress.prodcls.hsbc-********-unyempf-prod.prod.gcp.cloud.hk.hsbc:3128", "envSuffix": "-EMPF", "region": "asia-east2", "gcpBuildServiceAccount": "<EMAIL>", "storage_bucket": "gs://hsbc-********-unity-prod-key-management", "storage_bucket_app_dependencies": "gs://hsbc-********-unity-prod-app-runtime-dependencies", "image_builder_sa_file": "/home/<USER>/ServiceAccountKeys/<EMAIL>", "module_list": ["Cloud SQL", "Monitoring"], "modules": {"Cloud SQL": {"folder_name": "cloud_sql"}, "Monitoring": {"folder_name": "monitoring"}}, "teams": {"prodcls": {"team_name": "bitlm", "default_vars": "project-asia-east2"}}}, "ali.hsbc-********-unity-dev.unity-dev-ack": {"credential_id": "", "team": "unity-dev-ack", "credential_foldername": "Unity", "project_id": "hsbc-********-unity-dev", "network": "hsbc-default-network", "environment": "hsbc-********-unity-dev", "clusterName": "unity-dev-ack", "clusterRegion": "cn-guangzhou", "clusterProject": "hsbc-********-unity-dev", "kubectlProxy": "10.189.187.124:6443", "envSuffix": "-ALI", "region": "cn-guangzhou", "gcpBuildServiceAccount": "", "storage_bucket": "", "storage_bucket_app_dependencies": "", "image_builder_sa_file": "", "module_list": [], "modules": {}, "teams": {"unity-dev-ack": {"team_name": "", "default_vars": ""}}}, "ali.hsbc-********-unity-prod.unity-prod-ack": {"credential_id": "", "team": "unity-prod-ack", "credential_foldername": "Unity", "project_id": "hsbc-********-unity-prod", "network": "hsbc-default-network", "environment": "hsbc-********-unity-prod", "clusterName": "unity-prod-ack", "clusterRegion": "cn-guangzhou", "clusterProject": "hsbc-********-unity-prod", "kubectlProxy": "10.189.187.145:6443", "envSuffix": "-ALI", "region": "cn-guangzhou", "gcpBuildServiceAccount": "", "storage_bucket": "", "storage_bucket_app_dependencies": "", "image_builder_sa_file": "", "module_list": [], "modules": {}, "teams": {"unity-prod-ack": {"team_name": "", "default_vars": ""}}}, "hk-kops-u2kopp01": {"credential_id": "", "team": "hk-kops-u2kopp01", "credential_foldername": "Unity", "project_id": "hsbc-onprem", "network": "hsbc-default-network", "environment": "KOPS", "clusterName": "u2kopp01", "clusterRegion": "hk-gdc-tko", "clusterProject": "KOPS", "kubectlProxy": "", "envSuffix": "-KOPS", "region": "hk-gdc-tko", "gcpBuildServiceAccount": "", "storage_bucket": "", "storage_bucket_app_dependencies": "", "image_builder_sa_file": "", "module_list": [], "modules": {}, "teams": {"hk-kops-u2kopp01": {"team_name": "", "default_vars": ""}}}, "hk-kops-u2kopc01": {"credential_id": "", "team": "hk-kops-u2kopc01", "credential_foldername": "Unity", "project_id": "hsbc-onprem", "network": "hsbc-default-network", "environment": "KOPS", "clusterName": "u2kopc01", "clusterRegion": "hk-gdc-skm", "clusterProject": "KOPS", "kubectlProxy": "", "envSuffix": "-KOPS", "region": "hk-gdc-tko", "gcpBuildServiceAccount": "", "storage_bucket": "", "storage_bucket_app_dependencies": "", "image_builder_sa_file": "", "module_list": [], "modules": {}, "teams": {"hk-kops-u2kopc01": {"team_name": "", "default_vars": ""}}}, "hk-kops-u2kopd01": {"credential_id": "", "team": "hk-kops-u2kopd01", "credential_foldername": "Unity", "project_id": "hsbc-onprem", "network": "hsbc-default-network", "environment": "KOPS", "clusterName": "u2kopd01", "clusterRegion": "hk-gdc-skm", "clusterProject": "KOPS", "kubectlProxy": "", "envSuffix": "-KOPS", "region": "hk-gdc-tko", "gcpBuildServiceAccount": "", "storage_bucket": "", "storage_bucket_app_dependencies": "", "image_builder_sa_file": "", "module_list": [], "modules": {}, "teams": {"hk-kops-u2kopd01": {"team_name": "", "default_vars": ""}}}}, "terraform": {"terraform_state_file": "terraform.tfstate", "terraform_plan_file": "tfplan", "tfvars_file_path": "terraform.tfvars.json", "terraform_plugins_file": ".terraform.tar.gz"}, "file_path": {"gcp_service_account_json_file": "/home/<USER>/ServiceAccountKeys/gce-stage3-image-builder.json", "gcloud_sdk_path": "/", "groovy_home_path": "/opt/jenkins_agent/build_tools/groovy/groovy-3.0.3/bin", "java_home_path": "/opt/jenkins_agent/build_tools/jdk-8u212/bin", "terraform_sdk_path": "/opt/terraform/", "packer_sdk_path": "/opt/jenkins_agent/build_tools/packer166/packer", "ansible_sdk_path": "/usr/bin/ansible/********/python/bin", "packer_json_file_path": "packer/image.json", "packer_variables_file_path": "packer/packer-variables.json", "ansible_variables_file_path": "ansible/group_vars/ansible-variables.json", "json_schema_validator_file_path": "jsonvalidator/src/main/groovy/groovyschema/Validator.groovy", "connections_groovy_file_path": "scripts/Connections.groovy", "service_account_key_rotation_file_path": "scripts/hsbc-gcp-sa-key-rotate.sh", "service_account_key_file_google_storage_path": "service_account/terraform-provisioner.json", "replica_set_rolling_updates_file_path": "infra_schema/replica_sets", "shard_cluster_rolling_updates_file_path": "infra_schema/shard_clusters", "gke_service_account_json_file": "Credentials/hsbc-9087302-unity-dev-c3bdf1db6a51.json", "image_builder_service_account_json_file": "Credentials/hsbc-9087302-unity-dev-image3.json"}, "proxy_url": {"google_http_proxy_url": "http://googleapis-dev.gcp.cloud.uk.hsbc:3128", "google_https_proxy_url": "http://googleapis-dev.gcp.cloud.uk.hsbc:3128"}}