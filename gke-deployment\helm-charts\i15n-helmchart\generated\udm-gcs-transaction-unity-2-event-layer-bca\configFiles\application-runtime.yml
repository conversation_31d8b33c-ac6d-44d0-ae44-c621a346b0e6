---
spring:
  application:
    name: "udm-gcs-transaction-unity-2-event-layer-bca"
kafkaConfig:
  auto.offset.reset: "latest"
  bootstrap.servers: "kafka-0.kafka-internal.ns-core-prod-apps.svc.cluster.local:9093,kafka-1.kafka-internal.ns-core-prod-apps.svc.cluster.local:9093,kafka-2.kafka-internal.ns-core-prod-apps.svc.cluster.local:9093"
  group.id: "${KAFKA_CONSUMER_GROUP}"
  schema.registry.url: "https://hkl20077608.hc.cloud.hk.hsbc:8082/"
  security.protocol: "SSL"
  ssl.keystore.location: "/hss/apps/certs/unity-microservices.jks"
  ssl.keystore.password: "${KAFKA_KEYSTORE_PASSWORD}"
  ssl.truststore.location: "/hss/apps/certs/unity-microservices.ts"
  ssl.truststore.password: "${KAFKA_TRUSTSTORE_PASSWORD}"
management:
  endpoints:
    web:
      exposure:
        include: "health,prometheus"
  metrics:
    tags:
      application: "${spring.application.name}"
cache:
  provider: "memory"
consumerAdaptor:
  name: "unity-2-event-layer"
  sourceSchema: "UDM_GCS_TRANSACTION"
  sourceTopic: "unity2-PROD-core-fusion-udm-gcs-transaction-out"
  sourceBatchTopicSuffix: "-batch"
  consumerSourceMode: "MESSAGE"
  outputFormatConfig:
    customFormatConfig:
      processorName: "Unity2EventLayerMessageFormatter.groovy"
  consumerTargetDataFormat: "CUSTOM"
  consumerTargetChannel: "KAFKA"
  consumerTargetChannelConnectionConfig:
    kafkaConfig:
      kafkaProperties:
        security.protocol: "SSL"
        schema.registry.url: "https://schema-registry-0.ns-ctrl-kafka-apps.prodcls.hsbc-12064156-evlayer-prod.prod.gcp.cloud.uk.hsbc/"
        ssl.truststore.location: "/hss/apps/certs/evlayer-confluent.ts"
        ssl.keystore.password: "${EVENT_LAYER_KAFKA_KEYSTORE_PASSWORD}"
        group.id: "${KAFKA_CONSUMER_GROUP}"
        ssl.keystore.location: "/hss/apps/certs/evlayer-confluent.jks"
        bootstrap.servers: "kafka-0.ns-ctrl-kafka-apps.prodcls.hsbc-12064156-evlayer-prod.prod.gcp.cloud.uk.hsbc:443,kafka-1.ns-ctrl-kafka-apps.prodcls.hsbc-12064156-evlayer-prod.prod.gcp.cloud.uk.hsbc:443,kafka-2.ns-ctrl-kafka-apps.prodcls.hsbc-12064156-evlayer-prod.prod.gcp.cloud.uk.hsbc:443"
        ssl.truststore.password: "${EVENT_LAYER_KAFKA_TRUSTSTORE_PASSWORD}"
        auto.offset.reset: "latest"
      targetTopic: "prod-evlayer-source-adaptor-custody-v1-2-0-inbound"
    sftpConfig:
      host: ""
      port: 0
      userName: ""
      password: ""
      privateKey: ""
      remoteDirectory: ""
      fileNamePattern: ""
      generateChecksum: false
    restConfig:
      oauth2Config:
        scopes: ""
        url: ""
        clientId: ""
        clientSecret: ""
        grantType: ""
        username: ""
        password: ""
      targetURL: ""
    mqConfig:
      host: ""
      port: 1414
      channel: ""
      queueManager: ""
      cipherSuite: ""
      appName: ""
      queueName: ""
      keyStore: ""
      keyPassword: ""
    solaceConfig:
      host: ""
      port: 1943
      userName: ""
      password: ""
      queueName: ""
      vpnName: ""
      clientId: ""
      trustStore: ""
      pubSubDomain: false
    symphonyConfig:
      basicAuthConfig:
        username: ""
        password: ""
      targetURL: "https://edist-tools.prd.fx.gbm.cloud.uk.hsbc:12000/symphony-webhook/v3/generic/room/"
    emailConfig:
      to: ""
      cc: ""
      bcc: ""
      subject: ""
      classification: "INTERNAL"
      targetURL: "https://apprunner.hk.hsbc/email-service/api/v1/send-email"
    pubSubConfig:
      targetProject: ""
      targetTopic: ""
      location: ""
      customerManagedEncryptionKey: ""
    xmatterConfig:
      key: ""
      source: "unity2-consumer-adaptor"
      object: "ns-core-prod-apps.udm-gcs-transaction-unity-2-event-layer-bca"
      severity: "INFO"
      targetURL: "https://httpevents.systems.uk.hsbc/api/sendAlert"
  fieldNameMappings: []
