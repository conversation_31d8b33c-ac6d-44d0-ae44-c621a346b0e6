# Information for project teams

**NOTE: This process and deployment code is under heavy development and some structural changes are likely. The GCP Core
Engineering Team will endeavour to make any changes as seamless as possible.**

## Project Configuration
This repositories master branch will need cloning to a new repository with the name of the project intended to be 
deployed. For example, if you GCP project is to be called `hsbc-123456-amazing-dev`, then the repository should also 
be called `hsbc-123456-amazing-dev`

project configuration can be found in the `sot/` directory where:

SOT file name | Format | Additional control(Config check) | Resource user guide | Created by default
--- | :---: | :---: | :--- | :---
| `project_roles.auto.tfvars.json` |array- typed value for the corresponding role key of the [JSON formatted object](https://tools.ietf.org/html/rfc7159#section-4)| [Config Check - IAM Role Bindings](https://alm-confluence.systems.uk.hsbc/confluence/x/8g5QSg) | [Project Level IAM role binding](https://alm-confluence.systems.uk.hsbc/confluence/x/4bk6Sg) | True
| `service_accounts.auto.tfvars.json` | "account_id": "description" | N/A | [Manage GCP service account](https://alm-confluence.systems.uk.hsbc/confluence/x/T7c6Sg) | True
| `service_account_roles.auto.tfvars.json` |[JSON formatted object](https://tools.ietf.org/html/rfc7159#section-4) | [Config Check - Service Account Role Bindings](https://alm-confluence.systems.uk.hsbc/confluence/x/Gw9QSg) | [Service Account IAM role binding](https://alm-confluence.systems.uk.hsbc/confluence/x/Mr06Sg) | False
| `project_apis.auto.tfvars.json` | [JSON formatted list/array](https://tools.ietf.org/html/rfc7159#section-5) | N/A | [Manage Google Services (APIs)](https://alm-confluence.systems.uk.hsbc/confluence/x/-lDxSg) | True
| `project_var.auto.tfvars.json` | [JSON formatted list/array](https://tools.ietf.org/html/rfc7159#section-5) | [Config Check - Shared VPC](https://alm-confluence.systems.uk.hsbc/confluence/x/r8W2Sg) | [Implement Project Variables](https://alm-confluence.systems.uk.hsbc/confluence/x/dsC2Sg) [Attach to Shared VPC Host Project](https://alm-confluence.systems.uk.hsbc/confluence/x/scC2Sg) | True
| `project_metadata.auto.tfvars` | key/value pairs in metadata list | N/A | [Implement Project Metadata](https://alm-confluence.systems.uk.hsbc/confluence/x/bBN1Sw) | True
| `shared_vpc_users.auto.tfvars.json` | [JSON formatted object](https://tools.ietf.org/html/rfc7159#section-4) |N/A| [Shared VPC network subnets access](https://alm-confluence.systems.uk.hsbc/confluence/x/XMK2Sg) | False
| `networks.auto.tfvars.json` | [JSON formatted object](https://tools.ietf.org/html/rfc7159#section-4) |N/A| [Provision Custom (self-service) Networks](https://alm-confluence.systems.uk.hsbc/confluence/x/bzGVSQ) | False
| `subnets.auto.tfvars.json` | [JSON formatted object](https://tools.ietf.org/html/rfc7159#section-4) | [Config Check - Subnets](https://alm-confluence.systems.uk.hsbc/confluence/x/-sW2Sg) | [Provision Subnets](https://alm-confluence.systems.uk.hsbc/confluence/x/z8K2Sg) | False
| `cloud_dns.auto.tfvars.json` | [JSON formatted object](https://tools.ietf.org/html/rfc7159#section-4) |N/A| [Configure Cloud DNS Peering](https://alm-confluence.systems.uk.hsbc/confluence/x/u8O2Sg) | False
| `routes.auto.tfvars.json` | [JSON formatted object](https://tools.ietf.org/html/rfc7159#section-4) | [Config Check - Routes](https://alm-confluence.systems.uk.hsbc/confluence/x/cca2Sg) | [Provision Routes](https://alm-confluence.systems.uk.hsbc/confluence/x/SMO2Sg) | False
| `firewall.json` | [JSON formatted object](https://tools.ietf.org/html/rfc7159#section-4) | [Config Check - Firewall Rules](https://alm-confluence.systems.uk.hsbc/confluence/x/FMe2Sg) | [Provision Firewall Rules](https://alm-confluence.systems.uk.hsbc/confluence/x/u8O2Sg) | True
| `cloudbuild.auto.tfvars.json` | [JSON formatted object](https://tools.ietf.org/html/rfc7159#section-4) |N/A| [Provision Cloud Build Worker Pool](https://alm-confluence.systems.uk.hsbc/confluence/x/TR1cUQ) | False
| `sgp_roles.auto.tfvars.json` | [JSON formatted object](https://tools.ietf.org/html/rfc7159#section-4) |N/A| [Allow Super Admin Account Access](https://alm-confluence.systems.uk.hsbc/confluence/x/oFHxSg) | False
* User-group to role bindings are done by add Active Directory Groups into  `sot/project_roles.auto.tfvars.json`
    * The items in the list need to be formatted with a prefix of `group:` or `serviceAccount` respectively. E.g.
    * `"group:<EMAIL>"`
* PLADA references must also be included by swapping out the value `plada-ref-update` in 
`sot/project_vars.auto.tfvars`
* `sot/sgp_roles.auto.tfvars.json` requested by core team for troubleshooting

There are many [JSON format checkers](https://jsonformatter.curiousconcept.com) available on the Internet where you can 
verify your changes.

Please refer to the [Confluence user-guide](https://alm-confluence.systems.uk.hsbc/confluence/x/GPsXDw)
 for more details