import com.poc.hss.fasttrack.service.LookupService
import com.poc.hss.fasttrack.transform.model.LookupRequest
import com.poc.hss.fasttrack.transform.model.TransformerRecord
import io.vertx.core.json.JsonObject
import org.slf4j.Logger
import org.slf4j.LoggerFactory

import java.util.function.BiConsumer
import java.util.stream.Collectors

class UdmGcsInstrumentPrice {

    private static final Logger logger = LoggerFactory.getLogger(UdmGcsInstrument.class);

    static BiConsumer<List<TransformerRecord>, LookupService> queryAndBendInstrumentPriceData = (targetData, lookupService) -> {
        logger.info("Start Blending Instrument Price data")
        String instrumentIds = targetData.stream()
                .filter(record -> record.getData().getString(UdmGcsTransactionConstant.INSTRUMENT_ID) != null)
                .map(record -> record.getData().getString(UdmGcsTransactionConstant.INSTRUMENT_ID))
                .map(id -> "'" + id + "'")
                .distinct()
                .collect(Collectors.joining(","))
        if (instrumentIds.isEmpty()) {
            logger.info("Skipping instrument lookup request as instrument price ids are empty or respective trade records are not exist.")
            return
        }

        String criteria = String.format(UdmGcsTransactionConstant._ID + " in (%s)", instrumentIds)
        logger.debug("instrument price lookup request criteria [" + criteria + "]")

        LookupRequest instrumentLookupRequest = UdmGcsTransactionHelper.createLookupRequest(criteria, UdmGcsTransactionConstant.GCS_INSTRUMENT_PRICE, UdmGcsTransactionConstant.INSTRUMENT_PRICE_FIELDS)
        Map<String, JsonObject> instrumentPriceMap = lookupService.queryList(instrumentLookupRequest).stream()
                .collect(Collectors.toMap(json -> json.getString(UdmGcsTransactionConstant._ID), json -> json, (a, b) -> a))
        if (null == instrumentPriceMap || instrumentPriceMap.isEmpty()) {
            logger.debug("instrument price lookup request failed or no record for Criteria : " + criteria)
            return
        }
        logger.debug("instrument price lookup request result for Criteria " + criteria + " is \n" + instrumentPriceMap.toString())
        blendInstrumentPrice.accept(targetData, instrumentPriceMap)

    }

    static BiConsumer<List<TransformerRecord>, Map<String, JsonObject>> blendInstrumentPrice = (targetData, instrumentPriceMap) -> {
        targetData.forEach(record -> {
            String instrumentId = record.getData().getString(UdmGcsTransactionConstant.INSTRUMENT_ID)
            if (instrumentPriceMap.containsKey(instrumentId)) {
                JsonObject instrument = instrumentPriceMap.get(instrumentId)
                instrumentPriceDataMapping(instrument, record.getData())
            }
        })
    }

    static void instrumentPriceDataMapping(JsonObject instrument, JsonObject output) {
        output.put("transactionReferenceAmount", instrument.getString("market_price"))
        output.put("transactionReferenceAmountCurrency", instrument.getString("currency"))
    }
}