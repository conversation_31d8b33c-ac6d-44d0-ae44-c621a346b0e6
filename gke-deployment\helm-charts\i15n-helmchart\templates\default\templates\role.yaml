{{- if and .Values.rbac.create (not .Values.rbac.useExistingRole) -}}
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: {{ include "i15n-helmchart.fullname" . }}
  namespace: {{ .Values.namespace }}
  labels:
    {{- include "i15n-helmchart.labels" . | nindent 4 }}
  {{- with .Values.ingress.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
{{- if or .Values.rbac.pspEnabled (.Values.rbac.namespaced) }}
rules:
{{- if .Values.rbac.pspEnabled }}
- apiGroups:      ['extensions']
  resources:      ['podsecuritypolicies']
  verbs:          ['use']
  resourceNames:  [{{ include "i15n-helmchart.fullname" . }}]
{{- end }}
{{- if .Values.rbac.namespaced }}
{{- range .Values.rbac.rules }}
- apiGroups:
  {{- range .apiGroups }}
  - {{ . | quote }}
  {{- end}}
  resources:
  {{- range .resources }}
  - {{ . | quote }}
  {{- end }}
  verbs:
  {{- range .verbs }}
  - {{ . | quote }}
  {{- end }}
{{- end}}
{{- end }}
{{- with .Values.rbac.extraRoleRules }}
{{ toYaml . | indent 0 }}
{{- end}}
{{- else }}
rules: []
{{- end }}
{{- end }}
