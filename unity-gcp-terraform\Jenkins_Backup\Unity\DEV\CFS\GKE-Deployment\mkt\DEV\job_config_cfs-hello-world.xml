<?xml version='1.1' encoding='UTF-8'?>
<org.jenkinsci.plugins.workflow.multibranch.WorkflowMultiBranchProject plugin="workflow-multibranch@783.va_6eb_ef636fb_d">
  <actions/>
  <description></description>
  <properties>
    <org.jenkinsci.plugins.docker.workflow.declarative.FolderConfig plugin="docker-workflow@580.vc0c340686b_54">
      <dockerLabel></dockerLabel>
      <registry plugin="docker-commons@439.va_3cb_0a_6a_fb_29"/>
    </org.jenkinsci.plugins.docker.workflow.declarative.FolderConfig>
    <com.cloudbees.hudson.plugins.folder.properties.EnvVarsFolderProperty plugin="cloudbees-folders-plus@3.32">
      <properties></properties>
    </com.cloudbees.hudson.plugins.folder.properties.EnvVarsFolderProperty>
  </properties>
  <folderViews class="jenkins.branch.MultiBranchProjectViewHolder" plugin="branch-api@2.1169.va_f810c56e895">
    <owner class="org.jenkinsci.plugins.workflow.multibranch.WorkflowMultiBranchProject" reference="../.."/>
  </folderViews>
  <healthMetrics/>
  <icon class="jenkins.branch.MetadataActionFolderIcon" plugin="branch-api@2.1169.va_f810c56e895">
    <owner class="org.jenkinsci.plugins.workflow.multibranch.WorkflowMultiBranchProject" reference="../.."/>
  </icon>
  <orphanedItemStrategy class="com.cloudbees.hudson.plugins.folder.computed.DefaultOrphanedItemStrategy" plugin="cloudbees-folder@6.928.v7c780211d66e">
    <pruneDeadBranches>true</pruneDeadBranches>
    <daysToKeep>-1</daysToKeep>
    <numToKeep>-1</numToKeep>
    <abortBuilds>false</abortBuilds>
  </orphanedItemStrategy>
  <triggers/>
  <disabled>false</disabled>
  <sources class="jenkins.branch.MultiBranchProject$BranchSourceList" plugin="branch-api@2.1169.va_f810c56e895">
    <data>
      <jenkins.branch.BranchSource>
        <source class="jenkins.plugins.git.GitSCMSource" plugin="git@5.2.2">
          <id>d1fdefaf-bc97-4968-9902-8731867b4896</id>
          <remote>https://stash.hk.hsbc/scm/cfs/cfs-unity-gke-helm-app.git</remote>
          <credentialsId>CFS_STASH_AC</credentialsId>
          <traits>
            <jenkins.plugins.git.traits.BranchDiscoveryTrait/>
            <net.gleske.scmfilter.impl.trait.RegexSCMHeadFilterTrait plugin="scm-filter-branch-pr@148.v0b_5f06e8b_c84">
              <regex>((branch|bugfix|feature|hotfix).*)</regex>
              <tagRegex>(?!.*)</tagRegex>
            </net.gleske.scmfilter.impl.trait.RegexSCMHeadFilterTrait>
          </traits>
        </source>
        <strategy class="jenkins.branch.DefaultBranchPropertyStrategy">
          <properties class="java.util.Arrays$ArrayList">
            <a class="jenkins.branch.BranchProperty-array">
              <jenkins.branch.NoTriggerBranchProperty/>
            </a>
          </properties>
        </strategy>
        <buildStrategies>
          <com.cloudbees.jenkins.plugins.buildstrategies.SkipInitialBuildOnFirstIndexingResetRevision plugin="cloudbees-build-strategies-plugin@1.80"/>
        </buildStrategies>
      </jenkins.branch.BranchSource>
    </data>
    <owner class="org.jenkinsci.plugins.workflow.multibranch.WorkflowMultiBranchProject" reference="../.."/>
  </sources>
  <factory class="org.jenkinsci.plugins.workflow.multibranch.WorkflowBranchProjectFactory">
    <owner class="org.jenkinsci.plugins.workflow.multibranch.WorkflowMultiBranchProject" reference="../.."/>
    <scriptPath>Jenkinsfile</scriptPath>
  </factory>
</org.jenkinsci.plugins.workflow.multibranch.WorkflowMultiBranchProject>