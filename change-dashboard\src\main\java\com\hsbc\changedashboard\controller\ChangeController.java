package com.hsbc.changedashboard.controller;

import com.hsbc.changedashboard.model.EnrichedChangeRecord;
import com.hsbc.changedashboard.service.ChangeDataService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/v1")
public class ChangeController {

    private static final Logger logger = LoggerFactory.getLogger(ChangeController.class);

    @Autowired
    private ChangeDataService changeDataService;

    /**
     * Step 9: Endpoint to get enriched change data
     * 
     * This endpoint orchestrates all the steps:
     * 1. Calls ICE API with current/next week Monday dates
     * 2. Captures the response and parses it
     * 3. Extracts JIRA IDs from descriptions
     * 4. Calls JIRA API for each JIRA ID
     * 5. Enriches data with JIRA key and summary
     * 6. Gets staff IDs from assignee fields
     * 7. Calls Poddy API for each staff ID
     * 8. Enriches data with person name, email, and photo URL
     * 9. Returns the enriched map/list
     */
    @GetMapping("/changes/enriched")
    public ResponseEntity<Map<String, Object>> getEnrichedChanges() {
        try {
            logger.info("Received request for enriched changes");
            
            List<EnrichedChangeRecord> enrichedChanges = changeDataService.getEnrichedChangeData();
            
            Map<String, Object> response = new HashMap<>();
            response.put("status", "success");
            response.put("count", enrichedChanges.size());
            response.put("data", enrichedChanges);
            response.put("timestamp", System.currentTimeMillis());
            
            logger.info("Successfully returned {} enriched change records", enrichedChanges.size());
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("Error processing enriched changes request", e);
            
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("status", "error");
            errorResponse.put("message", "Failed to fetch enriched change data");
            errorResponse.put("error", e.getMessage());
            errorResponse.put("timestamp", System.currentTimeMillis());
            
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
        }
    }

    /**
     * Health check endpoint
     */
    @GetMapping("/health")
    public ResponseEntity<Map<String, Object>> health() {
        Map<String, Object> response = new HashMap<>();
        response.put("status", "UP");
        response.put("service", "change-dashboard");
        response.put("timestamp", System.currentTimeMillis());
        
        return ResponseEntity.ok(response);
    }
}
