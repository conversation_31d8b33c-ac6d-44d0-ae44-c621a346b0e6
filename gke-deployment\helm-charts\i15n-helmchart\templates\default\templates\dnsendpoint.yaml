{{- if .Values.dnsEndpoint.enabled -}}
apiVersion: externaldns.k8s.io/v1alpha1
kind: DNSEndpoint
metadata:
  name: {{ .Values.dnsEndpoint.subDomain }}-dns
spec:
  endpoints:
    - dnsName: {{ .Values.dnsEndpoint.subDomain }}.{{ .Values.dnsEndpoint.shortNamespace }}.{{ .Values.dnsEndpoint.domain }}
      recordTTL: 300
      recordType: CNAME
      targets:
        - ingress.{{ .Values.dnsEndpoint.domain }}
    - dnsName: {{ .Values.dnsEndpoint.subDomain }}-proxy.{{ .Values.dnsEndpoint.shortNamespace }}.{{ .Values.dnsEndpoint.domain }}
      recordTTL: 300
      recordType: CNAME
      targets:
        - ingress-nginx.{{ .Values.dnsEndpoint.domain }}
{{- end }}