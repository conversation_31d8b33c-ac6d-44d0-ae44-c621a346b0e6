{"standard_role_bindings": {"organizations/*************/roles/CustomComputeLoadBalancerAdmin": [{"principal": "serviceAccount:<EMAIL>"}], "organizations/*************/roles/cr.dnsRecordAdmin": [{"principal": "serviceAccount:<EMAIL>"}, {"principal": "serviceAccount:<EMAIL>"}], "roles/browser": [{"principal": "serviceAccount:<EMAIL>"}], "roles/cloudsql.admin": [{"principal": "group:<EMAIL>"}, {"principal": "serviceAccount:<EMAIL>"}], "roles/cloudsql.client": [{"principal": "group:<EMAIL>"}, {"principal": "group:<EMAIL>"}, {"principal": "serviceAccount:<EMAIL>"}], "roles/cloudsql.instanceUser": [{"principal": "serviceAccount:<EMAIL>"}], "roles/compute.instanceAdmin": [{"principal": "serviceAccount:<EMAIL>"}], "roles/compute.networkUser": [{"principal": "serviceAccount:<EMAIL>"}, {"principal": "serviceAccount:<EMAIL>"}], "roles/compute.serviceAgent": [{"principal": "serviceAccount:<EMAIL>"}], "roles/compute.viewer": [{"principal": "serviceAccount:<EMAIL>"}], "roles/container.admin": [{"principal": "serviceAccount:<EMAIL>"}], "roles/container.serviceAgent": [{"principal": "serviceAccount:<EMAIL>"}], "roles/container.viewer": [{"principal": "group:<EMAIL>"}, {"principal": "serviceAccount:<EMAIL>"}, {"principal": "serviceAccount:<EMAIL>"}, {"principal": "serviceAccount:<EMAIL>"}], "roles/essentialcontacts.admin": [{"principal": "group:<EMAIL>"}], "roles/essentialcontacts.viewer": [{"principal": "group:<EMAIL>"}], "roles/gkehub.viewer": [{"principal": "serviceAccount:<EMAIL>"}], "roles/iam.serviceAccountAdmin": [{"principal": "group:<EMAIL>"}, {"principal": "serviceAccount:<EMAIL>"}, {"principal": "serviceAccount:<EMAIL>"}], "roles/logging.admin": [{"principal": "group:<EMAIL>"}, {"principal": "serviceAccount:<EMAIL>"}], "roles/logging.configWriter": [{"principal": "group:<EMAIL>"}, {"principal": "serviceAccount:<EMAIL>"}], "roles/logging.logWriter": [{"principal": "serviceAccount:<EMAIL>"}, {"principal": "serviceAccount:<EMAIL>"}, {"principal": "serviceAccount:<EMAIL>"}, {"principal": "serviceAccount:<EMAIL>"}], "roles/logging.viewer": [{"principal": "group:<EMAIL>"}, {"principal": "group:<EMAIL>"}, {"principal": "serviceAccount:<EMAIL>"}, {"principal": "serviceAccount:<EMAIL>"}], "roles/monitoring.editor": [{"principal": "group:<EMAIL>"}, {"principal": "serviceAccount:<EMAIL>"}], "roles/monitoring.metricWriter": [{"principal": "serviceAccount:<EMAIL>"}, {"principal": "serviceAccount:<EMAIL>"}, {"principal": "serviceAccount:<EMAIL>"}, {"principal": "serviceAccount:<EMAIL>"}], "roles/monitoring.viewer": [{"principal": "serviceAccount:<EMAIL>"}, {"principal": "serviceAccount:<EMAIL>"}, {"principal": "serviceAccount:<EMAIL>"}], "roles/pubsub.viewer": [{"principal": "serviceAccount:<EMAIL>"}], "roles/servicenetworking.serviceAgent": [{"principal": "serviceAccount:<EMAIL>"}], "roles/storage.admin": [{"principal": "serviceAccount:<EMAIL>"}], "roles/storage.objectAdmin": [{"principal": "group:<EMAIL>"}, {"principal": "serviceAccount:<EMAIL>"}], "roles/storage.objectViewer": [{"principal": "group:<EMAIL>"}]}, "standard_role_bindings_version": 2}