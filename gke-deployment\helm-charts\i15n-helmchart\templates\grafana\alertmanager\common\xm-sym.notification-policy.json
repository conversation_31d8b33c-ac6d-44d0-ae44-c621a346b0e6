{"receiver": "grafana-default-email", "group_by": ["grafana_folder", "alertname"], "routes": [{"receiver": "symphony-webhook", "group_by": ["..."], "object_matchers": [["symphony", "=", "true-nogrouping"]], "group_wait": "0s", "group_interval": "5s", "repeat_interval": "4h"}, {"receiver": "symphony-webhook", "object_matchers": [["symphony", "=", "true"]], "group_wait": "0s", "group_interval": "5s", "repeat_interval": "4h"}, {"receiver": "xmatter-webhook", "group_by": ["..."], "object_matchers": [["xmatter", "=", "true-nogrouping"]], "group_wait": "0s", "group_interval": "5s", "repeat_interval": "4h"}, {"receiver": "xmatter-webhook", "object_matchers": [["xmatter", "=", "true"]], "group_wait": "5s", "group_interval": "10s", "repeat_interval": "4h"}]}