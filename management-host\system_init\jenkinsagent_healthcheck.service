[Unit]
Description=Jenkins Agent Healthcheck and Restart Service
After=network-online.target rsyslog.service google-guest-agent.Service
After=key_management.service

[Service]
User=root
ExecStartPre=-/usr/bin/chmod 755 /opt/script/jenkinsagent_healthcheck.bash
ExecStart=/bin/bash -c "/opt/script/jenkinsagent_healthcheck.bash"
Restart=always
StandardOutput=syslog
StandardError=syslog

[Install]
WantedBy=multi-user.target
