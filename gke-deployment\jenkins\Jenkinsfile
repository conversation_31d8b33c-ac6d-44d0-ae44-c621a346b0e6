def AGENT = 'unity-dev-jenkins-agent'
def charts = []
def undeployCharts = []
def deploymentEnv = ''
def rolloutStatusCallbackUrl = ''
def rolloutStatusId = ''
def namespaces = []
def filesToBeCleanup = []
def testHistoryCallbackUrl = ''
def testComponentList = []
def testReportUrl = BUILD_URL + "Html_20Report/"
def v_gcpKeyBucketAppDep = "hsbc-9087302-unity-dev-key-management"
def updateDeploymentBody = [:]
pipeline {
    agent { label env.AGENT_LABEL ?: AGENT }
    stages {
        stage('Connect GCP') {
            steps {
                script {
                    env.v_project = this.sh(
                            script: """gcloud config list --format 'value(core.project)'""",
                            returnStdout: true
                    ).trim()
                    env.v_clusterName = this.sh(
                            script: """gcloud container clusters list --filter="NAME:gke-t2-vpc3" --region=asia-east2 --format 'value(NAME)'""",
                            returnStdout: true
                    ).trim()
                    env.v_kubeCtlLib = this.sh(
                            script: """gcloud compute addresses list --filter="NAME:gke-kubectl-vpc3" --format 'value(ADDRESS)'""",
                            returnStdout: true
                    ).trim()
                }
            }
        }
        stage('Set Properties') {
            steps {
                script {
                    echo "ON BRANCH: ${env.GIT_BRANCH} "

                    dir('helm-charts/i15n-helmchart/generated/') {
                        if (fileExists('.env')) {
                            def envString = readFile('.env')
                            deploymentEnv = envString.split(',')[0]
                        }

                        if (fileExists('.version')) {
                            version = readFile('.version')
                            currentBuild.displayName = version
                        }

                        if (fileExists('.rolloutStatusCallbackUrl')) {
                            rolloutStatusCallbackUrl = readFile('.rolloutStatusCallbackUrl')
                        }
                    }

                    if ("${chartsList}".isEmpty()) {
                        dir('helm-charts/i15n-helmchart/generated/') {
                            if (fileExists('.chartList')) {
                                def chartsList = readFile('.chartList')
                                chartsList.readLines().each {
                                    String[] split = it.split(',');
                                    for (chart in split) {
                                        charts.add(chart)
                                    }
                                }
                            }
                        }
                    } else if ("${chartsList}".equalsIgnoreCase("ALL")) {
                        dir('helm-charts/i15n-helmchart/generated/') {
                            foundFiles = findFiles()
                        }
                        for (f in foundFiles) {
                            if (f.directory) {
                                charts.add(f.name)
                            }
                        }
                    } else {
                        "${chartsList}".readLines().each {
                            String[] chartsList;
                            chartsList = it.split(',');
                            for (chart in chartsList) {
                                charts.add(chart)
                            }
                        }
                    }

                    if ("${undeployChartsList}".isEmpty()) {
                        dir('helm-charts/i15n-helmchart/generated/') {
                            if (fileExists('.undeployChartList')) {
                                def undeployChartsList = readFile('.undeployChartList')
                                undeployChartsList.readLines().each {
                                    String[] split = it.split(',');
                                    for (chart in split) {
                                        undeployCharts.add(chart)
                                    }
                                }
                            }
                        }
                    } else if ("${undeployChartsList}".equalsIgnoreCase("ALL")) {
                        dir('helm-charts/i15n-helmchart/generated/') {
                            foundFiles = findFiles()
                        }
                        for (f in foundFiles) {
                            if (f.directory) {
                                undeployCharts.add(f.name)
                            }
                        }
                    } else {
                        "${undeployChartsList}".readLines().each {
                            String[] undeployChartsList;
                            undeployChartsList = it.split(',');
                            for (chart in undeployChartsList) {
                                undeployCharts.add(chart)
                            }
                        }
                    }

                    dir('helm-charts/i15n-helmchart/generated/') {
                        if (fileExists('.namespace')) {
                            namespaces.add(readFile('.namespace'))
                        }
                    }

                    dir('tests/generated/') {
                        if (fileExists('.testHistoryCallbackUrl')) {
                            testHistoryCallbackUrl = readFile('.testHistoryCallbackUrl')
                        }
                        if (fileExists('.testComponentList')) {
                            def componentList = readFile('.testComponentList')
                            componentList.readLines().each {
                                testComponentList.add(it)
                            }
                        }
                    }

                    charts = sortCharts(charts)
                    echo "charts: ${charts}."
                    echo "namespaces: ${namespaces}."
                    echo "rolloutStatusCallbackUrl ${rolloutStatusCallbackUrl}"
                    echo "testHistoryCallbackUrl ${testHistoryCallbackUrl}"
                    def jobUserId
                    wrap([$class: 'BuildUser']) {
                        jobUserId = "${BUILD_USER_ID}"
                    }
                    echo "jobUserId ${jobUserId}"
                    jobUserId = jobUserId == "scmChange" ? "SYSTEM" : jobUserId
                    rolloutStatusId = startRollout(deploymentEnv, rolloutStatusCallbackUrl, charts, jobUserId)
                }
            }
        }
        /*
        stage('Remove orphan microservice') {
            steps {
                script {
                    def hasFullChartList = false
                    dir('helm-charts/i15n-helmchart/generated/') {
                        if (fileExists('.fullChartList')) {
                            hasFullChartList = true
                        }
                    }
                    if (hasFullChartList) {
                        this.sh """set +x;
                            gcloud container clusters get-credentials ${env.v_clusterName} --region=asia-east2;
                            export HTTPS_PROXY=${env.v_kubeCtlLib}:3128;
                            chmod +x ./removeOrphan.sh;
                            ./removeOrphan.sh"""
                    }
                }
            }
        }
        */
        stage('Undeploy helm charts') {
            steps {
                script {
                    for (chart in undeployCharts) {
                        for (namespace in namespaces) {
                            echo "Executing undeployment script for: ${chart} in ${namespace}"
                            retry(3) {
                                this.sh """set +x;
                                        gcloud container clusters get-credentials ${env.v_clusterName} --region=asia-east2;
                                        export HTTPS_PROXY=${env.v_kubeCtlLib}:3128;
                                        chmod +x ./undeploy.sh;
                                        ./undeploy.sh ${chart} ${namespace}"""
                            }
                        }
                    }
                }
            }
        }
        stage('Deploy helm charts') {
            steps {
                script {
                    def parallelBatches = charts.collate(10);
                    for (parallelBatch in parallelBatches) {
                        def tasks = [:]
                        for (tmpChart in parallelBatch) {
                            def chart = tmpChart
                            tasks["${chart}"] = {
                                for (namespace in namespaces) {
                                    echo "Downloading custom datasource configuration from gcp bucket"
                                    if (chart == "grafana") {
                                        this.sh """set -x;
                                          set +e;
                                          tree ./helm-charts/i15n-helmchart/generated/grafana
                                          if [[ ! -f "./helm-charts/i15n-helmchart/generated/grafana/datasource/plaintext/datasource.yaml" ]] ; then
                                             mkdir -p ./helm-charts/i15n-helmchart/generated/grafana/datasource/plaintext
                                             if [[ \$(gsutil ls gs://${v_project}-app-runtime-dependencies/secrets/env/gcp/grafana/${namespace}/datasource/plaintext/datasource.yaml 2> /dev/null) ]]; then
                                               gsutil cp gs://${v_project}-app-runtime-dependencies/secrets/env/gcp/grafana/${namespace}/datasource/plaintext/datasource.yaml ./helm-charts/i15n-helmchart/generated/grafana/datasource/plaintext/
                                               rm -f ./helm-charts/i15n-helmchart/templates/grafana/datasource/plaintext/datasource.yaml
                                             elif [[ \$(gsutil ls gs://${v_project}-app-runtime-dependencies/secrets/env/gcp/grafana/default/datasource/plaintext/datasource.yaml 2> /dev/null) ]]; then
                                               gsutil cp gs://${v_project}-app-runtime-dependencies/secrets/env/gcp/grafana/default/datasource/plaintext/datasource.yaml ./helm-charts/i15n-helmchart/generated/grafana/datasource/plaintext/
                                               rm -f ./helm-charts/i15n-helmchart/templates/grafana/datasource/plaintext/datasource.yaml
                                             fi
                                          else
                                            rm -f ./helm-charts/i15n-helmchart/templates/grafana/datasource/plaintext/datasource.yaml
                                          fi
                                          tree ./helm-charts/i15n-helmchart/generated/grafana"""
                                    }
                                    echo "Executing deployment script for: ${chart} in ${namespace}"
                                    retry(3) {
                                        this.sh """set +x;
                                                gcloud container clusters get-credentials ${env.v_clusterName} --region=asia-east2;
                                                export HTTPS_PROXY=${env.v_kubeCtlLib}:3128;
                                                chmod +x ./deploy.sh;
                                                ./deploy.sh ${chart} ${namespace} 3 start"""
                                    }
                                    updateDeploymentRollout(rolloutStatusId, rolloutStatusCallbackUrl, chart, 'In_Progress', 'Rollout in progress', updateDeploymentBody)
                                }
                            }
                        }
                        parallel tasks
                    }
                }
            }
        }
        stage('Wait until rollout complete') {
            options {
                timeout(time: 15, unit: "MINUTES")
            }
            steps {
                script {
                    def parallelBatches = charts.collate(10);
                    for (parallelBatch in parallelBatches) {
                        def tasks = [:]
                        for (tmpChart in parallelBatch) {
                            def chart = tmpChart
                            tasks["${chart}"] = {
                                try {
                                    for (namespace in namespaces) {
                                        echo "Checking rollout status for: ${chart} in ${namespace}"
                                        retry(3) {
                                            this.sh """set +x;
                                                gcloud container clusters get-credentials ${env.v_clusterName} --region=asia-east2;
                                                export HTTPS_PROXY=${env.v_kubeCtlLib}:3128;
                                                kubectl rollout status deployment ${chart} -n ${namespace} """
                                        }
                                        updateDeploymentRollout(rolloutStatusId, rolloutStatusCallbackUrl, chart, 'Success', 'Rollout Successfully', updateDeploymentBody)
                                    }
                                } catch (org.jenkinsci.plugins.workflow.steps.FlowInterruptedException e) {
                                    updateDeploymentRollout(rolloutStatusId, rolloutStatusCallbackUrl, chart, 'Fail', 'Rollout timeout', updateDeploymentBody)
                                    unstable(message: "Timeout reached, please check the status in GKE directly")
                                } catch (Throwable e) {
                                    updateDeploymentRollout(rolloutStatusId, rolloutStatusCallbackUrl, chart, 'Fail', e.getCause(), updateDeploymentBody)
                                    unstable(message: "Caught unexpected Exception: ${e.toString()}")
                                }
                            }
                        }
                        parallel tasks
                    }
                }
            }
        }
        stage('Scale Up Test Components') {
            options {
                timeout(time: 30, unit: "MINUTES")
            }
            steps {
                script {
                    if (testComponentList) {
                        def parallelBatches = testComponentList.findAll{!charts.contains(it)}.collate(10);
                        for (parallelBatch in parallelBatches) {
                            def tasks = [:]
                            for (tmpComponent in parallelBatch) {
                                def component = tmpComponent
                                tasks["${component}"] = {
                                    for (namespace in namespaces) {
                                        try {
                                            echo "Checking rollout status for: ${component} in ${namespace}"
                                            retry(3) {
                                                this.sh """set +x;
                                                gcloud container clusters get-credentials ${env.v_clusterName} --region=asia-east2;
                                                export HTTPS_PROXY=${env.v_kubeCtlLib}:3128;
                                                chmod +x ./scaleUp.sh;
                                                ./scaleUp.sh ${component} ${namespace}"""
                                            }

                                            echo "Check component rollout status: ${component} in ${namespace}"
                                            retry(3) {
                                                this.sh """
                                                    gcloud container clusters get-credentials ${env.v_clusterName} --region=asia-east2;
                                                    export HTTPS_PROXY=${env.v_kubeCtlLib}:3128;
                                                    kubectl rollout status deployment ${component} -n ${namespace}
                                                """
                                            }
                                        } catch (Throwable e) {
                                            echo "Failed to scale up component: ${component}"
                                        }
                                    }
                                }
                            }
                            parallel tasks
                        }
                    }
                }
            }
        }
        stage('Execute Tests') {
            options {
                timeout(time: 30, unit: "MINUTES")
            }
            steps {
                script {
                    if (testHistoryCallbackUrl) {
                        startTestExecution(testHistoryCallbackUrl)

                        def curTest = ''
                        try {
                            setupTestFrameworkRuntime()
                            supplementTestConfigFiles(filesToBeCleanup, v_gcpKeyBucketAppDep)

                            dir('tests/') {
                                for (namespace in namespaces) {
                                    def lookupPod = this.sh(
                                            script: """set +x;
                                             gcloud container clusters get-credentials ${env.v_clusterName} --region=asia-east2;
                                             export HTTPS_PROXY=${env.v_kubeCtlLib}:3128;
                                             kubectl get pods -n ${namespace} -o json | jq '.items[] | select(.metadata.name|test("data-lookup"))| .metadata.name' | head -1 | tr -d '"';
                                         """,
                                            returnStdout: true
                                    ).trim()

                                    def saPod = this.sh(
                                            script: """set +x;
                                             gcloud container clusters get-credentials ${env.v_clusterName} --region=asia-east2;
                                             export HTTPS_PROXY=${env.v_kubeCtlLib}:3128;
                                             kubectl get pods -n ${namespace} -o json | jq '.items[] | select(.metadata.name|test("-psa-"))| .metadata.name' | head -1 | tr -d '"';
                                         """,
                                            returnStdout: true
                                    ).trim()

                                    echo "Executing tests in namespace: ${namespace}, supplement config by data lookup pod: ${lookupPod} and sa pod: ${saPod}"
                                    supplementTestConfig(namespace, lookupPod, saPod, filesToBeCleanup)
                                    executeTest(namespace, lookupPod)

                                }
                            }

                            finishTestExecution(testHistoryCallbackUrl, "COMPLETED", testReportUrl)
                        }  catch (org.jenkinsci.plugins.workflow.steps.FlowInterruptedException e) {
                            finishTestExecution(testHistoryCallbackUrl, "TIMED_OUT", testReportUrl)
                            unstable(message: "Timeout reached, please check the test execution log")
                        } catch (Throwable e) {
                            finishTestExecution(testHistoryCallbackUrl, "ERROR", testReportUrl)
                            unstable(message: "Caught unexpected Exception: ${e.toString()}")
                        }
                    } else {
                        echo "No test required, skipping..."
                    }
                }
            }
        }
    }
    post {
        success {
            script {
                finishRollout(rolloutStatusId, rolloutStatusCallbackUrl, 'Success', 'Rollout successfully', updateDeploymentBody)
            }
        }
        unsuccessful {
            finishRollout(rolloutStatusId, rolloutStatusCallbackUrl, 'Fail', 'Rollout failed, please check Jenkins job url for more details.', updateDeploymentBody)
        }
        always {
            script {
                if (testHistoryCallbackUrl) {
                    echo "Publish test report"
                    dir('tests/test-framework-runtime/') {
                        this.sh "mvn -Dmaven.test.skip=true site"
                        publishHTML target: [
                                allowMissing: false,
                                alwaysLinkToLastBuild: false,
                                keepAll: true,
                                reportDir: 'target/site',
                                reportFiles: 'surefire-report.html',
                                reportName: 'Test Report'
                        ]
                    }

                    dir('tests/') {
                        cleanupTestFiles(filesToBeCleanup)
                    }
                }
            }
        }
    }
}

def startRollout(env, rolloutStatusCallbackUrl, charts, jobUserId) {
    def deploymentBody = "["
    charts.eachWithIndex { item, index ->
        deploymentBody = deploymentBody + """
        {
            "name": "${item}",
            "status": "Not_Started",
            "statusDescription": "Deployment not started"
        }
        """
        if (index != (charts.size() - 1)) {
            deploymentBody = deploymentBody + ','
        }
    }

    deploymentBody = deploymentBody + ']'


    def requestBodyPayload = """
        {
            "jobId":"${BUILD_ID}",
            "jobUrl": "${BUILD_URL}",
            "overallProgress": 0,
            "overallStatus": "In_Progress",
            "overallStatusDescription": "Rollout in progress",
            "envName": "${env}",
            "deployments" : ${deploymentBody},
            "createdBy": "${jobUserId}"
        }
    """
    println('requestPayload: ' + requestBodyPayload)
    try {
        def response
        retry(5) {
            response = httpRequest httpMode: 'PUT', contentType: 'APPLICATION_JSON', requestBody: requestBodyPayload, ignoreSslErrors: true, url: rolloutStatusCallbackUrl, timeout: 5
            println('Status: ' + response.status)
            println('Content: ' + response.content)
            def responseObj = readJSON text: response.content
            return responseObj['id']
        }
    } catch (Exception e) {
        echo "Caught unexpected Exception when updating rollout status: ${e.toString()}"
        return ''
    }
}

def finishRollout(rolloutStatusId, rolloutStatusCallbackUrl, status, statusDescription, updateDeploymentBody) {
    def updateDeploymentBodyList = updateDeploymentBody.collect { it -> it.value }
    def requestBodyPayload = """
        {
            "id": "${rolloutStatusId}",
            "overallStatus" : "${status}",
            "overallStatusDescription": "${statusDescription}",
            "deployments" : ${updateDeploymentBodyList}
        }
    """
    println('requestPayload: ' + requestBodyPayload)
    try {
        def response
        retry(5) {
            response = httpRequest httpMode: 'PATCH', contentType: 'APPLICATION_JSON', requestBody: requestBodyPayload, ignoreSslErrors: true, url: rolloutStatusCallbackUrl, timeout: 5
            println('Status: ' + response.status)
            println('Content: ' + response.content)
        }
    } catch (Exception e) {
        echo "Caught unexpected Exception when updating rollout status: ${e.toString()}"
        return ''
    }
}


def updateDeploymentRollout(rolloutStatusId, rolloutStatusCallbackUrl, deployment, status, statusDescription, updateDeploymentBody) {
    updateDeploymentBody["${deployment}"] = """
    {
            "name": "${deployment}",
            "status": "${status}",
            "statusDescription": "${statusDescription}"
    }
    """
    def updateDeploymentBodyList = updateDeploymentBody.collect { it -> it.value }
    def requestBodyPayload = """
        {
            "id": "${rolloutStatusId}",
            "deployments" : ${updateDeploymentBodyList}
        }
    """
    println('requestPayload: ' + requestBodyPayload)
    try {
        def response
        retry(5) {
            response = httpRequest httpMode: 'PATCH', contentType: 'APPLICATION_JSON', requestBody: requestBodyPayload, ignoreSslErrors: true, url: rolloutStatusCallbackUrl, timeout: 5
            println('Status: ' + response.status)
            println('Content: ' + response.content)
        }
    } catch (Exception e) {
        echo "Caught unexpected Exception when updating rollout status: ${e.toString()}"
        return ''
    }
}

def setupTestFrameworkRuntime() {
    dir('tests/') {

        if (fileExists('test-framework-runtime')) {
            this.sh '''rm -rf test-framework-runtime'''
        }

        this.sh """
            mkdir -p test-framework-runtime/src/test/resources;
            cp generated/logback-test.xml test-framework-runtime/src/test/resources/;
            cp generated/pom.xml test-framework-runtime/;
            cp -r generated/test-cases test-framework-runtime/src/test/resources/;
        """
    }
}

def supplementTestConfigFiles(filesToBeCleanup, v_gcpKeyBucketAppDep) {
    echo "Supplementing GCP credential file"
    this.sh """
        mkdir -p /tmp/gcp/;
        gsutil -m cp -r gs://${v_gcpKeyBucketAppDep}/gce-stage3-image-builder/gce-stage3-image-builder.json /tmp/gcp/;
    """
    filesToBeCleanup.add('/tmp/gcp/gce-stage3-image-builder.json')
}

def supplementTestConfig(namespace, lookupPod, saPod, filesToBeCleanup) {
    try {
        this.sh """set +x;
                gcloud container clusters get-credentials ${env.v_clusterName} --region=asia-east2;
                export HTTPS_PROXY=${env.v_kubeCtlLib}:3128;
                chmod +x ../supplementTestConfig.sh;
                ../supplementTestConfig.sh ${namespace} ${lookupPod} ${saPod};"""

        dir("generated/") {
            if (fileExists('.kafkaConfigFiles')) {
                def fileList = readFile('.kafkaConfigFiles')
                fileList.readLines().each { file ->
                    def (source, destination) = file.split(',')
                    echo "Supplementing Kafka file: ${source} from pod [${saPod}] to: ${destination} in local"
                    this.sh """mkdir -p /tmp/kafka/;
                    cd /tmp/kafka/;
                    set +x;
                    gcloud container clusters get-credentials ${env.v_clusterName} --region=asia-east2;
                    export HTTPS_PROXY=${env.v_kubeCtlLib}:3128;
                    kubectl exec ${saPod} -n ${namespace} -- tar cfh - ${source} | tar xfh -"""
                    filesToBeCleanup.add(destination)
                }
            }
            if (fileExists('.mqConfigFiles')) {
                def fileList = readFile('.mqConfigFiles')
                fileList.readLines().each { file ->
                    def (source, destination) = file.split(',')
                    echo "Supplementing MQ file: ${source} from pod [${saPod}] to: ${destination} in local"
                    this.sh """mkdir -p /tmp/mq/;
                    cd /tmp/mq/;
                    set +x;
                    gcloud container clusters get-credentials ${env.v_clusterName} --region=asia-east2;
                    export HTTPS_PROXY=${env.v_kubeCtlLib}:3128;
                    kubectl exec ${saPod} -n ${namespace} -- tar cfh - ${source} | tar xfh -"""
                    filesToBeCleanup.add(destination)
                }
            }
            if (fileExists('.sftpConfigFiles')) {
                def fileList = readFile('.sftpConfigFiles')
                fileList.readLines().each { file ->
                    def (source, destination) = file.split(',')
                    echo "Supplementing SFTP file: ${source} from pod [${saPod}] to: ${destination} in local"
                    this.sh """mkdir -p /tmp/sftp/;
                    cd /tmp/sftp/;
                    set +x;
                    gcloud container clusters get-credentials ${env.v_clusterName} --region=asia-east2;
                    export HTTPS_PROXY=${env.v_kubeCtlLib}:3128;
                    kubectl exec ${saPod} -n ${namespace} -- tar cfh - ${source} | tar xfh -"""
                    filesToBeCleanup.add(destination)
                }
                def knownHostsFile = "/tmp/sftp/known_hosts"
                this.sh """find ./ -name test.json -exec grep -w 'host' {} \\;| uniq > .hosts"""
                def hostList = readFile('.hosts')
                hostList.readLines().each {
                    String[] split = it.split(': "');
                    def host = split[1].split('"')[0]
                    this.sh """ssh-keyscan -t rsa ${host} >> ${knownHostsFile};"""
                }
                this.sh """rm .hosts"""

                filesToBeCleanup.add(knownHostsFile)
            }
        }

    } catch (Exception e) {
        echo "Caught unexpected Exception when supplementing test config in test: ${namespace} ${lookupPod} ${saPod}"
        echo "${e.toString()}"
        return ''
    }
}

def executeTest(namespace, pod) {
    try {
        this.sh """set +x;
               gcloud container clusters get-credentials ${env.v_clusterName} --region=asia-east2;
               export HTTPS_PROXY=${env.v_kubeCtlLib}:3128;
               kubectl port-forward ${pod} -n ${namespace} 3307:3307 &"""

        this.sh """chmod +x ../executeTest.sh;
                cat /home/<USER>/.m2/settings.xml| sed  's|https://efx-nexus.systems.uk.hsbc:8082/nexus/content/groups/public/|https://gbmt-nexus.prd.fx.gbm.cloud.uk.hsbc/repository/maven-public|g'  > /home/<USER>/.m2/settings-gbmt.xml;
                mkdir -p test-framework-runtime/.mvn;
                echo "-s /home/<USER>/.m2/settings-gbmt.xml" > test-framework-runtime/.mvn/maven.config;
                export JAVA_HOME=/opt/java/zulu17;
                export MAVEN_OPTS="-Xmx1G -Djavax.net.ssl.trustStore=/etc/pki/java/cacerts"
                export GOOGLE_PROXY_HOST=googleapis-dev.gcp.cloud.hk.hsbc;
                export GOOGLE_PROXY_PORT=3128;
                export GOOGLE_APPLICATION_CREDENTIALS=/tmp/gcp/gce-stage3-image-builder.json;
                ../executeTest.sh;"""
    } catch (Exception e) {
        echo "Caught unexpected Exception when executing test: ${namespace} ${pod}"
        echo "${e.toString()}"
        return ''
    }
}


def cleanupTestFiles(filesToBeCleanup) {
    this.sh """rm -rf test-framework-runtime"""
    filesToBeCleanup.each { item ->
        try {
            echo "Removing ${item}"
            this.sh "rm ${item}"
        } catch (Exception e) {
            echo "Caught unexpected Exception when cleaning up test files"
            echo "${e.toString()}"
            return ''
        }
    }
}

def startTestExecution(testHistoryCallbackUrl) {
    def requestBodyPayload = """
        {
            "status" : "IN_PROGRESS"
        }
    """
    println('Start test execution, update test history requestPayload: ' + requestBodyPayload)
    try {
        def response = httpRequest httpMode: 'PUT', contentType: 'APPLICATION_JSON', requestBody: requestBodyPayload, ignoreSslErrors: true, url: testHistoryCallbackUrl, timeout: 60
        println('Status: ' + response.status)
        println('Content: ' + response.content)
    } catch (Exception e) {
        echo "Caught unexpected Exception when updating test history: ${e.toString()}"
        return ''
    }
}

def finishTestExecution(testHistoryCallbackUrl, status, result) {
    def requestBodyPayload = """
        {
            "status": "${status}",
            "result": "${result}"
        }
    """
    println('Test execution finished, update test history requestPayload: ' + requestBodyPayload)
    try {
        def response = httpRequest httpMode: 'PUT', contentType: 'APPLICATION_JSON', requestBody: requestBodyPayload, ignoreSslErrors: true, url: testHistoryCallbackUrl, timeout: 5
        println('Status: ' + response.status)
        println('Content: ' + response.content)
    } catch (Exception e) {
        echo "Caught unexpected Exception when updating test history: ${e.toString()}"
        return ''
    }
}

@NonCPS
def sortCharts(List<String> charts) {
    // prioritize data lookup and cache
    return charts.toSorted{a, b -> sortChartComparator(a, b)}
}

@NonCPS
def sortChartComparator(String a , String b){
    if ((a.equals("data-lookup") || a.equals("cache")) && (b.equals("data-lookup") || b.equals("cache"))) {
        return 0
    }

    if (a.equals("data-lookup") || a.equals("cache")) {
        return -1
    }
    if (b.equals("data-lookup") || b.equals("cache")) {
        return 1
    }
    return a.compareTo(b)
}
