import com.poc.hss.fasttrack.service.LookupService
import com.poc.hss.fasttrack.transform.AbstractTransformer
import com.poc.hss.fasttrack.transform.model.TransformerBatchInputContext
import com.poc.hss.fasttrack.transform.model.TransformerRecord
import io.vertx.core.json.JsonObject
import org.slf4j.Logger
import org.slf4j.LoggerFactory

import java.util.stream.Collectors

class UdmGcsHoldingBlend extends AbstractTransformer {

    private static final Logger logger = LoggerFactory.getLogger(UdmGcsHoldingBlend.class);

    List<TransformerRecord> batchTransform(TransformerBatchInputContext context) {
        List<TransformerRecord> finalOutputData = new ArrayList<>();
        LookupService lookupService = context.getLookupService();
        List<TransformerRecord> records = context.getRecords();
        Map<String, List<TransformerRecord>> records_by_source =
                records.stream().collect(Collectors.groupingBy(rec -> rec.source))


        List<TransformerRecord> holdingRecords = records_by_source.get(UdmGcsHoldingConstant.GCS_HOLDING);
        if (UdmGcsHoldingHelper.isListNotNullAndEmpty.test(holdingRecords)) {
            logger.info("================UDM GCS HOLDING BLEND=================")
            finalOutputData = processHoldingDataEvent(holdingRecords, lookupService);
        }
        List<TransformerRecord> outputFieldList = new ArrayList<>()
        if (null != finalOutputData && finalOutputData.size() > 0) {
            outputFieldList = mapGcsHoldingFields(finalOutputData)
        }
        return outputFieldList;
    }


    static List<TransformerRecord> processHoldingDataEvent(List<TransformerRecord> holdingRecordList, LookupService lookupService) {

        String positionListToProcess = holdingRecordList.stream()
                .map(rec -> rec.getId())
                .distinct()
                .collect(Collectors.joining(","));
        logger.info("Start Processing Position Data for records :: " + positionListToProcess)

        //1. query and blend Account data from GCS_ACCOUNT using account_id
        UdmGcsAccount.queryAndBlendAccountData.accept(holdingRecordList, lookupService)

        //2. query and blend location data from GCS_LOCATION using location_id
        UdmGcsLocation.queryAndBlendLocationDataForHolding.accept(holdingRecordList, lookupService)

        //3. query and blend Instrument data from GCS_INSTRUMENT using instrument_id
        UdmGcsInstrument.queryAndBendInstrumentData.accept(holdingRecordList, lookupService)

        return holdingRecordList
    }

    static List<TransformerRecord> mapGcsHoldingFields(List<TransformerRecord> targetData) {
        return targetData.stream()
                .map(record -> {
                    JsonObject msg = record.getData()
                    String _id = String.format("%s-%s-%s-%s-%s", UdmGcsHoldingHelper.deriveSafekeepingAccountIdentification(msg),UdmGcsHoldingHelper.deriveDepositoryCodeOrDescriptionOrCountryTerritoryCode(
                            msg.getString("place_of_safekeeping_country_code"),
                            msg.getString("location_id")), UdmGcsHoldingHelper.deriveLocalCustodianCodeOrDescriptionOrCountryTerritoryCode(
                            msg.getString("place_of_safekeeping_country_code"),
                            msg.getString("location_id")), UdmGcsHoldingHelper.formatOutputFieldNullToEmpty(msg.getString("registration")),msg.getString("sec_id"))

                    logger.info("Upserting records for id : " + record.getId())
                    return TransformerRecord.from(record).id(_id)
                            .data(new JsonObject()
                                    .put("Site", msg.getString("site"))
                                    .put("SafekeepingAccountIdentification", UdmGcsHoldingHelper.deriveSafekeepingAccountIdentification(msg))
                                    .put("SafekeepingAccountName", UdmGcsHoldingHelper.formatOutputFieldNullToEmpty(msg.getString("account_name")))
                                    .put("AssociatedAccountIdentification", UdmGcsHoldingHelper.deriveAssociatedAccountIdentification(msg))
                                    .put("AssociatedSubAccountIdentification", UdmGcsHoldingHelper.deriveAssociatedSubAccountIdentification(msg))
                                    .put("AccountBaseCurrency", UdmGcsHoldingHelper.deriveAccountBaseCurrency(msg))
                            // Depository Information
                                    .put("DepositoryCode", UdmGcsHoldingHelper.deriveDepositoryCodeOrDescriptionOrCountryTerritoryCode(
                                            msg.getString("place_of_safekeeping_country_code"),
                                            msg.getString("location_id")))
                                    .put("DepositoryDescription", UdmGcsHoldingHelper.deriveDepositoryCodeOrDescriptionOrCountryTerritoryCode(
                                            msg.getString("place_of_safekeeping_country_code"),
                                            msg.getString("place_of_safekeeping_name")))
                                    .put("DepositoryCountryTerritoryCode", UdmGcsHoldingHelper.deriveDepositoryCodeOrDescriptionOrCountryTerritoryCode(
                                            msg.getString("place_of_safekeeping_country_code"),"GB"))
                            // Local Custodian
                                    .put("LocalCustodianCode", UdmGcsHoldingHelper.deriveLocalCustodianCodeOrDescriptionOrCountryTerritoryCode(
                                            msg.getString("place_of_safekeeping_country_code"),
                                            msg.getString("location_id")))
                                    .put("LocalCustodianDescription",UdmGcsHoldingHelper.deriveLocalCustodianCodeOrDescriptionOrCountryTerritoryCode(
                                            msg.getString("place_of_safekeeping_country_code"),
                                            msg.getString("place_of_safekeeping_name")))
                                    .put("LocalCustodianCountryTerritoryCode", UdmGcsHoldingHelper.deriveLocalCustodianCodeOrDescriptionOrCountryTerritoryCode(
                                            msg.getString("place_of_safekeeping_country_code"),
                                            msg.getString("place_of_safekeeping_country_code")))
                                    .put("RegistrationCode", UdmGcsHoldingHelper.formatOutputFieldNullToEmpty(msg.getString("registration")))
                                    .put("RegistrationDescription", UdmGcsHoldingHelper.formatOutputFieldNullToEmpty(msg.getString("registration_description")))
                                    .put("FxRateToAccountBaseCurrency", UdmGcsHoldingHelper.formatOutputFieldNullToEmpty(msg.getString("fx_rate")))
                                    .put("BusinessDate", UdmGcsHoldingHelper.formatOutputFieldNullToEmpty(msg.getString("business_date")))
                                    .put("FinancialInstrumentIdentificationPrimaryIdentifier", UdmGcsHoldingHelper.formatOutputFieldNullToEmpty(msg.getString("sec_id")))
                                    .put("FinancialInstrumentIdentificationIsin", UdmGcsHoldingHelper.formatOutputFieldNullToEmpty(msg.getString("sec_id")))
                                    .put("FinancialInstrumentIdentificationSedol", msg.getString("sedol"))
                                    .put("FinancialInstrumentIdentificationCusip", msg.getString("cusip"))
                                    .put("FinancialInstrumentIdentificationLocalSecurityCode", "")
                                    .put("FinancialInstrumentIdentificationDescription", UdmGcsHoldingHelper.formatOutputFieldNullToEmpty(msg.getString("instrument_description")))
                                    .put("FinancialInstrumentIdentificationProductType", UdmGcsHoldingHelper.formatOutputFieldNullToEmpty(msg.getString("instrument_type")))
                                    .put("FinancialInstrumentMaturityDate", (msg.getString("maturity_date")==UdmGcsHoldingConstant.ZERO_DATE_VALUE)?"":msg.getString("maturity_date"))
                                    .put("FinancialInstrumentIndicativePriceCurrency", UdmGcsHoldingHelper.formatOutputFieldNullToEmpty(msg.getString("security_price_currency")))
                                    .put("FinancialInstrumentIndicativePrice", UdmGcsHoldingHelper.formatOutputFieldNullToEmpty(msg.getString("security_price")))
                                    .put("RegistrarIdentification", UdmGcsHoldingHelper.formatOutputFieldNullToEmpty(msg.getString("registrar_id")))
                                    .put("RegistrarDescription", UdmGcsHoldingHelper.formatOutputField(msg.getString("registrar_desc")))
                                    .put("AvailableBalanceQuantity", UdmGcsHoldingHelper.formatOutputField(msg.getString("registered")))
                                    .put("OnLoanBalanceQuantity", UdmGcsHoldingHelper.formatOutputFieldNullToEmpty(msg.getString("on_loan")))
                                    .put("SettledBalanceQuantity", UdmGcsHoldingHelper.formatOutputFieldNullToEmpty(msg.getString("settled_balance_quantity")))
                                    .put("SettledBalanceMarketValue", UdmGcsHoldingHelper.formatOutputFieldNullToEmpty(msg.getString("settled_market_value_in_security_currency")))
                                    .put("AccountBaseCurrencySettledBalanceMarketValue", UdmGcsHoldingHelper.formatOutputField(msg.getString("settled_market_value_in_report_currency")))
                                    .put("TradedBalanceQuantity", UdmGcsHoldingHelper.formatOutputFieldNullToEmpty(msg.getString("traded_balance_quantity")))
                                    .put("TradedBalanceMarketValue", UdmGcsHoldingHelper.formatOutputFieldNullToEmpty(msg.getString("traded_market_value_in_security_currency")))
                                    .put("AccountBaseCurrencyTradedBalanceMarketValue", UdmGcsHoldingHelper.formatOutputFieldNullToEmpty(msg.getString("traded_market_value_in_report_currency")))
                            ).build()
                }).collect(Collectors.toList())
    }
}

