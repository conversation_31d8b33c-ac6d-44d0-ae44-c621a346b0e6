#!/usr/bin/env bash
set -euo pipefail


#sudo chmod 644 /var/run/docker.sock
sudo mv $BUILD_PATH/build_scripts/docker.service /etc/systemd/system/
sudo chown root:root /etc/systemd/system/docker.service
sudo chmod 644 /etc/systemd/system/docker.service
echo "[CPE] moved docker.service to /etc/systemd/system/docker.service, changed group to root and systemlink created"

sudo mv $BUILD_PATH/build_scripts/docker.socket /etc/systemd/system/
sudo chown root:root /etc/systemd/system/docker.socket
sudo chmod 644 /etc/systemd/system/docker.socket
echo "[CPE] moved docker.socket to /etc/systemd/system/docker.socket, changed group to root and systemlink created"

#copying jenkins.service file

sudo cp $BUILD_PATH/build_scripts/jenkinsagent.service /usr/lib/systemd/system/

#if [ "$BUILDSTAGE3" = false ];then
#    sudo cp $BUILD_PATH/build_scripts/jenkinsagent.service /usr/lib/systemd/system/
#fi

#if [ "$BUILDSTAGE3" = true ];then
#    sudo cp $BUILD_PATH/build_scripts/jenkinsagent_prod.service /usr/lib/systemd/system/jenkinsagent.service
#fi

sudo chown root:root /usr/lib/systemd/system/jenkinsagent.service
sudo chmod 644 /usr/lib/systemd/system/jenkinsagent.service
echo "[MGMT_HOST] moved docker.service to /usr/lib/systemd/system/jenkinsagent.service, changed group to root and systemlink created"

sudo systemctl daemon-reload
sudo systemctl enable docker.socket
sudo systemctl start docker.socket

sleep 40s

sudo systemctl enable docker
sudo systemctl start docker

sleep 20s

sudo systemctl enable jenkinsagent.service
echo "[MGMT_HOST] enabled docker.service and jenkinsagent.service"