#!/bin/bash

#gcp_project_id=hsbc-********-unyeu1-dev
#namespace=support-pod
#ksa_name=support-pod-sa
#gke_workload_identity_account=runtime-gke@${gcp_project_id}.iam.gserviceaccount.com
#gcloud iam service-accounts add-iam-policy-binding ${gke_workload_identity_account} \
#    --role roles/iam.workloadIdentityUser \
#   --member "serviceAccount:${gcp_project_id}.svc.id.goog[${namespace}/${ksa_name}]"
# 
# --project=hsbc-********-unyin1-dev --region=asia-south1 --clustername=devcls --image-project=hsbc-9087302-unity-dev --kubeproxy=ingress.devcls.hsbc-********-unyin1-dev.dev.gcp.cloud.in.hsbc:3128 --filter="faasia" --service-account-key-file=/home/<USER>/ServiceAccountKeys/<EMAIL>

function print_usage {
  echo "Usage:$(basename $0)"
  echo ' --gcp-project               '
  echo "Example:$(basename $0) --gcp-project=hsbc-9087302-unity-dev"
}

function options() {
while [ "$1" != "" ];
do
  _param_=`printf "%s\n" $1 | awk -F= '{print $1}'`
  _value_=`printf "%s\n" $1 | sed 's/^[^=]*=//g'`
  if [[ $_value_ == $_param_ && $_value_ != "--psc" ]]; then
    shift
    _value_=$1
  fi
  case ${_param_} in
  --project)
    project="${_value_}"
    ;;
  --image-project)
    image_project="${_value_}"
    ;;   
  --region)
    region="${_value_}"
    ;;
  --vpc)
    vpc="${_value_}"
    ;;
  --filter)
    filter="${_value_}"
    ;;
  --clustername)
    clustername="${_value_}"
    ;;  
  --kubeproxy)
    kubeproxy="${_value_}"
    ;;
  --dbuser)
    dbuser="${_value_}"
    ;;
  --dbname)
    dbname="${_value_}"
    ;;
  --service-account-key-file)
    service_account_key="${_value_}"
    ;;
  --psc)
    psc=1
    ;;    
  --help)
    print_usage
    exit
    ;;
  esac
  shift;
done
}

function gcloud_authenticate(){
   echo ${gcp_project}:${project}
  if [[  "${gcp_project}" != "${project}" ]]; then 
    export CLOUDSDK_CONFIG=$HOME/.cloudsdk/${project}
    project_env=$(echo ${project}| rev | cut -f1 -d- | rev)
    gcloud config set project ${project}
    gcloud config set proxy/address googleapis-${project_env}.gcp.cloud.hk.hsbc
    gcloud config set proxy/port 3128
    gcloud config set proxy/type http_no_tunnel
    gcloud auth activate-service-account --key-file=${service_account_key}
  fi 
  gcloud config list
}

function setup_namespace() {
  kubectl apply -f ns-support-pod.yaml
  kubectl apply -f support-pod-sa.yaml
  gcloud iam service-accounts add-iam-policy-binding runtime-gke@${project}.iam.gserviceaccount.com \
    --role roles/iam.workloadIdentityUser \
    --member "serviceAccount:${project}.svc.id.goog[support-pod/support-pod-sa]"
}

function setup_kubeconfig(){
  set -x 
  export KUBECONFIG=${root}/kubeconfig.conf
  if [[ ! -z "${clustername}" ]]; then 
    gcloud container clusters get-credentials ${clustername} --region=${region} --project=${project}
  else 
    gcloud container clusters list --format='value(name,zone)' | grep ${vpc} | while read cluster region ; do 
      gcloud container clusters get-credentials ${cluster} --region=${region}
    done
  fi 
  if [[ -z "${kubeproxy}" ]] ; then 
    export KUBE_PROXY=$(gcloud compute addresses list --filter="NAME:gke-kubectl-${vpc}" --format='value(ADDRESS)'):3128
  else
    export KUBE_PROXY=${kubeproxy}
  fi
  export HTTPS_PROXY=${KUBE_PROXY}
  set +x
}

function generate_instances_list(){
echo generating instance list
instance_list=$(gcloud sql instances list --filter="STATUS=RUNNABLE" --format 'value(NAME)'|egrep "${filter}")
instance_list=(`echo $instance_list | tr '\r' ' '`)
instance_list=( "${instance_list[@]/#/${project}:${region}:}" )
instance_list_psc=${instance_list}
port=1200
for ((c=0; c<=${#instance_list[@]}-1; c++ ))
do
    port=$(($port+1))	
    instance_list[c]="${instance_list[c]/%/=tcp:127.0.0.1:${port}}"
done
}

function generate_deployment_yaml(){
#prepare config map 
cat > pg-grant-privileges.yaml <<-'EOT'
---
apiVersion: v1 
data:
  pgGrantPriv: | 
    #!/bin/bash   
    instance_list=(`cat /hss/apps/secrets/instance_secrets | tr ',' ' '`)
    for ((c=0; c<=${#instance_list[@]}-1; c++ ))
    do
    echo instance=$(echo ${instance_list[c]}|cut -f3- -d:)
    port=$(echo ${instance_list[c]}|cut -f5 -d:)
    export PGPASSWORD=$(echo ${instance_list[c]}|cut -f6 -d:)
    
    export retrycounter=0  
    export numberofretry=60
    while [[ $(timeout 2 bash -c "</dev/tcp/127.0.0.1/${port}" 2> /dev/null ; echo $?) -ne 0 && ${retrycounter} -lt ${numberofretry} ]]; do 
      sleep 1;
      echo waiting for cloudsqlproxy for connect on port: ${port}
      retrycounter=$((retrycounter+1))
    done; 

    cd /tmp
    psql -h 127.0.0.1 -p ${port} -U postgres <<-'EOF'
    do $$
    declare
      v_grantee varchar := 'runtime-gke@%';
      r record;
    begin
    FOR r IN select 'GRANT cloudsqlsuperuser to "' || usename ||'"' as v_statement from pg_user where usename like v_grantee LOOP 
      BEGIN
        execute r.v_statement;
      EXCEPTION 
        WHEN others THEN RAISE NOTICE '%, skipping', SQLERRM USING ERRCODE = SQLSTATE;
      END;   
    END LOOP;
    end $$;
    DO $$
    DECLARE 
      r record;
    BEGIN
    FOR r IN SELECT unnest(ARRAY['uny_role_owner','uny_role_user_ro','uny_role_user_ff','uny_role_user_app']) as role LOOP 
      BEGIN 
        execute 'CREATE ROLE ' ||r.role;
      EXCEPTION 
        WHEN duplicate_object THEN RAISE NOTICE '%, skipping', SQLERRM USING ERRCODE = SQLSTATE;
      END;   
    END LOOP;
    END
    $$;  
    --add user to uny_role_owner group 
    DO $$
    declare
      v_grantee varchar := 'runtime-gke@%';
      r record;
    begin
    FOR r IN select 'GRANT uny_role_owner to "' || usename ||'"' as v_statement
             from pg_user where usename like v_grantee              
    LOOP 
      BEGIN
        execute r.v_statement;
      EXCEPTION 
        WHEN others THEN RAISE NOTICE '%, skipping', SQLERRM USING ERRCODE = SQLSTATE;
      END;   
    END LOOP;
    end 
    $$;

    DO $$
    declare
      v_grantee varchar := 'runtime-gke@%';
      r record;
    begin
    FOR r IN select 'ALTER ROLE "' || usename ||'" IN DATABASE pipeline_data RESET ROLE' as v_statement	
             from pg_user where usename like v_grantee      
             and exists (select 1 from pg_database where datname = 'pipeline_data')        
    LOOP 
      BEGIN
        RAISE NOTICE '%', r.v_statement;
        execute r.v_statement;
      EXCEPTION 
        WHEN others THEN RAISE NOTICE '%, skipping', SQLERRM USING ERRCODE = SQLSTATE;
      END;   
    END LOOP;
    end 
    $$;
    
    DO $$
    DECLARE 
      r record;
    BEGIN  
    FOR r IN select usename from pg_user where usename like 'denodo-%-cloudsql-ro@hsbc-%-unity-%.iam'
    LOOP 
      BEGIN 
        execute 'grant "uny_role_user_ro" to "' || r.usename || '"';
      EXCEPTION 
        WHEN duplicate_object THEN RAISE NOTICE '%, skipping', SQLERRM USING ERRCODE = SQLSTATE;
      END; 
    END LOOP;
    END
    $$;
    
    --change database owner to uny_role_owner
    DO $$
    DECLARE 
      r record;
    BEGIN
    FOR r IN select count(1) as cnt
               from pg_database a, pg_roles b 
               where a.datdba = b.oid
                and a.datname = 'pipeline_data'
                and b.rolname = 'cloudsqlsuperuser'
     LOOP 
      BEGIN 
        IF r.cnt > 0 THEN
          execute 'grant uny_role_owner to postgres';
    	    execute 'ALTER DATABASE pipeline_data OWNER TO uny_role_owner';
    	    execute 'revoke uny_role_owner from postgres';
        END IF;
      END;
     END LOOP; 
    END
    $$;        
    \q
    EOF
EOT
cat >> pg-grant-privileges.yaml <<-EOT    
    export PGPASSWORD=""
    export PGUSER=${dbuser}
    export PGDB=${dbname}    
EOT
cat >> pg-grant-privileges.yaml <<-'EOT'    
    cd /tmp
    dbexists=$(psql -h 127.0.0.1 -p ${port} -U ${PGUSER} -lqt | grep -w "${PGDB}" | wc -l)
    echo ${PGDB}:${dbexists}
    if [[ ${dbexists} -eq 1 ]] ; then 
    
    psql -h 127.0.0.1 -p ${port} -U ${PGUSER} -d ${PGDB} <<-'EOF'
    select current_user, current_role;
    --migrate legacy database with object ownership under cloudsqlsuperuser
    --change database schema to uny_role_owner
    DO $$
    DECLARE 
      r record;
    BEGIN
    FOR r IN select 'ALTER SCHEMA "'||a.nspname||'" OWNER TO CURRENT_USER' v_statement from pg_namespace a
             where  a.nspowner in 
             (
               select b.oid from pg_roles b where b.rolname = 'cloudsqlsuperuser'
             )
            and a.nspname ~ E'^[[:xdigit:]]{8}_([[:xdigit:]]{4})_([[:xdigit:]]{4}).*'
     LOOP 
      BEGIN 
        RAISE NOTICE '%', r.v_statement;
        execute r.v_statement;
      EXCEPTION 
        WHEN others THEN RAISE NOTICE '%, skipping', SQLERRM USING ERRCODE = SQLSTATE;
      END; 
     END LOOP; 
    END
    $$;        

    DO $$
    declare
      v_statement varchar;
    begin
    FOR v_statement IN
    select
        'ALTER '||
        case cls.relkind
            when 'r' then 'TABLE'
            when 'm' then 'MATERIALIZED_VIEW'
            when 'i' then 'INDEX'
            when 'S' then 'SEQUENCE'
            when 'v' then 'VIEW'
            when 'c' then 'TYPE'
            when 'p' then 'TABLE'
            else cls.relkind::text
        end ||
    	' "'||nsp.nspname||'"."'||cls.relname|| '" OWNER TO CURRENT_USER' as sql
    from pg_class cls
    join pg_roles rol
    	on rol.oid = cls.relowner
    join pg_namespace nsp
    	on nsp.oid = cls.relnamespace
    where nsp.nspname not in ('information_schema', 'pg_catalog')
        and nsp.nspname not like 'pg_toast%'
        and rol.rolname = 'cloudsqlsuperuser'
    	and cls.relkind in ('v','r','p')
    order by nsp.nspname, cls.relname
    LOOP
     RAISE NOTICE '%', v_statement;
     execute v_statement;
    END LOOP;
    END 
    $$;

    DO $$
    DECLARE 
      r record;
    BEGIN
    FOR r IN select 'ALTER SCHEMA "'||a.nspname||'" OWNER TO "uny_role_owner"' v_statement from pg_namespace a
             where  a.nspowner in 
             (
               select b.oid from pg_roles b where b.rolname = CURRENT_USER
             )
            and a.nspname ~ E'^[[:xdigit:]]{8}_([[:xdigit:]]{4})_([[:xdigit:]]{4}).*'
     LOOP 
      BEGIN 
        RAISE NOTICE '%', r.v_statement;
        execute r.v_statement;
      EXCEPTION 
        WHEN others THEN RAISE NOTICE '%, skipping', SQLERRM USING ERRCODE = SQLSTATE;
      END; 
     END LOOP; 
    END
    $$;    
    
    --reassign ownership to uny_role_owner
    DO $$
    declare
      v_grantee varchar := 'runtime-gke@%';
      r record;
    begin
    FOR r IN select 'REASSIGN OWNED BY "'|| usename ||'" to uny_role_owner' as v_statement, usename as v_usename from pg_user where usename like v_grantee  LOOP 
      BEGIN
        RAISE NOTICE '%', r.v_statement;
        execute r.v_statement;
      EXCEPTION 
        WHEN others THEN RAISE NOTICE '%, skipping', SQLERRM USING ERRCODE = SQLSTATE;
      END;   
    END LOOP;
    end 
    $$;  

    DO $$
    declare
      v_grantee varchar := 'runtime-gke@%';
      r record;
    begin
    FOR r IN select 'ALTER USER "' || usename ||'" IN DATABASE pipeline_data SET role TO uny_role_owner' as v_statement	
             from pg_user where usename like v_grantee  
             and exists (select 1 from pg_database where datname = 'pipeline_data')            
    LOOP 
      BEGIN
        RAISE NOTICE '%', r.v_statement;
        execute r.v_statement;
      EXCEPTION 
        WHEN others THEN RAISE NOTICE '%, skipping', SQLERRM USING ERRCODE = SQLSTATE;
      END;   
    END LOOP;
    end 
    $$;
    \q
    EOF
    
    psql -h 127.0.0.1 -p ${port} -U ${PGUSER} -d ${PGDB} <<-'EOF'
    RESET ROLE;
    select current_user, current_role;
    --reassign ownership to uny_role_owner
    DO $$
    declare
      v_grantee varchar := 'runtime-gke@%';
      r record;
    begin
    FOR r IN select 'REASSIGN OWNED BY "'|| usename ||'" to uny_role_owner' as v_statement, usename as v_usename from pg_user where usename like v_grantee  LOOP 
      BEGIN
        RAISE NOTICE '%', r.v_statement;
        execute 'SET ROLE "'||r.v_usename||'"';
        execute r.v_statement;
        execute 'RESET ROLE';
      EXCEPTION 
        WHEN others THEN RAISE NOTICE '%, skipping', SQLERRM USING ERRCODE = SQLSTATE;
      END;   
    END LOOP;
    end 
    $$;  
    
    DO $$
    DECLARE 
      r record;
    BEGIN
    FOR r IN select a.nspname from pg_namespace a
             where  a.nspowner in 
             (
              select b.usesysid from pg_user b where (b.usename like ('uny%user') or b.usename like 'runtime-gke%') union all
              select b.oid from pg_roles b where b.rolname like 'uny_role%' 
             )
            and a.nspname ~ E'^[[:xdigit:]]{8}_([[:xdigit:]]{4})_([[:xdigit:]]{4}).*'
     LOOP 
      BEGIN 
        execute 'grant usage on schema "'||r.nspname||'" to "uny_role_user_ro"';
        execute 'grant usage on schema "'||r.nspname||'" to "uny_role_user_app"';
        execute 'grant usage on schema "'||r.nspname||'" to "uny_role_user_ff"';
      END;
    END LOOP;   
    
    END
    $$;

    --set default privileges 
    SET SESSION ROLE uny_role_owner;
    DO $$
    DECLARE
        r record;
        i int;
        v_new_owner varchar := 'uny_role_user_ro';
    BEGIN
        FOR r IN
              select a.nspname v_schema_name
              from pg_namespace a 
              where a.nspowner in 
              (select b.usesysid from pg_user b where (b.usename like ('uny%user') or b.usename like 'runtime-gke%') union all
               select b.oid from pg_roles b where b.rolname like 'uny_role%' 
              ) and a.nspname ~ E'^[[:xdigit:]]{8}_([[:xdigit:]]{4})_([[:xdigit:]]{4}).*'
        LOOP
          RAISE NOTICE 'ALTER DEFAULT PRIVILEGES IN SCHEMA "%" GRANT SELECT, INSERT, UPDATE, DELETE ON TABLES TO uny_role_user_app', r.v_schema_name;
          EXECUTE 'ALTER DEFAULT PRIVILEGES IN SCHEMA "'||r.v_schema_name||'" GRANT SELECT, INSERT, UPDATE, DELETE ON TABLES TO uny_role_user_app';
          
          RAISE NOTICE 'ALTER DEFAULT PRIVILEGES IN SCHEMA "%" GRANT USAGE, SELECT ON SEQUENCES TO uny_role_user_app', r.v_schema_name;
          EXECUTE 'ALTER DEFAULT PRIVILEGES IN SCHEMA "'||r.v_schema_name||'" GRANT USAGE, SELECT ON SEQUENCES TO uny_role_user_app';
          
          RAISE NOTICE 'ALTER DEFAULT PRIVILEGES IN SCHEMA "%" GRANT EXECUTE ON FUNCTIONS TO uny_role_user_app', r.v_schema_name;
          EXECUTE 'ALTER DEFAULT PRIVILEGES IN SCHEMA "'||r.v_schema_name||'" GRANT EXECUTE ON FUNCTIONS TO uny_role_user_app';
          
          RAISE NOTICE 'ALTER DEFAULT PRIVILEGES IN SCHEMA "%" GRANT EXECUTE ON ROUTINES TO uny_role_user_app', r.v_schema_name;
          EXECUTE 'ALTER DEFAULT PRIVILEGES IN SCHEMA "'||r.v_schema_name||'" GRANT EXECUTE ON ROUTINES TO uny_role_user_app';
          
          RAISE NOTICE 'ALTER DEFAULT PRIVILEGES IN SCHEMA "%" GRANT SELECT, INSERT, UPDATE, DELETE ON TABLES TO uny_role_user_ff', r.v_schema_name;
          EXECUTE 'ALTER DEFAULT PRIVILEGES IN SCHEMA "'||r.v_schema_name||'" GRANT SELECT, INSERT, UPDATE, DELETE ON TABLES TO uny_role_user_ff';
          
          RAISE NOTICE 'ALTER DEFAULT PRIVILEGES IN SCHEMA "%" GRANT USAGE, SELECT ON SEQUENCES TO uny_role_user_ff', r.v_schema_name;
          EXECUTE 'ALTER DEFAULT PRIVILEGES IN SCHEMA "'||r.v_schema_name||'" GRANT USAGE, SELECT ON SEQUENCES TO uny_role_user_ff';
          
          RAISE NOTICE 'ALTER DEFAULT PRIVILEGES IN SCHEMA "%" GRANT EXECUTE ON FUNCTIONS TO uny_role_user_ff', r.v_schema_name;
          EXECUTE 'ALTER DEFAULT PRIVILEGES IN SCHEMA "'||r.v_schema_name||'" GRANT EXECUTE ON FUNCTIONS TO uny_role_user_ff';
                  
          RAISE NOTICE 'ALTER DEFAULT PRIVILEGES IN SCHEMA "%" GRANT EXECUTE ON ROUTINES TO uny_role_user_ff', r.v_schema_name;
          EXECUTE 'ALTER DEFAULT PRIVILEGES IN SCHEMA "'||r.v_schema_name||'" GRANT EXECUTE ON ROUTINES TO uny_role_user_ff';
          
          RAISE NOTICE 'ALTER DEFAULT PRIVILEGES IN SCHEMA "%" GRANT SELECT ON TABLES TO uny_role_user_ro', r.v_schema_name;
          EXECUTE 'ALTER DEFAULT PRIVILEGES IN SCHEMA "'||r.v_schema_name||'" GRANT SELECT ON TABLES TO uny_role_user_ro';
          
          RAISE NOTICE 'ALTER DEFAULT PRIVILEGES IN SCHEMA "%" GRANT USAGE, SELECT ON SEQUENCES TO uny_role_user_ro', r.v_schema_name;
          EXECUTE 'ALTER DEFAULT PRIVILEGES IN SCHEMA "'||r.v_schema_name||'" GRANT USAGE, SELECT ON SEQUENCES TO uny_role_user_ro';
        END LOOP;
    END
    $$;

    --for application role grant permission
    DO $$
    DECLARE
        r record;
        i int;
        v_new_owner varchar := 'uny_role_user_app';
    BEGIN
        FOR r IN
            SELECT 'grant select, insert, update, delete on  "' || table_schema || '"."' || table_name || '"  TO ' || v_new_owner || ';' AS a FROM information_schema.tables WHERE table_schema = ANY (
              select a.nspname 
              from pg_namespace a 
              where a.nspowner in 
              (select b.usesysid from pg_user b where (b.usename like ('uny%user') or b.usename like 'runtime-gke%') union all
               select b.oid from pg_roles b where b.rolname like 'uny_role%' 
              ) and a.nspname ~ E'^[[:xdigit:]]{8}_([[:xdigit:]]{4})_([[:xdigit:]]{4}).*') 
            UNION ALL
            SELECT 'grant usage, select on "' || sequence_schema || '"."' || sequence_name || '"  TO ' || v_new_owner || ';' AS a FROM information_schema.sequences WHERE sequence_schema = ANY (
              select a.nspname 
              from pg_namespace a 
              where a.nspowner in 
              (select b.usesysid from pg_user b where (b.usename like ('uny%user') or b.usename like 'runtime-gke%') union all
               select b.oid from pg_roles b where b.rolname like 'uny_role%' 
              ) and a.nspname ~ E'^[[:xdigit:]]{8}_([[:xdigit:]]{4})_([[:xdigit:]]{4}).*') 
            UNION ALL
            SELECT 'grant select on  "' || table_schema || '"."' || table_name || '" TO ' || v_new_owner || ';' AS a FROM information_schema.views WHERE table_schema = ANY (
              select a.nspname 
              from pg_namespace a 
              where a.nspowner in 
              (select b.usesysid from pg_user b where (b.usename like ('uny%user') or b.usename like 'runtime-gke%') union all
               select b.oid from pg_roles b where b.rolname like 'uny_role%' 
              ) and a.nspname ~ E'^[[:xdigit:]]{8}_([[:xdigit:]]{4})_([[:xdigit:]]{4}).*') 
            UNION ALL
            SELECT CASE
                    WHEN p.prokind = 'f' THEN 'grant execute on FUNCTION "' || n.nspname || '"."' || p.proname  || '" TO ' || v_new_owner || ';'
                    ELSE 'grant execute on PROCEDURE "' || n.nspname || '"."' || p.proname  || '" TO ' || v_new_owner || ';'
                   END AS a 
            from pg_proc p left join pg_namespace n on p.pronamespace = n.oid where  n.nspname = ANY (
              select a.nspname 
              from pg_namespace a 
              where a.nspowner in 
              (select b.usesysid from pg_user b where (b.usename like ('uny%user') or b.usename like 'runtime-gke%') union all
               select b.oid from pg_roles b where b.rolname like 'uny_role%' 
              ) and a.nspname ~ E'^[[:xdigit:]]{8}_([[:xdigit:]]{4})_([[:xdigit:]]{4}).*')  and n.nspname not in ('''pg_catalog''', '''information_schema''')  
             and p.proname not like 'pg\_%' and p.proname not like 'ssl\_%'  and p.proname not like 'autoprewarm%' and p.proowner = n.nspowner
        LOOP
          EXECUTE  r.a;
        END LOOP;
    END
    $$;
    --for FF role grant permission
    DO $$
    DECLARE
        r record;
        i int;
        v_new_owner varchar := 'uny_role_user_ff';
    BEGIN
        FOR r IN
            SELECT 'grant select, insert, update, delete on  "' || table_schema || '"."' || table_name || '"  TO ' || v_new_owner || ';' AS a FROM information_schema.tables WHERE table_schema = ANY (
              select a.nspname 
              from pg_namespace a 
              where a.nspowner in 
              (select b.usesysid from pg_user b where (b.usename like ('uny%user') or b.usename like 'runtime-gke%') union all
               select b.oid from pg_roles b where b.rolname like 'uny_role%' 
              ) and a.nspname ~ E'^[[:xdigit:]]{8}_([[:xdigit:]]{4})_([[:xdigit:]]{4}).*') 
            UNION ALL
            SELECT 'grant usage, select on "' || sequence_schema || '"."' || sequence_name || '"  TO ' || v_new_owner || ';' AS a FROM information_schema.sequences WHERE sequence_schema = ANY (
              select a.nspname 
              from pg_namespace a 
              where a.nspowner in 
              (select b.usesysid from pg_user b where (b.usename like ('uny%user') or b.usename like 'runtime-gke%') union all
               select b.oid from pg_roles b where b.rolname like 'uny_role%' 
              ) and a.nspname ~ E'^[[:xdigit:]]{8}_([[:xdigit:]]{4})_([[:xdigit:]]{4}).*') 
            UNION ALL
            SELECT 'grant  select on  "' || table_schema || '"."' || table_name || '" TO ' || v_new_owner || ';' AS a FROM information_schema.views WHERE table_schema = ANY (
              select a.nspname 
              from pg_namespace a 
              where a.nspowner in 
              (select b.usesysid from pg_user b where (b.usename like ('uny%user') or b.usename like 'runtime-gke%') union all
               select b.oid from pg_roles b where b.rolname like 'uny_role%' 
              ) and a.nspname ~ E'^[[:xdigit:]]{8}_([[:xdigit:]]{4})_([[:xdigit:]]{4}).*') 
            UNION ALL
            SELECT CASE
                    WHEN p.prokind = 'f' THEN 'grant execute on FUNCTION "' || n.nspname || '"."' || p.proname  || '" TO ' || v_new_owner || ';'
                    ELSE 'grant execute on PROCEDURE "' || n.nspname || '"."' || p.proname  || '" TO ' || v_new_owner || ';'
                   END AS a 
            from pg_proc p left join pg_namespace n on p.pronamespace = n.oid where  n.nspname = ANY (
              select a.nspname 
              from pg_namespace a 
              where a.nspowner in 
              (select b.usesysid from pg_user b where (b.usename like ('uny%user') or b.usename like 'runtime-gke%') union all
               select b.oid from pg_roles b where b.rolname like 'uny_role%' 
              ) and a.nspname ~ E'^[[:xdigit:]]{8}_([[:xdigit:]]{4})_([[:xdigit:]]{4}).*')  and n.nspname not in ('''pg_catalog''', '''information_schema''')  
             and p.proname not like 'pg\_%' and p.proname not like 'ssl\_%'  and p.proname not like 'autoprewarm%' and p.proowner = n.nspowner
        LOOP
          EXECUTE  r.a;
        END LOOP;
    END
    $$;
    --for read only role grant permission
    DO $$
    DECLARE
        r record;
        i int;
        v_new_owner varchar := 'uny_role_user_ro';
    BEGIN
        FOR r IN
            SELECT 'grant select on  "' || table_schema || '"."' || table_name || '"  TO ' || v_new_owner || ';' AS a FROM information_schema.tables WHERE table_schema = ANY (
              select a.nspname 
              from pg_namespace a 
              where a.nspowner in 
              (select b.usesysid from pg_user b where (b.usename like ('uny%user') or b.usename like 'runtime-gke%') union all
               select b.oid from pg_roles b where b.rolname like 'uny_role%' 
              ) and a.nspname ~ E'^[[:xdigit:]]{8}_([[:xdigit:]]{4})_([[:xdigit:]]{4}).*') 
            UNION ALL
            SELECT 'grant usage, select on "' || sequence_schema || '"."' || sequence_name || '"  TO ' || v_new_owner || ';' AS a FROM information_schema.sequences WHERE sequence_schema = ANY (
              select a.nspname 
              from pg_namespace a 
              where a.nspowner in 
              (select b.usesysid from pg_user b where (b.usename like ('uny%user') or b.usename like 'runtime-gke%') union all
               select b.oid from pg_roles b where b.rolname like 'uny_role%' 
              ) and a.nspname ~ E'^[[:xdigit:]]{8}_([[:xdigit:]]{4})_([[:xdigit:]]{4}).*') 
            UNION ALL
            SELECT 'grant  select on  "' || table_schema || '"."' || table_name || '" TO ' || v_new_owner || ';' AS a FROM information_schema.views WHERE table_schema = ANY (
              select a.nspname 
              from pg_namespace a 
              where a.nspowner in 
              (select b.usesysid from pg_user b where (b.usename like ('uny%user') or b.usename like 'runtime-gke%') union all
               select b.oid from pg_roles b where b.rolname like 'uny_role%' 
              ) and a.nspname ~ E'^[[:xdigit:]]{8}_([[:xdigit:]]{4})_([[:xdigit:]]{4}).*') 
        LOOP
          EXECUTE  r.a;
        END LOOP;
    END
    $$;
    EOF
    fi
    done
    echo [Grant privileges][Completed]
    while true; do sleep 10; done;
kind: ConfigMap
metadata:
  name: pg-grant-privileges-cm
  namespace: support-pod
EOT
cat >> pg-grant-privileges.yaml <<-EOT
---
apiVersion: v1                                          
data:                                                   
  instance_secrets: $(IFS=, ; echo "${instance_secret_list[*]}" | base64 --wrap=0)    
kind: Secret                                            
metadata:                                               
  name: pg-grant-privileges-secret         
  namespace: support-pod                                
type: Opaque                                            
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: pg-grant-privileges-deployment
  namespace: support-pod
spec:
  selector:
    matchLabels:
      app: pg-grant-privileges
  template:
    metadata:
      annotations: 
        cluster-autoscaler.kubernetes.io/safe-to-evict: "true"
      labels:
        app: pg-grant-privileges
    spec:      
      serviceAccountName: support-pod-sa
      containers:
        - name: pg-grant-privileges
          image: gcr.io/${image_project}/unity/support/support_pod/ubuntu_gcloud:1.25
          imagePullPolicy: Always
          command: ["/bin/bash","/usr/local/bin/pgGrantPriv"] 
          ports:
            - containerPort: 8000
          volumeMounts:
            - mountPath: /tmp
              name: tmp-volume
            - name: pg-grant-privileges-cm
              mountPath: /usr/local/bin
            - name: pg-grant-privileges-secret
              mountPath: /hss/apps/secrets    
          securityContext:
            allowPrivilegeEscalation: false
            capabilities:
              drop:
              - ALL
            readOnlyRootFilesystem: true
            runAsNonRoot: true
            runAsUser: *********
            seccompProfile:
              type: RuntimeDefault  
          resources:
            requests:
              memory: "1Gi"
              cpu:    "250m"    
        - name: cloud-sql-proxy
EOT
if [[ $psc -eq 0 ]]; then 
cat >> pg-grant-privileges.yaml <<-EOT
          image: gcr.io/cloudsql-docker/gce-proxy:1.31.0
          imagePullPolicy: Always
          command: ["/cloud_sql_proxy",
                    "-instances=$(IFS=, ; echo "${instance_list[*]}")", 
                    "-ip_address_types=PRIVATE",
                    "-enable_iam_login"]
EOT
else 
cat >> pg-grant-privileges.yaml <<-EOT
          image: gcr.io/cloud-sql-connectors/cloud-sql-proxy:2.11.0
          imagePullPolicy: Always
          args:
            - ${instance_list_psc} 
            - --port=1201
            - --address=127.0.0.1
            - --auto-iam-authn
            - --psc	
EOT
fi
cat >> pg-grant-privileges.yaml <<-EOT
          securityContext:
            allowPrivilegeEscalation: false
            capabilities:
              drop:
              - ALL
            readOnlyRootFilesystem: true
            runAsNonRoot: true
            runAsUser: *********
            seccompProfile:
              type: RuntimeDefault
          resources:
            requests:
              memory: "1Gi"
              cpu:    "250m"
      volumes:
        - emptyDir: {}
          name: tmp-volume
        - name: pg-grant-privileges-cm
          configMap:
            name: pg-grant-privileges-cm  
        - name: pg-grant-privileges-secret
          secret:
            secretName: pg-grant-privileges-secret
EOT
cat >> ns-support-pod.yaml <<-EOT
apiVersion: v1
kind: Namespace
metadata:
  labels:
    kubernetes.io/metadata.name: support-pod
  name: support-pod
EOT
cat >> support-pod-sa.yaml <<-EOT
apiVersion: v1
automountServiceAccountToken: true
kind: ServiceAccount
metadata:
  annotations:
    iam.gke.io/gcp-service-account: runtime-gke@${project}.iam.gserviceaccount.com  
  name: support-pod-sa
  namespace: support-pod
EOT
}

function reset_postgres_password(){

for ((c=0; c<=${#instance_list[@]}-1; c++ ))
do
  export DBPasswd=$(echo $RANDOM | md5sum | tr -dc '[:alnum:]' | fold -w ${1:-30} | head -n 1)
  instance=$(echo ${instance_list[c]}|cut -f3 -d:|cut -f1 -d=)
  gcloud sql users set-password postgres --instance=${instance} --password=${DBPasswd}  
  rc=$?
  retry=0
  while [[ $retry -lt 10 && $rc -eq 1 ]];
  do 
   sleep 5 && gcloud sql users set-password postgres --instance=${instance} --password=${DBPasswd}  
   rc=$?
   retry=$(($retry+1))
  done
  echo "Password changed for ${instance_list[c]}"
  instance_secret_list[c]="${instance_list[c]/%/:${DBPasswd}}"
done
}

function constructor(){
root=$(readlink -f $(dirname $0))
setup_kubeconfig=1
psc=0
filter=".*"
export no_proxy=${no_proxy},systems.uk.hsbc

}

###############################################
# main 
###############################################
constructor
options "$@"
gcp_project=$(gcloud config list --format 'value(core.project)')
gcloud_authenticate
image_project=${image_project:-${project}}
dbuser=${dbuser:-runtime-gke@${project}.iam}
dbname=${dbname:-pipeline_data}
generate_instances_list
reset_postgres_password
generate_deployment_yaml

[[ -z ${project} ]] && echo please enter gcp project namee. exit  && exit 1
[[ ${#instance_list[@]} -eq 0 ]] && echo no matching database found. exit && exit 0

setup_kubeconfig
setup_namespace
kubectl apply -f pg-grant-privileges.yaml
timeout=300
i=0
kubectl rollout status deployment pg-grant-privileges-deployment -n support-pod
while [[ $(kubectl rollout status deployment pg-grant-privileges-deployment -n support-pod 2> /dev/null | grep successful | wc -l) -eq 0 && $i -lt ${timeout} && $(kubectl -n support-pod logs deployment.apps/pg-grant-privileges-deployment pg-grant-privileges 2> /dev/null | wc -l) -eq 0 ]] ; do 
  i=$((i+10))
  echo waiting for deployment to complete
  sleep 10
  [[ $i -ge ${timeout} ]] && echo deployment timeout. exit 1 && exit 1
done
timeout=300
i=0
while [[ $(kubectl -n support-pod logs deployment.apps/pg-grant-privileges-deployment pg-grant-privileges 2> /dev/null | grep Completed | wc -l) -eq 0 && $i -lt ${timeout} ]] ; do 
  i=$((i+10))
  echo waiting for job to complete
  sleep 10
  [[ $i -ge ${timeout} ]] && echo job execution timeout. exit 1 && exit 1
done
kubectl -n support-pod logs deployment.apps/pg-grant-privileges-deployment pg-grant-privileges
kubectl delete -f pg-grant-privileges.yaml
