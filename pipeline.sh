#!/bin/bash
set -euo pipefail

# Master Pipeline Script for change-dashboard and risc
# Usage: ./pipeline.sh [options]

# Function to print usage
usage() {
    echo "Usage: $0 [options]"
    echo ""
    echo "Options:"
    echo "  --projects      Comma-separated list of projects (default: change-dashboard,risc)"
    echo "  --stages        Comma-separated list of stages to run (default: all)"
    echo "                  Available stages: maven-build,security-scan,image-build,deploy"
    echo "  --environment   Target environment: dev|uat|prod (default: dev)"
    echo "  --namespace     Target namespace (default: auto-detected from environment)"
    echo "  --parallel      Run projects in parallel (default: sequential)"
    echo "  --continue      Continue with other projects if one fails"
    echo "  --dry-run       Show what would be executed without running"
    echo "  --help, -h      Show this help message"
    echo ""
    echo "Stage-specific options:"
    echo "  --skip-tests    Skip tests in maven-build stage"
    echo "  --skip-sast     Skip SAST in security-scan stage"
    echo "  --push          Push images in image-build stage"
    echo "  --wait          Wait for deployment completion in deploy stage"
    echo ""
    echo "Examples:"
    echo "  $0                                           # Full pipeline for both projects"
    echo "  $0 --projects change-dashboard               # Only change-dashboard"
    echo "  $0 --stages maven-build,image-build          # Only build stages"
    echo "  $0 --environment uat --push --wait           # Deploy to UAT with push and wait"
    echo "  $0 --parallel --continue                     # Parallel execution, continue on failures"
    echo "  $0 --dry-run                                 # Show what would be executed"
    exit 1
}

# Default configuration
PROJECTS="change-dashboard,risc"
STAGES="maven-build,security-scan,image-build,deploy"
ENVIRONMENT="dev"
NAMESPACE=""
PARALLEL=false
CONTINUE_ON_FAILURE=false
DRY_RUN=false

# Stage-specific options
SKIP_TESTS=false
SKIP_SAST=false
PUSH_IMAGES=false
WAIT_DEPLOYMENT=false

# Parse options
while [[ $# -gt 0 ]]; do
    case $1 in
        --projects)
            PROJECTS="$2"
            shift 2
            ;;
        --stages)
            STAGES="$2"
            shift 2
            ;;
        --environment)
            ENVIRONMENT="$2"
            shift 2
            ;;
        --namespace)
            NAMESPACE="$2"
            shift 2
            ;;
        --parallel)
            PARALLEL=true
            shift
            ;;
        --continue)
            CONTINUE_ON_FAILURE=true
            shift
            ;;
        --dry-run)
            DRY_RUN=true
            shift
            ;;
        --skip-tests)
            SKIP_TESTS=true
            shift
            ;;
        --skip-sast)
            SKIP_SAST=true
            shift
            ;;
        --push)
            PUSH_IMAGES=true
            shift
            ;;
        --wait)
            WAIT_DEPLOYMENT=true
            shift
            ;;
        --help|-h)
            usage
            ;;
        *)
            echo "Error: Unknown option $1"
            usage
            ;;
    esac
done

# Convert comma-separated lists to arrays
IFS=',' read -ra PROJECT_ARRAY <<< "${PROJECTS}"
IFS=',' read -ra STAGE_ARRAY <<< "${STAGES}"

# Validate environment
case "${ENVIRONMENT}" in
    "dev"|"uat"|"prod")
        ;;
    *)
        echo "Error: Invalid environment '${ENVIRONMENT}'. Must be dev, uat, or prod"
        exit 1
        ;;
esac

# Set default namespace if not provided
if [ -z "${NAMESPACE}" ]; then
    NAMESPACE="ns-i15n-${ENVIRONMENT}"
fi

# Validate stages
VALID_STAGES=("maven-build" "security-scan" "image-build" "deploy")
for stage in "${STAGE_ARRAY[@]}"; do
    if [[ ! " ${VALID_STAGES[@]} " =~ " ${stage} " ]]; then
        echo "Error: Invalid stage '${stage}'. Valid stages: ${VALID_STAGES[*]}"
        exit 1
    fi
done

# Validate projects exist
for project in "${PROJECT_ARRAY[@]}"; do
    if [ ! -d "${project}" ]; then
        echo "Error: Project directory '${project}' not found"
        exit 1
    fi
done

echo "=== Master Pipeline Execution ==="
echo "Configuration:"
echo "  Projects: ${PROJECT_ARRAY[*]}"
echo "  Stages: ${STAGE_ARRAY[*]}"
echo "  Environment: ${ENVIRONMENT}"
echo "  Namespace: ${NAMESPACE}"
echo "  Parallel: ${PARALLEL}"
echo "  Continue on Failure: ${CONTINUE_ON_FAILURE}"
echo "  Dry Run: ${DRY_RUN}"
echo ""
echo "Stage Options:"
echo "  Skip Tests: ${SKIP_TESTS}"
echo "  Skip SAST: ${SKIP_SAST}"
echo "  Push Images: ${PUSH_IMAGES}"
echo "  Wait Deployment: ${WAIT_DEPLOYMENT}"

if [ "${DRY_RUN}" = "true" ]; then
    echo ""
    echo "=== DRY RUN: Commands that would be executed ==="
    
    for project in "${PROJECT_ARRAY[@]}"; do
        echo ""
        echo "--- Project: ${project} ---"
        
        for stage in "${STAGE_ARRAY[@]}"; do
            case "${stage}" in
                "maven-build")
                    cmd="./stage-1-maven-build.sh ${project}"
                    [ "${SKIP_TESTS}" = "true" ] && cmd="${cmd} --skip-tests"
                    echo "  ${cmd}"
                    ;;
                "security-scan")
                    cmd="./stage-2-security-scan.sh ${project}"
                    [ "${SKIP_SAST}" = "true" ] && cmd="${cmd} --skip-sast"
                    echo "  ${cmd}"
                    ;;
                "image-build")
                    cmd="./stage-3-image-build.sh ${project}"
                    [ "${PUSH_IMAGES}" = "true" ] && cmd="${cmd} --push"
                    echo "  ${cmd}"
                    ;;
                "deploy")
                    cmd="./stage-4-deploy.sh ${project} --environment ${ENVIRONMENT} --namespace ${NAMESPACE}"
                    [ "${WAIT_DEPLOYMENT}" = "true" ] && cmd="${cmd} --wait"
                    echo "  ${cmd}"
                    ;;
            esac
        done
    done
    
    echo ""
    echo "=== DRY RUN COMPLETED ==="
    echo "To execute, run without --dry-run flag"
    exit 0
fi

# Function to run a stage for a project
run_stage() {
    local project="$1"
    local stage="$2"
    local stage_num="$3"
    
    echo ""
    echo "=== Running Stage ${stage_num}: ${stage} for ${project} ==="
    
    local cmd=""
    local stage_options=""
    
    case "${stage}" in
        "maven-build")
            cmd="./stage-1-maven-build.sh"
            [ "${SKIP_TESTS}" = "true" ] && stage_options="${stage_options} --skip-tests"
            ;;
        "security-scan")
            cmd="./stage-2-security-scan.sh"
            [ "${SKIP_SAST}" = "true" ] && stage_options="${stage_options} --skip-sast"
            ;;
        "image-build")
            cmd="./stage-3-image-build.sh"
            [ "${PUSH_IMAGES}" = "true" ] && stage_options="${stage_options} --push"
            ;;
        "deploy")
            cmd="./stage-4-deploy.sh"
            stage_options="${stage_options} --environment ${ENVIRONMENT} --namespace ${NAMESPACE}"
            [ "${WAIT_DEPLOYMENT}" = "true" ] && stage_options="${stage_options} --wait"
            ;;
    esac
    
    # Make script executable
    chmod +x "${cmd}"
    
    # Execute the stage
    echo "Executing: ${cmd} ${project} ${stage_options}"
    ${cmd} ${project} ${stage_options}
    
    local exit_code=$?
    
    if [ ${exit_code} -eq 0 ]; then
        echo "✅ Stage ${stage_num} (${stage}) completed successfully for ${project}"
        return 0
    else
        echo "❌ Stage ${stage_num} (${stage}) failed for ${project} (exit code: ${exit_code})"
        return ${exit_code}
    fi
}

# Function to run all stages for a project
run_project() {
    local project="$1"
    
    echo ""
    echo "========================================="
    echo "Starting pipeline for project: ${project}"
    echo "========================================="
    
    local stage_num=1
    for stage in "${STAGE_ARRAY[@]}"; do
        if ! run_stage "${project}" "${stage}" "${stage_num}"; then
            if [ "${CONTINUE_ON_FAILURE}" = "true" ]; then
                echo "⚠️  Continuing with next stage despite failure"
            else
                echo "❌ Pipeline failed for ${project} at stage: ${stage}"
                return 1
            fi
        fi
        ((stage_num++))
    done
    
    echo ""
    echo "✅ Pipeline completed successfully for ${project}"
    return 0
}

# Main execution
echo ""
echo "=== Starting Pipeline Execution ==="

FAILED_PROJECTS=()
SUCCESSFUL_PROJECTS=()

if [ "${PARALLEL}" = "true" ]; then
    echo "Running projects in parallel..."
    
    # Start background processes for each project
    PIDS=()
    for project in "${PROJECT_ARRAY[@]}"; do
        (
            run_project "${project}"
            echo $? > "/tmp/pipeline_${project}_result"
        ) &
        PIDS+=($!)
    done
    
    # Wait for all processes to complete
    for i in "${!PIDS[@]}"; do
        wait ${PIDS[$i]}
        project="${PROJECT_ARRAY[$i]}"
        result=$(cat "/tmp/pipeline_${project}_result" 2>/dev/null || echo "1")
        
        if [ "${result}" = "0" ]; then
            SUCCESSFUL_PROJECTS+=("${project}")
        else
            FAILED_PROJECTS+=("${project}")
        fi
        
        # Clean up temp file
        rm -f "/tmp/pipeline_${project}_result"
    done
    
else
    echo "Running projects sequentially..."
    
    for project in "${PROJECT_ARRAY[@]}"; do
        if run_project "${project}"; then
            SUCCESSFUL_PROJECTS+=("${project}")
        else
            FAILED_PROJECTS+=("${project}")
            
            if [ "${CONTINUE_ON_FAILURE}" = "false" ]; then
                echo "❌ Stopping pipeline due to failure in ${project}"
                break
            fi
        fi
    done
fi

# Final summary
echo ""
echo "========================================="
echo "PIPELINE EXECUTION SUMMARY"
echo "========================================="
echo "Environment: ${ENVIRONMENT}"
echo "Stages executed: ${STAGE_ARRAY[*]}"
echo ""

if [ ${#SUCCESSFUL_PROJECTS[@]} -gt 0 ]; then
    echo "✅ Successful projects (${#SUCCESSFUL_PROJECTS[@]}):"
    for project in "${SUCCESSFUL_PROJECTS[@]}"; do
        echo "  - ${project}"
    done
fi

if [ ${#FAILED_PROJECTS[@]} -gt 0 ]; then
    echo ""
    echo "❌ Failed projects (${#FAILED_PROJECTS[@]}):"
    for project in "${FAILED_PROJECTS[@]}"; do
        echo "  - ${project}"
    done
fi

echo ""
if [ ${#FAILED_PROJECTS[@]} -eq 0 ]; then
    echo "🎉 All projects completed successfully!"
    
    if [[ " ${STAGE_ARRAY[@]} " =~ " deploy " ]]; then
        echo ""
        echo "Access URLs:"
        for project in "${SUCCESSFUL_PROJECTS[@]}"; do
            case "${ENVIRONMENT}" in
                "dev")
                    echo "  ${project}: https://${project}.hsbc-9087302-unity-dev.dev.gcp.cloud.hk.hsbc"
                    ;;
                "uat")
                    echo "  ${project}: https://${project}.hsbc-9087302-unity-uat.uat.gcp.cloud.hk.hsbc"
                    ;;
                "prod")
                    echo "  ${project}: https://${project}.hsbc-9087302-unity-prod.prod.gcp.cloud.hk.hsbc"
                    ;;
            esac
        done
    fi
    
    exit 0
else
    echo "❌ Pipeline completed with failures"
    exit 1
fi
