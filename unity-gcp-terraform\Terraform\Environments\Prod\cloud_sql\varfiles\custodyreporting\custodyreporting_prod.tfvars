json_data_path = ""
sql_proxy_zone = "asia-east2-c"
db_version = "POSTGRES_14"

multi_instance_sql_data = [{
    db_instance_name : "custodyreporting-prod-projection-558431",
    db_tier : "db-custom-2-8192",
    db_databases : ["pipeline_data"],
    database_user_names : [
      { "username" : "<EMAIL>", "type" : "CLOUD_IAM_SERVICE_ACCOUNT" },  
      { "username" : "<EMAIL>", "type" : "CLOUD_IAM_SERVICE_ACCOUNT" },
      { "username" : "<EMAIL>", "type" : "CLOUD_IAM_SERVICE_ACCOUNT" }, 
      { "username" : "<EMAIL>", "type" : "CLOUD_IAM_USER" },
      { "username" : "<EMAIL>", "type" : "CLOUD_IAM_USER" },
      { "username" : "<EMAIL>", "type" : "CLOUD_IAM_USER" },
      { "username" : "<EMAIL>", "type" : "CLOUD_IAM_USER" },
      { "username" : "<EMAIL>", "type" : "CLOUD_IAM_USER" }
    ],
    delete_protection : true,
    retention_log_time : 7,
    maintenance_window_day : 6,
    # The maintenance window is specified in UTC time. It supports: Hour of day (0-23)
    maintenance_window_hour : 5,
    query_insights_enabled: true
    master_instance_name: null
  }
]
