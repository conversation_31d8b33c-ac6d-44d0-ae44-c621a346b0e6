{"service_accounts": {"gce-stage3-image-builder": "Automation Service account for Deployment - hsbc-********-unycsreport-dev", "hgke-custom-metrics-adapter": "HSBC GKE Managed Service Custom Metrics Stackdriver Adapter service account", "hgke-egress": "HSBC GKE Managed Service egress service account", "hgke-external-dns": "HSBC GKE Managed Service ExternalDNS service account", "hgke-flux": "HSBC GKE Managed Service Flux service account", "hgke-ingress": "HSBC GKE Managed Service ingress service account", "hgke-manager": "HSBC GKE Managed Service manager service account", "hgke-node": "HSBC GKE Managed Service GKE node service account", "hgke-prometheus": "HSBC GKE Managed Service Prometheus service account", "runtime-gke": "runtime user for namespace workload"}}