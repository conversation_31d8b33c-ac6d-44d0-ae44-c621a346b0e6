#!/bin/bash
_project_=$(curl -s -H "Metadata-Flavor: Google" "http://metadata.google.internal/computeMetadata/v1/project/project-id")
if [ $(echo ${_project_}|grep "\-dev"|wc -l) -gt 0 ]; then 

scp -o StrictHostKeyChecking=no -rp /home/<USER>/ServiceAccountKeys/gce-stage3-image-builder.json <EMAIL>:/hss/apps/gcp/docker-home-devops/.secrets/hsbc-9087302-unity-dev-gce.json
scp -o StrictHostKeyChecking=no -rp /home/<USER>/ServiceAccountKeys/gce-stage3-image-builder.json <EMAIL>:/hss/apps/gcp/docker-home-devops/.secrets/hsbc-9087302-unity-dev-gce.json
ssh -o StrictHostKeyChecking=no <EMAIL> "bash /hss/apps/gcp/pack-devopshome.bash"
ssh -o StrictHostKeyChecking=no <EMAIL> "bash /hss/apps/gcp/pack-devopshome.bash"
#ssh -o StrictHostKeyChecking=no <EMAIL> "bash /hss/apps/gcp/sync-consul-gcr-key.bash"

else

_consul_master_token_=/home/<USER>/ServiceAccountKeys/unity1-consul-token
gsutil cp gs://${_project_}-key-management/unity1-consul/unity1-consul-master-token ${_consul_master_token_}
gsutil cp gs://${_project_}-key-management/dmeshpd10-gcs/dmeshpd10-gcs.json /home/<USER>/ServiceAccountKeys/dmeshpd10-gcs.json 
export no_proxy=${no_proxy},hc.cloud.hk.hsbc
curl -XPUT --header "X-Consul-Token: $(cat ${_consul_master_token_})" \
https://hkl20077614.hc.cloud.hk.hsbc:8500/v1/kv/app-config/datamesh/storage/creds \
-d@-<<-EOT
$(cat /home/<USER>/ServiceAccountKeys/dmeshpd10-gcs.json|base64)
EOT
	 
fi
