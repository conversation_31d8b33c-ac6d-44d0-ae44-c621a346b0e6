pipeline {    
    agent  { 
        label "unity-dev-jenkins-agent"    
    }
    environment {
	    STASH_TOKEN= credentials('github-connect')
	    NEXUS_CREDS=credentials('NEXUS_CREDS')
   
    }
    stages {
    stage ('Checkout imagefactory') {
            steps {
		  sh 'whoami'
                 sh 'sudo -i'
                 dir ("/root/test") {
                    git(
             credentialsId: 'github-connect', url:'https://alm-github.systems.uk.hsbc/HASE-IM/hsbc-9087302-unity-dev-mgmt-host'
                    )
                   sh 'chmod -R 777 mgmt_host'

                }
            }
        }
    stage ('create template') {
            steps {
		  sh 'whoami'
                 sh 'sudo -i'
                 dir ("/root/test/mgmt_host") 
                 {
                     sh './initial_setup/create_template.sh dev'
                      sh './initial_setup/create_mig.sh  dev'

                }
            }
        }
    }
}
