static final boolean C_TRUE = true
static final boolean C_FALSE = false
static final String C_SUCCESS = 'SUCCESS'
static final String C_FAILURE = 'FAILURE'
static final String C_NULL = null
static final String C_YES = 'Yes'
static final String C_NO = 'No'

pipeline {    
    agent  { 
        label "unity-dev-jenkins-agent"    
    }
    environment {
	    STASH_TOKEN= credentials('github-connect')   
    }
    stages {

        stage('Sa Key Rotation') {
            steps {
                script {
                    withCredentials([[$class: 'StringBinding', credentialsId: 'uat_img_builder_path', variable: 'gcp_sa_key']]) {
                        this.sh(returnStdout: true, script: "gcloud auth activate-service-account --key-file ${gcp_sa_key}")
                        this.sh 'export GOOGLE_APPLICATION_CREDENTIALS=$gcp_sa_key'
                        this.sh 'chmod 755 ./initial_setup/rotate_keys.sh'
                        this.sh ' ./initial_setup/rotate_keys.sh uat '
	                }		           
                }
            }
        }
    }
    post {
        success {
            script {
                echo "Sucess begins..."   
            }
        }
        unsuccessful {
            script {
                echo "Rollback begins..."      
            }
        }       
        cleanup {
            script {
                cleanWs()
            }
        }
    }
} 