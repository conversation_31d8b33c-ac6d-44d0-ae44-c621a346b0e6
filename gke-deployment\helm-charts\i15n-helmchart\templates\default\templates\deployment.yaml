{{- $namespace := .Values.namespace -}}
apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "i15n-helmchart.fullname" . }}
  namespace: {{ .Values.namespace }}
  labels:
    {{- include "i15n-helmchart.labels" . | nindent 4 }}
  {{- with .Values.customLabels }}
    {{- toYaml . | nindent 4 }}
  {{- end }}
spec:
{{- if not .Values.autoscaling.enabled }}
  replicas: {{ .Values.replicaCount }}
{{- end }}
  selector:
    matchLabels:
      {{- include "i15n-helmchart.selectorLabels" . | nindent 6 }}
  template:
    metadata:
      annotations:
        {{- if semverCompare ">=1.21-0" .Capabilities.KubeVersion.GitVersion }}
        kubectl.kubernetes.io/default-container: {{ include "i15n-helmchart.fullname" . }}
        {{- else }}
        kubectl.kubernetes.io/default-logs-container: {{ include "i15n-helmchart.fullname" . }}
        {{- end }}
        checksum/configmap: {{ include (print $.Template.BasePath "/configmap.yaml") . | sha256sum }}
        checksum/secret: {{ include (print $.Template.BasePath "/secret.yaml") . | sha256sum }}
      {{- with .Values.podAnnotations }}
        {{- toYaml . | nindent 8 }}
      {{- end }}
      labels:
        {{- include "i15n-helmchart.selectorLabels" . | nindent 8 }}
      {{- with .Values.customLabels }}
        {{- toYaml . | nindent 8 }}
      {{- end }}
    spec:
      {{- with .Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      terminationGracePeriodSeconds: {{ .Values.terminationGracePeriodSeconds }}
      serviceAccountName: {{ include "i15n-helmchart.serviceAccountName" . }}
      securityContext:
        {{- toYaml .Values.podSecurityContext | nindent 8 }}
      {{- if .Values.priorityClassName }}
      priorityClassName: {{ .Values.priorityClassName }}
      {{- end }}
{{- with .Values.appDynamic }}
{{- if .enabled }}
      initContainers:
        - image: "{{ .image.repository}}:{{.image.tag}}"
          imagePullPolicy: {{.image.pullPolicy}}
          name: app-dynamics-agent
          resources:
            limits:
              cpu: 100m
              memory: 128Mi
            requests:
              cpu: 10m
              memory: 16Mi
          volumeMounts:
            - name: app-dynamics-configmap-volume
              readOnly: true
              mountPath: /config
            - name:  app-dynamics-keystore-volume
              readOnly: true
              mountPath: /app-dynamics-keystore
            - name: app-dynamics-agent-volume
              mountPath: /app-dynamics
          command: ["sh",  "-c",  "cp -rf /appvol/app-dynamics/* /app-dynamics/ && cp -f /config/controller-info.xml /app-dynamics-keystore/* /app-dynamics/conf/ "]
{{- end }}
{{- end}}
      containers:
        - name: {{ include "i15n-helmchart.fullname" . }}
          securityContext:
            {{- toYaml .Values.securityContext | nindent 12 }}
          image: "{{ .Values.image.repository }}/{{ .Values.image.prefix }}{{ .Values.image.name }}:{{ .Values.image.tag | default .Chart.AppVersion }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          lifecycle:
            preStop:
              exec:
                command: ["/bin/bash", "-c", "PID=`pidof java` && kill -SIGTERM $PID && while ps -p $PID > /dev/null; do sleep 1; done;"]
          args: 
{{- range .Values.args }}
            - {{ . }}
{{- end }}
          {{- with .Values.env }}
          env:
            {{- toYaml . | nindent 12 }}
          {{- end}}
          {{- with .securityContext }}
          securityContext:
            {{- toYaml . | nindent 12 }}
          {{- end}}
          {{- with .Values.volumeMounts }}
          volumeMounts: 
            {{ toYaml . | nindent 12}}
          {{- end}}
          ports:
            - name: http
              containerPort: {{ default 80 .Values.containerPort }}
              protocol: TCP
          {{- with .Values.livenessProbe }}
          livenessProbe:
            {{- toYaml . | nindent 12 }}
          {{- end}}
          {{- with .Values.readinessProbe }}
          readinessProbe:
            {{- toYaml . | nindent 12 }}
          {{- end}}
          resources:
            {{- toYaml .Values.resources | nindent 12 }}
{{- with .Values.externalCloudSQLProxy }}
{{- if .enabled }}
        - name: 'external-cloudsql-proxy'
          image: "{{ .image.repository}}:{{.image.tag}}"
          imagePullPolicy: {{.image.pullPolicy}}
          lifecycle:
            preStop:
              exec:
                command: [ "/bin/sh","-c","while nc -w 2 -v 127.0.0.1 8080 < /dev/null; do sleep 1; done; sleep 30;" ]
          command:
          args:
{{- range .args }}
            - {{ . }}
{{- end }}
          {{- with .env }}
          env:
            {{- toYaml . | nindent 12 }}
          {{- end}}
          {{- with .securityContext }}
          securityContext:
            {{- toYaml . | nindent 12 }}
          {{- end}}
          resources:
            requests:
              cpu: "50m"
              memory: "200Mi"
            limits:
              cpu: "300m"
              memory: "500Mi"
{{- end }}
{{- end}}
{{- with .Values.cloudSQLProxy }}
{{- if .enabled }}
        - name: 'cloudsql-proxy'
          image: "{{ .image.repository}}:{{.image.tag}}"
          imagePullPolicy: {{.image.pullPolicy}}
          lifecycle:
            preStop:
              exec:
                command: [ "/bin/sh","-c","while nc -w 2 -v 127.0.0.1 8080 < /dev/null; do sleep 1; done; sleep 30;" ]
          command:
          args: 
{{- range .args }}
            - {{ . }}
{{- end }}
          {{- with .env }}
          env:
            {{- toYaml . | nindent 12 }}
          {{- end}}
          {{- with .securityContext }}
          securityContext:
            {{- toYaml . | nindent 12 }}
          {{- end}}
          resources:
            requests:
              cpu: "50m"
              memory: "200Mi"
            limits:
              cpu: "300m"
              memory: "500Mi"
{{- end }}
{{- end}}
{{- with .Values.fluentBit }}
{{- if .enabled }}
        - name: fluent-bit
          image: "{{ .image.repository}}:{{.image.tag}}"
          imagePullPolicy: {{ .image.pullPolicy }}
          resources:
            limits:
              cpu: 200m
              memory: 256Mi
            requests:
              cpu: 10m
              memory: 64Mi
          env:
            - name: FLUENT_KEY
              valueFrom:
                secretKeyRef:
                  name: fluent-bit-secret
                  key: FLUENT_KEY
            - name: FLUENT_SSL_PWD
              valueFrom:
                secretKeyRef:
                  name: fluent-bit-secret
                  key: FLUENT_SSL_PWD
          securityContext:
            allowPrivilegeEscalation: false
            capabilities:
              drop:
                - ALL
            readOnlyRootFilesystem: false
            runAsNonRoot: true
            runAsUser: 200005331
            seccompProfile:
              type: RuntimeDefault
          volumeMounts:
            - name: fluent-bit-config-volume
              readOnly: true
              mountPath: /fluent-bit/etc
            - name: log-volume
              mountPath: /appvol/{{ $namespace }}/unity/log/
{{- end }}
{{- end}}
{{- range .Values.additionalContainers }}
        - name: '{{.name}}'
          image: "{{ .image.repository }}/{{ .image.prefix }}{{ .image.name }}:{{ .image.tag }}"
          imagePullPolicy: {{.image.pullPolicy}}
          command:
          args:
          {{- range .args }}
           - {{ . }}
          {{- end }}
          {{- with .env }}
          env:
            {{- toYaml . | nindent 12 }}
          {{- end}}
          {{- with .securityContext }}
          securityContext:
            {{- toYaml . | nindent 12 }}
          {{- end }}
          resources:
            {{- toYaml .resources | nindent 12 }}
          {{- with .volumeMounts }}
          volumeMounts:
            {{ toYaml . | nindent 12 }}
          {{- end }}
{{- end}}
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.hostAliases }}
      hostAliases:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.volumes }}
      volumes:
        {{- toYaml . | nindent 8 }}
      {{- end }}