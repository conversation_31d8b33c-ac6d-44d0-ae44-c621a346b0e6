
  
  <!DOCTYPE html><html><head resURL="/static/258c3f79" data-rooturl="" data-resurl="/static/258c3f79" data-extensions-available="true" data-unit-test="false" data-imagesurl="/static/258c3f79/images" data-crumb-header="Jenkins-Crumb" data-crumb-value="28f272db6b1e108e45eb2b739f17ceb2ddc21697b1b2c902bc2b86d59e9f9890">
    
    

    <title>Jenkins</title><link rel="stylesheet" href="/static/258c3f79/jsbundles/styles.css" type="text/css"></link><link rel="stylesheet" href="/static/258c3f79/css/responsive-grid.css" type="text/css"></link><link rel="icon" href="/static/258c3f79/favicon.svg" type="image/svg+xml"></link><link sizes="any" rel="alternate icon" href="/static/258c3f79/favicon.ico"></link><link sizes="180x180" rel="apple-touch-icon" href="/static/258c3f79/apple-touch-icon.png"></link><link color="#191717" rel="mask-icon" href="/static/258c3f79/mask-icon.svg"></link><meta name="theme-color" content="#ffffff"></meta><script src="/static/258c3f79/scripts/behavior.js" type="text/javascript"></script><script src='/adjuncts/258c3f79/org/kohsuke/stapler/bind.js' type='text/javascript'></script><script src="/static/258c3f79/scripts/yui/yahoo/yahoo-min.js"></script><script src="/static/258c3f79/scripts/yui/dom/dom-min.js"></script><script src="/static/258c3f79/scripts/yui/event/event-min.js"></script><script src="/static/258c3f79/scripts/yui/animation/animation-min.js"></script><script src="/static/258c3f79/scripts/yui/dragdrop/dragdrop-min.js"></script><script src="/static/258c3f79/scripts/yui/container/container-min.js"></script><script src="/static/258c3f79/scripts/yui/connection/connection-min.js"></script><script src="/static/258c3f79/scripts/yui/datasource/datasource-min.js"></script><script src="/static/258c3f79/scripts/yui/autocomplete/autocomplete-min.js"></script><script src="/static/258c3f79/scripts/yui/menu/menu-min.js"></script><script src="/static/258c3f79/scripts/yui/element/element-min.js"></script><script src="/static/258c3f79/scripts/yui/button/button-min.js"></script><script src="/static/258c3f79/scripts/yui/storage/storage-min.js"></script><script src="/static/258c3f79/scripts/hudson-behavior.js" type="text/javascript"></script><script src="/static/258c3f79/scripts/sortable.js" type="text/javascript"></script><link rel="stylesheet" href="/static/258c3f79/scripts/yui/container/assets/container.css" type="text/css"></link><link rel="stylesheet" href="/static/258c3f79/scripts/yui/container/assets/skins/sam/container.css" type="text/css"></link><link rel="stylesheet" href="/static/258c3f79/scripts/yui/menu/assets/skins/sam/menu.css" type="text/css"></link><link rel="search" href="/opensearch.xml" type="application/opensearchdescription+xml" title="Jenkins"></link><meta name="ROBOTS" content="INDEX,NOFOLLOW"></meta><meta name="viewport" content="width=device-width, initial-scale=1"></meta><script src="/adjuncts/258c3f79/org/kohsuke/stapler/jquery/jquery.full.js" type="text/javascript"></script><script>var Q=jQuery.noConflict()</script><script src='/adjuncts/258c3f79/org/jenkinsci/plugins/scriptsecurity/scripts/ScriptApproval/FormValidationPageDecorator/validate.js' type='text/javascript'></script><script>
    if(window.Prototype && JSON) {
    var _json_stringify = JSON.stringify;
    JSON.stringify = function(value) {
    var _array_tojson = Array.prototype.toJSON;
    delete Array.prototype.toJSON;
    var r=_json_stringify(value);
    Array.prototype.toJSON = _array_tojson;
    return r;
    };
    }
  </script><script src="/static/258c3f79/plugin/extended-choice-parameter/js/selectize.min.js" type="text/javascript"></script><script src="/static/258c3f79/plugin/extended-choice-parameter/js/jsoneditor.min.js" type="text/javascript"></script><script src="/static/258c3f79/plugin/extended-choice-parameter/js/jquery.jsonview.min.js" type="text/javascript"></script><link rel="stylesheet" href="/static/258c3f79/plugin/extended-choice-parameter/css/jquery.jsonview.css"></link><link rel="stylesheet" id="icon_stylesheet" href="/static/258c3f79/plugin/extended-choice-parameter/css/selectize.css"></link><link rel="stylesheet" id="icon_stylesheet" href="/static/258c3f79/plugin/extended-choice-parameter/css/selectize.bootstrap2.css"></link><link rel="stylesheet" id="theme_stylesheet"></link><link rel="stylesheet" id="icon_stylesheet"></link><style type="text/css">.cloudbees-license {
    display: block;
    width: 100%;
    background-image: url(/static/258c3f79/plugin/cloudbees-license/images/32x32/shield.png);
    background-position: 10px center;
    background-repeat: no-repeat;
    text-align: left;
    padding-left: 52px !important;
    font-size: 14px;
    line-height: 32px;
}

.cloudbees-license.alert.warning-icon {
    background-image: url(/static/258c3f79/plugin/cloudbees-license/images/32x32/warning.png) !important;
    background-position: 10px center !important;
    background-repeat: no-repeat !important;
}

.cloudbees-license form {
    float: right;
    margin-left: 20px;
}

.cloudbees-license.alert {
    padding: 6px;
    margin-bottom: 15px;
    border: 1px solid transparent;
    border-radius: 4px;
}

.cloudbees-license.alert-success {
    background-color: #dff0d8;
    border-color: #d6e9c6;
    color: #3c763d;
}

.cloudbees-license.alert-info {
    background-color: #d9edf7;
    border-color: #bce8f1;
    color: #31708f;
}

.cloudbees-license.alert-warning {
    background-color: #fcf8e3;
    border-color: #faebcc;
    color: #8a6d3b;
}

.cloudbees-license.alert-danger {
    background-color: #f2dede;
    border-color: #ebccd1;
    color: #a94442;
}

@media (max-width: 1024px) {
    .cloudbees-license {
        line-height: 18px;
    }
}</style><link type="text/css" rel="stylesheet" href="https://alm-jenkins216.hc.cloud.uk.hsbc:8706/userContent/layout/style.css"/><script src="/static/258c3f79/jsbundles/vendors.js" type="text/javascript"></script><script src="/static/258c3f79/jsbundles/sortable-drag-drop.js" type="text/javascript"></script><script defer="true" src="/static/258c3f79/jsbundles/app.js" type="text/javascript"></script><link rel="stylesheet" href="/static/258c3f79/css/customized.css" type="text/css"></link></head><body data-model-type="hudson.model.Hudson" id="jenkins" class="yui-skin-sam two-column jenkins-2.452.4.1" data-version="2.452.4.1"><a href="#skip2content" class="jenkins-skip-link">Skip to content</a><script src="/static/258c3f79/unified-ui-assets/jsbundles/header.js" type="text/javascript"></script><link rel="stylesheet" href="/static/258c3f79/unified-ui-assets/jsbundles/header.css" type="text/css"></link><link rel="stylesheet" href="/static/258c3f79/unified-ui-assets/jsbundles/honey-theme.css" type="text/css"></link><header id="header-honeyui" class="hui"><nav class="navbar hui-custom navbar--no-leading-space navbar-expand-md navbar-dark bg-dark"><div class="nav-link btn btn-link"><div style="width:24px;height:24px"></div></div><a href="" class="navbar-brand"><svg xmlns="http://www.w3.org/2000/svg" class="sda-logo" height="70" viewBox="0 0 184 64" width="201.25"><g fill-rule="evenodd" fill="none"><path d="M0 0h184v64H0z"></path><path fill-rule="nonzero" d="M21.313 23.112c.84 0 1.55983-.13533 2.1595-.406.59967-.27067 1.10483-.66033 1.5155-1.169l-.742-.805c-.07467-.07933-.16333-.119-.266-.119-.06067 0-.11667.01283-.168.0385-.05133.02567-.10033.0595-.147.1015-.154.14-.30917.25783-.4655.3535-.15633.09567-.322.17383-.497.2345-.175.06067-.36633.105-.574.133-.20767.028-.43983.042-.6965.042-.42933 0-.83067-.0805-1.204-.2415-.37333-.161-.69767-.39317-.973-.6965-.27533-.30333-.4935-.67667-.6545-1.12-.161-.44333-.2415-.94967-.2415-1.519 0-.55067.0805-1.0465.2415-1.4875.161-.441.385-.8155.672-1.1235.287-.308.63-.54483 1.029-.7105.399-.16567.8365-.2485 1.3125-.2485.406 0 .74083.042 1.0045.126.26367.084.48183.175.6545.273.17267.098.315.189.427.273.112.084.22167.126.329.126.10267 0 .18317-.021.2415-.063.05833-.042.10617-.091.1435-.147l.63-.875c-.40133-.39667-.88317-.71517-1.4455-.9555-.56233-.24033-1.21917-.3605-1.9705-.3605-.77 0-1.4735.12717-2.1105.3815s-1.18417.61017-1.6415 1.0675c-.45733.45733-.812 1.00217-1.064 1.6345-.252.63233-.378 1.32883-.378 2.0895 0 .75133.11317 1.44433.3395 2.079.22633.63467.55183 1.18067.9765 1.638.42467.45733.93683.81433 1.5365 1.071.59967.25667 1.27517.385 2.0265.385zM28.152 23V12.598h-1.729V23h1.729zm5.019.105c.53667 0 1.02317-.08633 1.4595-.259.43633-.17267.8085-.42 1.1165-.742.308-.322.546-.71167.714-1.169.168-.45733.252-.97067.252-1.54 0-.56467-.084-1.07567-.252-1.533-.168-.45733-.406-.84467-.714-1.162-.308-.31733-.68017-.56233-1.1165-.735s-.92283-.259-1.4595-.259-1.02433.08633-1.463.259c-.43867.17267-.81317.41767-1.1235.735s-.55067.70467-.721 1.162c-.17033.45733-.2555.96833-.2555 1.533 0 .56933.08517 1.08267.2555 1.54s.41067.847.721 1.169c.31033.322.68483.56933 1.1235.742.43867.17267.92633.259 1.463.259zm0-1.33c-.60667 0-1.05467-.20183-1.344-.6055-.28933-.40367-.434-.9905-.434-1.7605 0-.77.14467-1.35917.434-1.7675.28933-.40833.73733-.6125 1.344-.6125.59733 0 1.0395.203 1.3265.609.287.406.4305.99633.4305 1.771 0 .77467-.1435 1.36267-.4305 1.764-.287.40133-.72917.602-1.3265.602zm7.168 1.337c.238 0 .46083-.0245.6685-.0735.20767-.049.40133-.11667.581-.203.17967-.08633.34883-.19133.5075-.315.15867-.12367.31267-.26017.462-.4095l.119.574c.07.21.217.315.441.315h1.057v-7.182h-1.729v5.194c-.23333.23333-.48067.4165-.742.5495s-.54133.1995-.84.1995c-.406 0-.7105-.12017-.9135-.3605-.203-.24033-.3045-.57983-.3045-1.0185v-4.564h-1.729v4.564c0 .40133.0525.76883.1575 1.1025.105.33367.26017.62183.4655.8645.20533.24267.4585.4305.7595.5635.301.133.6475.1995 1.0395.1995zm7.847-.007c.252 0 .4865-.02683.7035-.0805.217-.05367.42-.1295.609-.2275s.36517-.21467.5285-.35c.16333-.13533.31967-.287.469-.455l.14.693c.07.21.217.315.441.315h1.057V12.598h-1.729v3.794c-.224-.21467-.47833-.38617-.763-.5145-.28467-.12833-.62767-.1925-1.029-.1925-.47133 0-.896.0945-1.274.2835-.378.189-.70233.4515-.973.7875-.27067.336-.47833.73267-.623 1.19-.14467.45733-.217.952-.217 1.484 0 .588.06417 1.11067.1925 1.568s.31033.84117.546 1.1515c.23567.31033.51683.54717.8435.7105.32667.16333.686.245 1.078.245zm.588-1.372c-.224 0-.427-.04083-.609-.1225-.182-.08167-.336-.21467-.462-.399-.126-.18433-.22283-.42233-.2905-.714-.06767-.29167-.1015-.6475-.1015-1.0675 0-.41533.03967-.77583.119-1.0815.07933-.30567.1925-.55767.3395-.756.147-.19833.32667-.3465.539-.4445.21233-.098.44917-.147.7105-.147.266 0 .5145.049.7455.147.231.098.4445.266.6405.504v3.22c-.224.27533-.462.48767-.714.637-.252.14933-.55767.224-.917.224zM57.923 23c.588 0 1.11067-.07467 1.568-.224.45733-.14933.84233-.35583 1.155-.6195.31267-.26367.55067-.5775.714-.9415.16333-.364.245-.76067.245-1.19 0-1.19-.69533-1.94133-2.086-2.254.31267-.10733.58217-.238.8085-.392.22633-.154.413-.3255.56-.5145.147-.189.2555-.392.3255-.609.07-.217.105-.4445.105-.6825 0-.41533-.07-.78867-.21-1.12-.14-.33133-.3605-.61367-.6615-.847-.301-.23333-.686-.413-1.155-.539-.469-.126-1.03483-.189-1.6975-.189h-3.493V23h3.822zm-.434-5.74h-1.505v-2.926h1.61c.63467 0 1.10133.112 1.4.336.29867.224.448.59967.448 1.127 0 .50867-.16567.87967-.497 1.113-.33133.23333-.81667.35-1.456.35zm.406 4.27h-1.911v-2.968h1.904c.33133 0 .61367.03267.847.098.23333.06533.42467.15983.574.2835.14933.12367.259.273.329.448s.105.37217.105.5915c0 .21-.0315.40833-.0945.595-.063.18667-.16567.35-.308.49-.14233.14-.33133.252-.567.336-.23567.084-.5285.126-.8785.126zm8.281 1.575c.252 0 .51217-.01867.7805-.056.26833-.03733.532-.1015.791-.1925s.5075-.21233.7455-.364c.238-.15167.45267-.3395.644-.5635l-.504-.63c-.03733-.05133-.08167-.091-.133-.119-.05133-.028-.112-.042-.182-.042-.10733 0-.21817.03267-.3325.098-.11433.06533-.24733.13767-.399.217-.15167.07933-.329.15167-.532.217s-.4445.098-.7245.098c-.56933 0-1.02667-.17267-1.372-.518-.34533-.34533-.54367-.88667-.595-1.624h4.438c.07933 0 .14467-.0105.196-.0315.05133-.021.09217-.056.1225-.105.03033-.049.05133-.11667.063-.203.01167-.08633.0175-.19483.0175-.3255 0-.518-.077-.97883-.231-1.3825-.154-.40367-.36867-.74433-.644-1.022s-.60317-.48883-.9835-.6335c-.38033-.14467-.79683-.217-1.2495-.217-.53667 0-1.0185.09333-1.4455.28-.427.18667-.78867.43983-1.085.7595-.29633.31967-.52383.69533-.6825 1.127-.15867.43167-.238.8925-.238 1.3825 0 .63.091 1.18417.273 1.6625.182.47833.43167.87967.749 1.204s.69067.56933 1.12.735c.42933.16567.89367.2485 1.393.2485zm1.47-4.522h-3.248c.07933-.52267.26133-.92633.546-1.211.28467-.28467.679-.427 1.183-.427.25667 0 .48067.04317.672.1295.19133.08633.35.20417.476.3535s.21933.32317.28.5215c.06067.19833.091.4095.091.6335zm6.013 4.522c.252 0 .51217-.01867.7805-.056.26833-.03733.532-.1015.791-.1925s.5075-.21233.7455-.364c.238-.15167.45267-.3395.644-.5635l-.504-.63c-.03733-.05133-.08167-.091-.133-.119-.05133-.028-.112-.042-.182-.042-.10733 0-.21817.03267-.3325.098-.11433.06533-.24733.13767-.399.217-.15167.07933-.329.15167-.532.217s-.4445.098-.7245.098c-.56933 0-1.02667-.17267-1.372-.518-.34533-.34533-.54367-.88667-.595-1.624h4.438c.07933 0 .14467-.0105.196-.0315.05133-.021.09217-.056.1225-.105.03033-.049.05133-.11667.063-.203.01167-.08633.0175-.19483.0175-.3255 0-.518-.077-.97883-.231-1.3825-.154-.40367-.36867-.74433-.644-1.022s-.60317-.48883-.9835-.6335c-.38033-.14467-.79683-.217-1.2495-.217-.53667 0-1.0185.09333-1.4455.28-.427.18667-.78867.43983-1.085.7595-.29633.31967-.52383.69533-.6825 1.127-.15867.43167-.238.8925-.238 1.3825 0 .63.091 1.18417.273 1.6625.182.47833.43167.87967.749 1.204s.69067.56933 1.12.735c.42933.16567.89367.2485 1.393.2485zm1.47-4.522h-3.248c.07933-.52267.26133-.92633.546-1.211.28467-.28467.679-.427 1.183-.427.25667 0 .48067.04317.672.1295.19133.08633.35.20417.476.3535s.21933.32317.28.5215c.06067.19833.091.4095.091.6335zm4.823 4.529c.462 0 .8715-.0595 1.2285-.1785.357-.119.65683-.28467.8995-.497.24267-.21233.427-.46433.553-.756.126-.29167.189-.61017.189-.9555 0-.28933-.05017-.53667-.1505-.742s-.23333-.3815-.399-.5285c-.16567-.147-.3535-.26833-.5635-.364a6.97263 6.97263 0 00-.644-.2555c-.21933-.07467-.434-.1435-.644-.2065-.21-.063-.39783-.13417-.5635-.2135-.16567-.07933-.29867-.1715-.399-.2765-.10033-.105-.1505-.2345-.1505-.3885 0-.23333.098-.42233.294-.567.196-.14467.46433-.217.805-.217.21933 0 .4095.02333.5705.07.161.04667.3045.09917.4305.1575.126.05833.23683.11083.3325.1575s.1855.07.2695.07c.07933 0 .14467-.01517.196-.0455.05133-.03033.10033-.08283.147-.1575l.392-.623c-.27067-.266-.609-.48067-1.015-.644-.406-.16333-.85633-.245-1.351-.245-.44333 0-.83417.0595-1.1725.1785-.33833.119-.62067.27767-.847.476-.22633.19833-.39783.4305-.5145.6965-.11667.266-.175.546-.175.84 0 .31733.05017.588.1505.812.10033.224.23333.413.399.567.16567.154.35467.28117.567.3815a6.02225 6.02225 0 00.651.2625c.22167.07467.43867.1435.651.2065.21233.063.40133.13417.567.2135.16567.07933.29867.17267.399.28.10033.10733.1505.245.1505.413 0 .112-.02217.2205-.0665.3255-.04433.105-.11433.19717-.21.2765-.09567.07933-.217.1435-.364.1925-.147.049-.32317.0735-.5285.0735-.26133 0-.47717-.03033-.6475-.091-.17033-.06067-.3185-.126-.4445-.196s-.23683-.13533-.3325-.196-.19717-.091-.3045-.091c-.10733 0-.196.021-.266.063-.07.042-.13067.10267-.182.182l-.399.658c.14.126.30217.24267.4865.35.18433.10733.385.20067.602.28.217.07933.44333.14117.679.1855.23567.04433.47717.0665.7245.0665zm9.611 0c.56 0 1.057-.08517 1.491-.2555.434-.17033.8015-.40367 1.1025-.7.301-.29633.52967-.644.686-1.043.15633-.399.2345-.82717.2345-1.2845 0-.42-.0665-.777-.1995-1.071-.133-.294-.30917-.5425-.5285-.7455-.21933-.203-.469-.371-.749-.504a8.25783 8.25783 0 00-.8575-.35c-.29167-.10033-.5775-.19483-.8575-.2835-.28-.08867-.52967-.189-.749-.301-.21933-.112-.3955-.24733-.5285-.406-.133-.15867-.1995-.357-.1995-.595 0-.18667.03267-.35817.098-.5145.06533-.15633.1645-.2905.2975-.4025.133-.112.29633-.1995.49-.2625s.42117-.0945.6825-.0945c.28933 0 .54017.03617.7525.1085.21233.07233.39783.15283.5565.2415.15867.08867.29633.16917.413.2415s.22167.1085.315.1085.17383-.021.2415-.063c.06767-.042.12717-.11433.1785-.217l.462-.889c-.35467-.336-.784-.59733-1.288-.784s-1.05467-.28-1.652-.28c-.52733 0-.99633.08167-1.407.245-.41067.16333-.75717.38033-1.0395.651-.28233.27067-.497.581-.644.931-.147.35-.2205.70933-.2205 1.078 0 .462.0665.8505.1995 1.1655.133.315.30917.5775.5285.7875.21933.21.469.38033.749.511s.56467.24267.854.336c.28933.09333.574.17967.854.259.28.07933.52967.17617.749.2905.21933.11433.3955.2555.5285.4235.133.168.1995.385.1995.651 0 .476-.147.84117-.441 1.0955-.294.25433-.70933.3815-1.246.3815-.35 0-.64983-.04783-.8995-.1435-.24967-.09567-.46667-.20183-.651-.3185-.18433-.11667-.34417-.22283-.4795-.3185s-.26133-.1435-.378-.1435c-.08867 0-.16917.02217-.2415.0665-.07233.04433-.13417.09917-.1855.1645l-.546.903c.196.20067.41883.38267.6685.546.24967.16333.518.30333.805.42.287.11667.58683.2065.8995.2695.31267.063.63.0945.952.0945zm8.82-.112c.76533 0 1.463-.12367 2.093-.371.63-.24733 1.169-.595 1.617-1.043.448-.448.79567-.98233 1.043-1.603.24733-.62067.371-1.302.371-2.044s-.12367-1.42333-.371-2.044-.595-1.15383-1.043-1.5995c-.448-.44567-.987-.79333-1.617-1.043-.63-.24967-1.32767-.3745-2.093-.3745h-3.864V23h3.864zm0-1.498h-1.967v-7.126h1.967c.49467 0 .93917.08167 1.3335.245.39433.16333.72917.39783 1.0045.7035.27533.30567.48767.679.637 1.12.14933.441.224.93917.224 1.4945s-.07467 1.0535-.224 1.4945c-.14933.441-.36167.81433-.637 1.12-.27533.30567-.61017.54017-1.0045.7035-.39433.16333-.83883.245-1.3335.245zM105.11 23c.15867 0 .29283-.04433.4025-.133.10967-.08867.18317-.18667.2205-.294l.756-2.065h4.193l.756 2.065c.04667.12133.1225.22283.2275.3045.105.08167.23917.1225.4025.1225h1.456l-3.976-10.122h-1.925L103.647 23h1.463zm5.089-3.822h-3.227l1.232-3.353c.06067-.154.1225-.33367.1855-.539s.12717-.427.1925-.665c.06533.238.13183.4585.1995.6615.06767.203.1295.37917.1855.5285l1.232 3.367z" fill="#FFF"></path><path fill-rule="nonzero" d="M25.276 50.192c1.416 0 2.626-.228 3.63-.684 1.004-.456 1.858-1.092 2.562-1.908l-.912-.996c-.104-.104-.22-.156-.348-.156-.128 0-.26.06-.396.18-.304.272-.606.506-.906.702-.3.196-.624.358-.972.486s-.726.222-1.134.282c-.408.06-.868.09-1.38.09-.84 0-1.624-.152-2.352-.456-.728-.304-1.358-.746-1.89-1.326-.532-.58-.952-1.29-1.26-2.13-.308-.84-.462-1.796-.462-2.868 0-1.04.154-1.98.462-2.82.308-.84.738-1.552 1.29-2.136.552-.584 1.21-1.034 1.974-1.35.764-.316 1.606-.474 2.526-.474.512 0 .964.038 1.356.114.392.076.736.17 1.032.282.296.112.552.232.768.36.216.128.404.248.564.36.16.112.298.206.414.282.116.076.226.114.33.114.136 0 .24-.026.312-.078.072-.052.136-.118.192-.198l.756-1.068c-.712-.672-1.526-1.204-2.442-1.596-.916-.392-2.006-.588-3.27-.588-1.28 0-2.45.214-3.51.642-1.06.428-1.97 1.03-2.73 1.806s-1.35 1.704-1.77 2.784c-.42 1.08-.63 2.268-.63 3.564 0 1.296.196 2.484.588 3.564.392 1.08.944 2.006 1.656 2.778.712.772 1.572 1.372 2.58 1.8 1.008.428 2.132.642 3.372.642zM37.288 50V32.804H34.96V50h2.328z" fill="#95DBFF"></path></g></svg><span class="sda-product-section">Operations Center</span></a><div class="navbar-nav navbar-nav--grow"><div class="nav-item"></div></div></nav></header><div data-show-logout="true" data-help-url-feedback="https://cloudbees.com/feedback" data-use-fips-logo="false" data-navigation-cd-available="false" data-override-login="false" data-help-url-documentation="https://cloudbees.com/r/doc/ci" data-admin-monitors-security-number="0" data-help-url-troubleshooting="https://cloudbees.com/r/trouble/ci" data-admin-monitors-security-url="/administrativeMonitorsApi/securityPopupContent" data-navigation-analytics-available="false" data-navigation-ci-available="false" data-use-security="true" data-admin-monitors-non-security-number="0" id="header-data-holder" data-show-login="false" data-signup-url="/signup" data-help-url-whats-new="https://cloudbees.com/r/whats-new/ci" data-show-navigation="true" data-header-label="" data-root-url="/" data-admin-monitors-non-security-url="/administrativeMonitorsApi/nonSecurityPopupContent" data-help-url-onboarding="https://cloudbees.com/r/onboarding-trad/ci" data-navigation-cd-url="https://cloudbees.com/r/sda-cd-1" data-user-url="/user/45276799" data-help-url-new-support-request="https://cloudbees.com/r/support/request/new/ci" style="display:none" data-use-old-logo="false" data-logout-url="/logout" data-search-url="/search/" data-user-name="Justin Chow" data-navigation-analytics-url="https://cloudbees.com/r/sda-analytics-1" data-is-anonymous="false" data-product-name="Operations Center"></div><template id="plugin-login-holder" style="display:none"></template><div id="breadcrumbBar" class="jenkins-breadcrumbs" aria-label="breadcrumb"><ol class="jenkins-breadcrumbs__list" id="breadcrumbs"><li class="jenkins-breadcrumbs__list-item"><a href="/" class="model-link">Dashboard</a></li><li class="children" data-href="/"></li></ol></div><div id="page-body" class="app-page-body app-page-body--two-column clear"><div id="side-panel" class="app-page-body__sidebar "></div><div id="main-panel"><a id="skip2content"></a><h1>Access Denied</h1><p class="error">45276799 is missing the Job/ExtendedRead permission</p></div></div><footer class="page-footer jenkins-mobile-hide"><div class="page-footer__flex-row"><div class="page-footer__footer-id-placeholder" id="footer"></div><div style="display: none;"><span data-root-display-name="Master216" id="extra-left-top-nav"><li class="item"><a href="https://alm-jenkins-cjoc.hc.cloud.uk.hsbc:8706/" class=" inside">Jenkins</a></li><li class="separator"></li></span></div><script src='/adjuncts/258c3f79/com/cloudbees/opscenter/client/plugin/OperationsCenterPageDecorator/footer.js' type='text/javascript'></script><div class="page-footer__links"><a class="jenkins-button jenkins-button--tertiary rest-api" href="api/">REST API</a><button type="button" class="jenkins-button jenkins-button--tertiary jenkins_ver" data-dropdown="true">

    CloudBees CI Operations Center 2.452.4.1-rolling
  </button><template><div class="jenkins-dropdown"><template data-dropdown-icon="&lt;svg aria-hidden=&quot;true&quot; xmlns=&quot;http://www.w3.org/2000/svg&quot; viewBox=&quot;0 0 512 512&quot;&gt;&lt;path d=&quot;M384 224v184a40 40 0 01-40 40H104a40 40 0 01-40-40V168a40 40 0 0140-40h167.48M336 64h112v112M224 288L440 72&quot; fill=&quot;none&quot; stroke=&quot;currentColor&quot; stroke-linecap=&quot;round&quot; stroke-linejoin=&quot;round&quot; stroke-width=&quot;32&quot;/&gt;&lt;/svg&gt;
" data-dropdown-text="Website" data-dropdown-type="ITEM" data-dropdown-href="https://release-notes.cloudbees.com/product/Traditional+platforms+-+Operations+Center"></template></div></template></div></div></footer></body></html>