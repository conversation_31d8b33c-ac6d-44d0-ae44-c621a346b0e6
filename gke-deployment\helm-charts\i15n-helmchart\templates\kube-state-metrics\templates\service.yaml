apiVersion: v1
kind: Service
metadata:
  name: {{ template "kube-state-metrics.fullname" . }}
  namespace: {{ template "kube-state-metrics.namespace" . }}
  labels:
    {{- include "kube-state-metrics.labels" . | indent 4 }}
  annotations:
    {{- if .Values.prometheusScrape }}
    prometheus.io/scrape: '{{ .Values.prometheusScrape }}'
    {{- end }}
    {{- if .Values.customService.annotations }}
    {{- toYaml .Values.customService.annotations | nindent 4 }}
    {{- end }}
spec:
  type: "{{ .Values.customService.type }}"
  ports:
  - name: "http"
    protocol: TCP
    port: {{ .Values.customService.port | default 8080}}
  {{- if .Values.customService.nodePort }}
    nodePort: {{ .Values.customService.nodePort }}
  {{- end }}
    targetPort: {{ .Values.customService.port | default 8080}}
  {{ if .Values.selfMonitor.enabled }}
  - name: "metrics"
    protocol: TCP
    port: {{ .Values.selfMonitor.telemetryPort | default 8081 }}
    targetPort: {{ .Values.selfMonitor.telemetryPort | default 8081 }}
  {{ end }}
{{- if .Values.customService.loadBalancerIP }}
  loadBalancerIP: "{{ .Values.customService.loadBalancerIP }}"
{{- end }}
  selector:
    {{- include "kube-state-metrics.selectorLabels" . | indent 4 }}
