{{- if and .Values.testFramework.enabled .Values.rbac.pspEnabled -}}
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: {{ template "grafana.fullname" . }}-test
  namespace: {{ template "grafana.namespace" . }}
  labels:
    {{- include "grafana.labels" . | nindent 4 }}
rules:
- apiGroups:      ['policy']
  resources:      ['podsecuritypolicies']
  verbs:          ['use']
  resourceNames:  [{{ template "grafana.fullname" . }}-test]
{{- end }}
