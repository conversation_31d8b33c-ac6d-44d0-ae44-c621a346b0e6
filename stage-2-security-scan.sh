#!/bin/bash
set -euo pipefail

# Stage 2: Security Scanning
# Usage: ./stage-2-security-scan.sh <project-name> [options]

# Function to print usage
usage() {
    echo "Usage: $0 <project-name> [options]"
    echo ""
    echo "Arguments:"
    echo "  project-name    Name of the project directory (e.g., change-dashboard, risc)"
    echo ""
    echo "Options:"
    echo "  --skip-sast     Skip SAST (Static Application Security Testing)"
    echo "  --skip-deps     Skip dependency vulnerability scanning"
    echo "  --force         Continue even if scans fail"
    echo "  --help, -h      Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 change-dashboard"
    echo "  $0 risc --skip-sast"
    echo "  $0 change-dashboard --force"
    exit 1
}

# Parse arguments
if [ $# -lt 1 ]; then
    echo "Error: Project name is required"
    usage
fi

PROJECT_NAME="$1"
shift

# Default options
SKIP_SAST=false
SKIP_DEPS=false
FORCE_CONTINUE=false

# Parse options
while [[ $# -gt 0 ]]; do
    case $1 in
        --skip-sast)
            SKIP_SAST=true
            shift
            ;;
        --skip-deps)
            SKIP_DEPS=true
            shift
            ;;
        --force)
            FORCE_CONTINUE=true
            shift
            ;;
        --help|-h)
            usage
            ;;
        *)
            echo "Error: Unknown option $1"
            usage
            ;;
    esac
done

echo "=== Stage 2: Security Scanning for ${PROJECT_NAME} ==="
echo "Configuration:"
echo "  Project: ${PROJECT_NAME}"
echo "  Skip SAST: ${SKIP_SAST}"
echo "  Skip Dependencies: ${SKIP_DEPS}"
echo "  Force Continue: ${FORCE_CONTINUE}"

# Validate project directory exists
if [ ! -d "${PROJECT_NAME}" ]; then
    echo "Error: Project directory '${PROJECT_NAME}' not found"
    exit 1
fi

# Check if previous stage completed successfully
BUILD_INFO_FILE="${PROJECT_NAME}/target/build-info.properties"
if [ ! -f "${BUILD_INFO_FILE}" ]; then
    echo "Error: Build info file not found: ${BUILD_INFO_FILE}"
    echo "Please run stage-1-maven-build.sh first"
    exit 1
fi

# Load build info
source "${BUILD_INFO_FILE}"
if [ "${build.status:-}" != "success" ]; then
    echo "Error: Previous Maven build stage failed"
    echo "Please ensure stage-1-maven-build.sh completed successfully"
    exit 1
fi

echo "Previous stage info:"
echo "  Build timestamp: ${build.timestamp:-unknown}"
echo "  JAR file: ${jar.file:-unknown}"

# Load build configuration
BUILD_CONFIG_FILE="${PROJECT_NAME}/.devops/build.properties"
if [ -f "${BUILD_CONFIG_FILE}" ]; then
    echo "Loading build configuration from ${BUILD_CONFIG_FILE}"
    source "${BUILD_CONFIG_FILE}"
else
    echo "Warning: No build.properties found, using defaults"
    export sast_scan_enable=false
    export container_scan_enable=false
fi

# Validate devops-helper exists
DEVOPS_HELPER_SCRIPT="devops-helper/src/devops/scripts/build/actions.bash"
if [ ! -f "${DEVOPS_HELPER_SCRIPT}" ]; then
    echo "Error: devops-helper script not found at ${DEVOPS_HELPER_SCRIPT}"
    exit 1
fi

# Change to project directory
cd "${PROJECT_NAME}"

# Create scan results directory
SCAN_RESULTS_DIR="target/scan-results"
mkdir -p "${SCAN_RESULTS_DIR}"

SCAN_SUCCESS=true
SCAN_SUMMARY=""

echo ""
echo "=== Running Security Scans ==="

# SAST Scanning
if [ "${SKIP_SAST}" = "false" ] && [ "${sast_scan_enable:-false}" = "true" ]; then
    echo ""
    echo "--- SAST (Static Application Security Testing) ---"
    
    # Use devops-helper for SAST scan
    echo "Running SAST scan using devops-helper..."
    ../"${DEVOPS_HELPER_SCRIPT}" sast_scan
    
    SAST_EXIT_CODE=$?
    if [ ${SAST_EXIT_CODE} -eq 0 ]; then
        echo "✅ SAST scan completed successfully"
        SCAN_SUMMARY="${SCAN_SUMMARY}SAST: ✅ PASSED\n"
    else
        echo "❌ SAST scan failed with exit code ${SAST_EXIT_CODE}"
        SCAN_SUMMARY="${SCAN_SUMMARY}SAST: ❌ FAILED (${SAST_EXIT_CODE})\n"
        if [ "${FORCE_CONTINUE}" = "false" ]; then
            SCAN_SUCCESS=false
        fi
    fi
else
    echo "--- SAST Scanning (SKIPPED) ---"
    SCAN_SUMMARY="${SCAN_SUMMARY}SAST: ⏭️  SKIPPED\n"
fi

# Dependency Vulnerability Scanning
if [ "${SKIP_DEPS}" = "false" ]; then
    echo ""
    echo "--- Dependency Vulnerability Scanning ---"
    
    # Check if Maven dependency check plugin is available
    if mvn help:describe -Dplugin=org.owasp:dependency-check-maven > /dev/null 2>&1; then
        echo "Running OWASP dependency check..."
        mvn org.owasp:dependency-check-maven:check -DfailBuildOnCVSS=7 || {
            DEPS_EXIT_CODE=$?
            echo "❌ Dependency scan failed with exit code ${DEPS_EXIT_CODE}"
            SCAN_SUMMARY="${SCAN_SUMMARY}Dependencies: ❌ FAILED (${DEPS_EXIT_CODE})\n"
            if [ "${FORCE_CONTINUE}" = "false" ]; then
                SCAN_SUCCESS=false
            fi
        }
        
        if [ $? -eq 0 ]; then
            echo "✅ Dependency vulnerability scan completed"
            SCAN_SUMMARY="${SCAN_SUMMARY}Dependencies: ✅ PASSED\n"
        fi
    else
        echo "⚠️  OWASP dependency check plugin not configured"
        echo "Consider adding it to your pom.xml for dependency vulnerability scanning"
        SCAN_SUMMARY="${SCAN_SUMMARY}Dependencies: ⚠️  NOT CONFIGURED\n"
    fi
else
    echo "--- Dependency Scanning (SKIPPED) ---"
    SCAN_SUMMARY="${SCAN_SUMMARY}Dependencies: ⏭️  SKIPPED\n"
fi

# License Scanning (optional)
echo ""
echo "--- License Compliance Check ---"
if mvn help:describe -Dplugin=org.codehaus.mojo:license-maven-plugin > /dev/null 2>&1; then
    echo "Running license compliance check..."
    mvn license:check || {
        echo "⚠️  License compliance issues found (non-blocking)"
        SCAN_SUMMARY="${SCAN_SUMMARY}Licenses: ⚠️  ISSUES FOUND\n"
    }
    
    if [ $? -eq 0 ]; then
        echo "✅ License compliance check passed"
        SCAN_SUMMARY="${SCAN_SUMMARY}Licenses: ✅ PASSED\n"
    fi
else
    echo "ℹ️  License plugin not configured"
    SCAN_SUMMARY="${SCAN_SUMMARY}Licenses: ℹ️  NOT CONFIGURED\n"
fi

# Code Quality Check (optional)
echo ""
echo "--- Code Quality Analysis ---"
if command -v sonar-scanner &> /dev/null; then
    echo "SonarQube scanner available"
    # Note: This would require SonarQube server configuration
    echo "ℹ️  SonarQube analysis can be run separately if server is configured"
    SCAN_SUMMARY="${SCAN_SUMMARY}Code Quality: ℹ️  AVAILABLE\n"
else
    echo "ℹ️  SonarQube scanner not available"
    SCAN_SUMMARY="${SCAN_SUMMARY}Code Quality: ℹ️  NOT AVAILABLE\n"
fi

# Update build info
echo ""
echo "=== Updating Build Information ==="

# Append scan results to build info
cat >> "target/build-info.properties" <<EOF

# Security scan results
scan.timestamp=$(date -u +"%Y-%m-%dT%H:%M:%SZ")
scan.stage=security-scan
scan.status=$([ "${SCAN_SUCCESS}" = "true" ] && echo "success" || echo "failed")
scan.sast.enabled=${sast_scan_enable:-false}
scan.sast.skipped=${SKIP_SAST}
scan.deps.skipped=${SKIP_DEPS}
scan.force_continue=${FORCE_CONTINUE}
EOF

# Create scan summary report
SCAN_REPORT_FILE="target/scan-results/scan-summary.txt"
cat > "${SCAN_REPORT_FILE}" <<EOF
Security Scan Summary for ${PROJECT_NAME}
==========================================
Scan Date: $(date)
Project Version: ${project.version:-unknown}

Results:
$(echo -e "${SCAN_SUMMARY}")

Overall Status: $([ "${SCAN_SUCCESS}" = "true" ] && echo "✅ PASSED" || echo "❌ FAILED")
EOF

echo "✅ Scan summary saved to ${SCAN_REPORT_FILE}"

# Final result
if [ "${SCAN_SUCCESS}" = "true" ]; then
    echo ""
    echo "=== Stage 2 Complete: Security Scanning ==="
    echo "✅ All enabled security scans passed"
    echo ""
    echo "Scan Summary:"
    echo -e "${SCAN_SUMMARY}"
    echo ""
    echo "Next stage: ./stage-3-image-build.sh ${PROJECT_NAME}"
else
    echo ""
    echo "=== Stage 2 Failed: Security Scanning ==="
    echo "❌ One or more security scans failed"
    echo ""
    echo "Scan Summary:"
    echo -e "${SCAN_SUMMARY}"
    echo ""
    echo "Options:"
    echo "1. Fix the security issues and re-run this stage"
    echo "2. Use --force flag to continue despite failures"
    echo "3. Use --skip-* flags to skip specific scans"
    exit 1
fi

# Return to original directory
cd ..
