validation_group:
  failed:
  - UNITY.base.configuration.item:
      - fields:
        - field: .configItems
          type: array
          expected:
            - type: "AND"
              rules:          
                - "any:name:^GBM-HSS-UNITY-ONE-CUSTODY$"  # Field must not be null 
        type: "OR"  # At least one of these fields must meet its expected conditions            
  - UNITY.ansible.configuration.item:
      - fields:
        - field: .configItems
          type: array
          expected:
            - type: "AND"
              rules:          
                - "any:name:^HKL20071946$"  # Field must not be null
            - type: "AND"
              rules:          
                - "any:name:^HKL20071285$"  # Field must not be null
            - type: "AND"
              rules:          
                - "any:name:^GBM-HSS-UNITY-ONE-CUSTODY$"  # Field must not be null
        - field: .changeTasks
          type: array
          expected:
            - type: "AND"
              rules:          
                - "any:releaseTool:^GCP Deployment Service$"  # Field must not be null
        - field: .description
          type: field
          expected:
            - type: "AND"
              rules:
                - "regex:.*-UNITY2.0-MESH-CUSTODY-ANSIBLE-NOTPAM"  # At least one item in the array must start with https://                                        
        type: "OR"  # At least one of these fields must meet its expected conditions            
  - UNITY.change.tasks:  
    - fields:
        - field: .changeTasks
          type: array
          expected:
            - type: "AND"
              rules:          
                - "any:packageId:^pkg:bitbucket\/unity-i15n-poc\/devops-helper@d77c414cf2c5b3e2897d7b24d0256b392ad52986[?]artifactinstance=gbmt-bitbucket.prd.fx.gbm.cloud.uk.hsbc&branchname=master&playbook=playbooks/jenkins-helm-chart-deployment-trigger-v2.yaml.*"  # Field must not be null    
            - type: "AND"
              rules:          
                - "any:releaseTool:^Ansible(\\s+Global)?$"  # Field must not be null    
            - type: "AND"
              rules:          
                - "any:json:description"  # Field must not be null
        - field: .changeTasks
          type: array
          expected:
            - type: "AND"
              rules:          
                - "any:releaseTool:^GCP Deployment Service$"  # Field must not be null
        - field: .description
          type: field
          expected:
            - type: "AND"
              rules:
                - "regex:.*-UNITY2.0-MESH-CUSTODY-ANSIBLE-NOTPAM"  # At least one item in the array must start with https://                                 
      type: "OR"  # At least one of these fields must meet its expected conditions            
