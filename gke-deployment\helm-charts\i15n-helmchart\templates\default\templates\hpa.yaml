{{- if .Values.autoscaling.enabled }}
{{- $k8sVersion := .Capabilities.KubeVersion.GitVersion -}}
{{- if semverCompare ">=1.23-0" $k8sVersion -}}
apiVersion: autoscaling/v2
{{- else -}}
apiVersion: autoscaling/v2beta2
{{- end }}
kind: HorizontalPodAutoscaler
metadata:
  name: {{ include "i15n-helmchart.fullname" . }}
  namespace: {{ .Values.namespace }}
  labels:
    {{- include "i15n-helmchart.labels" . | nindent 4 }}
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: {{ include "i15n-helmchart.fullname" . }}
  minReplicas: {{ .Values.autoscaling.minReplicas }}
  maxReplicas: {{ .Values.autoscaling.maxReplicas }}
  metrics:
  {{- if .Values.autoscaling.targetCPUUtilizationPercentage }}
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: {{ .Values.autoscaling.targetCPUUtilizationPercentage }}
  {{- end }}
  {{- if .Values.autoscaling.targetCPUUtilizationValue }}
  - type: Resource
    resource:
      name: cpu
      target:
        type: AverageValue
        averageValue: {{ .Values.autoscaling.targetCPUUtilizationValue }}
  {{- end }}
  {{- if .Values.autoscaling.targetMemoryUtilizationPercentage }}
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: {{ .Values.autoscaling.targetMemoryUtilizationPercentage }}
  {{- end }}
  {{- if .Values.autoscaling.behavior.enabled }}
  behavior:
    scaleDown:
      stabilizationWindowSeconds: 60
      policies:
        - type: Pods
          value: 1
          periodSeconds: 60
    scaleUp:
      stabilizationWindowSeconds: 20
      policies:
        - type: Pods
          value: 1
          periodSeconds: 20
  {{- end }}
  {{- end }}
