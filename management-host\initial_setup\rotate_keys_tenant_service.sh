#!/usr/bin/env bash
# GCP project service account key will need to renewed every 60 days for Production and 30 days for Development
# otherwise this will be caught by the HGMS scanning
# https://alm-confluence.systems.uk.hsbc/confluence/display/GCP/HGMS+Violation+Rules
#
# This script is to demonstrate we can renew the service account key automatically
# https://cloud.google.com/iam/docs/creating-managing-service-account-keys#iam-service-account-keys-create-gcloud
#
# in order to renew the service account key, the service account must be in this role roles/iam.serviceAccountKeyAdmin
# https://cloud.google.com/iam/docs/understanding-roles#service-accounts-roles

# Initial Bootstrap, requires binding serviceAccountKeyAdmin 
###############################################################
# NON-PROD
###############################################################
# gcloud iam service-accounts add-iam-policy-binding <EMAIL> --member='serviceAccount:<EMAIL>' --role='roles/iam.serviceAccountKeyAdmin'
# gcloud iam service-accounts add-iam-policy-binding <EMAIL> --member='serviceAccount:<EMAIL>' --role='roles/iam.serviceAccountKeyAdmin'
# gcloud iam service-accounts add-iam-policy-binding <EMAIL> --member='serviceAccount:<EMAIL>' --role='roles/iam.serviceAccountKeyAdmin'
# gcloud iam service-accounts add-iam-policy-binding <EMAIL> --member='serviceAccount:<EMAIL>' --role='roles/iam.serviceAccountKeyAdmin'
# gsutil mb -p hsbc-9087302-unity-dev -c standard -l asia-east2 gs://hsbc-9087302-unity-dev-tenant-artifacts 
# gsutil kms encryption -k projects/hsbc-6320774-kms-dev/locations/asia-east2/keyRings/cloudStorage/cryptoKeys/cloudStorage -w gs://hsbc-9087302-unity-dev-tenant-artifacts 
# gsutil ls -L -b gs://hsbc-9087302-unity-dev-tenant-artifacts 
# gcloud storage buckets add-iam-policy-binding gs://hsbc-9087302-unity-dev-tenant-artifacts --member=<EMAIL> --role=roles/storage.objectViewer
# gcloud storage buckets add-iam-policy-binding gs://hsbc-9087302-unity-dev-tenant-artifacts --member=serviceAccount:<EMAIL> --role=roles/storage.objectViewer
# gcloud storage buckets add-iam-policy-binding gs://hsbc-9087302-unity-dev-tenant-artifacts --member=serviceAccount:<EMAIL> --role=roles/storage.objectViewer
# gcloud storage buckets add-iam-policy-binding gs://hsbc-9087302-unity-dev-tenant-artifacts --member=serviceAccount:<EMAIL> --role=roles/storage.objectViewer

function print_usage {
  echo "Usage:$(basename $0)"
  echo ' --expiry-days={expiry_day:-7}'
  echo ' --retention-days={retention_days:-21} '
  echo "Example:$(basename $0) "
}

function options() {
while [ "$1" != "" ];
do
  _param_=`printf "%s\n" $1 | awk -F= '{print $1}'`
  _value_=`printf "%s\n" $1 | sed 's/^[^=]*=//g'`
  if [[ $_value_ == $_param_ ]]; then
    shift
    _value_=$1
  fi
  case ${_param_} in
  --expiry-days)
    expiry_days="${_value_}"
    ;;
  --retention-days)
    retention_days="${_value_}"
    ;;
  --max-keys)
    max_num_of_keys="${_value_}"
    ;;
  --filter)
    filter="${_value_}"
    ;;
  --help)
    print_usage
    exit
    ;;
  esac
  shift;
done
}

################################################
# MAIN
################################################
options "$@"
project_id=`gcloud config list --format 'value(core.project)'`
rootdir=$HOME/sa-key-rotation
retention_days=${retention_days:-7}
expiry_days=${expiry_days:-21}

if [ "${project_id}" == "" ]
then
    echo "To run you must set project for gcloud locally to define target project for the shell host"
    exit 255
fi

mkdir -p $rootdir
cd $rootdir
echo $PWD
#################################################################
## Renew key for image-deployment service account
#################################################################

# Key before this date will be deleted
# The requirement from HGMS report is for GCP dev project, keys older than 30 days must be removed while for production, it is older then 60 days
# See rule 34 resourceViolationInvalidServiceAccountKey in here https://alm-confluence.systems.uk.hsbc/confluence/display/GCP/HGMS+Violation+Rules

filter=${filter:-.*}
validation_file=${rootdir}/sa_validation.lst
gcloud iam service-accounts list --format 'table[no-heading](Email:sort=1)'| tr -d '\r' > ${validation_file}
# encrypt keys
# download encryption keys
gsutil ls -d gs://${project_id}-tenant-artifacts | eval "egrep \"${filter}\"" | while read d ; do 
  tenant=$(basename $d)
  workdir=${rootdir}/${tenant}/sa-key-rotation
  mkdir -p ${workdir} ${workdir}-raw
  cd ${workdir}
  gsutil rsync gs://${project_id}-tenant-artifacts/${tenant}/sa-key-rotation/ ./
  cat sa.lst | while read sa ; do 
     # get encryption key 
     gpg_pub=${sa}.pub
     gsutil cp gs://${project_id}-tenant-artifacts/${tenant}/encryption-keys/${gpg_pub} ./ 2> /dev/null 
     if [[ $? -eq 1 ]]; then 
      # encryption key - {{service_account}}.pub not found 
      # try to use default key 
      gsutil cp gs://${project_id}-tenant-artifacts/${tenant}/encryption-keys/*.default.pub ./ 2> /dev/null 
      if [[ $? -eq 1 ]]; then 
        echo "aborting key rotation for ${sa} - no encryption key found"
        break;
      else
        gpg_pub=$(basename $(ls *.default.pub))
      fi 
     fi
     if [[ $(grep ${sa} ${validation_file} | wc -l) -eq 1 ]]; then 
       recipient=$(echo ${gpg_pub}|sed -e 's|\.default\.pub||g' -e 's|\.pub||g')
       expiry_date=$(date --date="${retention_days} day ago" +%Y-%m-%d)
       delete_date=$(date --date="$((${expiry_days}-1)) day ago" +%Y-%m-%d)
       num_of_keys_found=$(gcloud iam service-accounts keys list --iam-account ${sa} --managed-by=user  --format 'table[no-heading](KEY_ID,CREATED_AT:sort=1)'|wc -l)
       num_of_keys_expired=$(gcloud iam service-accounts keys list --iam-account ${sa} --managed-by=user  --format 'table[no-heading](KEY_ID,CREATED_AT:sort=1)' --filter="CREATED_AT<${expiry_date}"|wc -l)
       num_of_keys_to_be_removed=$(gcloud iam service-accounts keys list --iam-account ${sa} --managed-by=user  --format 'table[no-heading](KEY_ID,CREATED_AT:sort=1)' --filter="CREATED_AT<${delete_date}"|wc -l)
       max_num_of_keys=${max_num_of_keys:-2}
       key_quota=$((${max_num_of_keys}-${num_of_keys_found}+${num_of_keys_to_be_removed}))
       echo key_rotation: ${sa}
       echo gpg_pub: ${gpg_pub}
       echo recipient: ${recipient}
       echo key_expiry: ${retention_days}d
       echo key_retention: ${expiry_days}d
       echo num_of_keys_found: ${num_of_keys_found} 
       echo num_of_keys_expired: ${num_of_keys_expired} 
       echo num_of_keys_to_be_removed: ${num_of_keys_to_be_removed}
       echo key_quota: ${key_quota}       
       gcloud iam service-accounts keys list --iam-account ${sa} --managed-by=user
       
       #only create one key as duplicate key will expiry on the same day
       [[ ${key_quota} -gt 0 ]] && key_quota=1
       while [[ ${key_quota} -gt 0 ]]; do
          if [[ -f ${sa}.json.enc ]] ; then 
           [[ ${num_of_keys_expired} -gt 0 ]] && rm -f ${sa}.json.enc 
          fi           
          gcloud iam service-accounts keys create ${sa}.json --iam-account ${sa} --quiet
          gpg --import ${gpg_pub}
          gpg --batch --yes --trust-model always -r "${recipient}" --armor --out ${sa}.json.enc --encrypt ${sa}.json
          private_key_id=$(jq -r '.private_key_id' ${sa}.json)
          cp -rp ${sa}.json.enc ${sa}.json.enc.$(date +%Y-%m-%d-%H:%M:%S).${private_key_id}
          mv *.json ${workdir}-raw/
          ls -l ${sa}.*          
          ((key_quota--))
       done 
       gpg --batch --yes --delete-keys "${recipient}"
       rm -f ${gpg_pub}         
       
       #remove all expired keys
       echo "removing history enc"
       find ./ -name "${sa}*.enc.*" -mtime +${expiry_days}
       find ./ -name "${sa}*.enc.*" -mtime +${expiry_days} -exec rm -f {} ';'
       gsutil rsync -d -n -y ".*.pub" ./ gs://${project_id}-tenant-artifacts/${tenant}/sa-key-rotation
       gsutil rsync -d -y ".*.pub" ./ gs://${project_id}-tenant-artifacts/${tenant}/sa-key-rotation
       #deleting keys
       echo "removing expired keys"
       gcloud iam service-accounts keys list --iam-account ${sa} --managed-by=user  --format 'table[no-heading](KEY_ID,CREATED_AT:sort=1)' --filter="CREATED_AT<${delete_date}"
       gcloud iam service-accounts keys list --iam-account ${sa} --managed-by=user  --format 'table[no-heading](KEY_ID,CREATED_AT:sort=1)' --filter="CREATED_AT<${delete_date}" | cut -d" " -f1 | while read KEY; do
         gcloud iam service-accounts keys delete ${KEY} --iam-account ${sa} --quiet
       done
     fi
  done
done
