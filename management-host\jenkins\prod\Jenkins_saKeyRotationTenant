static final boolean C_TRUE = true
static final boolean C_FALSE = false
static final String C_SUCCESS = 'SUCCESS'
static final String C_FAILURE = 'FAILURE'
static final String C_NULL = null
static final String C_YES = 'Yes'
static final String C_NO = 'No'
def AGENT = "unity-prod-jenkins-agent" 

pipeline {    
    agent { label env.AGENT_LABEL ?: AGENT }
    
    environment {
        // Fix github connection so it points to env specific variable
	    STASH_TOKEN= credentials('STASH_TOKEN') 

    }
    stages {
        stage('Sa Key Rotation') {
            steps {
                script {
                    this.sh "chmod 755 ./initial_setup/*; ./initial_setup/rotate_keys_tenant_service.sh; rc=\$?; exit \$rc"
                }
            }
        }
    }
    post {
        unsuccessful {
            mail bcc: '',
                body: "Attention @here ${env.JOB_NAME} #${env.BUILD_NUMBER} has failed.",
                cc: '',
                from: 'unity_cloud_devops@noreply',
                replyTo: '',
                subject: "ERROR in automatic key rotation notification [tenant]",
                to: '<EMAIL>,<EMAIL>'
        }     
        cleanup {
            script {
                cleanWs()
            }
        }
    }
}
