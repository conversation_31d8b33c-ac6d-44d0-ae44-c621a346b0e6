#!/bin/bash

sudo chmod -R 600 .ssh
cd /root/.ssh
echo "$PWD"
echo "in .ssh"

echo "ssh key adding started"
echo "adding shreyas shkeys for terraformdeployment for filenet"

cat > id_rsa <<HELLO
*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************HELLO

cat > id_rsa.pub <<HELLO
ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAQC+ptG00PuXtEQriRKJZpRxg1Nphj447Pwge/tnLR8JGgvkLvhQJXnf4709HoTYJjeP1UbMnnz9pRy1JKRyx7WY5Bb356yQ+6mLpOXolyE7zyVUXlgLfpDYeN+56Idpe64+UTz3qkfTAbPlRhQzlPVyXJxFyAl3rSmDETWa7rRS6pFExdM2lTwt5yhx6aT6US1l/rdcBI9K7eKIbkYlbm8rGkjQmEfnnjQXByf9RXd4iGYHkZm5EuW64iJU8l7opnp2EMw5UrhQRIKRs3ikywEzdg/lV5qgOpNfzy+1NnllqoCU5RbanR2mJqg/+sSOJIbF0B30NQvdpBnlf84xA+DD
HELLO

cat > config <<HELLO
#gitPersonal
Host personal
    HostName stash.hk.hsbc
    User git
    Port 8203
    IdentityFile ~/.ssh/id_rsa
HELLO

sudo chmod -R 750 /root/.ssh
sudo chmod -R 600 /root/.ssh/id_rsa
sudo chmod -R 644 /root/.ssh/id_rsa.pub

eval `ssh-agent -s`
ssh-add ~/.ssh/id_rsa

ssh-keyscan -H stash.hk.hsbc >> ~/.ssh/known_hosts

ls -l /root/.ssh

echo "ssh key adding completed"
