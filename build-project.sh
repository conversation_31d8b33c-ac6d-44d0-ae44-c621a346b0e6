#!/bin/bash
set -euo pipefail

# Generic build script for Maven projects using devops-helper
# Usage: ./build-project.sh <project-name> [options]

# Function to print usage
usage() {
    echo "Usage: $0 <project-name> [options]"
    echo ""
    echo "Arguments:"
    echo "  project-name    Name of the project directory (e.g., change-dashboard, risc)"
    echo ""
    echo "Options:"
    echo "  --skip-maven    <PERSON><PERSON> build"
    echo "  --skip-docker   <PERSON><PERSON> build"
    echo "  --skip-scan     Skip security scanning"
    echo "  --help, -h      Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 change-dashboard"
    echo "  $0 risc --skip-scan"
    echo "  $0 change-dashboard --skip-docker"
    exit 1
}

# Parse arguments
if [ $# -lt 1 ]; then
    echo "Error: Project name is required"
    usage
fi

PROJECT_NAME="$1"
shift

# Default options
SKIP_MAVEN=false
SKIP_DOCKER=false
SKIP_SCAN=false

# Parse options
while [[ $# -gt 0 ]]; do
    case $1 in
        --skip-maven)
            SKIP_MAVEN=true
            shift
            ;;
        --skip-docker)
            SKIP_DOCKER=true
            shift
            ;;
        --skip-scan)
            SKIP_SCAN=true
            shift
            ;;
        --help|-h)
            usage
            ;;
        *)
            echo "Error: Unknown option $1"
            usage
            ;;
    esac
done

# Validate project directory exists
if [ ! -d "${PROJECT_NAME}" ]; then
    echo "Error: Project directory '${PROJECT_NAME}' not found"
    echo "Available projects:"
    ls -d */ 2>/dev/null | grep -E "(change-dashboard|risc)" || echo "  No valid projects found"
    exit 1
fi

echo "=== Building ${PROJECT_NAME} ==="
echo "Configuration:"
echo "  Skip Maven: ${SKIP_MAVEN}"
echo "  Skip Docker: ${SKIP_DOCKER}"
echo "  Skip Scan: ${SKIP_SCAN}"

# Load build configuration
BUILD_CONFIG_FILE="${PROJECT_NAME}/.devops/build.properties"
if [ -f "${BUILD_CONFIG_FILE}" ]; then
    echo "Loading build configuration from ${BUILD_CONFIG_FILE}"
    source "${BUILD_CONFIG_FILE}"
else
    echo "Warning: No build.properties found at ${BUILD_CONFIG_FILE}, using defaults"
    # Set default values
    export mvn_build_enable=true
    export docker_build_enable=true
    export jdk_version=17
    export mvn_skip_tests=false
    export mvn_maven_test_skip=false
    export container_scan_enable=false
    export sast_scan_enable=false
    export docker_registry_nexus_path="unity/i15n/"
    export docker_image_name_prefix="${PROJECT_NAME}-"
    export java_base_image="nexus3.systems.uk.hsbc:18096/com/hsbc/group/itid/es/mw/java/mw-azuljava-v17:17.36"
fi

# Validate devops-helper exists
DEVOPS_HELPER_SCRIPT="devops-helper/src/devops/scripts/build/actions.bash"
if [ ! -f "${DEVOPS_HELPER_SCRIPT}" ]; then
    echo "Error: devops-helper script not found at ${DEVOPS_HELPER_SCRIPT}"
    echo "Please ensure devops-helper is available in the repository"
    exit 1
fi

# Change to project directory
cd "${PROJECT_NAME}"

# Check if pom.xml exists
if [ ! -f "pom.xml" ]; then
    echo "Error: pom.xml not found in ${PROJECT_NAME}"
    echo "This script is designed for Maven projects"
    exit 1
fi

# Get project version for reporting
PROJECT_VERSION=$(mvn help:evaluate -Dexpression=project.version -q -DforceStdout 2>/dev/null || echo "unknown")
echo "Project version: ${PROJECT_VERSION}"

# Step 1: Maven Build
if [ "${SKIP_MAVEN}" = "false" ] && [ "${mvn_build_enable:-true}" = "true" ]; then
    echo ""
    echo "=== Step 1: Maven Build ==="
    
    # Ensure Maven wrapper or Maven is available
    if [ -f "./mvnw" ]; then
        echo "Using Maven wrapper"
        MVN_CMD="./mvnw"
    elif command -v mvn &> /dev/null; then
        echo "Using system Maven"
        MVN_CMD="mvn"
    else
        echo "Error: Neither Maven wrapper nor system Maven found"
        exit 1
    fi
    
    # Use devops-helper for Maven build
    echo "Running Maven build using devops-helper..."
    ../"${DEVOPS_HELPER_SCRIPT}" mvn_build
    
    if [ $? -eq 0 ]; then
        echo "✅ Maven build completed successfully"
        
        # Verify JAR file was created
        JAR_FILE="target/${PROJECT_NAME}-${PROJECT_VERSION}.jar"
        if [ -f "${JAR_FILE}" ]; then
            echo "✅ JAR file created: ${JAR_FILE}"
            ls -lh "${JAR_FILE}"
        else
            echo "⚠️  Warning: Expected JAR file not found: ${JAR_FILE}"
            echo "Available JAR files:"
            ls -lh target/*.jar 2>/dev/null || echo "  No JAR files found"
        fi
    else
        echo "❌ Maven build failed"
        exit 1
    fi
else
    echo "=== Step 1: Maven Build (SKIPPED) ==="
fi

# Step 2: Docker Build
if [ "${SKIP_DOCKER}" = "false" ] && [ "${docker_build_enable:-true}" = "true" ]; then
    echo ""
    echo "=== Step 2: Docker Build ==="
    
    # Check if Dockerfile exists
    if [ ! -f "Dockerfile" ]; then
        echo "Error: Dockerfile not found in ${PROJECT_NAME}"
        echo "Please create a Dockerfile for this project"
        exit 1
    fi
    
    # Create auth.conf for Docker build if it doesn't exist
    if [ ! -f "auth.conf" ]; then
        echo "Creating auth.conf for Docker build..."
        # This would typically be provided by your CI/CD system
        # For now, create an empty file
        touch auth.conf
    fi
    
    # Use devops-helper for Docker build
    echo "Running Docker build using devops-helper..."
    ../"${DEVOPS_HELPER_SCRIPT}" docker_build
    
    if [ $? -eq 0 ]; then
        echo "✅ Docker build completed successfully"
        
        # Show created images
        echo "Docker images created:"
        docker images | grep "${PROJECT_NAME}" | head -5 || echo "  No images found with project name"
    else
        echo "❌ Docker build failed"
        exit 1
    fi
else
    echo "=== Step 2: Docker Build (SKIPPED) ==="
fi

# Step 3: Security Scanning
if [ "${SKIP_SCAN}" = "false" ]; then
    echo ""
    echo "=== Step 3: Security Scanning ==="
    
    # Container scanning
    if [ "${container_scan_enable:-false}" = "true" ]; then
        echo "Running container security scan..."
        ../"${DEVOPS_HELPER_SCRIPT}" container_scan
        
        if [ $? -eq 0 ]; then
            echo "✅ Container security scan completed"
        else
            echo "⚠️  Container security scan failed (continuing...)"
        fi
    else
        echo "Container scanning disabled"
    fi
    
    # SAST scanning
    if [ "${sast_scan_enable:-false}" = "true" ]; then
        echo "Running SAST security scan..."
        ../"${DEVOPS_HELPER_SCRIPT}" sast_scan
        
        if [ $? -eq 0 ]; then
            echo "✅ SAST security scan completed"
        else
            echo "⚠️  SAST security scan failed (continuing...)"
        fi
    else
        echo "SAST scanning disabled"
    fi
else
    echo "=== Step 3: Security Scanning (SKIPPED) ==="
fi

# Build Summary
echo ""
echo "=== Build Summary ==="
echo "Project: ${PROJECT_NAME}"
echo "Version: ${PROJECT_VERSION}"
echo "Maven Build: $([ "${SKIP_MAVEN}" = "false" ] && echo "✅ Completed" || echo "⏭️  Skipped")"
echo "Docker Build: $([ "${SKIP_DOCKER}" = "false" ] && echo "✅ Completed" || echo "⏭️  Skipped")"
echo "Security Scan: $([ "${SKIP_SCAN}" = "false" ] && echo "✅ Completed" || echo "⏭️  Skipped")"

if [ "${SKIP_DOCKER}" = "false" ] && [ "${docker_build_enable:-true}" = "true" ]; then
    DOCKER_IMAGE="gcr.io/hsbc-9087302-unity-dev/${docker_registry_nexus_path}${PROJECT_NAME}:${PROJECT_VERSION}"
    echo "Docker Image: ${DOCKER_IMAGE}"
fi

echo ""
echo "✅ Build completed successfully for ${PROJECT_NAME}"

# Return to original directory
cd ..
