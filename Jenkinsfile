#!/usr/bin/env groovy

def AGENT = 'unity-dev-jenkins-agent'

pipeline {
    agent { label env.AGENT_LABEL ?: AGENT }
    
    parameters {
        choice(
            name: 'PROJECT_TO_BUILD',
            choices: ['change-dashboard', 'risc', 'both'],
            description: 'Select the project to build'
        )
        booleanParam(
            name: 'STAGE_0_CHECKOUT',
            defaultValue: true,
            description: 'Stage 0: Checkout repository'
        )
        booleanParam(
            name: 'STAGE_1_MAVEN_BUILD',
            defaultValue: true,
            description: 'Stage 1: Maven build and package'
        )
        booleanParam(
            name: 'STAGE_2_SECURITY_SCAN',
            defaultValue: true,
            description: 'Stage 2: Security scanning (SAST, dependency scan)'
        )
        booleanParam(
            name: 'STAGE_3_IMAGE_BUILD',
            defaultValue: true,
            description: 'Stage 3: Docker image build and push'
        )
        booleanParam(
            name: 'STAGE_4_DEPLOY',
            defaultValue: true,
            description: 'Stage 4: Deploy to Kubernetes'
        )
        choice(
            name: 'TARGET_ENVIRONMENT',
            choices: ['dev', 'uat', 'prod'],
            description: 'Target environment for deployment'
        )
        booleanParam(
            name: 'SKIP_TESTS',
            defaultValue: false,
            description: 'Skip Maven tests in build stage'
        )
        booleanParam(
            name: 'SKIP_SAST',
            defaultValue: false,
            description: 'Skip SAST scanning in security stage'
        )
        booleanParam(
            name: 'PUSH_IMAGES',
            defaultValue: true,
            description: 'Push Docker images to registry'
        )
        booleanParam(
            name: 'WAIT_FOR_DEPLOYMENT',
            defaultValue: true,
            description: 'Wait for deployment to complete'
        )
        booleanParam(
            name: 'PARALLEL_EXECUTION',
            defaultValue: false,
            description: 'Run projects in parallel (when building both)'
        )
        string(
            name: 'CHANGE_ORDER',
            defaultValue: '',
            description: 'SNOW change order number (required for UAT/PROD)'
        )
    }
    
    environment {
        // Jenkins environment variables
        ICE_AUTH_TOKEN = credentials('ICE_AUTH_TOKEN')
        DOCKER_REGISTRY = 'gcr.io/hsbc-9087302-unity-dev'
        NEXUS_REGISTRY = 'nexus3.systems.uk.hsbc:18096'

        // Build configuration
        MAVEN_OPTS = '-Xmx2048m -Djavax.net.ssl.trustStore=/etc/pki/java/cacerts'
        JAVA_HOME = '/opt/java/zulu17'

        // Pipeline configuration
        PROJECTS_TO_BUILD = "${params.PROJECT_TO_BUILD}"
        TARGET_ENV = "${params.TARGET_ENVIRONMENT}"
        NAMESPACE = "ns-i15n-${params.TARGET_ENVIRONMENT}"

        // Git information
        GIT_COMMIT_SHORT = "${env.GIT_COMMIT?.take(8) ?: 'unknown'}"
        BUILD_VERSION = "${env.BUILD_NUMBER}-${env.GIT_COMMIT_SHORT}"

        // SNOW integration
        SNOW_CHANGE_ORDER = "${params.CHANGE_ORDER}"
    }
    
    options {
        timeout(time: 60, unit: 'MINUTES')
        buildDiscarder(logRotator(numToKeepStr: '10'))
        disableConcurrentBuilds()
        timestamps()
    }
    
    stages {
        stage('Initialize') {
            steps {
                script {
                    echo "=== Pipeline Configuration ==="
                    echo "Project(s): ${params.PROJECT_TO_BUILD}"
                    echo "Target Environment: ${params.TARGET_ENVIRONMENT}"
                    echo "Namespace: ${env.NAMESPACE}"
                    echo "Parallel Execution: ${params.PARALLEL_EXECUTION}"
                    echo "Build Version: ${env.BUILD_VERSION}"
                    echo "Git Commit: ${env.GIT_COMMIT_SHORT}"
                    echo ""
                    echo "Stages to execute:"
                    echo "  Stage 0 (Checkout): ${params.STAGE_0_CHECKOUT}"
                    echo "  Stage 1 (Maven): ${params.STAGE_1_MAVEN_BUILD}"
                    echo "  Stage 2 (Security): ${params.STAGE_2_SECURITY_SCAN}"
                    echo "  Stage 3 (Image): ${params.STAGE_3_IMAGE_BUILD}"
                    echo "  Stage 4 (Deploy): ${params.STAGE_4_DEPLOY}"

                    // Validate change order for non-dev environments
                    if (params.TARGET_ENVIRONMENT != 'dev' && params.STAGE_4_DEPLOY) {
                        if (!params.CHANGE_ORDER) {
                            error "Change order is required for ${params.TARGET_ENVIRONMENT} deployment"
                        }
                        echo "Change Order: ${params.CHANGE_ORDER}"

                        // Validate change order format (example: CHG0123456)
                        if (!params.CHANGE_ORDER.matches(/CHG\d{7}/)) {
                            error "Invalid change order format. Expected format: CHG1234567"
                        }
                    }

                    // Set projects list
                    if (params.PROJECT_TO_BUILD == 'both') {
                        env.PROJECT_LIST = 'change-dashboard,risc'
                    } else {
                        env.PROJECT_LIST = params.PROJECT_TO_BUILD
                    }
                    echo "Projects to build: ${env.PROJECT_LIST}"

                    // Set build description
                    currentBuild.description = "Projects: ${params.PROJECT_TO_BUILD} | Env: ${params.TARGET_ENVIRONMENT} | Change: ${params.CHANGE_ORDER ?: 'N/A'}"

                    // Validate prerequisites
                    validatePrerequisites()
                }
            }
        }
        
        stage('Stage 0: Checkout') {
            when {
                expression { params.STAGE_0_CHECKOUT }
            }
            steps {
                script {
                    echo "=== Stage 0: Repository Checkout ==="
                    
                    // Checkout main repository
                    checkout scm
                    
                    // Make scripts executable
                    sh '''
                        chmod +x stage-*.sh
                        chmod +x pipeline.sh
                        chmod +x quick-start.sh
                    '''
                    
                    // Verify required files exist
                    sh '''
                        echo "Verifying pipeline scripts..."
                        ls -la stage-*.sh pipeline.sh
                        
                        echo "Verifying devops-helper..."
                        ls -la devops-helper/src/devops/scripts/build/actions.bash
                        
                        echo "Verifying gke-deployment..."
                        ls -la gke-deployment/deploy.sh
                    '''
                    
                    echo "✅ Checkout completed successfully"
                }
            }
        }
        
        stage('Stage 1: Maven Build') {
            when {
                expression { params.STAGE_1_MAVEN_BUILD }
            }
            steps {
                script {
                    echo "=== Stage 1: Maven Build ==="
                    
                    def projects = env.PROJECT_LIST.split(',')
                    def buildTasks = [:]
                    
                    if (params.PARALLEL_EXECUTION && projects.size() > 1) {
                        // Parallel execution
                        for (project in projects) {
                            def proj = project.trim()
                            buildTasks[proj] = {
                                runMavenBuild(proj)
                            }
                        }
                        parallel buildTasks
                    } else {
                        // Sequential execution
                        for (project in projects) {
                            def proj = project.trim()
                            runMavenBuild(proj)
                        }
                    }
                    
                    echo "✅ Maven build stage completed"
                }
            }
        }
        
        stage('Stage 2: Security Scanning') {
            when {
                expression { params.STAGE_2_SECURITY_SCAN }
            }
            steps {
                script {
                    echo "=== Stage 2: Security Scanning ==="
                    
                    def projects = env.PROJECT_LIST.split(',')
                    def scanTasks = [:]
                    
                    if (params.PARALLEL_EXECUTION && projects.size() > 1) {
                        // Parallel execution
                        for (project in projects) {
                            def proj = project.trim()
                            scanTasks[proj] = {
                                runSecurityScan(proj)
                            }
                        }
                        parallel scanTasks
                    } else {
                        // Sequential execution
                        for (project in projects) {
                            def proj = project.trim()
                            runSecurityScan(proj)
                        }
                    }
                    
                    echo "✅ Security scanning stage completed"
                }
            }
        }
        
        stage('Stage 3: Image Build & Push') {
            when {
                expression { params.STAGE_3_IMAGE_BUILD }
            }
            steps {
                script {
                    echo "=== Stage 3: Docker Image Build & Push ==="
                    
                    def projects = env.PROJECT_LIST.split(',')
                    def imageTasks = [:]
                    
                    if (params.PARALLEL_EXECUTION && projects.size() > 1) {
                        // Parallel execution
                        for (project in projects) {
                            def proj = project.trim()
                            imageTasks[proj] = {
                                runImageBuild(proj)
                            }
                        }
                        parallel imageTasks
                    } else {
                        // Sequential execution
                        for (project in projects) {
                            def proj = project.trim()
                            runImageBuild(proj)
                        }
                    }
                    
                    echo "✅ Image build stage completed"
                }
            }
        }
        
        stage('Stage 4: Deploy') {
            when {
                expression { params.STAGE_4_DEPLOY }
            }
            steps {
                script {
                    echo "=== Stage 4: Deployment ==="
                    
                    // Verify change order for non-dev environments
                    if (params.TARGET_ENVIRONMENT != 'dev') {
                        echo "Validating change order: ${params.CHANGE_ORDER}"
                        // Add change order validation logic here if needed
                    }
                    
                    def projects = env.PROJECT_LIST.split(',')
                    def deployTasks = [:]
                    
                    if (params.PARALLEL_EXECUTION && projects.size() > 1) {
                        // Parallel execution
                        for (project in projects) {
                            def proj = project.trim()
                            deployTasks[proj] = {
                                runDeploy(proj)
                            }
                        }
                        parallel deployTasks
                    } else {
                        // Sequential execution
                        for (project in projects) {
                            def proj = project.trim()
                            runDeploy(proj)
                        }
                    }
                    
                    echo "✅ Deployment stage completed"
                }
            }
        }
    }
    
    post {
        always {
            script {
                echo "=== Pipeline Summary ==="
                echo "Project(s): ${params.PROJECT_TO_BUILD}"
                echo "Environment: ${params.TARGET_ENVIRONMENT}"
                echo "Build Status: ${currentBuild.currentResult}"
                echo "Duration: ${currentBuild.durationString}"
                echo "Build Version: ${env.BUILD_VERSION}"

                // Archive build artifacts
                archiveArtifacts artifacts: '**/target/build-info.properties', allowEmptyArchive: true
                archiveArtifacts artifacts: '**/target/scan-results/**', allowEmptyArchive: true
                archiveArtifacts artifacts: '**/target/*.jar', allowEmptyArchive: true

                // Publish test results if available
                publishTestResults testResultsPattern: '**/target/surefire-reports/*.xml', allowEmptyResults: true

                // Publish security scan results
                publishHTML([
                    allowMissing: true,
                    alwaysLinkToLastBuild: true,
                    keepAll: true,
                    reportDir: 'target/scan-results',
                    reportFiles: '*.html',
                    reportName: 'Security Scan Report'
                ])

                // Send notifications
                sendNotifications()

                // Update SNOW change order
                updateSnowChangeOrder()
            }
        }
        success {
            script {
                echo "✅ Pipeline completed successfully!"
                
                if (params.STAGE_4_DEPLOY) {
                    def projects = env.PROJECT_LIST.split(',')
                    echo ""
                    echo "Application URLs:"
                    for (project in projects) {
                        def proj = project.trim()
                        def domain = getDomainForEnvironment(params.TARGET_ENVIRONMENT)
                        echo "  ${proj}: https://${proj}.${domain}"
                    }
                }
            }
        }
        failure {
            script {
                echo "❌ Pipeline failed!"
                echo "Check the logs above for specific error details"
            }
        }
        cleanup {
            cleanWs()
        }
    }
}

// Helper functions
def runMavenBuild(project) {
    echo "Building Maven project: ${project}"
    
    def skipTestsFlag = params.SKIP_TESTS ? '--skip-tests' : ''
    
    sh """
        echo "=== Maven Build: ${project} ==="
        ./stage-1-maven-build.sh ${project} ${skipTestsFlag}
    """
}

def runSecurityScan(project) {
    echo "Running security scan for: ${project}"
    
    def skipSastFlag = params.SKIP_SAST ? '--skip-sast' : ''
    def forceFlag = '--force' // Continue on scan failures in Jenkins
    
    sh """
        echo "=== Security Scan: ${project} ==="
        ./stage-2-security-scan.sh ${project} ${skipSastFlag} ${forceFlag}
    """
}

def runImageBuild(project) {
    echo "Building Docker image for: ${project}"
    
    def pushFlag = params.PUSH_IMAGES ? '--push' : ''
    def scanFlag = '--scan' // Always scan in Jenkins
    
    sh """
        echo "=== Image Build: ${project} ==="
        ./stage-3-image-build.sh ${project} ${pushFlag} ${scanFlag}
    """
}

def runDeploy(project) {
    echo "Deploying project: ${project}"
    
    def waitFlag = params.WAIT_FOR_DEPLOYMENT ? '--wait' : ''
    
    sh """
        echo "=== Deploy: ${project} ==="
        ./stage-4-deploy.sh ${project} --environment ${params.TARGET_ENVIRONMENT} --namespace ${env.NAMESPACE} ${waitFlag}
    """
}

def getDomainForEnvironment(environment) {
    switch(environment) {
        case 'dev':
            return 'hsbc-9087302-unity-dev.dev.gcp.cloud.hk.hsbc'
        case 'uat':
            return 'hsbc-9087302-unity-uat.uat.gcp.cloud.hk.hsbc'
        case 'prod':
            return 'hsbc-9087302-unity-prod.prod.gcp.cloud.hk.hsbc'
        default:
            return 'unknown'
    }
}

def sendNotifications() {
    def status = currentBuild.currentResult
    def projects = env.PROJECT_LIST.split(',')
    def environment = params.TARGET_ENVIRONMENT
    def changeOrder = params.CHANGE_ORDER ?: 'N/A'

    def message = """
Pipeline ${status}: ${env.JOB_NAME} #${env.BUILD_NUMBER}
Projects: ${params.PROJECT_TO_BUILD}
Environment: ${environment}
Change Order: ${changeOrder}
Duration: ${currentBuild.durationString}
Build Version: ${env.BUILD_VERSION}

Build URL: ${env.BUILD_URL}
"""

    // Email notification
    if (status == 'FAILURE') {
        emailext (
            subject: "❌ Pipeline Failed: ${env.JOB_NAME} #${env.BUILD_NUMBER}",
            body: message,
            to: "${env.CHANGE_AUTHOR_EMAIL ?: '<EMAIL>'}",
            attachLog: true
        )
    } else if (status == 'SUCCESS' && environment != 'dev') {
        emailext (
            subject: "✅ Pipeline Success: ${env.JOB_NAME} #${env.BUILD_NUMBER}",
            body: message,
            to: "${env.CHANGE_AUTHOR_EMAIL ?: '<EMAIL>'}"
        )
    }

    // Slack notification (if configured)
    try {
        def color = status == 'SUCCESS' ? 'good' : 'danger'
        def emoji = status == 'SUCCESS' ? ':white_check_mark:' : ':x:'

        slackSend(
            channel: '#unity-deployments',
            color: color,
            message: "${emoji} Pipeline ${status}: ${env.JOB_NAME} #${env.BUILD_NUMBER}\nProjects: ${params.PROJECT_TO_BUILD} | Env: ${environment}"
        )
    } catch (Exception e) {
        echo "Slack notification failed: ${e.message}"
    }
}

def updateSnowChangeOrder() {
    if (!params.CHANGE_ORDER || params.TARGET_ENVIRONMENT == 'dev') {
        return
    }

    try {
        def status = currentBuild.currentResult
        def updateMessage = """
Jenkins Pipeline Update:
Job: ${env.JOB_NAME}
Build: #${env.BUILD_NUMBER}
Status: ${status}
Projects: ${params.PROJECT_TO_BUILD}
Environment: ${params.TARGET_ENVIRONMENT}
Build Version: ${env.BUILD_VERSION}
URL: ${env.BUILD_URL}
"""

        // Update SNOW change order with deployment status
        // This would integrate with your SNOW API
        echo "Updating SNOW change order ${params.CHANGE_ORDER} with status: ${status}"

        // Example SNOW API call (customize based on your SNOW integration)
        /*
        sh """
            curl -X PUT "https://your-snow-instance.service-now.com/api/now/table/change_request/${params.CHANGE_ORDER}" \
                -H "Authorization: Bearer \${SNOW_API_TOKEN}" \
                -H "Content-Type: application/json" \
                -d '{
                    "work_notes": "${updateMessage}",
                    "state": "${status == 'SUCCESS' ? 'implemented' : 'implement'}"
                }'
        """
        */

    } catch (Exception e) {
        echo "SNOW update failed: ${e.message}"
    }
}

def validatePrerequisites() {
    echo "Validating pipeline prerequisites..."

    // Check required tools
    sh '''
        echo "Checking required tools..."
        java -version
        mvn -version
        docker --version
        kubectl version --client
        helm version --client
    '''

    // Check connectivity
    sh '''
        echo "Checking connectivity..."
        kubectl cluster-info --request-timeout=10s
    '''

    // Validate project directories
    def projects = env.PROJECT_LIST.split(',')
    for (project in projects) {
        def proj = project.trim()
        if (!fileExists("${proj}/pom.xml")) {
            error "Project ${proj} does not have a pom.xml file"
        }
        if (!fileExists("${proj}/Dockerfile")) {
            error "Project ${proj} does not have a Dockerfile"
        }
        if (!fileExists("gke-deployment/helm-charts/i15n-helmchart/${proj}/values.yaml")) {
            error "Helm values not found for project ${proj}"
        }
    }

    echo "✅ Prerequisites validation passed"
}
