#!/usr/bin/env bash
set -euo pipefail

profile_env=$1

echo "[MGMT_HOST] checking for default conf"
if [ ! -f "./initial_setup/var-${profile_env}.conf" ]; then
 echo "Parameter file var-${profile_env}.conf not found in initial_setup."
 exit 1
fi

source ./initial_setup/var-${profile_env}.conf

if [ ! -d $"$HOME/ServiceAccountKeys/" ]
then
    mkdir $"$HOME/ServiceAccountKeys/"
fi

PROJECT_ID=$PROJECT_ID

IFS=', ' read -r -a sa_list <<< $SERVICE_ACCOUNT_LIST

for i in "${sa_list[@]}"
do
    SA_KEY=$"$HOME/ServiceAccountKeys/${i}.json"

    if [ -s $SA_KEY ]
    then
        echo "[MGMT_HOST] ${i} service account key exists at $SA_KEY"        
    else
        echo "[MGMT_HOST] creating ${i} service account key"  
        gcloud beta --project=$PROJECT_ID iam service-accounts keys create $SA_KEY --iam-account ${i}@$PROJECT_ID.iam.gserviceaccount.com
    fi
done

echo "[MGMT_HOST] Authenticating gce-stage3-image-builder service account key"  

# SA_KEY=$"$HOME/ServiceAccountKeys/gce-stage3-image-builder.json"

# gcloud auth activate-service-account gce-stage3-image-builder@$PROJECT_ID.iam.gserviceaccount.com --key-file=$SA_KEY --project=$PROJECT_ID
