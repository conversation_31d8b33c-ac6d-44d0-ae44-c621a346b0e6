#!/bin/bash
echo "<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<< JAVA >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>"
echo "[MGMT_HOST] installing google cloud sdk"
rc=0
mkdir -p /opt/google
cd /opt/google
proxy_protocol=http
proxy_type=http_no_tunnel
proxy_address=googleapis-dev.gcp.cloud.hk.hsbc
proxy_port=3128
export http_proxy=${proxy_protocol}://${proxy_address}:${proxy_port}
export https_proxy=${proxy_protocol}://${proxy_address}:${proxy_port}
curl -O https://dl.google.com/dl/cloudsdk/channels/rapid/downloads/google-cloud-sdk-269.0.0-darwin-x86_64.tar.gz
tar -xf google-cloud-sdk-269.0.0-darwin-x86_64.tar.gz
rm -f google-cloud-sdk-269.0.0-darwin-x86_64.tar.gz
export PATH=/opt/google/google-cloud-sdk/bin:$PATH
gcloud components update --quiet
[[ $? -ne 0 ]] && rc=1
gcloud components install kubectl --quiet
[[ $? -ne 0 ]] && rc=1
gcloud components install gke-gcloud-auth-plugin --quiet
[[ $? -ne 0 ]] && rc=1
echo "[MGMT_HOST] completed installation of google cloud sdk"
gcloud components list
echo linking kubectl to /usr/bin
ln -s /opt/google/google-cloud-sdk/bin/kubectl /usr/bin
unset http_proxy
unset https_proxy
exit $rc