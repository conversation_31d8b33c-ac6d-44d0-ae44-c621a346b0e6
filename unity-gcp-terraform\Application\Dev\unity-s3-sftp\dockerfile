FROM nexus3.systems.uk.hsbc:18096/com/hsbc/group/itid/es/dc/ubuntu/gcr-ubuntu-2404:latest
USER root

RUN --mount=type=secret,id=aptconf,target=/etc/apt/auth.conf \
 apt-get update && apt-get install -y openssh-server \
 && apt-get install -y nftables \
 && apt-get install -y vim \
 && apt-get install -y curl \
 && apt-get install -y s3fs 

RUN curl https://hkl20090861.hc.cloud.hk.hsbc/devops/yq-4.13.4.gz | tar -xz \
 && mv yq_linux_amd64 /usr/local/bin/yq  \
 && chmod 755 /usr/local/bin/yq

# configure sftp user
# SSH login fix (Keeping Session Alive). If not, user will be kick off after ssh
RUN useradd -rm -d /home/<USER>/bin/bash -G sudo -u 10001 sftp_user \
 && mkdir /var/run/sshd \
 && sed 's@session\s*required\s*pam_loginuid.so@session optional pam_loginuid.so@g' -i /etc/pam.d/sshd

ENV NOTVISIBLE "in users profile"
RUN echo "export VISIBLE=now" >> /etc/profile

#setup directory for sftp
RUN mkdir /var/sftp \
 && chown root:root /var/sftp \
 && chmod 755 /var/sftp

# update to only allow sftp and not ssh tunneling to limit the non-necessary activity

RUN echo '\n\
Port 2222 \n\
Match User sftp_user  \n\
PasswordAuthentication no \n\
ForceCommand internal-sftp \n\
ChrootDirectory /var/sftp \n\
PermitTunnel no  \n\
AllowAgentForwarding no \n\
AllowTcpForwarding no \n\
X11Forwarding no \n\
Match all \n\
MaxSessions 100 \n\
MaxStartups 50:50:100 \n\
HostKey /etc/ssh/ssh_host_rsa_key \n\
HostKey /etc/ssh/ssh_host_ecdsa_key \n\
HostKey /etc/ssh/ssh_host_ed25519_key \n\
LogLevel INFO \n\
PubkeyAcceptedKeyTypes ssh-rsa,rsa-sha2-256,ecdsa-sha2-nistp256,<EMAIL>,ecdsa-sha2-nistp384,<EMAIL>,rsa-sha2-512,ecdsa-sha2-nistp521,<EMAIL>,ssh-ed25519,<EMAIL> \n\
KexAlgorithms <EMAIL>,diffie-hellman-group14-sha1,diffie-hellman-group16-sha512,diffie-hellman-group18-sha512,diffie-hellman-group1-sha1,diffie-hellman-group-exchange-sha1,diffie-hellman-group-exchange-sha256,ecdh-sha2-nistp256,ecdh-sha2-nistp256,ecdh-sha2-nistp384,ecdh-sha2-nistp384,ecdh-sha2-nistp521 \n\ 
Ciphers <EMAIL>,<EMAIL>,aes256-ctr,aes256-cbc \n\
' >> /etc/ssh/sshd_config

EXPOSE 2222
COPY s3sftp /usr/local/bin/s3sftp
COPY s3sftp-mount /usr/local/bin/s3sftp-mount
RUN chmod 755 /usr/local/bin/s3sftp /usr/local/bin/s3sftp-mount
CMD ["/usr/local/bin/s3sftp" ]
