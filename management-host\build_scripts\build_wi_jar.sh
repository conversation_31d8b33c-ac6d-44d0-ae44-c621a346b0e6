set -euo


workdir_name="workdir"
branch="master" #TODO: change to master after merge

echo "current dir: $(pwd)"
mkdir "$workdir_name"
cd ${workdir_name}

git -c "http.extraHeader=Authorization: Bearer $STASH_TOKEN" clone -b "$branch"  https://stash.hk.hsbc/scm/uoci/unity-gcp-terraform.git


cd "unity-gcp-terraform/groovy_scripts"


mvn package -DskipTests
cd target

git status
echo "current commit hash: $(git rev-parse --verify HEAD)"

mkdir -p /opt/wi_creator/
sudo mv groovy_scripts-1.0-SNAPSHOT-jar-with-dependencies.jar  /opt/wi_creator/
chmod 777 -R /opt/wi_creator/
ln -s /opt/wi_creator/groovy_scripts-1.0-SNAPSHOT-jar-with-dependencies.jar /usr/bin