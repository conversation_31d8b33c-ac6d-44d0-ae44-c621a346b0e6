key               = "gcp"
unit              = "1"
label_value_type  = "STRING"
label_description = "metric for gcp instance"
api_key           = "ee8432b8-589c-4122-9622-118e97265f94"
team_name         = "core"

channel_recipients = {
    display_name  = "core_Prod_xMatters" 
    type = "webhook_basicauth"
    username = "BC000010001"
    url = "https://hap-api.hsbc.co.uk/api/sendAlert?@aboutSource=GCP"
    password = "DEWQ42@fgwer23"
}

//TODO: fill in
logs                = {
  custom_api_err = {
    name                = "core_Prod_Custom_API_internal_error"
    filter              = "resource.type:\"k8s_container\" AND jsonPayload.message: \"UNY2ERR500\" AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type          = "INT64"
    unit                = "1"
    display_name        = "Custom API internal error - Unexcepted error was raised during the invocation of a custom API"
    labels              = []
    label_extractors    = {}
  },
  pipeline_app_err = {
    name                = "core_Prod_Pipeline_application_fatal_error"
    filter              = "resource.type:\"k8s_container\" AND ((jsonPayload.message: \"UNY2ERR999\" AND NOT jsonPayload.stack_trace: \"Connection reset; nested exception is java.net.SocketException: Connection reset\" AND NOT jsonPayload.stack_trace: \"org.postgresql.util.PSQLException: ERROR: deadlock detected\" AND NOT jsonPayload.stack_trace: \"java.lang.IllegalStateException: EntityManagerFactory is closed\") OR (textPayload: \"org.postgresql.util.PSQLException: ERROR\" AND NOT textPayload: \"deadlock\")) AND NOT (resource.labels.container_name=\"cache\" AND jsonPayload.message: \"Batch ID: ,\")  AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type          = "INT64"
    unit                = "1"
    display_name        = "Unexpected error has caused the Unity 2.0 ETL pipelines to fail - container would be restarted"
    labels              = []
    label_extractors    = {}
  },
  pipeline_app_deadlock_err = {
    name                = "core_Prod_Pipeline_application_retryable_deadlock_error"
    filter              = "resource.type:\"k8s_container\" AND jsonPayload.message: \"UNY2ERR999\" AND jsonPayload.stack_trace: \"org.postgresql.util.PSQLException: ERROR: deadlock detected\" AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type          = "INT64"
    unit                = "1"
    display_name        = "Unexpected error has caused the Unity 2.0 ETL - org.postgresql.util.PSQLException: ERROR: deadlock detected [retryable]"
    labels              = []
    label_extractors    = {}
  },
  bnym_push_api_volume_HK = {
    name                = "HoldingApi_Prod_Bnym_Push_Api_Volume_HK"
    filter              = "resource.type:\"k8s_container\" AND jsonPayload.message: \"Json Message published to BNYM HK\" AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type          = "INT64"
    unit                = "1"
    display_name        = "BNYM PUSH API VOLUME HK - Json Message published to BNYM"
    labels              = []
    label_extractors    = {}
  },
  bnym_push_api_volume_TH = {
    name                = "HoldingApi_Prod_Bnym_Push_Api_Volume_TH"
    filter              = "resource.type:\"k8s_container\" AND jsonPayload.message: \"Json Message published to BNYM TH\" AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type          = "INT64"
    unit                = "1"
    display_name        = "BNYM PUSH API VOLUME TH - Json Message published to BNYM"
    labels              = []
    label_extractors    = {}
  },
  bnym_push_api_volume_KR = {
    name                = "HoldingApi_Prod_Bnym_Push_Api_Volume_KR"
    filter              = "resource.type:\"k8s_container\" AND jsonPayload.message: \"Json Message published to BNYM KR\" AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type           = "INT64"
    unit                = "1"
    display_name        = "BNYM PUSH API VOLUME KR - Json Message published to BNYM"
    labels              = []
    label_extractors    = {}
  },
  bnym_push_api_volume_AU = {
    name                = "HoldingApi_Prod_Bnym_Push_Api_Volume_AU"
    filter              = "resource.type:\"k8s_container\" AND jsonPayload.message: \"Json Message published to BNYM AU\" AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type           = "INT64"
    unit                = "1"
    display_name        = "BNYM PUSH API VOLUME AU - Json Message published to BNYM"
    labels              = []
    label_extractors    = {}
  },
  bnym_push_api_volume_PH = {
    name                = "HoldingApi_Prod_Bnym_Push_Api_Volume_PH"
    filter              = "resource.type:\"k8s_container\" AND jsonPayload.message: \"Json Message published to BNYM PH\" AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type           = "INT64"
    unit                = "1"
    display_name        = "BNYM PUSH API VOLUME PH - Json Message published to BNYM"
    labels              = []
    label_extractors    = {}
  },
  bnym_push_api_volume_MY = {
    name                = "HoldingApi_Prod_Bnym_Push_Api_Volume_MY"
    filter              = "resource.type: \"k8s_container\" AND jsonPayload.message: \"Json Message published to BNYM MY\" AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type           = "INT64"
    unit                = "1"
    display_name        = "BNYM PUSH API VOLUME MY - Json Message published to BNYM"
    labels              = []
    label_extractors    = {}
  },
  bnym_push_api_volume_VN = {
    name                = "HoldingApi_Prod_Bnym_Push_Api_Volume_VN"
    filter              = "resource.type:\"k8s_container\" AND jsonPayload.message: \"Json Message published to BNYM VN\" AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type           = "INT64"
    unit                = "1"
    display_name        = "BNYM PUSH API VOLUME VN - Json Message published to BNYM"
    labels              = []
    label_extractors    = {}
  },
  bnym_push_api_volume_NZ = {
    name                = "HoldingApi_Prod_Bnym_Push_Api_Volume_NZ"
    filter              = "resource.type:\"k8s_container\" AND jsonPayload.message: \"Json Message published to BNYM NZ\" AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type           = "INT64"
    unit                = "1"
    display_name        = "BNYM PUSH API VOLUME NZ - Json Message published to BNYM"
    labels              = []
    label_extractors    = {}
  },
  bnym_push_api_volume_AE = {
    name                = "HoldingApi_Prod_Bnym_Push_Api_Volume_AE"
    filter              = "resource.type:\"k8s_container\" AND jsonPayload.message: \"Json Message published to BNYM AE\" AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type           = "INT64"
    unit                = "1"
    display_name        = "BNYM PUSH API VOLUME AE - Json Message published to BNYM"
    labels              = []
    label_extractors    = {}
  },
  bnym_push_api_volume_BH = {
    name                = "HoldingApi_Prod_Bnym_Push_Api_Volume_BH"
    filter              = "resource.type:\"k8s_container\" AND jsonPayload.message: \"Json Message published to BNYM BH\" AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type           = "INT64"
    unit                = "1"
    display_name        = "BNYM PUSH API VOLUME BH - Json Message published to BNYM"
    labels              = []
    label_extractors    = {}
  },
  bnym_push_api_volume_TW = {
    name                = "HoldingApi_Prod_Bnym_Push_Api_Volume_TW"
    filter              = "resource.type:\"k8s_container\" AND jsonPayload.message: \"Json Message published to BNYM TW\" AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type           = "INT64"
    unit                = "1"
    display_name        = "BNYM PUSH API VOLUME TW - Json Message published to BNYM"
    labels              = []
    label_extractors    = {}
  },
  bnym_push_api_volume_SG = {
    name                = "HoldingApi_Prod_Bnym_Push_Api_Volume_SG"
    filter              = "resource.type:\"k8s_container\" AND jsonPayload.message: \"Json Message published to BNYM SG\" AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type           = "INT64"
    unit                = "1"
    display_name        = "BNYM PUSH API VOLUME SG - Json Message published to BNYM"
    labels              = []
    label_extractors    = {}
  },
  bnym_push_api_volume_ID = {
    name                = "HoldingApi_Prod_Bnym_Push_Api_Volume_ID"
    filter              = "resource.type:\"k8s_container\" AND jsonPayload.message: \"Json Message published to BNYM ID\" AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type           = "INT64"
    unit                = "1"
    display_name        = "BNYM PUSH API VOLUME ID - Json Message published to BNYM"
    labels              = []
    label_extractors    = {}
  },
  bnym_push_api_new_trade_trigger_only_volume_HK = {
    name                = "HoldingApi_Prod_Bnym_Push_Api_New_Trade_Trigger_Only_Volume_HK"
    filter              = "resource.type: \"k8s_container\" AND jsonPayload.message: (\"Trigger true\" AND \"id(HK-\" AND  \"new_trade(true) market_reference_changed(true) trade_status_changed(true)\") AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type           = "INT64"
    unit                = "1"
    display_name        = "BNYM PUSH API NEW TRADE TRIGGER ONLY VOLUME HK - Json Message published to BNYM"
    labels              = []
    label_extractors    = {}
  },
  bnym_push_api_new_trade_trigger_only_volume_TH = {
    name                = "HoldingApi_Prod_Bnym_Push_Api_New_Trade_Trigger_Only_Volume_TH"
    filter              = "resource.type: \"k8s_container\" AND jsonPayload.message: (\"Trigger true\" AND \"id(TH-\" AND \"new_trade(true) market_reference_changed(true) trade_status_changed(true)\") AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type           = "INT64"
    unit                = "1"
    display_name        = "BNYM PUSH API NEW TRADE TRIGGER ONLY VOLUME TH - Json Message published to BNYM"
    labels              = []
    label_extractors    = {}
  },
  bnym_push_api_new_trade_trigger_only_volume_AU = {
    name                = "HoldingApi_Prod_Bnym_Push_Api_New_Trade_Trigger_Only_Volume_AU"
    filter              = "resource.type: \"k8s_container\" AND jsonPayload.message: (\"Trigger true\" AND \"id(AU-\" AND \"new_trade(true) market_reference_changed(true) trade_status_changed(true)\") AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type           = "INT64"
    unit                = "1"
    display_name        = "BNYM PUSH API NEW TRADE TRIGGER ONLY VOLUME AU - Json Message published to BNYM"
    labels              = []
    label_extractors    = {}
  },
  bnym_push_api_new_trade_trigger_only_volume_MY = {
    name                = "HoldingApi_Prod_Bnym_Push_Api_New_Trade_Trigger_Only_Volume_MY"
    filter              = "resource.type: \"k8s_container\" AND jsonPayload.message: (\"Trigger true\" AND \"id(MY-\" AND \"new_trade(true) market_reference_changed(true) trade_status_changed(true)\") AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type           = "INT64"
    unit                = "1"
    display_name        = "BNYM PUSH API NEW TRADE TRIGGER ONLY VOLUME MY - Json Message published to BNYM"
    labels              = []
    label_extractors    = {}
  },
  bnym_push_api_new_trade_trigger_only_volume_NZ = {
    name                = "HoldingApi_Prod_Bnym_Push_Api_New_Trade_Trigger_Only_Volume_NZ"
    filter              = "resource.type: \"k8s_container\" AND jsonPayload.message: (\"Trigger true\" AND \"id(NZ-\" AND \"new_trade(true) market_reference_changed(true) trade_status_changed(true)\") AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type           = "INT64"
    unit                = "1"
    display_name        = "BNYM PUSH API NEW TRADE TRIGGER ONLY VOLUME NZ - Json Message published to BNYM"
    labels              = []
    label_extractors    = {}
  },
  bnym_push_api_new_trade_trigger_only_volume_VN = {
    name                = "HoldingApi_Prod_Bnym_Push_Api_New_Trade_Trigger_Only_Volume_VN"
    filter              = "resource.type: \"k8s_container\" AND jsonPayload.message: (\"Trigger true\" AND \"id(VN-\" AND \"new_trade(true) market_reference_changed(true) trade_status_changed(true)\") AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type           = "INT64"
    unit                = "1"
    display_name        = "BNYM PUSH API NEW TRADE TRIGGER ONLY VOLUME VN - Json Message published to BNYM"
    labels              = []
    label_extractors    = {}
  },
  bnym_push_api_new_trade_trigger_only_volume_AE = {
    name                = "HoldingApi_Prod_Bnym_Push_Api_New_Trade_Trigger_Only_Volume_AE"
    filter              = "resource.type: \"k8s_container\" AND jsonPayload.message: (\"Trigger true\" AND \"id(AE-\" AND \"new_trade(true) market_reference_changed(true) trade_status_changed(true)\") AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type           = "INT64"
    unit                = "1"
    display_name        = "BNYM PUSH API NEW TRADE TRIGGER ONLY VOLUME AE - Json Message published to BNYM"
    labels              = []
    label_extractors    = {}
  },
  bnym_push_api_new_trade_trigger_only_volume_PH = {
    name                = "HoldingApi_Prod_Bnym_Push_Api_New_Trade_Trigger_Only_Volume_PH"
    filter              = "resource.type: \"k8s_container\" AND jsonPayload.message: (\"Trigger true\" AND \"id(PH-\" AND \"new_trade(true) market_reference_changed(true) trade_status_changed(true)\") AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type           = "INT64"
    unit                = "1"
    display_name        = "BNYM PUSH API NEW TRADE TRIGGER ONLY VOLUME PH - Json Message published to BNYM"
    labels              = []
    label_extractors    = {}
  },
  bnym_push_api_new_trade_trigger_only_volume_BH = {
    name                = "HoldingApi_Prod_Bnym_Push_Api_New_Trade_Trigger_Only_Volume_BH"
    filter              = "resource.type: \"k8s_container\" AND jsonPayload.message: (\"Trigger true\" AND \"id(BH-\" AND \"new_trade(true) market_reference_changed(true) trade_status_changed(true)\") AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type           = "INT64"
    unit                = "1"
    display_name        = "BNYM PUSH API NEW TRADE TRIGGER ONLY VOLUME BH - Json Message published to BNYM"
    labels              = []
    label_extractors    = {}
  },
  bnym_push_api_new_trade_trigger_only_volume_TW = {
    name                = "HoldingApi_Prod_Bnym_Push_Api_New_Trade_Trigger_Only_Volume_TW"
    filter              = "resource.type: \"k8s_container\" AND jsonPayload.message: (\"Trigger true\" AND \"id(TW-\" AND \"new_trade(true) market_reference_changed(true) trade_status_changed(true)\") AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type           = "INT64"
    unit                = "1"
    display_name        = "BNYM PUSH API NEW TRADE TRIGGER ONLY VOLUME TW - Json Message published to BNYM"
    labels              = []
    label_extractors    = {}
  },
  bnym_push_api_new_trade_trigger_only_volume_SG = {
    name                = "HoldingApi_Prod_Bnym_Push_Api_New_Trade_Trigger_Only_Volume_SG"
    filter              = "resource.type: \"k8s_container\" AND jsonPayload.message: (\"Trigger true\" AND \"id(SG-\" AND \"new_trade(true) market_reference_changed(true) trade_status_changed(true)\") AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type           = "INT64"
    unit                = "1"
    display_name        = "BNYM PUSH API NEW TRADE TRIGGER ONLY VOLUME SG - Json Message published to BNYM"
    labels              = []
    label_extractors    = {}
  },
  bnym_push_api_new_trade_trigger_only_volume_ID = {
    name                = "HoldingApi_Prod_Bnym_Push_Api_New_Trade_Trigger_Only_Volume_ID"
    filter              = "resource.type: \"k8s_container\" AND jsonPayload.message: (\"Trigger true\" AND \"id(ID-\" AND \"new_trade(true) market_reference_changed(true) trade_status_changed(true)\") AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type           = "INT64"
    unit                = "1"
    display_name        = "BNYM PUSH API NEW TRADE TRIGGER ONLY VOLUME ID - Json Message published to BNYM"
    labels              = []
    label_extractors    = {}
  },
  bnym_push_api_market_reference_trigger_only_volume_HK = {
    name                = "HoldingApi_Prod_Bnym_Push_Api_Market_Reference_Trigger_Only_Volume_HK"
    filter              = "resource.type: \"k8s_container\" AND jsonPayload.message: (\"Trigger true\" AND \"id(HK-\" AND \"new_trade(false) market_reference_changed(true) trade_status_changed(false)\" AND \"swift_reason_code_changed(false) additional_reason_changed(false)\") AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type           = "INT64"
    unit                = "1"
    display_name        = "BNYM PUSH API MARKET REFERENCE TRIGGER ONLY VOLUME HK - Json Message published to BNYM"
    labels              = []
    label_extractors    = {}
  },
  bnym_push_api_market_reference_trigger_only_volume_TH = {
    name                = "HoldingApi_Prod_Bnym_Push_Api_Market_Reference_Trigger_Only_Volume_TH"
    filter              = "resource.type: \"k8s_container\" AND jsonPayload.message: (\"Trigger true\" AND \"id(TH-\" AND \"new_trade(false) market_reference_changed(true) trade_status_changed(false)\" AND \"swift_reason_code_changed(false) additional_reason_changed(false)\") AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type           = "INT64"
    unit                = "1"
    display_name        = "BNYM PUSH API MARKET REFERENCE TRIGGER ONLY VOLUME TH - Json Message published to BNYM"
    labels              = []
    label_extractors    = {}
  },
  bnym_push_api_market_reference_trigger_only_volume_AU = {
    name                = "HoldingApi_Prod_Bnym_Push_Api_Market_Reference_Trigger_Only_Volume_AU"
    filter              = "resource.type: \"k8s_container\" AND jsonPayload.message: (\"Trigger true\" AND \"id(AU-\" AND \"new_trade(false) market_reference_changed(true) trade_status_changed(false)\" AND \"swift_reason_code_changed(false) additional_reason_changed(false)\") AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type           = "INT64"
    unit                = "1"
    display_name        = "BNYM PUSH API MARKET REFERENCE TRIGGER ONLY VOLUME AU - Json Message published to BNYM"
    labels              = []
    label_extractors    = {}
  },
  bnym_push_api_market_reference_trigger_only_volume_MY = {
    name                = "HoldingApi_Prod_Bnym_Push_Api_Market_Reference_Trigger_Only_Volume_MY"
    filter              = "resource.type: \"k8s_container\" AND jsonPayload.message: (\"Trigger true\" AND \"id(MY-\" AND \"new_trade(false) market_reference_changed(true) trade_status_changed(false)\" AND \"swift_reason_code_changed(false) additional_reason_changed(false)\") AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type           = "INT64"
    unit                = "1"
    display_name        = "BNYM PUSH API MARKET REFERENCE TRIGGER ONLY VOLUME MY - Json Message published to BNYM"
    labels              = []
    label_extractors    = {}
  },
  bnym_push_api_market_reference_trigger_only_volume_VN = {
    name                = "HoldingApi_Prod_Bnym_Push_Api_Market_Reference_Trigger_Only_Volume_VN"
    filter              = "resource.type: \"k8s_container\" AND jsonPayload.message: (\"Trigger true\" AND \"id(VN-\" AND \"new_trade(false) market_reference_changed(true) trade_status_changed(false)\" AND \"swift_reason_code_changed(false) additional_reason_changed(false)\") AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type           = "INT64"
    unit                = "1"
    display_name        = "BNYM PUSH API MARKET REFERENCE TRIGGER ONLY VOLUME VN - Json Message published to BNYM"
    labels              = []
    label_extractors    = {}
  },
  bnym_push_api_market_reference_trigger_only_volume_NZ = {
    name                = "HoldingApi_Prod_Bnym_Push_Api_Market_Reference_Trigger_Only_Volume_NZ"
    filter              = "resource.type: \"k8s_container\" AND jsonPayload.message: (\"Trigger true\" AND \"id(NZ-\" AND \"new_trade(false) market_reference_changed(true) trade_status_changed(false)\" AND \"swift_reason_code_changed(false) additional_reason_changed(false)\") AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type           = "INT64"
    unit                = "1"
    display_name        = "BNYM PUSH API MARKET REFERENCE TRIGGER ONLY VOLUME NZ - Json Message published to BNYM"
    labels              = []
    label_extractors    = {}
  },
  bnym_push_api_market_reference_trigger_only_volume_AE = {
    name                = "HoldingApi_Prod_Bnym_Push_Api_Market_Reference_Trigger_Only_Volume_AE"
    filter              = "resource.type: \"k8s_container\" AND jsonPayload.message: (\"Trigger true\" AND \"id(AE-\" AND \"new_trade(false) market_reference_changed(true) trade_status_changed(false)\" AND \"swift_reason_code_changed(false) additional_reason_changed(false)\") AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type           = "INT64"
    unit                = "1"
    display_name        = "BNYM PUSH API MARKET REFERENCE TRIGGER ONLY VOLUME AE - Json Message published to BNYM"
    labels              = []
    label_extractors    = {}
  },
  bnym_push_api_market_reference_trigger_only_volume_PH = {
    name                = "HoldingApi_Prod_Bnym_Push_Api_Market_Reference_Trigger_Only_Volume_PH"
    filter              = "resource.type: \"k8s_container\" AND jsonPayload.message: (\"Trigger true\" AND \"id(PH-\" AND \"new_trade(false) market_reference_changed(true) trade_status_changed(false)\" AND \"swift_reason_code_changed(false) additional_reason_changed(false)\") AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type           = "INT64"
    unit                = "1"
    display_name        = "BNYM PUSH API MARKET REFERENCE TRIGGER ONLY VOLUME PH - Json Message published to BNYM"
    labels              = []
    label_extractors    = {}
  },
  bnym_push_api_market_reference_trigger_only_volume_BH = {
    name                = "HoldingApi_Prod_Bnym_Push_Api_Market_Reference_Trigger_Only_Volume_BH"
    filter              = "resource.type: \"k8s_container\" AND jsonPayload.message: (\"Trigger true\" AND \"id(BH-\" AND \"new_trade(false) market_reference_changed(true) trade_status_changed(false)\" AND \"swift_reason_code_changed(false) additional_reason_changed(false)\") AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type           = "INT64"
    unit                = "1"
    display_name        = "BNYM PUSH API MARKET REFERENCE TRIGGER ONLY VOLUME BH - Json Message published to BNYM"
    labels              = []
    label_extractors    = {}
  },
  bnym_push_api_market_reference_trigger_only_volume_TW = {
    name                = "HoldingApi_Prod_Bnym_Push_Api_Market_Reference_Trigger_Only_Volume_TW"
    filter              = "resource.type: \"k8s_container\" AND jsonPayload.message: (\"Trigger true\" AND \"id(TW-\" AND \"new_trade(false) market_reference_changed(true) trade_status_changed(false)\" AND \"swift_reason_code_changed(false) additional_reason_changed(false)\") AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type           = "INT64"
    unit                = "1"
    display_name        = "BNYM PUSH API MARKET REFERENCE TRIGGER ONLY VOLUME TW - Json Message published to BNYM"
    labels              = []
    label_extractors    = {}
  },
  bnym_push_api_market_reference_trigger_only_volume_SG = {
    name                = "HoldingApi_Prod_Bnym_Push_Api_Market_Reference_Trigger_Only_Volume_SG"
    filter              = "resource.type: \"k8s_container\" AND jsonPayload.message: (\"Trigger true\" AND \"id(SG-\" AND \"new_trade(false) market_reference_changed(true) trade_status_changed(false)\" AND \"swift_reason_code_changed(false) additional_reason_changed(false)\") AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type           = "INT64"
    unit                = "1"
    display_name        = "BNYM PUSH API MARKET REFERENCE TRIGGER ONLY VOLUME SG - Json Message published to BNYM"
    labels              = []
    label_extractors    = {}
  },
  bnym_push_api_market_reference_trigger_only_volume_ID = {
    name                = "HoldingApi_Prod_Bnym_Push_Api_Market_Reference_Trigger_Only_Volume_ID"
    filter              = "resource.type: \"k8s_container\" AND jsonPayload.message: (\"Trigger true\" AND \"id(ID-\" AND \"new_trade(false) market_reference_changed(true) trade_status_changed(false)\" AND \"swift_reason_code_changed(false) additional_reason_changed(false)\") AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type           = "INT64"
    unit                = "1"
    display_name        = "BNYM PUSH API MARKET REFERENCE TRIGGER ONLY VOLUME ID - Json Message published to BNYM"
    labels              = []
    label_extractors    = {}
  },
  bnym_push_api_trade_status_trigger_only_volume_HK = {
    name                = "HoldingApi_Prod_Bnym_Push_Api_Trade_Status_Trigger_Only_Volume_HK"
    filter              = "resource.type: \"k8s_container\" AND jsonPayload.message: (\"Trigger true\" AND \"id(HK-\" AND \"new_trade(false) market_reference_changed(false) trade_status_changed(true)\" AND \"swift_reason_code_changed(false)\" AND \"additional_reason_changed(false)\") AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type           = "INT64"
    unit                = "1"
    display_name        = "BNYM PUSH API TRADE STATUS TRIGGER ONLY VOLUME HK - Json Message published to BNYM"
    labels              = []
    label_extractors    = {}
  },
  bnym_push_api_trade_status_trigger_only_volume_TH = {
    name                = "HoldingApi_Prod_Bnym_Push_Api_Trade_Status_Trigger_Only_Volume_TH"
    filter              = "resource.type: \"k8s_container\" AND jsonPayload.message: (\"Trigger true\" AND \"id(TH-\" AND \"new_trade(false) market_reference_changed(false) trade_status_changed(true)\" AND \"swift_reason_code_changed(false)\" AND \"additional_reason_changed(false)\") AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type           = "INT64"
    unit                = "1"
    display_name        = "BNYM PUSH API TRADE STATUS TRIGGER ONLY VOLUME TH - Json Message published to BNYM"
    labels              = []
    label_extractors    = {}
  },
  bnym_push_api_trade_status_trigger_only_volume_AU = {
    name                = "HoldingApi_Prod_Bnym_Push_Api_Trade_Status_Trigger_Only_Volume_AU"
    filter              = "resource.type: \"k8s_container\" AND jsonPayload.message: (\"Trigger true\" AND \"id(AU-\" AND \"new_trade(false) market_reference_changed(false) trade_status_changed(true)\" AND \"swift_reason_code_changed(false)\" AND \"additional_reason_changed(false)\") AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type           = "INT64"
    unit                = "1"
    display_name        = "BNYM PUSH API TRADE STATUS TRIGGER ONLY VOLUME AU - Json Message published to BNYM"
    labels              = []
    label_extractors    = {}
  },
  bnym_push_api_trade_status_trigger_only_volume_MY = {
    name                = "HoldingApi_Prod_Bnym_Push_Api_Trade_Status_Trigger_Only_Volume_MY"
    filter              = "resource.type: \"k8s_container\" AND jsonPayload.message: (\"Trigger true\" AND \"id(MY-\" AND \"new_trade(false) market_reference_changed(false) trade_status_changed(true)\" AND \"swift_reason_code_changed(false)\" AND \"additional_reason_changed(false)\") AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type           = "INT64"
    unit                = "1"
    display_name        = "BNYM PUSH API TRADE STATUS TRIGGER ONLY VOLUME MY - Json Message published to BNYM"
    labels              = []
    label_extractors    = {}
  },
  bnym_push_api_trade_status_trigger_only_volume_VN = {
    name                = "HoldingApi_Prod_Bnym_Push_Api_Trade_Status_Trigger_Only_Volume_VN"
    filter              = "resource.type: \"k8s_container\" AND jsonPayload.message: (\"Trigger true\" AND \"id(VN-\" AND \"new_trade(false) market_reference_changed(false) trade_status_changed(true)\" AND \"swift_reason_code_changed(false)\" AND \"additional_reason_changed(false)\") AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type           = "INT64"
    unit                = "1"
    display_name        = "BNYM PUSH API TRADE STATUS TRIGGER ONLY VOLUME VN - Json Message published to BNYM"
    labels              = []
    label_extractors    = {}
  },
  bnym_push_api_trade_status_trigger_only_volume_NZ = {
    name                = "HoldingApi_Prod_Bnym_Push_Api_Trade_Status_Trigger_Only_Volume_NZ"
    filter              = "resource.type: \"k8s_container\" AND jsonPayload.message: (\"Trigger true\" AND \"id(NZ-\" AND \"new_trade(false) market_reference_changed(false) trade_status_changed(true)\" AND \"swift_reason_code_changed(false)\" AND \"additional_reason_changed(false)\") AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type           = "INT64"
    unit                = "1"
    display_name        = "BNYM PUSH API TRADE STATUS TRIGGER ONLY VOLUME NZ - Json Message published to BNYM"
    labels              = []
    label_extractors    = {}
  },
  bnym_push_api_trade_status_trigger_only_volume_AE = {
    name                = "HoldingApi_Prod_Bnym_Push_Api_Trade_Status_Trigger_Only_Volume_AE"
    filter              = "resource.type: \"k8s_container\" AND jsonPayload.message: (\"Trigger true\" AND \"id(AE-\" AND \"new_trade(false) market_reference_changed(false) trade_status_changed(true)\" AND \"swift_reason_code_changed(false)\" AND \"additional_reason_changed(false)\") AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type           = "INT64"
    unit                = "1"
    display_name        = "BNYM PUSH API TRADE STATUS TRIGGER ONLY VOLUME AE - Json Message published to BNYM"
    labels              = []
    label_extractors    = {}
  },
  bnym_push_api_trade_status_trigger_only_volume_PH = {
    name                = "HoldingApi_Prod_Bnym_Push_Api_Trade_Status_Trigger_Only_Volume_PH"
    filter              = "resource.type: \"k8s_container\" AND jsonPayload.message: (\"Trigger true\" AND \"id(PH-\" AND \"new_trade(false) market_reference_changed(false) trade_status_changed(true)\" AND \"swift_reason_code_changed(false)\" AND \"additional_reason_changed(false)\") AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type           = "INT64"
    unit                = "1"
    display_name        = "BNYM PUSH API TRADE STATUS TRIGGER ONLY VOLUME PH - Json Message published to BNYM"
    labels              = []
    label_extractors    = {}
  },
  bnym_push_api_trade_status_trigger_only_volume_BH = {
    name                = "HoldingApi_Prod_Bnym_Push_Api_Trade_Status_Trigger_Only_Volume_BH"
    filter              = "resource.type: \"k8s_container\" AND jsonPayload.message: (\"Trigger true\" AND \"id(BH-\" AND \"new_trade(false) market_reference_changed(false) trade_status_changed(true)\" AND \"swift_reason_code_changed(false)\" AND \"additional_reason_changed(false)\") AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type           = "INT64"
    unit                = "1"
    display_name        = "BNYM PUSH API TRADE STATUS TRIGGER ONLY VOLUME BH - Json Message published to BNYM"
    labels              = []
    label_extractors    = {}
  },
  bnym_push_api_trade_status_trigger_only_volume_TW = {
    name                = "HoldingApi_Prod_Bnym_Push_Api_Trade_Status_Trigger_Only_Volume_TW"
    filter              = "resource.type: \"k8s_container\" AND jsonPayload.message: (\"Trigger true\" AND \"id(TW-\" AND \"new_trade(false) market_reference_changed(false) trade_status_changed(true)\" AND \"swift_reason_code_changed(false)\" AND \"additional_reason_changed(false)\") AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type           = "INT64"
    unit                = "1"
    display_name        = "BNYM PUSH API TRADE STATUS TRIGGER ONLY VOLUME TW - Json Message published to BNYM"
    labels              = []
    label_extractors    = {}
  },
  bnym_push_api_trade_status_trigger_only_volume_SG = {
    name                = "HoldingApi_Prod_Bnym_Push_Api_Trade_Status_Trigger_Only_Volume_SG"
    filter              = "resource.type: \"k8s_container\" AND jsonPayload.message: (\"Trigger true\" AND \"id(SG-\" AND \"new_trade(false) market_reference_changed(false) trade_status_changed(true)\" AND \"swift_reason_code_changed(false)\" AND \"additional_reason_changed(false)\") AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type           = "INT64"
    unit                = "1"
    display_name        = "BNYM PUSH API TRADE STATUS TRIGGER ONLY VOLUME SG - Json Message published to BNYM"
    labels              = []
    label_extractors    = {}
  },
  bnym_push_api_trade_status_trigger_only_volume_ID = {
    name                = "HoldingApi_Prod_Bnym_Push_Api_Trade_Status_Trigger_Only_Volume_ID"
    filter              = "resource.type: \"k8s_container\" AND jsonPayload.message: (\"Trigger true\" AND \"id(ID-\" AND \"new_trade(false) market_reference_changed(false) trade_status_changed(true)\" AND \"swift_reason_code_changed(false)\" AND \"additional_reason_changed(false)\") AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type           = "INT64"
    unit                = "1"
    display_name        = "BNYM PUSH API TRADE STATUS TRIGGER ONLY VOLUME ID - Json Message published to BNYM"
    labels              = []
    label_extractors    = {}
  },
  bnym_push_api_market_reference_trade_status_trigger_both_volume_HK = {
    name                = "HoldingApi_Prod_Bnym_Push_Api_Market_Reference_Trade_Status_Trigger_Both_Volume_HK"
    filter              = "resource.type: \"k8s_container\" AND jsonPayload.message: (\"Trigger true\" AND \"id(HK-\" AND \"new_trade(false) market_reference_changed(true) trade_status_changed(true)\" AND \"swift_reason_code_changed(false) additional_reason_changed(false)\") AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type           = "INT64"
    unit                = "1"
    display_name        = "BNYM PUSH API MARKET REFERENCE TRADE STATUS TRIGGER BOTH VOLUME HK - Json Message published to BNYM"
    labels              = []
    label_extractors    = {}
  },
  bnym_push_api_market_reference_trade_status_trigger_both_volume_TH = {
    name                = "HoldingApi_Prod_Bnym_Push_Api_Market_Reference_Trade_Status_Trigger_Both_Volume_TH"
    filter              = "resource.type: \"k8s_container\" AND jsonPayload.message: (\"Trigger true\" AND \"id(TH-\" AND \"new_trade(false) market_reference_changed(true) trade_status_changed(true)\" AND \"swift_reason_code_changed(false) additional_reason_changed(false)\") AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type           = "INT64"
    unit                = "1"
    display_name        = "BNYM PUSH API MARKET REFERENCE TRADE STATUS TRIGGER BOTH VOLUME TH - Json Message published to BNYM"
    labels              = []
    label_extractors    = {}
  },
  bnym_push_api_market_reference_trade_status_trigger_both_volume_AU = {
    name                = "HoldingApi_Prod_Bnym_Push_Api_Market_Reference_Trade_Status_Trigger_Both_Volume_AU"
    filter              = "resource.type: \"k8s_container\" AND jsonPayload.message: (\"Trigger true\" AND \"id(AU-\" AND \"new_trade(false) market_reference_changed(true) trade_status_changed(true)\" AND \"swift_reason_code_changed(false) additional_reason_changed(false)\") AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type           = "INT64"
    unit                = "1"
    display_name        = "BNYM PUSH API MARKET REFERENCE TRADE STATUS TRIGGER BOTH VOLUME AU - Json Message published to BNYM"
    labels              = []
    label_extractors    = {}
  },
  bnym_push_api_market_reference_trade_status_trigger_both_volume_MY = {
    name                = "HoldingApi_Prod_Bnym_Push_Api_Market_Reference_Trade_Status_Trigger_Both_Volume_MY"
    filter              = "resource.type: \"k8s_container\" AND jsonPayload.message: (\"Trigger true\" AND \"id(MY-\" AND \"new_trade(false) market_reference_changed(true) trade_status_changed(true)\" AND \"swift_reason_code_changed(false) additional_reason_changed(false)\") AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type           = "INT64"
    unit                = "1"
    display_name        = "BNYM PUSH API MARKET REFERENCE TRADE STATUS TRIGGER BOTH VOLUME MY - Json Message published to BNYM"
    labels              = []
    label_extractors    = {}
  },
  bnym_push_api_market_reference_trade_status_trigger_both_volume_VN = {
    name                = "HoldingApi_Prod_Bnym_Push_Api_Market_Reference_Trade_Status_Trigger_Both_Volume_VN"
    filter              = "resource.type: \"k8s_container\" AND jsonPayload.message: (\"Trigger true\" AND \"id(VN-\" AND \"new_trade(false) market_reference_changed(true) trade_status_changed(true)\" AND \"swift_reason_code_changed(false) additional_reason_changed(false)\") AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type           = "INT64"
    unit                = "1"
    display_name        = "BNYM PUSH API MARKET REFERENCE TRADE STATUS TRIGGER BOTH VOLUME VN - Json Message published to BNYM"
    labels              = []
    label_extractors    = {}
  },
  bnym_push_api_market_reference_trade_status_trigger_both_volume_NZ = {
    name                = "HoldingApi_Prod_Bnym_Push_Api_Market_Reference_Trade_Status_Trigger_Both_Volume_NZ"
    filter              = "resource.type: \"k8s_container\" AND jsonPayload.message: (\"Trigger true\" AND \"id(NZ-\" AND \"new_trade(false) market_reference_changed(true) trade_status_changed(true)\" AND \"swift_reason_code_changed(false) additional_reason_changed(false)\") AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type           = "INT64"
    unit                = "1"
    display_name        = "BNYM PUSH API MARKET REFERENCE TRADE STATUS TRIGGER BOTH VOLUME NZ - Json Message published to BNYM"
    labels              = []
    label_extractors    = {}
  },
  bnym_push_api_market_reference_trade_status_trigger_both_volume_AE = {
    name                = "HoldingApi_Prod_Bnym_Push_Api_Market_Reference_Trade_Status_Trigger_Both_Volume_AE"
    filter              = "resource.type: \"k8s_container\" AND jsonPayload.message: (\"Trigger true\" AND \"id(AE-\" AND \"new_trade(false) market_reference_changed(true) trade_status_changed(true)\" AND \"swift_reason_code_changed(false) additional_reason_changed(false)\") AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type           = "INT64"
    unit                = "1"
    display_name        = "BNYM PUSH API MARKET REFERENCE TRADE STATUS TRIGGER BOTH VOLUME AE - Json Message published to BNYM"
    labels              = []
    label_extractors    = {}
  },
  bnym_push_api_market_reference_trade_status_trigger_both_volume_PH = {
    name                = "HoldingApi_Prod_Bnym_Push_Api_Market_Reference_Trade_Status_Trigger_Both_Volume_PH"
    filter              = "resource.type: \"k8s_container\" AND jsonPayload.message: (\"Trigger true\" AND \"id(PH-\" AND \"new_trade(false) market_reference_changed(true) trade_status_changed(true)\" AND \"swift_reason_code_changed(false) additional_reason_changed(false)\") AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type           = "INT64"
    unit                = "1"
    display_name        = "BNYM PUSH API MARKET REFERENCE TRADE STATUS TRIGGER BOTH VOLUME PH - Json Message published to BNYM"
    labels              = []
    label_extractors    = {}
  },
  bnym_push_api_market_reference_trade_status_trigger_both_volume_BH = {
    name                = "HoldingApi_Prod_Bnym_Push_Api_Market_Reference_Trade_Status_Trigger_Both_Volume_BH"
    filter              = "resource.type: \"k8s_container\" AND jsonPayload.message: (\"Trigger true\" AND \"id(BH-\" AND \"new_trade(false) market_reference_changed(true) trade_status_changed(true)\" AND \"swift_reason_code_changed(false) additional_reason_changed(false)\") AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type           = "INT64"
    unit                = "1"
    display_name        = "BNYM PUSH API MARKET REFERENCE TRADE STATUS TRIGGER BOTH VOLUME BH - Json Message published to BNYM"
    labels              = []
    label_extractors    = {}
  },
  bnym_push_api_market_reference_trade_status_trigger_both_volume_TW = {
    name                = "HoldingApi_Prod_Bnym_Push_Api_Market_Reference_Trade_Status_Trigger_Both_Volume_TW"
    filter              = "resource.type: \"k8s_container\" AND jsonPayload.message: (\"Trigger true\" AND \"id(TW-\" AND \"new_trade(false) market_reference_changed(true) trade_status_changed(true)\" AND \"swift_reason_code_changed(false) additional_reason_changed(false)\") AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type           = "INT64"
    unit                = "1"
    display_name        = "BNYM PUSH API MARKET REFERENCE TRADE STATUS TRIGGER BOTH VOLUME TW - Json Message published to BNYM"
    labels              = []
    label_extractors    = {}
  },
  bnym_push_api_market_reference_trade_status_trigger_both_volume_SG = {
    name                = "HoldingApi_Prod_Bnym_Push_Api_Market_Reference_Trade_Status_Trigger_Both_Volume_SG"
    filter              = "resource.type: \"k8s_container\" AND jsonPayload.message: (\"Trigger true\" AND \"id(SG-\" AND \"new_trade(false) market_reference_changed(true) trade_status_changed(true)\" AND \"swift_reason_code_changed(false) additional_reason_changed(false)\") AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type           = "INT64"
    unit                = "1"
    display_name        = "BNYM PUSH API MARKET REFERENCE TRADE STATUS TRIGGER BOTH VOLUME SG - Json Message published to BNYM"
    labels              = []
    label_extractors    = {}
  },
  bnym_push_api_market_reference_trade_status_trigger_both_volume_ID = {
    name                = "HoldingApi_Prod_Bnym_Push_Api_Market_Reference_Trade_Status_Trigger_Both_Volume_ID"
    filter              = "resource.type: \"k8s_container\" AND jsonPayload.message: (\"Trigger true\" AND \"id(ID-\" AND \"new_trade(false) market_reference_changed(true) trade_status_changed(true)\" AND \"swift_reason_code_changed(false) additional_reason_changed(false)\") AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type           = "INT64"
    unit                = "1"
    display_name        = "BNYM PUSH API MARKET REFERENCE TRADE STATUS TRIGGER BOTH VOLUME ID - Json Message published to BNYM"
    labels              = []
    label_extractors    = {}
  },
  bnym_push_api_reason_code_trigger_only_volume_HK = {
    name                = "HoldingApi_Prod_Bnym_Push_Api_Reason_Code_Trigger_Only_Volume_HK"
    filter              = "resource.type: \"k8s_container\" AND jsonPayload.message: (\"Trigger true\" AND \"id(HK-\" AND \"new_trade(false) market_reference_changed(false) trade_status_changed(false)\" AND \"swift_reason_code_changed(true) additional_reason_changed(false)\") AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type           = "INT64"
    unit                = "1"
    display_name        = "BNYM PUSH API REASON CODE TRIGGER ONLY VOLUME HK - Json Message published to BNYM"
    labels              = []
    label_extractors    = {}
  },
  bnym_push_api_reason_code_trigger_only_volume_TH = {
    name                = "HoldingApi_Prod_Bnym_Push_Api_Reason_Code_Trigger_Only_Volume_TH"
    filter              = "resource.type: \"k8s_container\" AND jsonPayload.message: (\"Trigger true\" AND \"id(TH-\" AND \"new_trade(false) market_reference_changed(false) trade_status_changed(false)\" AND \"swift_reason_code_changed(true) additional_reason_changed(false)\" ) AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type           = "INT64"
    unit                = "1"
    display_name        = "BNYM PUSH API REASON CODE TRIGGER ONLY VOLUME TH - Json Message published to BNYM"
    labels              = []
    label_extractors    = {}
  },
  bnym_push_api_reason_code_trigger_only_volume_AU = {
    name                = "HoldingApi_Prod_Bnym_Push_Api_Reason_Code_Trigger_Only_Volume_AU"
    filter              = "resource.type: \"k8s_container\" AND jsonPayload.message: (\"Trigger true\" AND \"id(AU-\" AND \"new_trade(false) market_reference_changed(false) trade_status_changed(false)\" AND \"swift_reason_code_changed(true) additional_reason_changed(false)\" ) AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type           = "INT64"
    unit                = "1"
    display_name        = "BNYM PUSH API REASON CODE TRIGGER ONLY VOLUME AU - Json Message published to BNYM"
    labels              = []
    label_extractors    = {}
  },
  bnym_push_api_reason_code_trigger_only_volume_MY = {
    name                = "HoldingApi_Prod_Bnym_Push_Api_Reason_Code_Trigger_Only_Volume_MY"
    filter              = "resource.type: \"k8s_container\" AND jsonPayload.message: (\"Trigger true\" AND \"id(MY-\" AND \"new_trade(false) market_reference_changed(false) trade_status_changed(false)\" AND \"swift_reason_code_changed(true) additional_reason_changed(false)\" ) AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type           = "INT64"
    unit                = "1"
    display_name        = "BNYM PUSH API REASON CODE TRIGGER ONLY VOLUME MY - Json Message published to BNYM"
    labels              = []
    label_extractors    = {}
  },
  bnym_push_api_reason_code_trigger_only_volume_VN = {
    name                = "HoldingApi_Prod_Bnym_Push_Api_Reason_Code_Trigger_Only_Volume_VN"
    filter              = "resource.type: \"k8s_container\" AND jsonPayload.message: (\"Trigger true\" AND \"id(VN-\" AND \"new_trade(false) market_reference_changed(false) trade_status_changed(false)\" AND \"swift_reason_code_changed(true) additional_reason_changed(false)\" ) AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type           = "INT64"
    unit                = "1"
    display_name        = "BNYM PUSH API REASON CODE TRIGGER ONLY VOLUME VN - Json Message published to BNYM"
    labels              = []
    label_extractors    = {}
  },
  bnym_push_api_reason_code_trigger_only_volume_NZ = {
    name                = "HoldingApi_Prod_Bnym_Push_Api_Reason_Code_Trigger_Only_Volume_NZ"
    filter              = "resource.type: \"k8s_container\" AND jsonPayload.message: (\"Trigger true\" AND \"id(NZ-\" AND \"new_trade(false) market_reference_changed(false) trade_status_changed(false)\" AND \"swift_reason_code_changed(true) additional_reason_changed(false)\" ) AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type           = "INT64"
    unit                = "1"
    display_name        = "BNYM PUSH API REASON CODE TRIGGER ONLY VOLUME NZ - Json Message published to BNYM"
    labels              = []
    label_extractors    = {}
  },
  bnym_push_api_reason_code_trigger_only_volume_AE = {
    name                = "HoldingApi_Prod_Bnym_Push_Api_Reason_Code_Trigger_Only_Volume_AE"
    filter              = "resource.type: \"k8s_container\" AND jsonPayload.message: (\"Trigger true\" AND \"id(AE-\" AND \"new_trade(false) market_reference_changed(false) trade_status_changed(false)\" AND \"swift_reason_code_changed(true) additional_reason_changed(false)\" ) AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type           = "INT64"
    unit                = "1"
    display_name        = "BNYM PUSH API REASON CODE TRIGGER ONLY VOLUME AE - Json Message published to BNYM"
    labels              = []
    label_extractors    = {}
  },
  bnym_push_api_reason_code_trigger_only_volume_PH = {
    name                = "HoldingApi_Prod_Bnym_Push_Api_Reason_Code_Trigger_Only_Volume_PH"
    filter              = "resource.type: \"k8s_container\" AND jsonPayload.message: (\"Trigger true\" AND \"id(PH-\" AND \"new_trade(false) market_reference_changed(false) trade_status_changed(false)\" AND \"swift_reason_code_changed(true) additional_reason_changed(false)\" ) AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type           = "INT64"
    unit                = "1"
    display_name        = "BNYM PUSH API REASON CODE TRIGGER ONLY VOLUME PH - Json Message published to BNYM"
    labels              = []
    label_extractors    = {}
  },
  bnym_push_api_reason_code_trigger_only_volume_BH = {
    name                = "HoldingApi_Prod_Bnym_Push_Api_Reason_Code_Trigger_Only_Volume_BH"
    filter              = "resource.type: \"k8s_container\" AND jsonPayload.message: (\"Trigger true\" AND \"id(BH-\" AND \"new_trade(false) market_reference_changed(false) trade_status_changed(false)\" AND \"swift_reason_code_changed(true) additional_reason_changed(false)\" ) AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type           = "INT64"
    unit                = "1"
    display_name        = "BNYM PUSH API REASON CODE TRIGGER ONLY VOLUME BH - Json Message published to BNYM"
    labels              = []
    label_extractors    = {}
  },
  bnym_push_api_reason_code_trigger_only_volume_TW = {
    name                = "HoldingApi_Prod_Bnym_Push_Api_Reason_Code_Trigger_Only_Volume_TW"
    filter              = "resource.type: \"k8s_container\" AND jsonPayload.message: (\"Trigger true\" AND \"id(TW-\" AND \"new_trade(false) market_reference_changed(false) trade_status_changed(false)\" AND \"swift_reason_code_changed(true) additional_reason_changed(false)\" ) AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type           = "INT64"
    unit                = "1"
    display_name        = "BNYM PUSH API REASON CODE TRIGGER ONLY VOLUME TW - Json Message published to BNYM"
    labels              = []
    label_extractors    = {}
  },
  bnym_push_api_reason_code_trigger_only_volume_SG = {
    name                = "HoldingApi_Prod_Bnym_Push_Api_Reason_Code_Trigger_Only_Volume_SG"
    filter              = "resource.type: \"k8s_container\" AND jsonPayload.message: (\"Trigger true\" AND \"id(SG-\" AND \"new_trade(false) market_reference_changed(false) trade_status_changed(false)\" AND \"swift_reason_code_changed(true) additional_reason_changed(false)\" ) AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type           = "INT64"
    unit                = "1"
    display_name        = "BNYM PUSH API REASON CODE TRIGGER ONLY VOLUME SG - Json Message published to BNYM"
    labels              = []
    label_extractors    = {}
  },
  bnym_push_api_reason_code_trigger_only_volume_ID = {
    name                = "HoldingApi_Prod_Bnym_Push_Api_Reason_Code_Trigger_Only_Volume_ID"
    filter              = "resource.type: \"k8s_container\" AND jsonPayload.message: (\"Trigger true\" AND \"id(ID-\" AND \"new_trade(false) market_reference_changed(false) trade_status_changed(false)\" AND \"swift_reason_code_changed(true) additional_reason_changed(false)\" ) AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type           = "INT64"
    unit                = "1"
    display_name        = "BNYM PUSH API REASON CODE TRIGGER ONLY VOLUME ID - Json Message published to BNYM"
    labels              = []
    label_extractors    = {}
  },
  bnym_push_api_trade_status_reason_code_trigger_both_volume_HK = {
    name                = "HoldingApi_Prod_Bnym_Push_Api_Trade_Status_Reason_Code_Trigger_Both_Volume_HK"
    filter              = "resource.type: \"k8s_container\" AND jsonPayload.message: (\"Trigger true\" AND \"id(HK-\" AND \"new_trade(false) market_reference_changed(false) trade_status_changed(true)\" AND \"swift_reason_code_changed(true) additional_reason_changed(false)\" ) AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type           = "INT64"
    unit                = "1"
    display_name        = "BNYM PUSH API TRADE STATUS REASON CODE TRIGGER BOTH VOLUME HK - Json Message published to BNYM"
    labels              = []
    label_extractors    = {}
  },
  bnym_push_api_trade_status_reason_code_trigger_both_volume_TH = {
    name                = "HoldingApi_Prod_Bnym_Push_Api_Trade_Status_Reason_Code_Trigger_Both_Volume_TH"
    filter              = "resource.type: \"k8s_container\" AND jsonPayload.message: (\"Trigger true\" AND \"id(TH-\" AND \"new_trade(false) market_reference_changed(false) trade_status_changed(true)\" AND \"swift_reason_code_changed(true) additional_reason_changed(false)\" ) AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type           = "INT64"
    unit                = "1"
    display_name        = "BNYM PUSH API TRADE STATUS REASON CODE TRIGGER BOTH VOLUME TH - Json Message published to BNYM"
    labels              = []
    label_extractors    = {}
  },
  bnym_push_api_trade_status_reason_code_trigger_both_volume_AU = {
    name                = "HoldingApi_Prod_Bnym_Push_Api_Trade_Status_Reason_Code_Trigger_Both_Volume_AU"
    filter              = "resource.type: \"k8s_container\" AND jsonPayload.message: (\"Trigger true\" AND \"id(AU-\" AND \"new_trade(false) market_reference_changed(false) trade_status_changed(true)\" AND \"swift_reason_code_changed(true) additional_reason_changed(false)\" ) AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type           = "INT64"
    unit                = "1"
    display_name        = "BNYM PUSH API TRADE STATUS REASON CODE TRIGGER BOTH VOLUME AU - Json Message published to BNYM"
    labels              = []
    label_extractors    = {}
  },
  bnym_push_api_trade_status_reason_code_trigger_both_volume_MY = {
    name                = "HoldingApi_Prod_Bnym_Push_Api_Trade_Status_Reason_Code_Trigger_Both_Volume_MY"
    filter              = "resource.type: \"k8s_container\" AND jsonPayload.message: (\"Trigger true\" AND \"id(MY-\" AND \"new_trade(false) market_reference_changed(false) trade_status_changed(true)\" AND \"swift_reason_code_changed(true) additional_reason_changed(false)\" ) AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type           = "INT64"
    unit                = "1"
    display_name        = "BNYM PUSH API TRADE STATUS REASON CODE TRIGGER BOTH VOLUME MY - Json Message published to BNYM"
    labels              = []
    label_extractors    = {}
  },
  bnym_push_api_trade_status_reason_code_trigger_both_volume_VN = {
    name                = "HoldingApi_Prod_Bnym_Push_Api_Trade_Status_Reason_Code_Trigger_Both_Volume_VN"
    filter              = "resource.type: \"k8s_container\" AND jsonPayload.message: (\"Trigger true\" AND \"id(VN-\" AND \"new_trade(false) market_reference_changed(false) trade_status_changed(true)\" AND \"swift_reason_code_changed(true) additional_reason_changed(false)\" ) AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type           = "INT64"
    unit                = "1"
    display_name        = "BNYM PUSH API TRADE STATUS REASON CODE TRIGGER BOTH VOLUME VN - Json Message published to BNYM"
    labels              = []
    label_extractors    = {}
  },
  bnym_push_api_trade_status_reason_code_trigger_both_volume_NZ = {
    name                = "HoldingApi_Prod_Bnym_Push_Api_Trade_Status_Reason_Code_Trigger_Both_Volume_NZ"
    filter              = "resource.type: \"k8s_container\" AND jsonPayload.message: (\"Trigger true\" AND \"id(NZ-\" AND \"new_trade(false) market_reference_changed(false) trade_status_changed(true)\" AND \"swift_reason_code_changed(true) additional_reason_changed(false)\" ) AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type           = "INT64"
    unit                = "1"
    display_name        = "BNYM PUSH API TRADE STATUS REASON CODE TRIGGER BOTH VOLUME NZ - Json Message published to BNYM"
    labels              = []
    label_extractors    = {}
  },
  bnym_push_api_trade_status_reason_code_trigger_both_volume_AE = {
    name                = "HoldingApi_Prod_Bnym_Push_Api_Trade_Status_Reason_Code_Trigger_Both_Volume_AE"
    filter              = "resource.type: \"k8s_container\" AND jsonPayload.message: (\"Trigger true\" AND \"id(AE-\" AND \"new_trade(false) market_reference_changed(false) trade_status_changed(true)\" AND \"swift_reason_code_changed(true) additional_reason_changed(false)\" ) AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type           = "INT64"
    unit                = "1"
    display_name        = "BNYM PUSH API TRADE STATUS REASON CODE TRIGGER BOTH VOLUME AE - Json Message published to BNYM"
    labels              = []
    label_extractors    = {}
  },
  bnym_push_api_trade_status_reason_code_trigger_both_volume_PH = {
    name                = "HoldingApi_Prod_Bnym_Push_Api_Trade_Status_Reason_Code_Trigger_Both_Volume_PH"
    filter              = "resource.type: \"k8s_container\" AND jsonPayload.message: (\"Trigger true\" AND \"id(PH-\" AND \"new_trade(false) market_reference_changed(false) trade_status_changed(true)\" AND \"swift_reason_code_changed(true) additional_reason_changed(false)\" ) AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type           = "INT64"
    unit                = "1"
    display_name        = "BNYM PUSH API TRADE STATUS REASON CODE TRIGGER BOTH VOLUME PH - Json Message published to BNYM"
    labels              = []
    label_extractors    = {}
  },
  bnym_push_api_trade_status_reason_code_trigger_both_volume_BH = {
    name                = "HoldingApi_Prod_Bnym_Push_Api_Trade_Status_Reason_Code_Trigger_Both_Volume_BH"
    filter              = "resource.type: \"k8s_container\" AND jsonPayload.message: (\"Trigger true\" AND \"id(BH-\" AND \"new_trade(false) market_reference_changed(false) trade_status_changed(true)\" AND \"swift_reason_code_changed(true) additional_reason_changed(false)\" ) AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type           = "INT64"
    unit                = "1"
    display_name        = "BNYM PUSH API TRADE STATUS REASON CODE TRIGGER BOTH VOLUME BH - Json Message published to BNYM"
    labels              = []
    label_extractors    = {}
  },
  bnym_push_api_trade_status_reason_code_trigger_both_volume_TW = {
    name                = "HoldingApi_Prod_Bnym_Push_Api_Trade_Status_Reason_Code_Trigger_Both_Volume_TW"
    filter              = "resource.type: \"k8s_container\" AND jsonPayload.message: (\"Trigger true\" AND \"id(TW-\" AND \"new_trade(false) market_reference_changed(false) trade_status_changed(true)\" AND \"swift_reason_code_changed(true) additional_reason_changed(false)\" ) AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type           = "INT64"
    unit                = "1"
    display_name        = "BNYM PUSH API TRADE STATUS REASON CODE TRIGGER BOTH VOLUME TW - Json Message published to BNYM"
    labels              = []
    label_extractors    = {}
  },
  bnym_push_api_trade_status_reason_code_trigger_both_volume_SG = {
    name                = "HoldingApi_Prod_Bnym_Push_Api_Trade_Status_Reason_Code_Trigger_Both_Volume_SG"
    filter              = "resource.type: \"k8s_container\" AND jsonPayload.message: (\"Trigger true\" AND \"id(SG-\" AND \"new_trade(false) market_reference_changed(false) trade_status_changed(true)\" AND \"swift_reason_code_changed(true) additional_reason_changed(false)\" ) AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type           = "INT64"
    unit                = "1"
    display_name        = "BNYM PUSH API TRADE STATUS REASON CODE TRIGGER BOTH VOLUME SG - Json Message published to BNYM"
    labels              = []
    label_extractors    = {}
  },
  bnym_push_api_trade_status_reason_code_trigger_both_volume_ID = {
    name                = "HoldingApi_Prod_Bnym_Push_Api_Trade_Status_Reason_Code_Trigger_Both_Volume_ID"
    filter              = "resource.type: \"k8s_container\" AND jsonPayload.message: (\"Trigger true\" AND \"id(ID-\" AND \"new_trade(false) market_reference_changed(false) trade_status_changed(true)\" AND \"swift_reason_code_changed(true) additional_reason_changed(false)\" ) AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type           = "INT64"
    unit                = "1"
    display_name        = "BNYM PUSH API TRADE STATUS REASON CODE TRIGGER BOTH VOLUME ID - Json Message published to BNYM"
    labels              = []
    label_extractors    = {}
  },
  bnym_push_api_narrative_code_trigger_only_volume_HK = {
    name                = "HoldingApi_Prod_Bnym_Push_Api_Narrative_Code_Trigger_Only_Volume_HK"
    filter              = "resource.type: \"k8s_container\" AND jsonPayload.message: (\"Trigger true\" AND \"id(HK-\" AND \"new_trade(false) market_reference_changed(false) trade_status_changed(false)\" AND \"swift_reason_code_changed(false) additional_reason_changed(true)\") AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type           = "INT64"
    unit                = "1"
    display_name        = "BNYM PUSH API NARRATIVE CODE TRIGGER ONLY VOLUME HK - Json Message published to BNYM"
    labels              = []
    label_extractors    = {}
  },
  bnym_push_api_narrative_code_trigger_only_volume_TH = {
    name                = "HoldingApi_Prod_Bnym_Push_Api_Narrative_Code_Trigger_Only_Volume_TH"
    filter              = "resource.type: \"k8s_container\" AND jsonPayload.message: (\"Trigger true\" AND \"id(TH-\" AND \"new_trade(false) market_reference_changed(false) trade_status_changed(false)\" AND \"swift_reason_code_changed(false) additional_reason_changed(true)\" ) AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type           = "INT64"
    unit                = "1"
    display_name        = "BNYM PUSH API NARRATIVE CODE TRIGGER ONLY VOLUME TH - Json Message published to BNYM"
    labels              = []
    label_extractors    = {}
  },
  bnym_push_api_narrative_code_trigger_only_volume_AU = {
    name                = "HoldingApi_Prod_Bnym_Push_Api_Narrative_Code_Trigger_Only_Volume_AU"
    filter              = "resource.type: \"k8s_container\" AND jsonPayload.message: (\"Trigger true\" AND \"id(AU-\" AND \"new_trade(false) market_reference_changed(false) trade_status_changed(false)\" AND \"swift_reason_code_changed(false) additional_reason_changed(true)\" ) AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type           = "INT64"
    unit                = "1"
    display_name        = "BNYM PUSH API NARRATIVE CODE TRIGGER ONLY VOLUME AU - Json Message published to BNYM"
    labels              = []
    label_extractors    = {}
  },
  bnym_push_api_narrative_code_trigger_only_volume_MY = {
    name                = "HoldingApi_Prod_Bnym_Push_Api_Narrative_Code_Trigger_Only_Volume_MY"
    filter              = "resource.type: \"k8s_container\" AND jsonPayload.message: (\"Trigger true\" AND \"id(MY-\" AND \"new_trade(false) market_reference_changed(false) trade_status_changed(false)\" AND \"swift_reason_code_changed(false) additional_reason_changed(true)\" ) AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type           = "INT64"
    unit                = "1"
    display_name        = "BNYM PUSH API NARRATIVE CODE TRIGGER ONLY VOLUME MY - Json Message published to BNYM"
    labels              = []
    label_extractors    = {}
  },
  bnym_push_api_narrative_code_trigger_only_volume_VN = {
    name                = "HoldingApi_Prod_Bnym_Push_Api_Narrative_Code_Trigger_Only_Volume_VN"
    filter              = "resource.type: \"k8s_container\" AND jsonPayload.message: (\"Trigger true\" AND \"id(VN-\" AND \"new_trade(false) market_reference_changed(false) trade_status_changed(false)\" AND \"swift_reason_code_changed(false) additional_reason_changed(true)\" ) AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type           = "INT64"
    unit                = "1"
    display_name        = "BNYM PUSH API NARRATIVE CODE TRIGGER ONLY VOLUME VN - Json Message published to BNYM"
    labels              = []
    label_extractors    = {}
  },
  bnym_push_api_narrative_code_trigger_only_volume_NZ = {
    name                = "HoldingApi_Prod_Bnym_Push_Api_Narrative_Code_Trigger_Only_Volume_NZ"
    filter              = "resource.type: \"k8s_container\" AND jsonPayload.message: (\"Trigger true\" AND \"id(NZ-\" AND \"new_trade(false) market_reference_changed(false) trade_status_changed(false)\" AND \"swift_reason_code_changed(false) additional_reason_changed(true)\" ) AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type           = "INT64"
    unit                = "1"
    display_name        = "BNYM PUSH API NARRATIVE CODE TRIGGER ONLY VOLUME NZ - Json Message published to BNYM"
    labels              = []
    label_extractors    = {}
  },
  bnym_push_api_narrative_code_trigger_only_volume_AE = {
    name                = "HoldingApi_Prod_Bnym_Push_Api_Narrative_Code_Trigger_Only_Volume_AE"
    filter              = "resource.type: \"k8s_container\" AND jsonPayload.message: (\"Trigger true\" AND \"id(AE-\" AND \"new_trade(false) market_reference_changed(false) trade_status_changed(false)\" AND \"swift_reason_code_changed(false) additional_reason_changed(true)\" ) AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type           = "INT64"
    unit                = "1"
    display_name        = "BNYM PUSH API NARRATIVE CODE TRIGGER ONLY VOLUME AE - Json Message published to BNYM"
    labels              = []
    label_extractors    = {}
  },
  bnym_push_api_narrative_code_trigger_only_volume_PH = {
    name                = "HoldingApi_Prod_Bnym_Push_Api_Narrative_Code_Trigger_Only_Volume_PH"
    filter              = "resource.type: \"k8s_container\" AND jsonPayload.message: (\"Trigger true\" AND \"id(PH-\" AND \"new_trade(false) market_reference_changed(false) trade_status_changed(false)\" AND \"swift_reason_code_changed(false) additional_reason_changed(true)\" ) AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type           = "INT64"
    unit                = "1"
    display_name        = "BNYM PUSH API NARRATIVE CODE TRIGGER ONLY VOLUME PH - Json Message published to BNYM"
    labels              = []
    label_extractors    = {}
  },
  bnym_push_api_narrative_code_trigger_only_volume_BH = {
    name                = "HoldingApi_Prod_Bnym_Push_Api_Narrative_Code_Trigger_Only_Volume_BH"
    filter              = "resource.type: \"k8s_container\" AND jsonPayload.message: (\"Trigger true\" AND \"id(BH-\" AND \"new_trade(false) market_reference_changed(false) trade_status_changed(false)\" AND \"swift_reason_code_changed(false) additional_reason_changed(true)\" ) AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type           = "INT64"
    unit                = "1"
    display_name        = "BNYM PUSH API NARRATIVE CODE TRIGGER ONLY VOLUME BH - Json Message published to BNYM"
    labels              = []
    label_extractors    = {}
  },
  bnym_push_api_narrative_code_trigger_only_volume_TW = {
    name                = "HoldingApi_Prod_Bnym_Push_Api_Narrative_Code_Trigger_Only_Volume_TW"
    filter              = "resource.type: \"k8s_container\" AND jsonPayload.message: (\"Trigger true\" AND \"id(TW-\" AND \"new_trade(false) market_reference_changed(false) trade_status_changed(false)\" AND \"swift_reason_code_changed(false) additional_reason_changed(true)\" ) AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type           = "INT64"
    unit                = "1"
    display_name        = "BNYM PUSH API NARRATIVE CODE TRIGGER ONLY VOLUME TW - Json Message published to BNYM"
    labels              = []
    label_extractors    = {}
  },
  bnym_push_api_narrative_code_trigger_only_volume_SG = {
    name                = "HoldingApi_Prod_Bnym_Push_Api_Narrative_Code_Trigger_Only_Volume_SG"
    filter              = "resource.type: \"k8s_container\" AND jsonPayload.message: (\"Trigger true\" AND \"id(SG-\" AND \"new_trade(false) market_reference_changed(false) trade_status_changed(false)\" AND \"swift_reason_code_changed(false) additional_reason_changed(true)\" ) AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type           = "INT64"
    unit                = "1"
    display_name        = "BNYM PUSH API NARRATIVE CODE TRIGGER ONLY VOLUME SG - Json Message published to BNYM"
    labels              = []
    label_extractors    = {}
  },
  bnym_push_api_narrative_code_trigger_only_volume_ID = {
    name                = "HoldingApi_Prod_Bnym_Push_Api_Narrative_Code_Trigger_Only_Volume_ID"
    filter              = "resource.type: \"k8s_container\" AND jsonPayload.message: (\"Trigger true\" AND \"id(ID-\" AND \"new_trade(false) market_reference_changed(false) trade_status_changed(false)\" AND \"swift_reason_code_changed(false) additional_reason_changed(true)\" ) AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type           = "INT64"
    unit                = "1"
    display_name        = "BNYM PUSH API NARRATIVE CODE TRIGGER ONLY VOLUME ID - Json Message published to BNYM"
    labels              = []
    label_extractors    = {}
  },
  proxymity_holdings_api_volume_PH = {
    name                = "HoldingApi_Prod_Proxymity_Holdings_Api_Volume_PH"
    filter              = "resource.type:\"k8s_container\" AND jsonPayload.message: \"Received proxymity holding request\" AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type           = "INT64"
    unit                = "1"
    display_name        = "PROXYMITY HOLDINGS API VOLUME PH - Received proxymity holding request"
    labels              = []
    label_extractors    = {}
  },
  proxymity_holders_api_volume_PH = {
    name                = "HoldingApi_Prod_Proxymity_Holders_Api_Volume_PH"
    filter              = "resource.type:\"k8s_container\" AND jsonPayload.message: \"Received proxymity holder request\" AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type           = "INT64"
    unit                = "1"
    display_name        = "PROXYMITY HOLDERS API VOLUME PH - Received proxymity holder request"
    labels              = []
    label_extractors    = {}
  },
  proxymity_holdings_api_response_time_over_thirty_sec_volume_PH = {
    name                = "HoldingApi_Prod_Proxymity_Holdings_Api_Response_Time_Over_Thirty_Sec_Volume_PH"
    filter              = "resource.type:\"k8s_container\" AND jsonPayload.message: \"Proxymity holding request processing took longer than 30 seconds\" AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type           = "INT64"
    unit                = "1"
    display_name        = "PROXYMITY HOLDINGS API VOLUME PH - Proxymity holding request processing took longer than 30 seconds"
    labels              = []
    label_extractors    = {}
  },
  proxymity_holders_api_response_time_over_thirty_sec_volume_PH = {
    name                = "HoldingApi_Prod_Proxymity_Holders_Api_Response_Time_Over_Thirty_Sec_Volume_PH"
    filter              = "resource.type:\"k8s_container\" AND jsonPayload.message: \"Proxymity holder request processing took longer than 30 seconds\" AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type           = "INT64"
    unit                = "1"
    display_name        = "PROXYMITY HOLDERS API VOLUME PH - Proxymity holder request processing took longer than 30 seconds"
    labels              = []
    label_extractors    = {}
  },
  gcuk_proxymity_holdings_api_volume_PH = {
    name                = "HoldingApi_Prod_GCUK_Proxymity_Holdings_Api_Volume_PH"
    filter              = "resource.type:\"k8s_container\" AND jsonPayload.message: \"Received GCS proxymity holding GCUK market request\" AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type           = "INT64"
    unit                = "1"
    display_name        = "GCUK PROXYMITY HOLDINGS API VOLUME PH - Received GCS proxymity holding GCUK market request"
    labels              = []
    label_extractors    = {}
  },
  gcuk_proxymity_holders_api_volume_PH = {
    name                = "HoldingApi_Prod_GCUK_Proxymity_Holders_Api_Volume_PH"
    filter              = "resource.type:\"k8s_container\" AND jsonPayload.message: \"Received GCS proxymity holder GCUK market request\" AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type           = "INT64"
    unit                = "1"
    display_name        = "GCUK PROXYMITY HOLDERS API VOLUME PH - Received GCS proxymity holder GCUK market request"
    labels              = []
    label_extractors    = {}
  },
  gcuk_proxymity_holdings_api_response_time_over_thirty_sec_volume_PH = {
    name                = "HoldingApi_Prod_GCUK_Proxymity_Holdings_Api_Response_Time_Over_Thirty_Sec_Volume_PH"
    filter              = "resource.type:\"k8s_container\" AND jsonPayload.message: \"Proxymity holding gcs GCUK market request processing took longer than 30 seconds\" AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type           = "INT64"
    unit                = "1"
    display_name        = "GCUK PROXYMITY HOLDINGS API VOLUME PH - Proxymity holding gcs GCUK market request processing took longer than 30 seconds"
    labels              = []
    label_extractors    = {}
  },
  gcuk_proxymity_holders_api_response_time_over_thirty_sec_volume_PH = {
    name                = "HoldingApi_Prod_GCUK_Proxymity_Holders_Api_Response_Time_Over_Thirty_Sec_Volume_PH"
    filter              = "resource.type:\"k8s_container\" AND jsonPayload.message: \"GCS Proxymity holder GCUK market request processing took longer than 30 seconds\" AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type           = "INT64"
    unit                = "1"
    display_name        = "GCUK PROXYMITY HOLDERS API VOLUME PH - GCS Proxymity holder GCUK market request processing took longer than 30 seconds"
    labels              = []
    label_extractors    = {}
  },
  gce_proxymity_holdings_api_volume_PH = {
    name                = "HoldingApi_Prod_GCE_Proxymity_Holdings_Api_Volume_PH"
    filter              = "resource.type:\"k8s_container\" AND jsonPayload.message: \"Received GCS proxymity holding GCE market request\" AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type           = "INT64"
    unit                = "1"
    display_name        = "GCE PROXYMITY HOLDINGS API VOLUME PH - Received GCS proxymity holding GCE market request"
    labels              = []
    label_extractors    = {}
  },
  gce_proxymity_holders_api_volume_PH = {
    name                = "HoldingApi_Prod_GCE_Proxymity_Holders_Api_Volume_PH"
    filter              = "resource.type:\"k8s_container\" AND jsonPayload.message: \"Received GCS proxymity holder GCE market request\" AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type           = "INT64"
    unit                = "1"
    display_name        = "GCE PROXYMITY HOLDERS API VOLUME PH - Received GCS proxymity holder GCE market request"
    labels              = []
    label_extractors    = {}
  },
  gce_proxymity_holdings_api_response_time_over_thirty_sec_volume_PH = {
    name                = "HoldingApi_Prod_GCE_Proxymity_Holdings_Api_Response_Time_Over_Thirty_Sec_Volume_PH"
    filter              = "resource.type:\"k8s_container\" AND jsonPayload.message: \"Proxymity holding gcs GCE market request processing took longer than 30 seconds\" AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type           = "INT64"
    unit                = "1"
    display_name        = "GCE PROXYMITY HOLDINGS API VOLUME PH - Proxymity holding gcs GCE market request processing took longer than 30 seconds"
    labels              = []
    label_extractors    = {}
  },
  gce_proxymity_holders_api_response_time_over_thirty_sec_volume_PH = {
    name                = "HoldingApi_Prod_GCE_Proxymity_Holders_Api_Response_Time_Over_Thirty_Sec_Volume_PH"
    filter              = "resource.type:\"k8s_container\" AND jsonPayload.message: \"GCS Proxymity holder GCE market request processing took longer than 30 seconds\" AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type           = "INT64"
    unit                = "1"
    display_name        = "GCE PROXYMITY HOLDERS API VOLUME PH - GCS Proxymity holder GCE market request processing took longer than 30 seconds"
    labels              = []
    label_extractors    = {}
  },
  Udm_unity2_evlayer_volume_stats = {
    name                = "core_Prod_Udm_unity2_evlayer_volume_stats"
    filter              = "resource.type: \"k8s_container\" AND labels.k8s-pod/app_kubernetes_io/instance=\"udm-aggregation-unity-2-event-layer-bca\" AND labels.k8s-pod/app_kubernetes_io/name=\"udm-aggregation-unity-2-event-layer-bca\" AND severity>=DEFAULT AND jsonPayload.message:\"Json Message\" AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type           = "INT64"
    unit                = "1"
    display_name        = "UDM BLEND - Json Message published to EVLAYER"
    labels              = []
    label_extractors    = {}
  },
  trade_push_event_api_volume_StateStreet_HK = {
    name                = "HoldingApi_Prod_Trade_Push_Event_Api_volume_StateStreet_HK"
    filter              = "resource.type:\"k8s_container\" AND jsonPayload.message: \"TradePushBusinessEvent Trigger true, client: StateStreet, market: HK\" AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type          = "INT64"
    unit                = "1"
    display_name        = "TRADE PUSH EVENT API VOLUME StateStreet HK - Json Message published"
    labels              = []
    label_extractors    = {}
  },
  trade_push_event_api_volume_StateStreet_AU = {
    name                = "HoldingApi_Prod_Trade_Push_Event_Api_volume_StateStreet_AU"
    filter              = "resource.type:\"k8s_container\" AND jsonPayload.message: \"TradePushBusinessEvent Trigger true, client: StateStreet, market: AU\" AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type          = "INT64"
    unit                = "1"
    display_name        = "TRADE PUSH EVENT API VOLUME StateStreet AU - Json Message published"
    labels              = []
    label_extractors    = {}
  },
  trade_push_event_api_new_trade_trigger_only_volume_StateStreet_HK = {
    name                = "HoldingApi_Prod_Trade_Push_Event_Api_New_Trade_Trigger_Only_volume_StateStreet_HK"
    filter              = "resource.type: \"k8s_container\" AND jsonPayload.message: (\"TradePushBusinessEvent Trigger true, client: StateStreet\" AND \"id(HK-\" AND \"new_trade(true) market_reference_changed(false) trade_status_changed(false)\" AND \"swift_reason_code_changed(false) additional_reason_changed(false)\") AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type           = "INT64"
    unit                = "1"
    display_name        = "TRADE PUSH EVENT API NEW TRADE TRIGGER ONLY VOLUME StateStreet HK - Json Message published"
    labels              = []
    label_extractors    = {}
  },
  trade_push_event_api_new_trade_trigger_only_volume_StateStreet_AU = {
    name                = "HoldingApi_Prod_Trade_Push_Event_Api_New_Trade_Trigger_Only_volume_StateStreet_AU"
    filter              = "resource.type: \"k8s_container\" AND jsonPayload.message: (\"TradePushBusinessEvent Trigger true, client: StateStreet\" AND \"id(AU-\" AND \"new_trade(true) market_reference_changed(false) trade_status_changed(false)\" AND \"swift_reason_code_changed(false) additional_reason_changed(false)\") AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type           = "INT64"
    unit                = "1"
    display_name        = "TRADE PUSH EVENT API NEW TRADE TRIGGER ONLY VOLUME StateStreet AU - Json Message published"
    labels              = []
    label_extractors    = {}
  },
  trade_push_event_api_market_reference_trigger_only_volume_StateStreet_HK = {
    name                = "HoldingApi_Prod_Trade_Push_Event_Api_Market_Reference_Trigger_Only_volume_StateStreet_HK"
    filter              = "resource.type: \"k8s_container\" AND jsonPayload.message: (\"TradePushBusinessEvent Trigger true, client: StateStreet\" AND \"id(HK-\" AND \"new_trade(false) market_reference_changed(true) trade_status_changed(false)\" AND \"swift_reason_code_changed(false) additional_reason_changed(false)\") AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type           = "INT64"
    unit                = "1"
    display_name        = "TRADE PUSH EVENT API MARKET REFERENCE TRIGGER ONLY VOLUME StateStreet HK - Json Message published"
    labels              = []
    label_extractors    = {}
  },
  trade_push_event_api_market_reference_trigger_only_volume_StateStreet_AU = {
    name                = "HoldingApi_Prod_Trade_Push_Event_Api_Market_Reference_Trigger_Only_volume_StateStreet_AU"
    filter              = "resource.type: \"k8s_container\" AND jsonPayload.message: (\"TradePushBusinessEvent Trigger true, client: StateStreet\" AND \"id(AU-\" AND \"new_trade(false) market_reference_changed(true) trade_status_changed(false)\" AND \"swift_reason_code_changed(false) additional_reason_changed(false)\") AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type           = "INT64"
    unit                = "1"
    display_name        = "TRADE PUSH EVENT API MARKET REFERENCE TRIGGER ONLY VOLUME StateStreet AU - Json Message published"
    labels              = []
    label_extractors    = {}
  },
  trade_push_event_api_trade_status_trigger_only_volume_StateStreet_HK = {
    name                = "HoldingApi_Prod_Trade_Push_Event_Api_Trade_Status_Trigger_Only_volume_StateStreet_HK"
    filter              = "resource.type: \"k8s_container\" AND jsonPayload.message: (\"TradePushBusinessEvent Trigger true, client: StateStreet\" AND \"id(HK-\" AND \"new_trade(false) market_reference_changed(false) trade_status_changed(true)\" AND \"swift_reason_code_changed(false)\" AND \"additional_reason_changed(false)\") AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type           = "INT64"
    unit                = "1"
    display_name        = "TRADE PUSH EVENT API TRADE STATUS TRIGGER ONLY VOLUME StateStreet HK - Json Message published"
    labels              = []
    label_extractors    = {}
  },
  trade_push_event_api_trade_status_trigger_only_volume_StateStreet_AU = {
    name                = "HoldingApi_Prod_Trade_Push_Event_Api_Trade_Status_Trigger_Only_volume_StateStreet_AU"
    filter              = "resource.type: \"k8s_container\" AND jsonPayload.message: (\"TradePushBusinessEvent Trigger true, client: StateStreet\" AND \"id(AU-\" AND \"new_trade(false) market_reference_changed(false) trade_status_changed(true)\" AND \"swift_reason_code_changed(false)\" AND \"additional_reason_changed(false)\") AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type           = "INT64"
    unit                = "1"
    display_name        = "TRADE PUSH EVENT API TRADE STATUS TRIGGER ONLY VOLUME StateStreet AU - Json Message published"
    labels              = []
    label_extractors    = {}
  },
  trade_push_event_api_market_reference_trade_status_trigger_both_volume_StateStreet_HK = {
    name                = "HoldingApi_Prod_Trade_Push_Event_Api_M_S_Trigger_Both_volume_StateStreet_HK"
    filter              = "resource.type: \"k8s_container\" AND jsonPayload.message: (\"TradePushBusinessEvent Trigger true, client: StateStreet\" AND \"id(HK-\" AND \"new_trade(false) market_reference_changed(true) trade_status_changed(true)\" AND \"swift_reason_code_changed(false) additional_reason_changed(false)\") AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type           = "INT64"
    unit                = "1"
    display_name        = "TRADE PUSH EVENT API MARKET REFERENCE TRADE STATUS TRIGGER BOTH VOLUME StateStreet HK - Json Message published"
    labels              = []
    label_extractors    = {}
  },
  trade_push_event_api_market_reference_trade_status_trigger_both_volume_StateStreet_AU = {
    name                = "HoldingApi_Prod_Trade_Push_Event_Api_M_S_Trigger_Both_volume_StateStreet_AU"
    filter              = "resource.type: \"k8s_container\" AND jsonPayload.message: (\"TradePushBusinessEvent Trigger true, client: StateStreet\" AND \"id(AU-\" AND \"new_trade(false) market_reference_changed(true) trade_status_changed(true)\" AND \"swift_reason_code_changed(false) additional_reason_changed(false)\") AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type           = "INT64"
    unit                = "1"
    display_name        = "TRADE PUSH EVENT API MARKET REFERENCE TRADE STATUS TRIGGER BOTH VOLUME StateStreet AU - Json Message published"
    labels              = []
    label_extractors    = {}
  },
  trade_push_event_api_reason_code_trigger_only_volume_StateStreet_HK = {
    name                = "HoldingApi_Prod_Trade_Push_Event_Api_Reason_Code_Trigger_Only_volume_StateStreet_HK"
    filter              = "resource.type: \"k8s_container\" AND jsonPayload.message: (\"TradePushBusinessEvent Trigger true, client: StateStreet\" AND \"id(HK-\" AND \"new_trade(false) market_reference_changed(false) trade_status_changed(false)\" AND \"swift_reason_code_changed(true) additional_reason_changed(false)\") AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type           = "INT64"
    unit                = "1"
    display_name        = "TRADE PUSH EVENT API REASON CODE TRIGGER ONLY VOLUME StateStreet HK - Json Message published"
    labels              = []
    label_extractors    = {}
  },
  trade_push_event_api_reason_code_trigger_only_volume_StateStreet_AU = {
    name                = "HoldingApi_Prod_Trade_Push_Event_Api_Reason_Code_Trigger_Only_volume_StateStreet_AU"
    filter              = "resource.type: \"k8s_container\" AND jsonPayload.message: (\"TradePushBusinessEvent Trigger true, client: StateStreet\" AND \"id(AU-\" AND \"new_trade(false) market_reference_changed(false) trade_status_changed(false)\" AND \"swift_reason_code_changed(true) additional_reason_changed(false)\") AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type           = "INT64"
    unit                = "1"
    display_name        = "TRADE PUSH EVENT API REASON CODE TRIGGER ONLY VOLUME StateStreet AU - Json Message published"
    labels              = []
    label_extractors    = {}
  },
  trade_push_event_api_trade_status_reason_code_trigger_both_volume_StateStreet_HK = {
    name                = "HoldingApi_Prod_Trade_Push_Event_Api_Trade_Status_Reason_Code_Trigger_Both_volume_StateStreet_HK"
    filter              = "resource.type: \"k8s_container\" AND jsonPayload.message: (\"TradePushBusinessEvent Trigger true, client: StateStreet\" AND \"id(HK-\" AND \"new_trade(false) market_reference_changed(false) trade_status_changed(true)\" AND \"swift_reason_code_changed(true) additional_reason_changed(false)\" ) AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type           = "INT64"
    unit                = "1"
    display_name        = "TRADE PUSH EVENT API TRADE STATUS REASON CODE TRIGGER BOTH VOLUME StateStreet HK - Json Message published"
    labels              = []
    label_extractors    = {}
  },
  trade_push_event_api_trade_status_reason_code_trigger_both_volume_StateStreet_AU = {
    name                = "HoldingApi_Prod_Trade_Push_Event_Api_Trade_Status_Reason_Code_Trigger_Both_volume_StateStreet_AU"
    filter              = "resource.type: \"k8s_container\" AND jsonPayload.message: (\"TradePushBusinessEvent Trigger true, client: StateStreet\" AND \"id(AU-\" AND \"new_trade(false) market_reference_changed(false) trade_status_changed(true)\" AND \"swift_reason_code_changed(true) additional_reason_changed(false)\" ) AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type           = "INT64"
    unit                = "1"
    display_name        = "TRADE PUSH EVENT API TRADE STATUS REASON CODE TRIGGER BOTH VOLUME StateStreet AU - Json Message published"
    labels              = []
    label_extractors    = {}
  },
  trade_push_event_api_narrative_code_trigger_only_volume_StateStreet_HK = {
    name                = "HoldingApi_Prod_Trade_Push_Event_Api_Narrative_Code_Trigger_Only_volume_StateStreet_HK"
    filter              = "resource.type: \"k8s_container\" AND jsonPayload.message: (\"TradePushBusinessEvent Trigger true, client: StateStreet\" AND \"id(HK-\" AND \"new_trade(false) market_reference_changed(false) trade_status_changed(false)\" AND \"swift_reason_code_changed(false) additional_reason_changed(true)\") AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type           = "INT64"
    unit                = "1"
    display_name        = "TRADE PUSH EVENT API NARRATIVE CODE TRIGGER ONLY VOLUME StateStreet HK - Json Message published"
    labels              = []
    label_extractors    = {}
  },
  trade_push_event_api_narrative_code_trigger_only_volume_StateStreet_AU = {
    name                = "HoldingApi_Prod_Trade_Push_Event_Api_Narrative_Code_Trigger_Only_volume_StateStreet_AU"
    filter              = "resource.type: \"k8s_container\" AND jsonPayload.message: (\"TradePushBusinessEvent Trigger true, client: StateStreet\" AND \"id(AU-\" AND \"new_trade(false) market_reference_changed(false) trade_status_changed(false)\" AND \"swift_reason_code_changed(false) additional_reason_changed(true)\") AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type           = "INT64"
    unit                = "1"
    display_name        = "TRADE PUSH EVENT API NARRATIVE CODE TRIGGER ONLY VOLUME StateStreet AU - Json Message published"
    labels              = []
    label_extractors    = {}
  },
  core_event_layer_custom_message_deliver_failure = {
    name                = "core_Prod_event_layer_custom_message_deliver_failure"
    filter              = "resource.type: \"k8s_container\" AND jsonPayload.message: \"Exception thrown when sending a message\" AND resource.labels.container_name=\"udm-aggregation-unity-2-event-layer-bca\" AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type           = "INT64"
    unit                = "1"
    display_name        = "Core Consumer Adapter to Event Layer message delivery failure"
    labels              = []
    label_extractors    = {}
  },
  core_event_layer_kafka_connection_failure = {
    name                = "core_Prod_event_layer_kafka_connection_failure"
    filter              = "resource.type: \"k8s_container\" AND jsonPayload.message: \"Error connecting to node\" AND resource.labels.container_name=\"udm-aggregation-unity-2-event-layer-bca\" AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type           = "INT64"
    unit                = "1"
    display_name        = "Core Consumer Adapter to Event Layer kafka connection failure"
    labels              = []
    label_extractors    = {}
  },
  core_kafka_coordinator_is_not_aware_of_member = {
    name                = "core_Prod_kafka_coordinator_is_not_aware_of_member"
    filter              = "resource.type: \"k8s_container\" AND jsonPayload.message: \"Transaction offset Commit failed due to consumer group metadata mismatch: The coordinator is not aware of this member\" AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type           = "INT64"
    unit                = "1"
    display_name        = "Core kafka coordinator is not aware of member"
    labels              = []
    label_extractors    = {}
  },
  core_holding_snapshot_creation_failure = {
    name                = "core_Prod_holding_snapshot_creation_failure"
    filter              = "resource.type:\"k8s_container\" AND resource.labels.container_name=\"eod-snapshot-holding-fusion\" AND textPayload: \"HOLDING_SNAPSHOT_au\" AND textPayload: \"CREATE TABLE\" AND NOT textPayload: \"CREATE TABLE IF NOT\" AND severity >= \"ERROR\" AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type           = "INT64"
    unit                = "1"
    display_name        = "Core Holding Snapshot Creation Failure"
    labels              = []
    label_extractors    = {}
  },
  pipeline_app_core_mesh_pub_sub_err = {
    name                = "core_Prod_Pipeline_core_mesh_pubsub_error"
    filter              = "resource.type:\"k8s_container\" AND (jsonPayload.stack_trace: \"Unsuccessful PubSub message publishing\" OR jsonPayload.stack_trace: \"Exception occurred while publishing message in PubSubMessagePublisher\" ) AND  resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type          = "INT64"
    unit                = "1"
    display_name        = "Unexpected core mesh pub sub publishing error"
    labels              = []
    label_extractors    = {}
  }
}

metrics = {
  Sql_Connections = {
    name                = "SQL Connections - Google Cloud CloudSQl PostGreSQL - Connections"
    object_name          = "sqlcon"
    alert_severity       = "MAJOR"     
    combiner            = "OR"
    filter              = "metric.type=\"cloudsql.googleapis.com/database/network/connections\" AND resource.type=\"cloudsql_database\" AND resource.label.database_id=has_substring(\""
    duration            = "120s"
    comparison          = "COMPARISON_GT"
    cross_series_reducer= ""
    alignment_period    = "120s"
    per_series_aligner  = "ALIGN_MAX"
    threshold           = 20
    trigger_count       = 1
    group_by_fields     =  ["resource.label.database_id"]
    api_key             = ""
    enabled             = true },
  Sql_cpu_utilization = {
    name                = "SQL CPU Utilization - Google Cloud CloudSQl PostGreSQL - SQL CPU Utilization"
    object_name          = "sqlcpu"
    alert_severity       = "MAJOR"     
    combiner            = "OR"
    filter              = "metric.type=\"cloudsql.googleapis.com/database/cpu/utilization\" AND resource.type=\"cloudsql_database\" AND resource.label.database_id=has_substring(\""
    duration            = "180s"
    comparison          = "COMPARISON_GT"
    cross_series_reducer= ""
    alignment_period    = "60s"
    per_series_aligner  = "ALIGN_MEAN"
    threshold           = 0.95
    trigger_count       = 2
    group_by_fields     =  ["resource.label.database_id"]
    api_key             = ""
    enabled             = true },
  Sql_disk_io_reads = {
    name                = "SQL Disk I/O Reads > 17500 /s - Google Cloud CloudSQl PostGreSQL - SQL Disk I/O Reads (70% of IOPS LIMIT)"
    object_name          = "sqldicread"
    alert_severity       = "MAJOR"     
    combiner            = "OR"
    filter              = "metric.type=\"cloudsql.googleapis.com/database/disk/read_ops_count\" AND resource.type=\"cloudsql_database\" AND resource.label.database_id=has_substring(\""
    duration            = "120s"
    comparison          = "COMPARISON_GT"
    cross_series_reducer= ""
    alignment_period    = "120s"
    per_series_aligner  = "ALIGN_RATE"
    threshold           = 17500
    trigger_count       = 1
    group_by_fields     =  ["resource.label.database_id"]
    api_key             = ""
    enabled             = true },
  Sql_disk_io_writes = {
    name                = "SQL Disk I/O Writes > 17500 /s - Google Cloud CloudSQl PostGreSQL - SQL Disk I/O Writes (70% of IOPS LIMIT)"
    object_name          = "sqldiscwrite"
    alert_severity       = "MAJOR"     
    combiner            = "OR"
    filter              = "metric.type=\"cloudsql.googleapis.com/database/disk/write_ops_count\" AND resource.type=\"cloudsql_database\" AND resource.label.database_id=has_substring(\""
    duration            = "300s"
    comparison          = "COMPARISON_GT"
    cross_series_reducer= ""
    alignment_period    = "300s"
    per_series_aligner  = "ALIGN_RATE"
    threshold           = 17500
    trigger_count       = 2
    group_by_fields     =  ["resource.label.database_id"]
    api_key             = ""
    enabled             = true },
  Sql_disk_utilization = {
    name                = "SQL Disk Utilization  - Google Cloud CloudSQl PostGreSQL - SQL Disk Utilization"
    object_name          = "sqldiscut"
    alert_severity       = "MAJOR"     
    combiner            = "OR"
    filter              = "metric.type=\"cloudsql.googleapis.com/database/disk/utilization\" AND resource.type=\"cloudsql_database\" AND resource.label.database_id=has_substring(\""
    duration            = "120s"
    comparison          = "COMPARISON_GT"
    cross_series_reducer= ""
    alignment_period    = "120s"
    per_series_aligner  = "ALIGN_PERCENT_CHANGE"
    threshold           = 15
    trigger_count       = 1
    group_by_fields     =  ["resource.label.database_id"]
    api_key             = ""
    enabled             = true },
  Sql_memory_utilization = {
    name                = "SQL Memory Utilization  - Google Cloud CloudSQl PostGreSQL - SQL Memory Utilization"
    object_name          = "sqlmem"
    alert_severity       = "MAJOR"     
    combiner            = "OR"
    filter              = "metric.type=\"cloudsql.googleapis.com/database/memory/utilization\" AND resource.type=\"cloudsql_database\" AND resource.label.database_id=has_substring(\""
    duration            = "300s"
    comparison          = "COMPARISON_GT"
    cross_series_reducer= ""
    alignment_period    = "300s"
    per_series_aligner  = "ALIGN_PERCENT_CHANGE"
    threshold           = 15
    trigger_count       = 1
    group_by_fields     =  ["resource.label.database_id"]
    api_key             = ""
    enabled             = true },
  Sql_send_byte_rate = {
    name                = "SQL Byte Rate > 120MBytes/s - Google Cloud CloudSQl PostGreSQL - SQL Byte Rate"
    object_name          = "sqlbytesrate"
    alert_severity       = "MAJOR"     
    combiner            = "OR"
    filter              = "metric.type=\"cloudsql.googleapis.com/database/network/sent_bytes_count\" AND resource.type=\"cloudsql_database\" AND resource.label.database_id=has_substring(\""
    duration            = "120s"
    comparison          = "COMPARISON_GT"
    cross_series_reducer= ""
    alignment_period    = "120s"
    per_series_aligner  = "ALIGN_RATE"
    threshold           = 120000000
    trigger_count       = 5
    group_by_fields     = ["resource.label.database_id"]
    api_key             = ""
    enabled             = true },
  Gke_containers_cpu = {
    name                = "GKE Container CPU - Google GKE Container - GKE Container CPU utilization"
    object_name          = "gkecpu"
    alert_severity       = "MINOR"     
    combiner            = "OR"
    filter              = "metric.type=\"kubernetes.io/container/cpu/limit_utilization\" AND resource.type=\"k8s_container\" AND resource.label.namespace_name=has_substring(\""
    duration            = "300s"
    comparison          = "COMPARISON_GT"
    cross_series_reducer= ""
    alignment_period    = "300s"
    per_series_aligner  = "ALIGN_MEAN"
    threshold           = 0.8
    trigger_count       = 1
    group_by_fields     = ["resource.label.container_name"]
    api_key             = ""
    enabled             = false },  
  Gke_containers_memory = {
    name                = "GKE Container Memory - Google GKE Container - GKE Container memory limit utilization"
    object_name          = "gkemem"
    alert_severity       = "MINOR"     
    combiner            = "OR"
    filter              = "metric.type=\"kubernetes.io/container/memory/limit_utilization\" AND resource.type=\"k8s_container\" AND resource.label.namespace_name=has_substring(\""
    duration            = "300s"
    comparison          = "COMPARISON_GT"
    cross_series_reducer= ""
    alignment_period    = "300s"
    per_series_aligner  = "ALIGN_MEAN"
    threshold           = 0.9
    trigger_count       = 1
    group_by_fields     = ["resource.label.container_name"]
    api_key             = ""
    enabled             = false },
  Gke_containers_restarts = {
    name                = "GKE Container Restarts - Google GKE Container - GKE Container Restarts"
    object_name          = "gkerestarts"
    alert_severity       = "MINOR"     
    combiner            = "OR"
    filter              = "metric.type=\"kubernetes.io/container/restart_count\" AND resource.type=\"k8s_container\" AND resource.label.namespace_name=has_substring(\""
    duration            = "120s"
    comparison          = "COMPARISON_GT"
    cross_series_reducer= "REDUCE_SUM"
    alignment_period    = "120s"
    per_series_aligner  = "ALIGN_DELTA"
    threshold           = 1
    trigger_count       = 2
    group_by_fields     = ["resource.label.container_name"]
    api_key             = ""
     enabled             = true },
 Custom_API_internal_error = {
    name                = "Custom API internal error - Unexcepted error was raised during the invocation of a custom API"
    object_name          = "custapierr"
    alert_severity       = "MAJOR"
    combiner            = "OR"
    filter              = "metric.type=\"logging.googleapis.com/user/core_Prod_Custom_API_internal_error\" AND resource.type=\"k8s_container\" AND resource.label.namespace_name=has_substring(\""
    duration            = "60s"
    comparison          = "COMPARISON_GT"
    cross_series_reducer= "REDUCE_COUNT"
    alignment_period    = "60s"
    per_series_aligner  = "ALIGN_DELTA"
    threshold           = 0
    trigger_count       = 1
    group_by_fields     = ["metric.labels.log","resource.label.namespace_name"]
    api_key             = ""
    enabled             = false },
   Pipeline_api_internal_error = {
    name                = "Pipeline application fatal error - Unexpected error has caused the Unity 2.0 ETL pipelines to fail"
    object_name          = "pipapiinterr"
    alert_severity       = "MAJOR"
    combiner            = "OR"
    filter              = "metric.type=\"logging.googleapis.com/user/core_Prod_Pipeline_application_fatal_error\" AND resource.type=\"k8s_container\" AND resource.label.namespace_name=has_substring(\""
    duration            = "60s"
    comparison          = "COMPARISON_GT"
    cross_series_reducer= "REDUCE_COUNT"
    alignment_period    = "60s"
    per_series_aligner  = "ALIGN_DELTA"
    threshold           = 0
    trigger_count       = 1
    group_by_fields     = ["metric.labels.log","resource.label.namespace_name"]
    api_key             = ""
    enabled             = false },
   Sql_server_is_down = {
    name                = "CloudSQL is Down - Unexpected error caused Cloud SQL to be down"
    object_name          = "sql-server-is-down"
    alert_severity       = "CRITICAL"
    combiner            = "OR"
    filter              = "metric.type=\"cloudsql.googleapis.com/database/up\" resource.type=\"cloudsql_database\" AND resource.label.database_id=has_substring(\""
    duration            = "0s"
    comparison          = "COMPARISON_LT"
    cross_series_reducer= ""
    alignment_period    = "60s"
    per_series_aligner  = "ALIGN_MEAN"
    threshold           = 1
    trigger_count       = 1
    group_by_fields     = []
    api_key             = ""
    enabled             = true },
  Sql_server_available_for_failover = {
    name                = "CloudSQL is Down - Database Server failover unavaliable"
    object_name          = "sql-failover-is-down"
    alert_severity       = "CRITICAL"
    combiner            = "OR"
    filter              = "metric.type=\"cloudsql.googleapis.com/database/up\" resource.type=\"cloudsql_database\" AND resource.label.database_id=has_substring(\""
    duration            = "180s"
    comparison          = "COMPARISON_LT"
    cross_series_reducer= ""
    alignment_period    = "60s"
    per_series_aligner  = "ALIGN_MEAN"
    threshold           = 1
    trigger_count       = 1
    group_by_fields     = []
    api_key             = ""
    enabled             = true },
  Sql_server_uptime = {
    name                = "CloudSQL is Down - Database Server down for more than 5 min"
    object_name          = "sql-uptime"
    alert_severity       = "CRITICAL"
    combiner            = "OR"
    filter              = "metric.type=\"cloudsql.googleapis.com/database/uptime\" resource.type=\"cloudsql_database\" AND resource.label.database_id=has_substring(\""
    duration            = "300s"
    comparison          = "COMPARISON_LT"
    cross_series_reducer= ""
    alignment_period    = "60s"
    per_series_aligner  = "ALIGN_MEAN"
    threshold           = 1
    trigger_count       = 1
    group_by_fields     = []
    api_key             = ""
    enabled             = true },
  Sql_server_state = {
    name                = "CloudSQL failed - Database Server is in state failed for 3 minutes"
    object_name          = "sql-state"
    alert_severity       = "CRITICAL"
    combiner            = "OR"
    filter              = "metric.type=\"cloudsql.googleapis.com/database/instance_state\" resource.type=\"cloudsql_database\" AND metric.label.state=\"FAILED\" AND resource.label.database_id=has_substring(\""
    duration            = "180s"
    comparison          = "COMPARISON_GT"
    cross_series_reducer= ""
    alignment_period    = "60s"
    per_series_aligner  = "ALIGN_COUNT_TRUE"
    threshold           = 0
    trigger_count       = 1
    group_by_fields     = []
    api_key             = ""
    enabled             = true
  }
}
