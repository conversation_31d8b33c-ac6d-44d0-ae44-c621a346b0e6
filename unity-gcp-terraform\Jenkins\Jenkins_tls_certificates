import hudson.model.ParameterDefinition
import hudson.model.JobProperty
import groovy.json.*
import jenkins.model.*
import java.io.*
import java.io.File
import groovy.io.FileType
def project_target = params.project_target ?: "GKE"
def AGENT = "unity-dev-jenkins-agent"
def extra_opts = ""
def clusterName = params.clusterName ?: ""

pipeline {    
    agent { label env.AGENT_LABEL ?: AGENT }
    environment {
        VEDSDK_CREDS = credentials('VEDSDK_CREDSTORE')
    }

    stages {      
      stage("Connect to GCP") {
            when {
                expression {
                    project_target == "GKE"
                }
            }
            steps {
                script {
                    env.v_project = this.sh(
                            script: """gcloud config list --format 'value(core.project)'""",
                            returnStdout: true
                    ).trim()
                    env.v_clusterName = this.sh(
                            script: """gcloud container clusters list --filter="NAME:gke-t2-vpc3" --region=asia-east2 --format 'value(NAME)'""",
                            returnStdout: true
                    ).trim()
                    env.v_kubeCtlLib = this.sh(
                            script: """gcloud compute addresses list --filter="NAME:gke-kubectl-vpc3" --format 'value(ADDRESS)'""",
                            returnStdout: true
                    ).trim()
                }
            }
        }
        stage("Connect to KOPS") {
            // What Role to use
            when {
                expression {
                    project_target == "KOPS"
                }
            }
            steps {
                script {
                    env.v_project = this.sh(
                            script: """gcloud config list --format 'value(core.project)'""",
                            returnStdout: true
                    ).trim()
                    this.sh """
                        unset HTTPS_PROXY
                        gsutil cp -r gs://${v_project}-app-runtime-dependencies/secrets/env/kop/kubeconfig/jenkins-admin.${clusterName} /tmp
                        kubectl get ns --kubeconfig=/tmp/jenkins-admin.${clusterName}
                        export KUBECONFIG=/tmp/jenkins-admin.${clusterName}
                    """
                }
            }
        }
      stage ('Create Venafi Certificates and TLS Secrets') { 
        steps {
          script {
            echo """create venafi certificates on ${vpc} cluster"""
            extra_opts= env.extra_opts ?: extra_opts
            kubectlProxy = env.kubectlProxy ? "--kubeproxy=\"" + kubectlProxy + "\"" : ""
            if ( namespace.isEmpty() ){
              if ( ! env.jenkinsCredential ) {
                sh("""
                 [[ "${project_target}" == "KOPS" ]] && export KUBECONFIG=/tmp/jenkins-admin.${clusterName} || export GKE_OPTS="--setup-kubeconfig=1" ;
                 ./Scripts/bash/create-tls-secrets.sh --subdomain=\"${vpc}\" --domain=${domain} --vedsdk-username=${VEDSDK_CREDS_USR} --vedsdk-password=${VEDSDK_CREDS_PSW} --upload-bucket=\"${bucket}\" \${GKE_OPTS} ${extra_opts} ${kubectlProxy}
                 """)
              } else {
                withCredentials([file(credentialsId: env.jenkinsCredential, variable: 'KUBECONFIG_FILE')]) {
                  sh("""
                    export KUBECONFIG=${KUBECONFIG_FILE};
                    ./Scripts/bash/create-tls-secrets.sh --subdomain=\"${vpc}\" --domain=${domain} --vedsdk-username=${VEDSDK_CREDS_USR} --vedsdk-password=${VEDSDK_CREDS_PSW} --upload-bucket=\"${bucket}\" \${GKE_OPTS} ${extra_opts} ${kubectlProxy}
                  """)
                }                
              }              
            } else {
              if ( ! env.jenkinsCredential ) {
              sh("""
                 [[ "${project_target}" == "KOPS" ]] && export KUBECONFIG=/tmp/jenkins-admin.${clusterName} || export GKE_OPTS="--setup-kubeconfig=1";
                 ./Scripts/bash/create-tls-secrets.sh --subdomain=\"${vpc}\" --domain=${domain} --vedsdk-username=${VEDSDK_CREDS_USR} --vedsdk-password=${VEDSDK_CREDS_PSW} --upload-bucket=\"${bucket}\" --namespace=${namespace} \${GKE_OPTS} ${extra_opts} ${kubectlProxy}
                 """)
              } else {
                withCredentials([file(credentialsId: env.jenkinsCredential, variable: 'KUBECONFIG_FILE')]) {
                  sh("""
                    export KUBECONFIG=${KUBECONFIG_FILE};
                    ./Scripts/bash/create-tls-secrets.sh --subdomain=\"${vpc}\" --domain=${domain} --vedsdk-username=${VEDSDK_CREDS_USR} --vedsdk-password=${VEDSDK_CREDS_PSW} --upload-bucket=\"${bucket}\" --namespace=${namespace} \${GKE_OPTS} ${extra_opts} ${kubectlProxy}
                  """)
                }                
              }  
            }
          }
        }
      }
    }
   
    post {
        /*unsuccessful {
            mail bcc: '',
                body: 'ERROR occured in automatic mgmt host rotation occured. Please inspect relevant Jenkins pipeline. Regards DevOps Team',
                cc: '',
                from: 'unity_cloud_devops@noreply',
                replyTo: '',
                subject: "ERROR in automatic mgmt host rotation notification",
                to: '<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>'
        } */      
        cleanup {
            script {
                cleanWs()
            }
        }
    }		

}
