#!/usr/bin/env bash
#set -euo pipefail
echo "$0 - started"
if [[ $(uname -r | grep el7 | wc -l) -gt 0 ]]; then 

  echo "<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<< PYTHON LIBRARIES AND PACKAGES>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>"
  echo "starting python3 installation"
  
  sudo touch /tmp/curloutput.log
  echo "assigning permissions "
  
  #installing the python3 from nexus
  wget --user=vagrant --password=vagrant "https://gbmt-nexus.prd.fx.gbm.cloud.uk.hsbc/repository/centos-yum/7/os/x86_64/Packages/python3-libs-3.6.8-17.el7.x86_64.rpm"
  
  yum -y install python3-libs-3.6.8-17.el7.x86_64.rpm
  
  #installing the rpm file pre-requisites
  sudo yum -y install python-rpm-macros
  sudo yum -y install python3-rpm-generators
  sudo yum -y install python3-rpm-macros
  sudo yum -y install redhat-rpm-config
  
  #downloading python3-dlevel compatible version
  echo "downloading python3-devel from nexus" 
  wget --user=vagrant --password=vagrant "https://gbmt-nexus.prd.fx.gbm.cloud.uk.hsbc/repository/centos-yum/7/os/x86_64/Packages/python3-devel-3.6.8-17.el7.x86_64.rpm"
  
  #installing python3-devel
  sudo yum -y install python3-devel-3.6.8-17.el7.x86_64.rpm
  
  #installing gcc package
  sudo yum -y install gcc
  
  #installing gcc-c++ package
  sudo yum -y install gcc-c++

else 
  sudo yum -y install python36
  sudo yum -y install python3-pip
  sudo yum -y install python3-devel
  sudo yum -y install python-rpm-macros
  sudo yum -y install python3-rpm-generators
  sudo yum -y install python3-rpm-macros
  sudo yum -y install redhat-rpm-config
  sudo yum -y install gcc
  sudo yum -y install gcc-c++
fi;

sudo chmod -R a+rx /usr/
sudo chmod -R 755 /root/
sudo chmod -R a+rx /root/
sudo chmod -R a+rx /usr/lib/python3.6/site-packages
sudo chmod -R 600 /root/.ssh/id_rsa
sudo chmod -R 644 /root/.ssh/id_rsa.pub

cd /usr/lib
ls -ltr

cd $BUILD_PATH/build_tools
export PATH="/usr/lib64/python3.6/site-packages:/usr/lib/python3.6/site-packages:/usr/local/lib/python3.6/site-packages:$PATH"
export PATH="/usr/local/bin:$PATH"
PYTHONPATH="$HOME/Scripts/:$PATH" 

# downloading pip packages for running sqltobq.py programs
#downloading latest pip and dependencies for cryptography package
wget --user=vagrant --password=vagrant "https://gbmt-nexus.prd.fx.gbm.cloud.uk.hsbc/repository/pypi-proxy/packages/pip/21.0.1/pip-21.0.1-py3-none-any.whl"
pip-3 install pip-21.0.1-py3-none-any.whl

wget --user=vagrant --password=vagrant "https://gbmt-nexus.prd.fx.gbm.cloud.uk.hsbc/repository/pypi-proxy/packages/toml/0.10.2/toml-0.10.2-py2.py3-none-any.whl"
pip-3 install toml-0.10.2-py2.py3-none-any.whl

wget --user=vagrant --password=vagrant "https://gbmt-nexus.prd.fx.gbm.cloud.uk.hsbc/repository/pypi-proxy/packages/semantic-version/2.8.5/semantic_version-2.8.5-py2.py3-none-any.whl"
pip-3 install semantic_version-2.8.5-py2.py3-none-any.whl

wget --user=vagrant --password=vagrant "https://gbmt-nexus.prd.fx.gbm.cloud.uk.hsbc/repository/pypi-proxy/packages/setuptools-rust/0.11.6/setuptools_rust-0.11.6-py3-none-any.whl"
pip-3 install setuptools_rust-0.11.6-py3-none-any.whl

#installing psycopg2-binary
echo "downloading and installing psycopg2-binary from nexus" 
pip-3 install psycopg2-binary --index-url https://vagrant:<EMAIL>/repository/pypi-proxy/simple --trusted-host gbmt-nexus.prd.fx.gbm.cloud.uk.hsbc

#installing pandas
echo "downloading pandas from nexus" 
pip-3 install pandas --index-url https://vagrant:<EMAIL>/repository/pypi-proxy/simple --trusted-host gbmt-nexus.prd.fx.gbm.cloud.uk.hsbc

#installing pymongo
echo "downloading pymongo from nexus" 
pip-3 install pymongo --index-url https://vagrant:<EMAIL>/repository/pypi-proxy/simple --trusted-host gbmt-nexus.prd.fx.gbm.cloud.uk.hsbc

#installing google-auth-oauthlib
echo "downloading google-auth-oauthlib from nexus" 
pip-3 install google-auth-oauthlib --index-url https://vagrant:<EMAIL>/repository/pypi-proxy/simple --trusted-host gbmt-nexus.prd.fx.gbm.cloud.uk.hsbc

#installing google-api-python-client
echo "downloading google-api-python-client from nexus" 
pip-3 install google-api-python --index-url https://vagrant:<EMAIL>/repository/pypi-proxy/simple --trusted-host gbmt-nexus.prd.fx.gbm.cloud.uk.hsbc

#installng oauth2client
echo "downloading oauth2client"
pip-3 install oauth2client --index-url https://vagrant:<EMAIL>/repository/pypi-proxy/simple --trusted-host gbmt-nexus.prd.fx.gbm.cloud.uk.hsbc

#installing google-cloud-storage
echo "downloading google-cloud-storage from nexus" 
pip-3 install google-cloud-storage --index-url https://vagrant:<EMAIL>/repository/pypi-proxy/simple --trusted-host gbmt-nexus.prd.fx.gbm.cloud.uk.hsbc

echo "python package for encrypting the password"
pip-3 install cryptography --index-url https://vagrant:<EMAIL>/repository/pypi-proxy/simple --trusted-host gbmt-nexus.prd.fx.gbm.cloud.uk.hsbc

pip-3 install proto-plus --index-url https://vagrant:<EMAIL>/repository/pypi-proxy/simple --trusted-host gbmt-nexus.prd.fx.gbm.cloud.uk.hsbc

pip-3 install jsonpath --index-url https://vagrant:<EMAIL>/repository/pypi-proxy/simple --trusted-host gbmt-nexus.prd.fx.gbm.cloud.uk.hsbc

pip-3 install jsonpath_rw --index-url https://vagrant:<EMAIL>/repository/pypi-proxy/simple --trusted-host gbmt-nexus.prd.fx.gbm.cloud.uk.hsbc

pip-3 install jsonpath_rw_ext --index-url https://vagrant:<EMAIL>/repository/pypi-proxy/simple --trusted-host gbmt-nexus.prd.fx.gbm.cloud.uk.hsbc

#installing google-cloud-bigquery
echo "downloading google-cloud-bigquery from nexus" 
pip-3 install google-cloud-bigquery --no-dependencies --index-url https://vagrant:<EMAIL>/repository/pypi-proxy/simple --trusted-host gbmt-nexus.prd.fx.gbm.cloud.uk.hsbc

echo "downloading google-cloud-logging from nexus" 
pip-3 install google-cloud-logging --no-dependencies --index-url https://vagrant:<EMAIL>/repository/pypi-proxy/simple --trusted-host gbmt-nexus.prd.fx.gbm.cloud.uk.hsbc

echo "downloading google-cloud-logging from nexus" 
pip-3 install google-cloud-logging --no-dependencies --index-url https://vagrant:<EMAIL>/repository/pypi-proxy/simple --trusted-host gbmt-nexus.prd.fx.gbm.cloud.uk.hsbc

echo "Installing Ansible"
df -Ph
pip-3 install ansible --index-url=https://vagrant:<EMAIL>/repository/pypi-proxy/simple --trusted-host gbmt-nexus.prd.fx.gbm.cloud.uk.hsbc --cert /etc/pki/tls/cert.pem

echo "Make pip modules dir accessible from jenbld users"
chmod 777 /usr/local/lib64/python3.6
chmod -R 777 /usr/local/lib64/python3.6
chmod -R 777 /usr/local/lib64/python3.6/

chmod 777 /usr/local/lib/python3.6
chmod -R 777 /usr/local/lib/python3.6
chmod -R 777 /usr/local/lib/python3.6/
#chmod -R 777 /usr/local/lib/python3.6
#chmod -R 777 /usr/local/lib/python3.6/
#chmod 777 /usr/local/lib/python3.6
#chmod -R a+rX /usr/lib/python3.6/site-packages/
#chmod -R a+rX /root/.ansible/plugins/modules
#chmod -R a+rX /usr/share/ansible/plugins/modules

sudo mkdir /etc/ansible
sudo mv $BUILD_PATH/ansible/ansible.cfg /etc/ansible/


echo "Check Ansible version"
ansible --version

echo "<<<<INSTALLATION COMPLETED>>>>>>>"

echo "$0 - ended"