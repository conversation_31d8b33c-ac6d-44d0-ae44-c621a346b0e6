#!/usr/bin/env bash
set -euo pipefail

sudo mkdir -p $BUILD_PATH/build_tools/ansible/setup

cd $BUILD_PATH/build_tools/ansible/setup
sudo git clone https://$<EMAIL>/google-foundation-platform/locked_type3_vm.git
sudo chmod -R 777 $BUILD_PATH/build_tools/ansible/setup
cd $BUILD_PATH/build_tools/ansible/setup
echo $(ls -ltr)
touch $BUILD_PATH/build_tools/ansible/setup/lock_down.yml
cat > $BUILD_PATH/build_tools/ansible/setup/lock_down.yml <<EOF
---
- hosts: localhost
  become: yes
  roles:
    - role: locked_type3_vm
      vars:
         host_shutdown: false
...
EOF

cat $BUILD_PATH/build_tools/ansible/setup/lock_down.yml